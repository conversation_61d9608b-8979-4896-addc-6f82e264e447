import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import matplotlib.pyplot as plt
import seaborn as sns

# 读取数据
print("读取股票数据...")
data = pd.read_excel('股票明细.xlsx')
print(f"数据形状: {data.shape}")
print("\n前5行数据:")
print(data.head())

# 检查数据日期范围
data['日期'] = pd.to_datetime(data['日期'])
print("\n数据日期范围:")
print(f"{data['日期'].min()} 至 {data['日期'].max()}")

# 检查每个日期的股票数量
date_counts = data.groupby('日期').size()
print("\n每个日期的股票数量:")
print(date_counts)

# 检查是否有连续日期的数据
print("\n检查部分股票的连续日期记录:")
stocks = data['股票代码'].unique()[:5]
for stock in stocks:
    stock_data = data[data['股票代码'] == stock].sort_values('日期')
    if len(stock_data) > 1:
        print(f"\n{stock} ({data[data['股票代码']==stock]['股票名称'].iloc[0]}) 的日期记录:")
        print(stock_data[['日期', '当前价格', '涨跌幅']].head())

# 创建标签：次日是否上涨100%
print("\n创建标签：次日是否上涨100%...")

# 按股票代码分组，计算次日涨幅
def create_next_day_return(group):
    group = group.sort_values('日期')
    group['次日涨幅'] = group['当前价格'].shift(-1) / group['当前价格'] - 1
    return group

# 应用到每个股票组
data_with_next_day = data.groupby('股票代码').apply(create_next_day_return).reset_index(drop=True)

# 创建目标变量：次日是否上涨100%
data_with_next_day['目标_次日上涨100%'] = (data_with_next_day['次日涨幅'] >= 1.0).astype(int)

# 删除最后一个日期的数据（因为没有次日数据）
data_with_next_day = data_with_next_day.dropna(subset=['次日涨幅'])

print(f"处理后的数据形状: {data_with_next_day.shape}")
print("\n目标变量分布:")
print(data_with_next_day['目标_次日上涨100%'].value_counts())
print(f"上涨100%的比例: {data_with_next_day['目标_次日上涨100%'].mean():.4f}")

# 特征工程
print("\n进行特征工程...")

# 提取技术指标特征
data_with_next_day['均线多头排列'] = data_with_next_day['技术指标'].str.contains('均线多头排列').astype(int)
data_with_next_day['成交量放大'] = data_with_next_day['技术指标'].str.contains('成交量放大').astype(int)
data_with_next_day['MACD金叉'] = data_with_next_day['技术指标'].str.contains('MACD金叉').astype(int)
data_with_next_day['RSI反弹'] = data_with_next_day['技术指标'].str.contains('RSI反弹').astype(int)
data_with_next_day['KDJ金叉'] = data_with_next_day['技术指标'].str.contains('KDJ金叉').astype(int)
data_with_next_day['布林带突破'] = data_with_next_day['技术指标'].str.contains('布林带突破').astype(int)

# 趋势特征
data_with_next_day['趋势_强势上涨'] = (data_with_next_day['趋势'] == 'strong_up').astype(int)
data_with_next_day['趋势_上涨'] = (data_with_next_day['趋势'] == 'up').astype(int)
data_with_next_day['趋势_盘整'] = (data_with_next_day['趋势'] == 'neutral').astype(int)
data_with_next_day['趋势_下跌'] = (data_with_next_day['趋势'] == 'down').astype(int)
data_with_next_day['趋势_强势下跌'] = (data_with_next_day['趋势'] == 'strong_down').astype(int)

# 价格与目标价/止损价的关系
data_with_next_day['目标价差比'] = (data_with_next_day['目标价'] - data_with_next_day['当前价格']) / data_with_next_day['当前价格']
data_with_next_day['止损价差比'] = (data_with_next_day['当前价格'] - data_with_next_day['止损价']) / data_with_next_day['当前价格']

# 选择特征
features = [
    '涨跌幅', '技术强度', '目标价差比', '止损价差比',
    '均线多头排列', '成交量放大', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
    '趋势_强势上涨', '趋势_上涨', '趋势_盘整', '趋势_下跌', '趋势_强势下跌'
]

X = data_with_next_day[features]
y = data_with_next_day['目标_次日上涨100%']

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)

print(f"训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")

# 训练随机森林模型
print("\n训练随机森林模型...")
rf_model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
rf_model.fit(X_train, y_train)

# 评估模型
y_pred = rf_model.predict(X_test)
print("\n随机森林模型评估:")
print(f"准确率: {accuracy_score(y_test, y_pred):.4f}")
print("\n分类报告:")
print(classification_report(y_test, y_pred))

# 特征重要性
feature_importance = pd.DataFrame({
    'feature': features,
    'importance': rf_model.feature_importances_
}).sort_values('importance', ascending=False)

print("\n特征重要性:")
print(feature_importance)

# 训练梯度提升模型
print("\n训练梯度提升模型...")
gb_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
gb_model.fit(X_train, y_train)

# 评估梯度提升模型
y_pred_gb = gb_model.predict(X_test)
print("\n梯度提升模型评估:")
print(f"准确率: {accuracy_score(y_test, y_pred_gb):.4f}")
print("\n分类报告:")
print(classification_report(y_test, y_pred_gb))

# 提取规则
print("\n提取预测次日上涨100%的规则...")

# 使用随机森林的特征重要性
top_features = feature_importance.head(5)['feature'].tolist()
print(f"最重要的5个特征: {top_features}")

# 分析正样本的特征分布
positive_samples = data_with_next_day[data_with_next_day['目标_次日上涨100%'] == 1]
negative_samples = data_with_next_day[data_with_next_day['目标_次日上涨100%'] == 0]

print("\n正样本(次日上涨100%)的特征统计:")
for feature in top_features:
    pos_mean = positive_samples[feature].mean()
    neg_mean = negative_samples[feature].mean()
    print(f"{feature}: 正样本均值={pos_mean:.4f}, 负样本均值={neg_mean:.4f}, 差异倍数={(pos_mean/neg_mean if neg_mean != 0 else 'inf')}")

# 提取决策规则
print("\n基于随机森林的决策规则:")
# 获取所有树的决策路径
n_trees = 5  # 只分析前5棵树
for i in range(min(n_trees, len(rf_model.estimators_))):
    tree = rf_model.estimators_[i]
    print(f"\n树 #{i+1} 的决策路径:")
    
    # 获取叶子节点的样本数和类别
    n_nodes = tree.tree_.node_count
    children_left = tree.tree_.children_left
    children_right = tree.tree_.children_right
    feature = tree.tree_.feature
    threshold = tree.tree_.threshold
    
    # 获取叶子节点
    leaf_nodes = [(i, tree.tree_.value[i]) for i in range(n_nodes) if children_left[i] == children_right[i]]
    
    # 找出预测正类的叶子节点
    positive_leaves = [node_id for node_id, value in leaf_nodes if value[0][1] > value[0][0]]
    
    if positive_leaves:
        print(f"找到 {len(positive_leaves)} 个预测正类的叶子节点")
        
        # 分析第一个正类叶子节点的路径
        if positive_leaves:
            node_id = positive_leaves[0]
            path = []
            
            # 回溯到根节点
            while node_id != 0:
                # 找到父节点
                parent_found = False
                for i in range(n_nodes):
                    if children_left[i] == node_id or children_right[i] == node_id:
                        is_left = (children_left[i] == node_id)
                        path.append((i, features[feature[i]], threshold[i], "小于等于" if is_left else "大于"))
                        node_id = i
                        parent_found = True
                        break
                if not parent_found:
                    break
            
            # 反转路径，从根节点开始
            path.reverse()
            
            print("从根节点到正类叶子节点的路径:")
            for i, feat, thresh, direction in path:
                print(f"节点 {i}: 如果 {feat} {direction} {thresh:.4f}")
    else:
        print("没有找到预测正类的叶子节点")

# 总结发现的规则
print("\n总结发现的规则:")
print("基于特征重要性和模型分析，以下是预测股票次日上涨100%的关键规则:")
