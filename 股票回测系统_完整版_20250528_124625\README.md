# 股票回测系统 - 完整版

## 📋 系统说明

这是一个完整的股票回测系统，包含以下功能：
- 策略回测分析
- 累积涨幅计算
- 智能数据预加载
- 图形化用户界面

## 🚀 使用方法

### 方法1：双击启动脚本（推荐）
1. 双击 `启动股票回测系统.bat`
2. 等待程序启动

### 方法2：直接运行程序
1. 双击 `股票回测系统_完整版.exe`

## 📁 数据目录要求

程序需要以下数据目录结构：
```
complete_excel_results/
├── stock_data/              # 历史股票数据
├── tech_strength/           # 技术强度数据
│   └── daily/              # 按日期存储的技术强度文件
├── 所有策略汇总.xlsx        # 策略汇总文件
└── 股票明细_完整.xlsx       # 股票明细文件
```

## ⚙️ 系统要求

- Windows 7/8/10/11 (64位)
- 至少 4GB 内存
- 至少 2GB 可用磁盘空间

## 🔧 故障排除

### 程序无法启动
1. 检查是否有杀毒软件拦截
2. 尝试以管理员身份运行
3. 检查系统是否缺少 Visual C++ 运行库

### 数据加载失败
1. 确保数据目录 `complete_excel_results` 存在
2. 检查数据文件是否完整
3. 确保有足够的磁盘空间

### 性能问题
1. 关闭其他占用内存的程序
2. 确保有足够的可用内存
3. 使用SSD硬盘可提升性能

## 📞 技术支持

如遇到问题，请检查：
1. 系统日志（程序界面中的日志区域）
2. 数据目录是否正确
3. 系统资源是否充足

## 📝 版本信息

- 版本: 完整版
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 包含功能: 完整回测系统 + 智能累积涨幅生成
