"""
股票交易策略示例用法

展示如何使用各种交易策略
"""

from trading_strategies.strategy_interface import (
    get_strategy_recommendations,
    get_all_strategy_recommendations,
    compare_strategies
)

def example_single_strategy():
    """
    示例：使用单个策略进行预测
    """
    print("示例：使用单个策略进行预测")
    print("=" * 50)

    # 使用策略1预测2025-05-10的股票
    print("\n使用策略1预测2025-05-10的股票:")
    strategy_1_stocks = get_strategy_recommendations('strategy_1', '2025-05-10')

    # 使用策略A预测2025-05-10的股票
    print("\n使用策略A预测2025-05-10的股票:")
    strategy_A_stocks = get_strategy_recommendations('strategy_A', '2025-05-10')

    # 使用策略B预测2025-05-10的股票
    print("\n使用策略B预测2025-05-10的股票:")
    strategy_B_stocks = get_strategy_recommendations('strategy_B', '2025-05-10')

    # 使用策略C预测2025-05-10的股票
    print("\n使用策略C预测2025-05-10的股票:")
    strategy_C_stocks = get_strategy_recommendations('strategy_C', '2025-05-10')

def example_all_strategies():
    """
    示例：使用所有策略进行预测
    """
    print("示例：使用所有策略进行预测")
    print("=" * 50)

    # 使用所有策略预测2025-05-10的股票
    print("\n使用所有策略预测2025-05-10的股票:")
    all_strategies = get_all_strategy_recommendations('2025-05-10')

def example_compare_strategies():
    """
    示例：比较不同策略在一段时间内的表现
    """
    print("示例：比较不同策略在一段时间内的表现")
    print("=" * 50)

    # 比较不同策略在2025-05-01至2025-05-09的表现
    print("\n比较不同策略在2025-05-01至2025-05-09的表现:")
    results, summary = compare_strategies('2025-05-01', '2025-05-09')

    # 打印策略汇总
    print("\n策略汇总:")
    print(summary)

if __name__ == "__main__":
    # 选择要运行的示例
    example_single_strategy()
    # example_all_strategies()
    # example_compare_strategies()
