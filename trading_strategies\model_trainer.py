"""
股票交易模型训练模块

提供独立的模型训练功能，可以通过按钮触发
"""

import pandas as pd
import numpy as np
import os
import joblib
from datetime import datetime, timedelta
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
from .strategy_implementations import preprocess_data

def train_and_save_model(data_file_path='股票明细.xlsx', model_dir='trained_models'):
    """
    训练模型并保存到指定目录

    参数:
    data_file_path: 股票数据文件路径
    model_dir: 模型保存目录

    返回:
    训练结果信息字典
    """
    start_time = datetime.now()
    print(f"开始训练模型: {start_time}")

    # 创建模型保存目录
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    # 加载数据
    print(f"加载股票数据: {data_file_path}")
    try:
        stock_data = pd.read_excel(data_file_path)
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
    except Exception as e:
        print(f"加载股票数据失败: {e}")
        return {
            'success': False,
            'error': f"加载股票数据失败: {e}",
            'start_time': start_time,
            'end_time': datetime.now(),
            'duration': (datetime.now() - start_time).total_seconds()
        }

    # 预处理数据
    try:
        processed_data = preprocess_data(stock_data)
    except Exception as e:
        print(f"预处理数据失败: {e}")
        return {
            'success': False,
            'error': f"预处理数据失败: {e}",
            'start_time': start_time,
            'end_time': datetime.now(),
            'duration': (datetime.now() - start_time).total_seconds()
        }

    # 提取特征
    features = [
        '技术强度', '连续技术强度5天数',
        '技术强度趋势', '价格趋势', '涨跌幅趋势',
        '涨跌幅'
    ]

    # 添加技术指标特征
    if '技术指标_均线多头排列' in processed_data.columns:
        tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
                          '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']
        for indicator in tech_indicators:
            features.append(f'技术指标_{indicator}')

    # 特征和目标变量
    X_train = processed_data[features]
    y_train = processed_data['是否盈利']

    # 处理缺失值
    valid_indices = ~X_train.isnull().any(axis=1)
    X_train = X_train[valid_indices]
    y_train = y_train[valid_indices]

    # 标准化特征
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)

    # 训练梯度提升模型
    print("训练梯度提升模型...")
    try:
        gb_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
        gb_model.fit(X_train_scaled, y_train)

        # 计算特征重要性
        feature_importance = pd.DataFrame({
            'feature': features,
            'importance': gb_model.feature_importances_
        }).sort_values('importance', ascending=False)

        print("模型训练完成")
        print("\n特征重要性:")
        for i, row in feature_importance.iterrows():
            print(f"{row['feature']}: {row['importance']:.4f}")

        # 保存模型和特征缩放器
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_file = f"{model_dir}/gb_model_{timestamp}.joblib"
        scaler_file = f"{model_dir}/scaler_{timestamp}.joblib"
        features_file = f"{model_dir}/features_{timestamp}.joblib"

        joblib.dump(gb_model, model_file)
        joblib.dump(scaler, scaler_file)
        joblib.dump(features, features_file)

        # 保存最新模型的路径
        latest_model_info = {
            'model_file': model_file,
            'scaler_file': scaler_file,
            'features_file': features_file,
            'timestamp': timestamp
        }
        joblib.dump(latest_model_info, f"{model_dir}/latest_model_info.joblib")

        # 保存特征重要性
        feature_importance.to_excel(f"{model_dir}/feature_importance_{timestamp}.xlsx", index=False)

        # 绘制特征重要性图表
        plt.figure(figsize=(10, 6))
        plt.barh(feature_importance['feature'], feature_importance['importance'])
        plt.xlabel('重要性')
        plt.ylabel('特征')
        plt.title('特征重要性')
        plt.tight_layout()
        plt.savefig(f"{model_dir}/feature_importance_{timestamp}.png")

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        print(f"\n模型保存完成:")
        print(f"模型文件: {model_file}")
        print(f"缩放器文件: {scaler_file}")
        print(f"特征文件: {features_file}")
        print(f"训练用时: {duration:.2f} 秒")

        return {
            'success': True,
            'model_file': model_file,
            'scaler_file': scaler_file,
            'features_file': features_file,
            'feature_importance': feature_importance,
            'start_time': start_time,
            'end_time': end_time,
            'duration': duration,
            'record_count': len(processed_data),
            'valid_record_count': len(X_train),
            'features': features
        }

    except Exception as e:
        print(f"训练模型失败: {e}")
        import traceback
        traceback.print_exc()

        return {
            'success': False,
            'error': f"训练模型失败: {e}",
            'start_time': start_time,
            'end_time': datetime.now(),
            'duration': (datetime.now() - start_time).total_seconds()
        }

def load_latest_model(model_dir='trained_models'):
    """
    加载最新训练的模型

    参数:
    model_dir: 模型保存目录

    返回:
    模型、特征缩放器和特征列表的元组
    """
    try:
        # 加载最新模型信息
        latest_model_info = joblib.load(f"{model_dir}/latest_model_info.joblib")

        # 加载模型、缩放器和特征
        model = joblib.load(latest_model_info['model_file'])
        scaler = joblib.load(latest_model_info['scaler_file'])
        features = joblib.load(latest_model_info['features_file'])

        print(f"成功加载最新模型 (训练时间: {latest_model_info['timestamp']})")

        return model, scaler, features

    except Exception as e:
        print(f"加载最新模型失败: {e}")
        return None, None, None
