#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
技术强度区间分析与高级特征组合挖掘
作者: Augment AI
版本: 1.0.0
"""

import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from datetime import datetime
import seaborn as sns
from tqdm import tqdm

# 创建结果目录
results_dir = 'technical_strength_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
        
        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")
        
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def analyze_technical_strength_detailed(data, target_feature='涨跌幅'):
    """详细分析技术强度与目标特征的关系"""
    print(f"详细分析技术强度与{target_feature}的关系...")
    
    # 创建更细粒度的技术强度区间
    strength_bins = list(range(0, 101, 5))  # 0, 5, 10, ..., 100
    labels = [f"{i}-{i+5}" for i in range(0, 100, 5)]
    
    # 创建技术强度区间列
    data['技术强度区间'] = pd.cut(data['技术强度'], bins=strength_bins, labels=labels, right=False)
    
    # 按区间分组计算统计量
    grouped_stats = data.groupby('技术强度区间')[target_feature].agg(['mean', 'std', 'count'])
    
    # 计算正收益比例
    positive_ratio = data.groupby('技术强度区间')[target_feature].apply(lambda x: (x > 0).mean() * 100)
    grouped_stats['正收益比例'] = positive_ratio
    
    # 计算每个区间的平均技术指标数量
    for indicator in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', '技术指标_KDJ金叉', '技术指标_布林带突破']:
        grouped_stats[f'{indicator}_比例'] = data.groupby('技术强度区间')[indicator].mean() * 100
    
    # 计算每个区间的平均看涨技术指标数量
    grouped_stats['平均看涨技术指标数量'] = data.groupby('技术强度区间')['看涨技术指标数量'].mean()
    
    print(f"按技术强度区间的 {target_feature} 详细统计:")
    print(grouped_stats)
    
    # 保存结果到文件
    grouped_stats.to_csv(f"{results_dir}/技术强度区间分析.csv")
    print(f"技术强度区间分析结果已保存到 {results_dir}/技术强度区间分析.csv")
    
    return grouped_stats

def analyze_technical_strength_by_date(data, target_feature='涨跌幅'):
    """按日期分析不同技术强度区间的表现"""
    print(f"按日期分析不同技术强度区间的{target_feature}表现...")
    
    # 定义技术强度区间
    strength_bins = [0, 50, 70, 80, 85, 90, 95, 100]
    labels = ['0-50', '50-70', '70-80', '80-85', '85-90', '90-95', '95-100']
    
    # 创建技术强度区间列
    data['技术强度区间'] = pd.cut(data['技术强度'], bins=strength_bins, labels=labels, right=True)
    
    # 获取所有日期
    all_dates = sorted(data['日期'].unique())
    
    # 初始化结果
    date_results = []
    
    # 对每个日期进行分析
    for date in tqdm(all_dates):
        # 获取当日数据
        daily_data = data[data['日期'] == date]
        
        # 按技术强度区间分组计算统计量
        for strength_range in labels:
            range_data = daily_data[daily_data['技术强度区间'] == strength_range]
            
            if len(range_data) > 0:
                # 计算统计量
                mean_value = range_data[target_feature].mean()
                std_value = range_data[target_feature].std()
                count = len(range_data)
                positive_ratio = (range_data[target_feature] > 0).mean() * 100
                
                # 记录结果
                date_results.append({
                    '日期': date,
                    '技术强度区间': strength_range,
                    '平均值': mean_value,
                    '标准差': std_value,
                    '样本数': count,
                    '正收益比例': positive_ratio
                })
    
    # 转换为DataFrame
    date_results_df = pd.DataFrame(date_results)
    
    # 保存结果到文件
    date_results_df.to_csv(f"{results_dir}/技术强度区间日期分析.csv", index=False)
    print(f"技术强度区间日期分析结果已保存到 {results_dir}/技术强度区间日期分析.csv")
    
    return date_results_df

def analyze_technical_indicators(data):
    """分析技术指标与涨跌幅的关系"""
    print("分析技术指标与涨跌幅的关系...")
    
    # 定义技术指标列表
    indicators = ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', '技术指标_KDJ金叉', '技术指标_布林带突破']
    
    # 初始化结果
    indicator_results = []
    
    # 对每个技术指标进行分析
    for indicator in indicators:
        # 计算指标为1时的平均涨跌幅
        positive_data = data[data[indicator] == 1]
        positive_mean = positive_data['涨跌幅'].mean()
        positive_std = positive_data['涨跌幅'].std()
        positive_count = len(positive_data)
        positive_win_rate = (positive_data['涨跌幅'] > 0).mean() * 100
        
        # 计算指标为0时的平均涨跌幅
        negative_data = data[data[indicator] == 0]
        negative_mean = negative_data['涨跌幅'].mean()
        negative_std = negative_data['涨跌幅'].std()
        negative_count = len(negative_data)
        negative_win_rate = (negative_data['涨跌幅'] > 0).mean() * 100
        
        # 记录结果
        indicator_results.append({
            '技术指标': indicator,
            '指标为1_平均涨跌幅': positive_mean,
            '指标为1_标准差': positive_std,
            '指标为1_样本数': positive_count,
            '指标为1_胜率': positive_win_rate,
            '指标为0_平均涨跌幅': negative_mean,
            '指标为0_标准差': negative_std,
            '指标为0_样本数': negative_count,
            '指标为0_胜率': negative_win_rate,
            '差异': positive_mean - negative_mean
        })
    
    # 转换为DataFrame
    indicator_results_df = pd.DataFrame(indicator_results)
    
    # 按差异排序
    indicator_results_df = indicator_results_df.sort_values('差异', ascending=False)
    
    print("技术指标与涨跌幅的关系:")
    print(indicator_results_df)
    
    # 保存结果到文件
    indicator_results_df.to_csv(f"{results_dir}/技术指标分析.csv", index=False)
    print(f"技术指标分析结果已保存到 {results_dir}/技术指标分析.csv")
    
    return indicator_results_df

def analyze_indicator_combinations(data):
    """分析技术指标组合与涨跌幅的关系"""
    print("分析技术指标组合与涨跌幅的关系...")
    
    # 定义技术指标列表
    indicators = ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', '技术指标_KDJ金叉', '技术指标_布林带突破']
    
    # 创建所有可能的指标组合
    data['指标组合'] = ''
    for i, indicator in enumerate(indicators):
        data['指标组合'] = data['指标组合'] + data[indicator].astype(str)
    
    # 按指标组合分组计算统计量
    grouped_stats = data.groupby('指标组合')['涨跌幅'].agg(['mean', 'std', 'count'])
    
    # 计算正收益比例
    positive_ratio = data.groupby('指标组合')['涨跌幅'].apply(lambda x: (x > 0).mean() * 100)
    grouped_stats['正收益比例'] = positive_ratio
    
    # 添加指标组合的解释
    grouped_stats['指标组合说明'] = ''
    for index in grouped_stats.index:
        explanation = []
        for i, indicator in enumerate(indicators):
            if index[i] == '1':
                explanation.append(indicator)
        grouped_stats.loc[index, '指标组合说明'] = ', '.join(explanation)
    
    # 按平均涨跌幅排序
    grouped_stats = grouped_stats.sort_values('mean', ascending=False)
    
    print("技术指标组合与涨跌幅的关系:")
    print(grouped_stats)
    
    # 保存结果到文件
    grouped_stats.to_csv(f"{results_dir}/技术指标组合分析.csv")
    print(f"技术指标组合分析结果已保存到 {results_dir}/技术指标组合分析.csv")
    
    return grouped_stats

def analyze_trend_features(data):
    """分析趋势特征与涨跌幅的关系"""
    print("分析趋势特征与涨跌幅的关系...")
    
    # 定义趋势特征列表
    trend_features = [
        '价格趋势', '涨跌幅趋势', '技术强度趋势', 
        '连续技术强度3天数趋势', '连续技术强度5天数趋势', '连续技术强度10天数趋势',
        '连续技术强度3天数价格趋势', '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势',
        '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势', '连续技术强度10天数涨跌幅趋势'
    ]
    
    # 初始化结果
    trend_results = []
    
    # 对每个趋势特征进行分析
    for feature in trend_features:
        # 计算特征为1时的平均涨跌幅
        positive_data = data[data[feature] == 1]
        positive_mean = positive_data['涨跌幅'].mean()
        positive_std = positive_data['涨跌幅'].std()
        positive_count = len(positive_data)
        positive_win_rate = (positive_data['涨跌幅'] > 0).mean() * 100
        
        # 计算特征为0时的平均涨跌幅
        negative_data = data[data[feature] == 0]
        negative_mean = negative_data['涨跌幅'].mean()
        negative_std = negative_data['涨跌幅'].std()
        negative_count = len(negative_data)
        negative_win_rate = (negative_data['涨跌幅'] > 0).mean() * 100
        
        # 记录结果
        trend_results.append({
            '趋势特征': feature,
            '特征为1_平均涨跌幅': positive_mean,
            '特征为1_标准差': positive_std,
            '特征为1_样本数': positive_count,
            '特征为1_胜率': positive_win_rate,
            '特征为0_平均涨跌幅': negative_mean,
            '特征为0_标准差': negative_std,
            '特征为0_样本数': negative_count,
            '特征为0_胜率': negative_win_rate,
            '差异': positive_mean - negative_mean
        })
    
    # 转换为DataFrame
    trend_results_df = pd.DataFrame(trend_results)
    
    # 按差异排序
    trend_results_df = trend_results_df.sort_values('差异', ascending=False)
    
    print("趋势特征与涨跌幅的关系:")
    print(trend_results_df)
    
    # 保存结果到文件
    trend_results_df.to_csv(f"{results_dir}/趋势特征分析.csv", index=False)
    print(f"趋势特征分析结果已保存到 {results_dir}/趋势特征分析.csv")
    
    return trend_results_df

def analyze_consecutive_strength(data):
    """分析连续技术强度与涨跌幅的关系"""
    print("分析连续技术强度与涨跌幅的关系...")
    
    # 定义连续技术强度特征
    strength_features = ['连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']
    
    # 初始化结果
    strength_results = []
    
    # 对每个连续技术强度特征进行分析
    for feature in strength_features:
        # 定义分位数
        percentiles = [0, 25, 50, 75, 90, 95, 100]
        thresholds = np.percentile(data[feature], percentiles)
        
        # 创建区间标签
        labels = [f"{int(thresholds[i])}-{int(thresholds[i+1])}" for i in range(len(thresholds)-1)]
        
        # 创建区间列
        data[f'{feature}_区间'] = pd.cut(data[feature], bins=thresholds, labels=labels, right=True)
        
        # 按区间分组计算统计量
        grouped_stats = data.groupby(f'{feature}_区间')['涨跌幅'].agg(['mean', 'std', 'count'])
        
        # 计算正收益比例
        positive_ratio = data.groupby(f'{feature}_区间')['涨跌幅'].apply(lambda x: (x > 0).mean() * 100)
        grouped_stats['正收益比例'] = positive_ratio
        
        # 记录结果
        for interval in grouped_stats.index:
            strength_results.append({
                '连续技术强度特征': feature,
                '区间': interval,
                '平均涨跌幅': grouped_stats.loc[interval, 'mean'],
                '标准差': grouped_stats.loc[interval, 'std'],
                '样本数': grouped_stats.loc[interval, 'count'],
                '正收益比例': grouped_stats.loc[interval, '正收益比例']
            })
    
    # 转换为DataFrame
    strength_results_df = pd.DataFrame(strength_results)
    
    # 按平均涨跌幅排序
    strength_results_df = strength_results_df.sort_values(['连续技术强度特征', '平均涨跌幅'], ascending=[True, False])
    
    print("连续技术强度与涨跌幅的关系:")
    print(strength_results_df)
    
    # 保存结果到文件
    strength_results_df.to_csv(f"{results_dir}/连续技术强度分析.csv", index=False)
    print(f"连续技术强度分析结果已保存到 {results_dir}/连续技术强度分析.csv")
    
    return strength_results_df

def generate_strategy_recommendations(data, date=None):
    """根据分析结果生成策略推荐"""
    print("根据分析结果生成策略推荐...")
    
    if date is None:
        date = data['日期'].max()
    else:
        date = pd.to_datetime(date)
    
    print(f"生成日期: {date.strftime('%Y-%m-%d')} 的策略推荐")
    
    # 策略1: 高技术强度 + KDJ金叉
    strategy1_stocks = data[
        (data['日期'] == date) &
        (data['技术强度'] >= 85) &
        (data['技术指标_KDJ金叉'] == 1)
    ]
    
    # 策略2: 连续技术强度5天数趋势 + 技术指标_RSI反弹
    strategy2_stocks = data[
        (data['日期'] == date) &
        (data['连续技术强度5天数趋势'] == 1) &
        (data['技术指标_RSI反弹'] == 1)
    ]
    
    # 策略3: 高连续技术强度10天数 + 技术强度趋势
    strategy3_stocks = data[
        (data['日期'] == date) &
        (data['连续技术强度10天数'] >= 500) &
        (data['技术强度趋势'] == 1)
    ]
    
    # 策略4: 多指标组合 (RSI反弹 + KDJ金叉 + 连续技术强度5天数涨跌幅趋势)
    strategy4_stocks = data[
        (data['日期'] == date) &
        (data['技术指标_RSI反弹'] == 1) &
        (data['技术指标_KDJ金叉'] == 1) &
        (data['连续技术强度5天数涨跌幅趋势'] == 1)
    ]
    
    # 保存推荐结果
    strategies = {
        '高技术强度+KDJ金叉': strategy1_stocks,
        '连续技术强度5天数趋势+RSI反弹': strategy2_stocks,
        '高连续技术强度10天数+技术强度趋势': strategy3_stocks,
        '多指标组合策略': strategy4_stocks
    }
    
    for strategy_name, stocks in strategies.items():
        if len(stocks) > 0:
            output_file = f"{results_dir}/{strategy_name}_{date.strftime('%Y%m%d')}.csv"
            stocks.to_csv(output_file, index=False)
            print(f"{strategy_name}: 推荐 {len(stocks)} 只股票，已保存到 {output_file}")
            
            # 打印前10只股票
            print(f"\n{strategy_name} 推荐股票列表 (前10只):")
            for i, (_, stock) in enumerate(stocks.iterrows()):
                if i < 10:
                    print(f"{i+1}. {stock['股票代码']} {stock['股票名称']}, 技术强度: {stock['技术强度']}, 连续技术强度5天数: {stock['连续技术强度5天数']}, 看涨技术指标数量: {stock['看涨技术指标数量']}")
            
            if len(stocks) > 10:
                print(f"... 共 {len(stocks)} 只股票")
        else:
            print(f"{strategy_name}: 无推荐股票")
    
    return strategies

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)
    
    # 获取最新日期
    latest_date = df['日期'].max()
    print(f"最新日期: {latest_date.strftime('%Y-%m-%d')}")
    
    # 详细分析技术强度与涨跌幅的关系
    strength_stats = analyze_technical_strength_detailed(df)
    
    # 按日期分析不同技术强度区间的表现
    date_results = analyze_technical_strength_by_date(df)
    
    # 分析技术指标与涨跌幅的关系
    indicator_results = analyze_technical_indicators(df)
    
    # 分析技术指标组合与涨跌幅的关系
    indicator_combinations = analyze_indicator_combinations(df)
    
    # 分析趋势特征与涨跌幅的关系
    trend_results = analyze_trend_features(df)
    
    # 分析连续技术强度与涨跌幅的关系
    strength_results = analyze_consecutive_strength(df)
    
    # 生成策略推荐
    strategies = generate_strategy_recommendations(df, latest_date)
