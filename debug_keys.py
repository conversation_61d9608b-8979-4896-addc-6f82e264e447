import pandas as pd
import os

# 检查策略1的Excel文件，特别关注关联键的匹配情况
excel_file = r'E:\机器学习\complete_excel_results\new_strategy_details\strategy_1.xlsx'

if os.path.exists(excel_file):
    print(f"检查文件: {excel_file}")
    
    try:
        # 读取选股明细
        df = pd.read_excel(excel_file, sheet_name='选股明细')
        print(f"选股明细数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        
        if not df.empty:
            print(f"\n=== 选股明细详细数据 ===")
            print(df.to_string())
            
            # 检查关键字段的数据分布
            key_fields = ['买入日涨跌幅', '卖出日股票涨跌幅', '买入日开盘涨跌幅', '卖出日开盘涨跌幅']
            
            print(f"\n=== 关键字段数据分析 ===")
            for field in key_fields:
                if field in df.columns:
                    values = df[field]
                    non_zero = values[values != 0]
                    zero_count = (values == 0).sum()
                    null_count = values.isnull().sum()
                    
                    print(f"\n{field}:")
                    print(f"  总记录数: {len(values)}")
                    print(f"  非零记录数: {len(non_zero)}")
                    print(f"  零值记录数: {zero_count}")
                    print(f"  空值记录数: {null_count}")
                    
                    if len(non_zero) > 0:
                        print(f"  非零值范围: {non_zero.min():.2f} 到 {non_zero.max():.2f}")
                        print(f"  非零值样例: {non_zero.head(5).tolist()}")
                    
                    # 显示具体的非零记录
                    if len(non_zero) > 0:
                        non_zero_records = df[df[field] != 0][['股票代码', '股票名称', '选股日期', '买入日期', '卖出日期', field]]
                        print(f"  非零记录详情:")
                        print(non_zero_records.to_string())
                else:
                    print(f"{field}: 列不存在")
            
            # 检查股票代码和日期格式
            print(f"\n=== 股票代码和日期格式分析 ===")
            if '股票代码' in df.columns:
                print(f"股票代码样例: {df['股票代码'].head(5).tolist()}")
                
            if '买入日期' in df.columns:
                print(f"买入日期样例: {df['买入日期'].head(5).tolist()}")
                
            if '卖出日期' in df.columns:
                print(f"卖出日期样例: {df['卖出日期'].head(5).tolist()}")
                
        else:
            print("选股明细数据为空")
            
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()
else:
    print(f"文件不存在: {excel_file}")

print(f"\n=== 检查完成 ===")
