#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术强度数据管理模块

负责技术强度数据的保存、加载和管理
"""

import os
import pandas as pd
import glob
from tqdm import tqdm

# 配置路径
daily_tech_strength_dir = r"E:\机器学习\complete_excel_results\tech_strength\daily"

def get_daily_tech_strength_path(date, trend_type='strong'):
    """
    获取指定日期的技术强度数据文件路径

    参数:
        date: 日期，可以是字符串(YYYY-MM-DD)、datetime对象或pandas Timestamp
        trend_type: 趋势类型，只支持 'strong'（强势股）

    返回:
        str: 文件路径
    """
    # 转换日期格式
    if isinstance(date, str):
        date_str = date
    else:
        date_str = date.strftime('%Y-%m-%d')

    # 只生成强势股文件
    subdir = 'strong'
    filename = f"tech_strength_strong_{date_str}.xlsx"

    # 构建完整路径
    file_path = os.path.join(daily_tech_strength_dir, subdir, filename)

    return file_path

def save_daily_tech_strength(df, date):
    """
    只保存强势股数据到文件

    参数:
        df: 包含技术强度数据的DataFrame
        date: 日期，可以是字符串(YYYY-MM-DD)、datetime对象或pandas Timestamp

    返回:
        bool: 保存是否成功
    """
    try:
        print(f"开始保存日期 {date} 的强势股数据...")

        # 保存所有技术强度 > 0 的股票
        strong_df = df[df['技术强度'] > 0].copy()

        if not strong_df.empty:
            strong_file_path = get_daily_tech_strength_path(date)
            os.makedirs(os.path.dirname(strong_file_path), exist_ok=True)

            # 处理强势股票数据
            processed_strong_df = process_tech_strength_data(strong_df, date)

            # 保存Excel格式
            processed_strong_df.to_excel(strong_file_path, index=False)

            print(f"成功保存强势股票数据到 {strong_file_path}，共 {len(strong_df)} 条记录")
        else:
            print(f"日期 {date} 没有强势股数据")

        return True
    except Exception as e:
        print(f"保存日期 {date} 的技术强度数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def process_tech_strength_data(df, date):
    """
    处理技术强度数据，确保格式正确

    参数:
        df: 要处理的DataFrame
        date: 日期

    返回:
        DataFrame: 处理后的数据
    """
    # 简单的数据处理，确保基本字段存在
    if '股票代码' in df.columns:
        df['股票代码'] = df['股票代码'].astype(str)

    if '技术指标特征' in df.columns:
        df['技术指标特征'] = df['技术指标特征'].astype(str)

    if '趋势组合' in df.columns:
        df['趋势组合'] = df['趋势组合'].astype(str)

    return df


def load_daily_tech_strength(date):
    """
    加载指定日期的强势股数据

    参数:
        date: 日期，可以是字符串(YYYY-MM-DD)、datetime对象或pandas Timestamp

    返回:
        DataFrame: 包含强势股数据的DataFrame，如果文件不存在则返回空DataFrame
    """
    try:
        # 获取强势股文件路径
        file_path = get_daily_tech_strength_path(date)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"日期 {date} 的强势股数据文件不存在")
            return pd.DataFrame()

        # 加载Excel格式，确保关键字段是字符串类型
        df = pd.read_excel(file_path, dtype={
            '股票代码': str,
            '技术指标特征': str,
            '趋势组合': str
        })

        print(f"成功加载日期 {date} 的强势股数据，共 {len(df)} 条记录")
        return df
    except Exception as e:
        print(f"加载日期 {date} 的强势股数据时出错: {e}")
        return pd.DataFrame()


def get_available_dates():
    """
    获取所有可用的强势股数据日期

    返回:
        list: 包含所有可用日期的列表，按日期排序
    """
    try:
        # 获取强势股文件
        files = glob.glob(os.path.join(daily_tech_strength_dir, "strong", "tech_strength_strong_*.xlsx"))

        # 提取日期
        dates = []
        for file in files:
            # 从文件名中提取日期
            file_name = os.path.basename(file)
            date_str = file_name.replace("tech_strength_strong_", "").replace(".xlsx", "")

            try:
                date = pd.to_datetime(date_str)
                dates.append(date)
            except:
                continue

        # 按日期排序
        dates = sorted(dates)

        return dates
    except Exception as e:
        print(f"获取可用日期时出错: {e}")
        return []


def load_all_tech_strength():
    """
    加载所有日期的强势股数据

    返回:
        DataFrame: 包含所有日期强势股数据的DataFrame
    """
    try:
        # 获取所有可用日期
        dates = get_available_dates()

        if not dates:
            print("没有找到可用的强势股数据")
            return pd.DataFrame()

        # 加载所有日期的数据
        all_data = []
        for date in tqdm(dates, desc="加载强势股数据"):
            df = load_daily_tech_strength(date)
            if not df.empty:
                all_data.append(df)

        # 合并所有数据
        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            print(f"成功加载所有强势股数据，共 {len(result)} 条记录")
            return result
        else:
            print("没有找到可用的强势股数据")
            return pd.DataFrame()
    except Exception as e:
        print(f"加载所有强势股数据时出错: {e}")
        return pd.DataFrame()
