import pandas as pd
import numpy as np
import os
import joblib
from datetime import datetime
from sklearn.ensemble import VotingClassifier, VotingRegressor, StackingClassifier, StackingRegressor
from sklearn.linear_model import LogisticRegression, Ridge
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
# 使用scikit-learn替代tensorflow

class EnsembleLearning:
    """
    集成学习模块，结合多个模型的预测结果，进一步提高准确率
    """

    def __init__(self):
        """初始化集成学习模块"""
        # 创建模型目录
        if not os.path.exists('ensemble_models'):
            os.makedirs('ensemble_models')

        # 设置随机种子，确保结果可复现
        np.random.seed(42)

    def load_base_models(self, model_type='classification'):
        """
        加载基础模型
        参数:
            model_type: 模型类型，可选值:
                - 'classification': 分类模型
                - 'regression': 回归模型
        返回基础模型列表
        """
        print(f"加载{model_type}基础模型...")

        base_models = []

        # 检查传统机器学习模型
        if model_type == 'classification':
            model_files = [
                'models/stock_predictor_model.pkl',
                'models/strength_increase_model.pkl',
                'models/strength_decrease_model.pkl'
            ]
        else:  # regression
            model_files = [
                'models/stock_return_model.pkl',
                'models/stock_price_model.pkl'
            ]

        # 加载传统机器学习模型
        for model_file in model_files:
            if os.path.exists(model_file):
                try:
                    model = joblib.load(model_file)
                    base_models.append(('ml_' + os.path.basename(model_file).split('.')[0], model))
                    print(f"已加载模型: {model_file}")
                except Exception as e:
                    print(f"加载模型 {model_file} 失败: {e}")

        # 检查深度学习模型
        if model_type == 'classification':
            dl_model_files = [
                'dl_models/lstm_next_day_direction_model.h5',
                'dl_models/transformer_next_day_direction_model.h5'
            ]
        else:  # regression
            dl_model_files = [
                'dl_models/lstm_next_day_return_model.h5',
                'dl_models/transformer_next_day_return_model.h5'
            ]

        # 加载深度学习模型（这里只记录模型路径，实际使用时再加载）
        for model_file in dl_model_files:
            if os.path.exists(model_file):
                base_models.append(('dl_' + os.path.basename(model_file).split('.')[0], model_file))
                print(f"已记录深度学习模型路径: {model_file}")

        print(f"共加载 {len(base_models)} 个基础模型")
        return base_models

    def build_voting_ensemble(self, base_models, model_type='classification'):
        """
        构建投票集成模型
        参数:
            base_models: 基础模型列表
            model_type: 模型类型
        返回投票集成模型
        """
        print(f"构建{model_type}投票集成模型...")

        # 过滤出传统机器学习模型
        ml_models = [(name, model) for name, model in base_models if not isinstance(model, str)]

        if len(ml_models) == 0:
            print("没有可用的传统机器学习模型，无法构建投票集成模型")
            return None

        # 构建投票集成模型
        if model_type == 'classification':
            ensemble = VotingClassifier(
                estimators=ml_models,
                voting='soft'  # 使用概率进行投票
            )
        else:  # regression
            ensemble = VotingRegressor(
                estimators=ml_models
            )

        return ensemble

    def build_stacking_ensemble(self, base_models, model_type='classification'):
        """
        构建堆叠集成模型
        参数:
            base_models: 基础模型列表
            model_type: 模型类型
        返回堆叠集成模型
        """
        print(f"构建{model_type}堆叠集成模型...")

        # 过滤出传统机器学习模型
        ml_models = [(name, model) for name, model in base_models if not isinstance(model, str)]

        if len(ml_models) == 0:
            print("没有可用的传统机器学习模型，无法构建堆叠集成模型")
            return None

        # 构建堆叠集成模型
        if model_type == 'classification':
            ensemble = StackingClassifier(
                estimators=ml_models,
                final_estimator=LogisticRegression(),
                cv=5
            )
        else:  # regression
            ensemble = StackingRegressor(
                estimators=ml_models,
                final_estimator=Ridge(),
                cv=5
            )

        return ensemble

    def train_ensemble_model(self, X, y, ensemble_model, model_type='classification'):
        """
        训练集成模型
        参数:
            X: 特征数据
            y: 目标变量
            ensemble_model: 集成模型
            model_type: 模型类型
        返回训练好的集成模型
        """
        print(f"训练{model_type}集成模型...")

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        print(f"训练集大小: {X_train.shape}")
        print(f"测试集大小: {X_test.shape}")

        # 训练模型
        ensemble_model.fit(X_train, y_train)

        # 预测
        y_pred = ensemble_model.predict(X_test)

        # 评估模型
        print("\n集成模型评估:")

        if model_type == 'classification':
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)

            print(f"准确率: {accuracy:.4f}")
            print(f"精确率: {precision:.4f}")
            print(f"召回率: {recall:.4f}")
            print(f"F1分数: {f1:.4f}")

            # 如果模型支持predict_proba，计算AUC
            if hasattr(ensemble_model, 'predict_proba'):
                y_prob = ensemble_model.predict_proba(X_test)[:, 1]
                auc = roc_auc_score(y_test, y_prob)
                print(f"AUC: {auc:.4f}")
        else:  # regression
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)

            print(f"均方误差: {mse:.4f}")
            print(f"平均绝对误差: {mae:.4f}")
            print(f"R²分数: {r2:.4f}")

        # 保存模型
        model_path = f'ensemble_models/{"classification" if model_type == "classification" else "regression"}_ensemble_model.pkl'
        joblib.dump(ensemble_model, model_path)
        print(f"集成模型已保存至 {model_path}")

        return ensemble_model

    def predict_with_dl_models(self, X, dl_models, scalers=None):
        """
        使用深度学习模型进行预测
        参数:
            X: 特征数据
            dl_models: 深度学习模型路径列表
            scalers: 特征缩放器字典
        返回预测结果
        """
        predictions = []

        for name, model_path in dl_models:
            # 加载模型
            model = joblib.load(model_path)

            # 如果有缩放器，缩放特征
            if scalers is not None and name in scalers:
                X_scaled = scalers[name].transform(X)
            else:
                X_scaled = X

            # 预测
            if hasattr(model, 'predict_proba'):
                pred = model.predict_proba(X_scaled)[:, 1]
            else:
                pred = model.predict(X_scaled)

            predictions.append(pred)

        return predictions

    def hybrid_ensemble_predict(self, X, base_models, ensemble_model, model_type='classification', scalers=None):
        """
        混合集成预测
        参数:
            X: 特征数据
            base_models: 基础模型列表
            ensemble_model: 集成模型
            model_type: 模型类型
            scalers: 特征缩放器字典
        返回预测结果
        """
        print(f"使用混合集成模型进行{model_type}预测...")

        # 分离传统机器学习模型和深度学习模型
        ml_models = [(name, model) for name, model in base_models if not isinstance(model, str)]
        dl_models = [(name, model) for name, model in base_models if isinstance(model, str)]

        # 使用集成模型预测
        ensemble_pred = ensemble_model.predict(X)

        # 如果有深度学习模型，使用深度学习模型预测
        if dl_models:
            dl_predictions = self.predict_with_dl_models(X, dl_models, scalers)

            # 计算所有模型的平均预测
            all_predictions = [ensemble_pred] + dl_predictions
            hybrid_pred = np.mean(all_predictions, axis=0)
        else:
            hybrid_pred = ensemble_pred

        return hybrid_pred

    def online_learning_update(self, ensemble_model, X_new, y_new, model_type='classification'):
        """
        在线学习更新模型
        参数:
            ensemble_model: 集成模型
            X_new: 新特征数据
            y_new: 新目标变量
            model_type: 模型类型
        返回更新后的集成模型
        """
        print(f"使用在线学习更新{model_type}集成模型...")

        # 更新模型
        ensemble_model.fit(X_new, y_new)

        # 保存更新后的模型
        model_path = f'ensemble_models/{"classification" if model_type == "classification" else "regression"}_ensemble_model.pkl'
        joblib.dump(ensemble_model, model_path)
        print(f"更新后的集成模型已保存至 {model_path}")

        return ensemble_model

    def run(self, stock_data, features, target, model_type='classification'):
        """
        运行集成学习
        参数:
            stock_data: 股票数据
            features: 特征列表
            target: 目标变量
            model_type: 模型类型
        """
        print(f"运行{model_type}集成学习...")

        # 准备数据
        X = stock_data[features].values
        y = stock_data[target].values

        # 加载基础模型
        base_models = self.load_base_models(model_type)

        if not base_models:
            print("没有可用的基础模型，无法进行集成学习")
            return

        # 构建投票集成模型
        voting_ensemble = self.build_voting_ensemble(base_models, model_type)

        if voting_ensemble:
            # 训练投票集成模型
            voting_ensemble = self.train_ensemble_model(X, y, voting_ensemble, model_type)

        # 构建堆叠集成模型
        stacking_ensemble = self.build_stacking_ensemble(base_models, model_type)

        if stacking_ensemble:
            # 训练堆叠集成模型
            stacking_ensemble = self.train_ensemble_model(X, y, stacking_ensemble, model_type)

        print("集成学习完成!")

if __name__ == "__main__":
    # 测试集成学习模块
    ensemble = EnsembleLearning()

    # 加载股票数据
    try:
        stock_data = pd.read_excel('股票明细.xlsx')
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")

        # 准备数据
        features = [
            '技术强度', '涨跌幅', '当前价格',
            '均线多头排列', '成交量放大', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
            '趋势_强势上涨', '趋势_上涨', '趋势_盘整', '趋势_下跌', '趋势_强势下跌',
            '目标价差比', '止损价差比'
        ]

        # 计算次日涨跌方向
        stock_data['次日涨跌方向'] = 0

        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 计算次日涨跌方向
            group['次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)

            # 更新原始数据
            stock_data.loc[group.index, '次日涨跌方向'] = group['次日涨跌方向']

        # 删除没有次日数据的记录
        stock_data = stock_data.dropna(subset=['次日涨跌方向'])

        # 运行分类集成学习
        ensemble.run(stock_data, features, '次日涨跌方向', 'classification')

        # 计算次日收益率
        stock_data['次日收益率'] = 0

        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 计算次日收益率
            group['次日收益率'] = group['当前价格'].shift(-1) / group['当前价格'] - 1

            # 更新原始数据
            stock_data.loc[group.index, '次日收益率'] = group['次日收益率']

        # 删除没有次日数据的记录
        stock_data = stock_data.dropna(subset=['次日收益率'])

        # 运行回归集成学习
        ensemble.run(stock_data, features, '次日收益率', 'regression')
    except Exception as e:
        print(f"测试失败: {e}")
