历史数据下载工具使用说明
====================

简介
----
这是一个用于下载A股历史数据的工具，使用Baostock库获取数据。

使用方法
--------
1. 双击运行"dist"目录中的"历史数据下载.exe"文件
2. 在程序界面中，选择开始日期和结束日期，然后点击"开始下载"按钮
3. 下载完成后，数据将保存在"complete_excel_results/stock_data"目录中

如果运行可执行文件时出现错误，可以尝试以下方法：

1. 双击运行"运行历史数据下载.bat"文件
2. 批处理文件会自动检查并安装必要的依赖库，然后启动程序

系统要求
--------
- Windows 7/8/10/11
- Python 3.8或更高版本
- 网络连接

注意事项
--------
- 首次运行时，程序会自动安装必要的依赖库，这可能需要一些时间
- 下载大量数据可能需要较长时间，请耐心等待
- 如果下载过程中出现错误，可以尝试重新运行程序
- 数据存储在"complete_excel_results/stock_data"目录中，按日期分别存储

文件说明
--------
- 历史数据下载.exe: 可执行文件，位于dist目录中
- 运行历史数据下载.bat: 用于安装依赖并运行程序的批处理文件
- download_stock_data_gui.py: 主程序文件
- stock_data_manager.py: 数据管理模块
- README.txt: 本文件

常见问题
--------
Q: 程序运行时提示"No module named 'baostock'"
A: 请确保已正确安装baostock库。可以尝试手动运行"pip install baostock"命令安装。

Q: 下载数据时出现错误
A: 可能是网络问题或服务器限制，请稍后再试。

Q: 下载速度很慢
A: 下载速度受网络状况和服务器响应速度影响，请耐心等待。

Q: 如何查看下载的数据
A: 数据保存在"complete_excel_results/stock_data"目录中，可以使用Excel打开查看。
