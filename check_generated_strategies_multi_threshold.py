import pandas as pd

# 读取生成的特征组合文件
file_path = r"E:\机器学习\complete_excel_results\特征组合_含成交量比_多阈值.xlsx"
df = pd.read_excel(file_path)

# 查看前几个策略
print("生成的策略示例:")
for i in range(5):
    print(f"策略编号: {df.iloc[i]['策略编号']}")
    print(f"策略组合: {df.iloc[i]['策略组合']}")
    print(f"成交量阈值: {df.iloc[i]['成交量阈值']}")
    print(f"策略代码: {df.iloc[i]['策略代码']}")
    print(f"策略条件描述: {df.iloc[i]['策略条件描述']}")
    print("-" * 50)

# 随机抽取不同成交量阈值的组合进行检查
print("\n不同成交量阈值的组合示例:")
for threshold in [0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5]:
    sample = df[df['成交量阈值'] == threshold].sample(1).iloc[0]
    print(f"\n成交量阈值 {threshold} 的组合示例:")
    print(f"策略编号: {sample['策略编号']}")
    print(f"策略组合: {sample['策略组合']}")
    print(f"策略代码: {sample['策略代码']}")
    print(f"策略条件描述: {sample['策略条件描述']}")
    print("-" * 50)
