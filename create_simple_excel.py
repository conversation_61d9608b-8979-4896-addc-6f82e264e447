"""
创建简单的Excel文件

这个脚本用于创建一个简单的Excel文件，测试文件创建功能是否正常。
"""

import os
import pandas as pd

# 创建一个简单的DataFrame
df = pd.DataFrame({
    '策略编号': [1],
    '策略描述': ['测试策略']
})

# 设置输出目录
output_dir = r'E:\机器学习\complete_excel_results\new_strategy_details'

# 确保输出目录存在
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 创建文件路径
file_path = os.path.join(output_dir, "strategy_1.xlsx")

# 保存到Excel
df.to_excel(file_path, index=False)

print(f"已创建Excel文件: {file_path}")

# 验证文件是否成功保存
if os.path.exists(file_path):
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size} 字节")
    if file_size > 0:
        print(f"文件创建成功")
    else:
        print(f"警告: 文件大小为0")
else:
    print(f"错误: 文件不存在")
