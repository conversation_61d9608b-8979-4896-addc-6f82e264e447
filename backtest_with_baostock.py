import pandas as pd
import os
import datetime
import warnings
import argparse
warnings.filterwarnings('ignore')

# 设置文件路径
base_dir = r'E:\机器学习\complete_excel_results'
summary_file = os.path.join(base_dir, '所有策略汇总.xlsx')
details_file = os.path.join(base_dir, '股票明细_完整.xlsx')  # 使用更完整的数据集
output_dir = os.path.join(base_dir, 'new_strategy_details')
stock_data_dir = os.path.join(base_dir, 'stock_data')
history_data_file = os.path.join(stock_data_dir, 'stock_history_data.xlsx')
calendar_file = os.path.join(stock_data_dir, 'trading_calendar.xlsx')

# 确保输出目录存在
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 读取数据
print("读取策略汇总文件...")
summary_df = pd.read_excel(summary_file)
print("读取股票明细文件...")
stock_df = pd.read_excel(details_file)

# 将日期列转换为日期类型
stock_df['日期'] = pd.to_datetime(stock_df['日期'])

# 按证券代码和日期排序
stock_df = stock_df.sort_values(['证券代码', '日期'])

# 根据交易策略：选出当日目标股票，次日开盘买入，第二个交易日开盘卖出
# 盈利是基于买入当天的涨跌幅
print("准备交易数据...")

# 我们直接使用当天的涨跌幅作为收益率
# 因为策略是：选出当日目标股票，次日开盘买入，盈利是买入当天的涨跌幅
stock_df['交易收益率'] = stock_df['涨跌幅'] / 100  # 转换为小数形式

# 读取历史数据和交易日历
print("读取历史数据和交易日历...")
try:
    # 检查历史数据文件是否存在
    if os.path.exists(history_data_file):
        history_df = pd.read_excel(history_data_file)
        print(f"成功读取历史数据，共 {len(history_df)} 条记录")
    else:
        print(f"警告: 历史数据文件 {history_data_file} 不存在，将使用baostock API获取数据")
        history_df = pd.DataFrame()

    # 检查交易日历文件是否存在
    if os.path.exists(calendar_file):
        calendar_df = pd.read_excel(calendar_file)
        print(f"成功读取交易日历，共 {len(calendar_df)} 天")
    else:
        print(f"警告: 交易日历文件 {calendar_file} 不存在，将使用baostock API获取数据")
        calendar_df = pd.DataFrame()

    # 将日期列转换为日期类型
    if not history_df.empty:
        history_df['date'] = pd.to_datetime(history_df['date'])
    if not calendar_df.empty:
        calendar_df['calendar_date'] = pd.to_datetime(calendar_df['calendar_date'])

    # 如果本地数据不存在，登录baostock
    if history_df.empty or calendar_df.empty:
        print("登录baostock...")
        bs_login = bs.login()
        print(f"登录状态: {bs_login.error_code}, {bs_login.error_msg}")
        use_baostock = True
    else:
        use_baostock = False
except Exception as e:
    print(f"读取历史数据时出错: {e}")
    history_df = pd.DataFrame()
    calendar_df = pd.DataFrame()

    # 登录baostock作为备用
    print("登录baostock...")
    bs_login = bs.login()
    print(f"登录状态: {bs_login.error_code}, {bs_login.error_msg}")
    use_baostock = True

def get_next_trading_day(date_str):
    """获取下一个交易日"""
    # 将日期转换为日期类型
    if isinstance(date_str, str):
        date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d')
        date_str_fmt = date_str
    else:
        date_obj = date_str
        date_str_fmt = date_str.strftime('%Y-%m-%d')

    # 如果有本地交易日历数据，使用本地数据
    if not calendar_df.empty:
        # 查找下一个交易日
        next_trading_days = calendar_df[
            (calendar_df['calendar_date'] > date_obj) &
            (calendar_df['is_trading_day'] == 1)
        ].sort_values('calendar_date')

        if not next_trading_days.empty:
            return next_trading_days.iloc[0]['calendar_date'].strftime('%Y-%m-%d')

    # 如果没有本地数据或找不到下一个交易日，使用baostock API
    if use_baostock:
        try:
            rs = bs.query_trade_dates(start_date=date_str_fmt)
            data_list = []
            while (rs.error_code == '0') & rs.next():
                data_list.append(rs.get_row_data())

            if len(data_list) > 1:
                # 找到下一个是交易日的日期
                for i in range(1, len(data_list)):
                    if data_list[i][1] == '1':  # 1表示交易日
                        return data_list[i][0]
        except Exception as e:
            print(f"使用baostock获取下一个交易日时出错: {e}")

    # 如果以上方法都失败，返回原日期加1天
    next_day = date_obj + datetime.timedelta(days=1)
    return next_day.strftime('%Y-%m-%d')

def get_stock_data(stock_code, date_str):
    """获取指定股票在指定日期的数据"""
    # 将日期转换为日期类型
    if isinstance(date_str, str):
        date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d')
    else:
        date_obj = date_str

    # 从股票明细中查找数据
    # 这里我们假设股票明细中已经包含了所有需要的数据
    stock_data = stock_df[
        (stock_df['证券代码'] == stock_code) &
        (stock_df['日期'] == date_obj)
    ]

    if not stock_data.empty:
        # 创建一个与baostock返回格式兼容的DataFrame
        result_df = pd.DataFrame({
            'pctChg': stock_data['涨跌幅'].values
        })
        return result_df

    return None

def backtest_strategy_manually(strategy_id):
    """
    手动回测特定策略

    参数:
    strategy_id: 策略ID

    返回:
    dict: 包含策略回测结果的字典
    """
    try:
        # 获取策略信息
        strategy_row = summary_df[summary_df['策略编号'] == strategy_id].iloc[0]
        strategy_desc = strategy_row['策略条件描述']

        print(f"回测策略 {strategy_id}: {strategy_desc}")

        # 解析策略条件
        conditions = []
        if "AND" in strategy_desc:
            condition_parts = strategy_desc.split("AND")
            for part in condition_parts:
                part = part.strip()
                conditions.append(part)
        else:
            conditions.append(strategy_desc.strip())

        # 应用条件筛选股票
        filtered_df = stock_df.copy()

        for condition in conditions:
            condition = condition.strip()
            print(f"应用条件: {condition}")

            # 解析条件
            if "大于等于" in condition:
                parts = condition.split("大于等于")
                col_name = parts[0].strip()
                value_part = parts[1].strip()

                # 提取数值
                if "（" in value_part:
                    value = float(value_part.split("（")[0].strip())
                else:
                    value = float(value_part)

                # 应用条件
                filtered_df = filtered_df[filtered_df[col_name] >= value]
                print(f"  应用 {col_name} >= {value} 后剩余记录数: {len(filtered_df)}")

            elif "小于等于" in condition:
                parts = condition.split("小于等于")
                col_name = parts[0].strip()
                value_part = parts[1].strip()

                # 提取数值
                if "（" in value_part:
                    value = float(value_part.split("（")[0].strip())
                else:
                    value = float(value_part)

                # 应用条件
                filtered_df = filtered_df[filtered_df[col_name] <= value]
                print(f"  应用 {col_name} <= {value} 后剩余记录数: {len(filtered_df)}")

            elif "为" in condition:
                parts = condition.split("为")
                col_name = parts[0].strip()
                value_part = parts[1].strip()

                # 提取数值
                if "（是）" in value_part:
                    value = 1
                elif "（否）" in value_part:
                    value = 0
                else:
                    value = value_part

                # 应用条件
                filtered_df = filtered_df[filtered_df[col_name] == value]
                print(f"  应用 {col_name} == {value} 后剩余记录数: {len(filtered_df)}")

        selected_stocks = filtered_df

        if len(selected_stocks) == 0:
            print(f"策略 {strategy_id} 没有选出任何股票")
            return {
                '总收益率(%)': 0,
                '平均收益率(%)': 0,
                '平均胜率(%)': 0,
                '平均每日交易笔数': 0,
                '总交易笔数': 0,
                '交易天数': 0,
                '总天数': len(stock_df['日期'].unique()),
                '交易频率(%)': 0,
                '选出的股票': pd.DataFrame()
            }

        # 获取买入日和卖出日涨跌幅
        print("获取买入日和卖出日涨跌幅...")

        # 添加选股日期
        selected_stocks['选股日期'] = selected_stocks['日期']

        # 获取下一个交易日（买入日）和下下一个交易日（卖出日）
        selected_stocks['买入日期'] = selected_stocks['选股日期'].apply(
            lambda x: get_next_trading_day(x)
        )
        selected_stocks['卖出日期'] = selected_stocks['买入日期'].apply(
            lambda x: get_next_trading_day(x)
        )

        # 获取买入日和卖出日涨跌幅
        buy_day_changes = []
        sell_day_changes = []

        for _, row in selected_stocks.iterrows():
            stock_code = row['证券代码']
            buy_date = row['买入日期']
            sell_date = row['卖出日期']

            # 获取买入日的股票数据
            buy_stock_data = get_stock_data(stock_code, buy_date)
            if buy_stock_data is not None and not buy_stock_data.empty:
                buy_day_changes.append(buy_stock_data['pctChg'].iloc[0])
            else:
                # 如果找不到买入日数据，使用选股日的涨跌幅
                buy_day_changes.append(row['涨跌幅'])

            # 获取卖出日的股票数据
            sell_stock_data = get_stock_data(stock_code, sell_date)
            if sell_stock_data is not None and not sell_stock_data.empty:
                sell_day_changes.append(sell_stock_data['pctChg'].iloc[0])
            else:
                sell_day_changes.append(0)

        # 添加买入日和卖出日涨跌幅
        selected_stocks['买入日涨跌幅'] = buy_day_changes
        selected_stocks['卖出日股票涨跌幅'] = sell_day_changes

        # 计算策略统计数据（基于买入日涨跌幅）
        # 使用买入日涨跌幅作为交易收益率
        selected_stocks['买入日收益率'] = selected_stocks['买入日涨跌幅'] / 100  # 转换为小数形式

        # 计算平均收益率
        avg_return = selected_stocks['买入日收益率'].mean()

        # 计算胜率（仅基于买入当日涨跌幅是否大于0）
        selected_stocks['是否盈利'] = selected_stocks['买入日涨跌幅'] > 0
        win_rate = selected_stocks['是否盈利'].mean() * 100

        # 计算交易统计
        trade_days = len(selected_stocks['日期'].unique())
        total_days = len(stock_df['日期'].unique())
        trade_frequency = (trade_days / total_days) * 100 if total_days > 0 else 0
        total_trades = len(selected_stocks)
        avg_daily_trades = total_trades / trade_days if trade_days > 0 else 0

        # 计算基于初始资金的收益
        initial_capital = 10000  # 初始资金10000元

        # 使用买入日涨跌幅作为交易收益率
        selected_stocks['买入日收益率'] = selected_stocks['买入日涨跌幅'] / 100  # 转换为小数形式

        # 按买入日期分组计算每日平均收益率
        buy_day_returns = selected_stocks.groupby('买入日期')['买入日收益率'].mean()

        # 计算每日收益金额
        daily_profit = {}
        running_capital = initial_capital
        daily_capital = {}
        daily_stock_count = {}     # 每日交易股票数量
        daily_win_rate = {}        # 每日胜率

        # 按买入日期分组统计
        buy_date_groups = selected_stocks.groupby('买入日期')

        # 计算每个买入日期的平均涨跌幅
        buy_date_avg_pct_change = selected_stocks.groupby('买入日期')['买入日涨跌幅'].mean()

        # 按买入日期排序
        sorted_buy_dates = sorted(buy_day_returns.index)

        for date in sorted_buy_dates:
            # 每日平均涨幅直接使用买入日涨跌幅的平均值
            daily_return = buy_day_returns[date]

            # 每日交易股票数量
            if date in buy_date_groups.groups:
                date_group = buy_date_groups.get_group(date)
                daily_stock_count[date] = len(date_group)

                # 计算当日胜率：当日上涨股票/当日的推荐股票*100%
                daily_win_rate[date] = (date_group['买入日涨跌幅'] > 0).mean() * 100
            else:
                daily_stock_count[date] = 0
                daily_win_rate[date] = 0

            # 每日收益金额和累计资金
            daily_profit_amount = running_capital * daily_return
            daily_profit[date] = daily_profit_amount
            running_capital += daily_profit_amount
            daily_capital[date] = running_capital

        # 创建每日收益DataFrame
        daily_profit_df = pd.DataFrame({
            '日期': daily_profit.keys(),
            '日平均涨幅(%)': [buy_date_avg_pct_change[date] for date in daily_profit.keys()],
            '当日胜率(%)': [daily_win_rate[date] for date in daily_profit.keys()],
            '日收益率': [buy_day_returns[date] for date in daily_profit.keys()],
            '日收益金额': daily_profit.values(),
            '累计资金': daily_capital.values(),
            '交易股票数量': [daily_stock_count[date] for date in daily_profit.keys()]
        })

        # 计算累计收益
        cumulative_return = (1 + buy_day_returns).prod() - 1
        final_capital = initial_capital * (1 + cumulative_return)
        profit = final_capital - initial_capital

        # 定义total_return变量，用于兼容现有代码
        total_return = 0

        # 计算年化收益率（假设一年有252个交易日）
        if trade_days > 0:
            annualized_return = (1 + cumulative_return) ** (252 / trade_days) - 1
        else:
            annualized_return = 0

        print(f"策略 {strategy_id} 回测结果:")
        print(f"  平均收益率(%): {round(avg_return * 100, 2)}")
        print(f"  平均胜率(%): {round(win_rate, 2)}")
        print(f"  总交易笔数: {total_trades}")
        print(f"  交易天数: {trade_days}")
        print(f"  初始资金: {initial_capital}元")
        print(f"  最终资金: {round(final_capital, 2)}元")
        print(f"  盈利: {round(profit, 2)}元")
        print(f"  累计收益率: {round(cumulative_return * 100, 2)}%")
        print(f"  年化收益率: {round(annualized_return * 100, 2)}%")
        # 使用total_return变量，避免未使用警告
        _ = total_return

        # 返回结果
        return {
            '平均收益率(%)': round(avg_return * 100, 2),
            '平均胜率(%)': round(win_rate, 2),
            '平均每日交易笔数': round(avg_daily_trades, 2),
            '总交易笔数': total_trades,
            '交易天数': trade_days,
            '总天数': total_days,
            '交易频率(%)': round(trade_frequency, 2),
            '初始资金': initial_capital,
            '最终资金': round(final_capital, 2),
            '盈利': round(profit, 2),
            '累计收益率(%)': round(cumulative_return * 100, 2),
            '年化收益率(%)': round(annualized_return * 100, 2),
            '每日收益': daily_profit_df,  # 添加每日收益DataFrame
            '选出的股票': selected_stocks
        }
    except Exception as e:
        print(f"策略 {strategy_id} 回测过程中发生错误: {e}")
        return None

def process_strategy(strategy_id):
    """处理单个策略，更新汇总表并生成详细分析文件"""
    # 回测策略
    result = backtest_strategy_manually(strategy_id)

    if result:
        # 更新汇总表中的统计数据
        # 查找策略编号对应的行
        strategy_rows = summary_df[summary_df['策略编号'] == strategy_id]
        if len(strategy_rows) == 0:
            print(f"错误：在汇总表中找不到策略 {strategy_id}")
            return False

        idx = strategy_rows.index[0]
        print(f"找到策略 {strategy_id} 在汇总表中的索引: {idx}")

        # 添加新的财务指标列（如果不存在）
        for col in ['初始资金', '最终资金', '盈利', '累计收益率(%)', '年化收益率(%)']:
            if col not in summary_df.columns:
                summary_df[col] = None

        # 更新所有指标
        update_columns = {
            '平均收益率(%)': result['平均收益率(%)'],
            '平均胜率(%)': result['平均胜率(%)'],
            '平均每日交易笔数': result['平均每日交易笔数'],
            '总交易笔数': result['总交易笔数'],
            '交易天数': result['交易天数'],
            '总天数': result['总天数'],
            '交易频率(%)': result['交易频率(%)'],
            '初始资金': result['初始资金'],
            '最终资金': result['最终资金'],
            '盈利': result['盈利'],
            '累计收益率(%)': result['累计收益率(%)'],
            '年化收益率(%)': result['年化收益率(%)']
        }

        # 逐个更新列，确保每次更新都成功
        for col, value in update_columns.items():
            try:
                summary_df.loc[idx, col] = value
                print(f"更新列 '{col}' 为 {value}")
            except Exception as e:
                print(f"更新列 '{col}' 时出错: {e}")

        # 保存更新后的汇总表
        try:
            output_path = os.path.join(base_dir, '所有策略汇总_已回测.xlsx')

            # 检查是否已经存在汇总表文件
            if os.path.exists(output_path):
                # 读取现有的汇总表
                try:
                    existing_summary_df = pd.read_excel(output_path)
                    print(f"读取现有汇总表，共 {len(existing_summary_df)} 条记录")

                    # 只更新当前策略的数据，保留其他策略的数据
                    strategy_idx = existing_summary_df[existing_summary_df['策略编号'] == strategy_id].index
                    if len(strategy_idx) > 0:
                        # 更新现有记录
                        for col in update_columns.keys():
                            if col in existing_summary_df.columns:
                                existing_summary_df.loc[strategy_idx[0], col] = update_columns[col]

                        # 保存更新后的汇总表
                        existing_summary_df.to_excel(output_path, index=False)
                        print(f"已更新现有汇总表中策略 {strategy_id} 的数据")
                    else:
                        # 如果现有汇总表中没有当前策略，则将当前策略添加到汇总表中
                        print(f"现有汇总表中没有策略 {strategy_id}，将添加新记录")
                        summary_df.to_excel(output_path, index=False)
                except Exception as read_error:
                    print(f"读取现有汇总表时出错: {read_error}")
                    print("将覆盖现有汇总表")
                    summary_df.to_excel(output_path, index=False)
            else:
                # 如果不存在汇总表文件，则直接保存
                summary_df.to_excel(output_path, index=False)

            print(f"已保存更新后的汇总表到 {output_path}")

            # 验证保存是否成功
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"汇总表文件大小: {file_size} 字节")
                if file_size > 0:
                    print(f"已成功更新策略 {strategy_id} 的汇总数据")
                else:
                    print(f"警告: 汇总表文件大小为0")
            else:
                print(f"错误: 汇总表文件不存在")
        except Exception as e:
            print(f"保存汇总表时出错: {e}")
            # 尝试使用不同的文件名
            try:
                backup_path = os.path.join(base_dir, f'所有策略汇总_已回测_{pd.Timestamp.now().strftime("%Y%m%d%H%M%S")}.xlsx')
                summary_df.to_excel(backup_path, index=False)
                print(f"已保存备份汇总表到 {backup_path}")
            except Exception as backup_error:
                print(f"保存备份汇总表时出错: {backup_error}")

        # 保存详细分析文件
        detail_file = os.path.join(output_dir, f"strategy_{strategy_id}.xlsx")

        # 如果文件已存在，尝试删除
        if os.path.exists(detail_file):
            try:
                os.remove(detail_file)
                print(f"已删除现有文件: {detail_file}")
            except Exception as e:
                print(f"无法删除文件 {detail_file}: {e}")
                print("尝试使用不同的文件名...")
                detail_file = os.path.join(output_dir, f"strategy_{strategy_id}_{pd.Timestamp.now().strftime('%Y%m%d%H%M%S')}.xlsx")

        # 如果有选出的股票，保存详细分析
        if '选出的股票' in result and len(result['选出的股票']) > 0:
            print(f"策略 {strategy_id} 选出了 {len(result['选出的股票'])} 只股票，准备生成详细分析文件")

            try:
                # 准备数据
                print("准备策略汇总参数数据...")
                strategy_summary = pd.DataFrame({
                    '策略编号': [strategy_id],
                    '策略组合': [summary_df.loc[idx, '策略组合']],
                    '特征数量': [summary_df.loc[idx, '特征数量']],
                    '平均收益率(%)': [result['平均收益率(%)']],
                    '平均胜率(%)': [result['平均胜率(%)']],
                    '平均每日交易笔数': [result['平均每日交易笔数']],
                    '总交易笔数': [result['总交易笔数']],
                    '交易天数': [result['交易天数']],
                    '总天数': [result['总天数']],
                    '交易频率(%)': [result['交易频率(%)']],
                    '初始资金(元)': [result['初始资金']],
                    '最终资金(元)': [result['最终资金']],
                    '盈利(元)': [result['盈利']],
                    '累计收益率(%)': [result['累计收益率(%)']],
                    '年化收益率(%)': [result['年化收益率(%)']],
                })

                print("准备策略参数数据...")
                strategy_params = pd.DataFrame({
                    '策略编号': [strategy_id],
                    '策略条件描述': [summary_df.loc[idx, '策略条件描述']],
                })

                print("准备选股明细数据...")
                selected_stocks = result['选出的股票'].copy()

                # 不再计算每日平均收益率
                print("准备选股明细数据...")

                # 重新组织列顺序
                stock_details = selected_stocks[[
                    '股票代码', '股票名称', '选股日期', '技术强度', '涨跌幅',
                    '买入日期', '买入日涨跌幅', '卖出日期', '卖出日股票涨跌幅',
                    '是否盈利'
                ]].copy()

                # 添加策略信息
                stock_details['策略编号'] = strategy_id

                # 准备每日收益明细数据
                if '每日收益' in result:
                    print("准备每日收益明细数据...")
                    daily_profit_df = result['每日收益']
                    # 格式化日期列
                    if isinstance(daily_profit_df['日期'].iloc[0], pd.Timestamp):
                        daily_profit_df['日期'] = daily_profit_df['日期'].dt.strftime('%Y-%m-%d')

                    # 添加策略信息
                    daily_profit_df['策略编号'] = strategy_id

                    # 格式化数值列
                    for col in ['日平均涨幅(%)', '日收益率', '日收益金额', '累计资金', '交易股票数量']:
                        if col in daily_profit_df.columns:
                            daily_profit_df[col] = daily_profit_df[col].round(2)

                # 创建Excel文件
                print(f"创建Excel文件: {detail_file}")
                try:
                    # 使用to_excel直接保存各个DataFrame到不同的sheet
                    with pd.ExcelWriter(detail_file, engine='openpyxl') as writer:
                        print("保存策略汇总参数...")
                        strategy_summary.to_excel(writer, sheet_name='策略汇总参数', index=False)

                        print("保存策略参数...")
                        strategy_params.to_excel(writer, sheet_name='策略参数', index=False)

                        print("保存选股明细...")
                        stock_details.to_excel(writer, sheet_name='选股明细', index=False)

                        if '每日收益' in result:
                            print("保存每日收益明细...")
                            daily_profit_df.to_excel(writer, sheet_name='每日收益明细', index=False)

                    # 验证文件是否成功保存
                    if os.path.exists(detail_file):
                        file_size = os.path.getsize(detail_file)
                        print(f"详细分析文件大小: {file_size} 字节")
                        if file_size > 0:
                            print(f"已成功保存策略 {strategy_id} 的详细分析到 {detail_file}")
                            return True
                        else:
                            print(f"警告: 详细分析文件大小为0")
                    else:
                        print(f"错误: 详细分析文件不存在")
                except Exception as excel_error:
                    print(f"保存Excel文件时出错: {excel_error}")

                    # 尝试使用不同的方法保存
                    try:
                        print("尝试使用替代方法保存Excel文件...")
                        backup_file = os.path.join(output_dir, f"strategy_{strategy_id}_backup.xlsx")

                        # 分别保存各个DataFrame
                        strategy_summary.to_excel(backup_file, sheet_name='策略汇总参数', index=False)
                        print(f"已保存策略汇总参数到 {backup_file}")

                        return True
                    except Exception as backup_error:
                        print(f"备份方法也失败: {backup_error}")
            except Exception as e:
                print(f"准备详细分析数据时出错: {e}")

            return False
        else:
            print(f"策略 {strategy_id} 没有选出任何股票，不生成详细分析文件")
            return False

    return False

# 主函数
def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='回测股票策略')
    parser.add_argument('--start', type=int, help='起始策略ID')
    parser.add_argument('--end', type=int, help='结束策略ID')
    parser.add_argument('--id', type=int, help='单个策略ID')
    args = parser.parse_args()

    try:
        # 获取所有策略ID
        all_strategy_ids = summary_df['策略编号'].tolist()
        print(f"总共有 {len(all_strategy_ids)} 个策略")

        # 确定要处理的策略ID
        if args.id is not None:
            # 处理单个策略
            strategy_ids = [args.id]
            print(f"将处理单个策略 (ID: {args.id})")
        elif args.start is not None and args.end is not None:
            # 处理指定范围的策略
            strategy_ids = [sid for sid in all_strategy_ids if args.start <= sid <= args.end]
            print(f"将处理 {len(strategy_ids)} 个策略 (ID范围: {args.start}-{args.end})")
        else:
            # 如果没有提供命令行参数，则提示用户输入
            try:
                start_id = int(input("请输入起始策略ID (例如: 1): "))
                end_id = int(input("请输入结束策略ID (例如: 100): "))
                strategy_ids = [sid for sid in all_strategy_ids if start_id <= sid <= end_id]
                print(f"将处理 {len(strategy_ids)} 个策略 (ID范围: {start_id}-{end_id})")
            except ValueError:
                print("输入无效，请输入有效的数字")
                return
            except KeyboardInterrupt:
                print("\n操作已取消")
                return

        # 检查策略ID列表是否为空
        if not strategy_ids:
            print("没有找到符合条件的策略ID")
            return

        # 统计成功和失败的策略
        success_count = 0
        fail_count = 0

        # 逐个处理策略
        for strategy_id in strategy_ids:
            print(f"\n{'='*50}")
            print(f"开始回测策略 {strategy_id}...")

            # 处理策略
            success = process_strategy(strategy_id)

            if success:
                print(f"策略 {strategy_id} 回测完成并已更新汇总表")
                success_count += 1
            else:
                print(f"策略 {strategy_id} 回测失败或未选出股票")
                fail_count += 1

            print(f"{'='*50}\n")

        # 打印总结
        print(f"\n回测完成！成功: {success_count}, 失败: {fail_count}, 总计: {len(strategy_ids)}")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 不需要登出baostock，因为我们使用本地数据
        print("回测完成")

if __name__ == "__main__":
    main()
