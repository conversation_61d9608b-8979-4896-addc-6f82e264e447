#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分析股票数据结构
作者: Augment AI
版本: 1.0.0

该脚本用于分析股票数据文件的结构，显示列名和数据样例。
"""

import os
import pandas as pd
import numpy as np

def analyze_file_structure(file_path):
    """
    分析文件结构
    
    参数:
        file_path (str): 文件路径
    """
    print(f"正在分析文件: {file_path}")
    
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return
        
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 显示基本信息
        print(f"\n文件信息:")
        print(f"  行数: {len(df)}")
        print(f"  列数: {len(df.columns)}")
        
        # 显示列名
        print(f"\n列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i}: {col}")
        
        # 显示数据类型
        print(f"\n数据类型:")
        for col in df.columns:
            print(f"  {col}: {df[col].dtype}")
        
        # 显示数据样例
        print(f"\n数据样例 (前5行):")
        print(df.head())
        
        # 检查是否有空值
        print(f"\n空值统计:")
        for col in df.columns:
            null_count = df[col].isnull().sum()
            if null_count > 0:
                print(f"  {col}: {null_count} 个空值")
        
        # 检查唯一值
        print(f"\n唯一值统计:")
        for col in df.columns:
            if df[col].dtype == 'object' or df[col].dtype == 'string':
                unique_count = df[col].nunique()
                print(f"  {col}: {unique_count} 个唯一值")
                
                # 如果唯一值较少，显示它们
                if unique_count <= 10:
                    unique_values = df[col].unique()
                    print(f"    唯一值: {unique_values}")
        
        print("\n分析完成")
    except Exception as e:
        print(f"分析文件时出错: {str(e)}")

def main():
    """主函数"""
    # 设置文件路径
    file_path = input("请输入股票数据文件路径 (默认: E:\\机器学习\\complete_excel_results\\股票明细_完整.xlsx): ") or "E:\\机器学习\\complete_excel_results\\股票明细_完整.xlsx"
    
    # 分析文件结构
    analyze_file_structure(file_path)

if __name__ == "__main__":
    main()
