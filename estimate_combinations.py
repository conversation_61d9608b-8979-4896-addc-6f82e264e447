import itertools

# 特征及其值的数量
features = {
    '技术强度': 1,  # 固定值，每个技术强度一个
    '连续技术强度3天数': 4,
    '连续技术强度5天数': 4,
    '连续技术强度10天数': 3,
    '成交量是前一日几倍': 7,
    '技术指标特征': 20,  # 根据我们生成的值
    '趋势组合': 20,      # 估算值
    '日内股票标记': 24   # 估算值
}

print("特征及其值的数量:")
for feature, count in features.items():
    print(f"  {feature}: {count}")

print("\n计算各种特征组合的数量:")

# 计算不同特征数量的组合
for size in [3, 4, 5, 6, 7, 8]:
    print(f"\n{size}个特征的组合:")
    
    # 除了技术强度外的其他特征
    other_features = {k: v for k, v in features.items() if k != '技术强度'}
    other_feature_names = list(other_features.keys())
    
    # 需要从其他特征中选择 size-1 个特征（因为技术强度是必须的）
    if size - 1 > len(other_feature_names):
        print(f"  无法生成{size}个特征的组合，因为总特征数不足")
        continue
    
    total_combinations = 0
    
    # 生成所有可能的特征组合
    for feature_combo in itertools.combinations(other_feature_names, size - 1):
        # 计算这个特征组合的值数量
        combo_count = 1
        for feature in feature_combo:
            combo_count *= other_features[feature]
        
        total_combinations += combo_count
        
        # 只显示前几个组合的详细信息
        if len(list(itertools.combinations(other_feature_names, size - 1))) <= 10:
            print(f"    {feature_combo}: {combo_count:,}")
    
    print(f"  总组合数: {total_combinations:,}")
    
    # 检查是否超过Excel限制
    if total_combinations > 500000:
        print(f"  ⚠️  超过Excel限制(50万行)，需要拆分文件")
        # 计算需要拆分成多少个文件
        num_files = (total_combinations + 499999) // 500000
        print(f"  需要拆分成 {num_files} 个文件")
    else:
        print(f"  ✅ 在Excel限制范围内")

print(f"\n所有技术强度值(6个)的总组合数:")
tech_strength_count = 6
for size in [3, 4, 5, 6, 7, 8]:
    if size <= len(features):
        # 重新计算单个技术强度的组合数
        other_features = {k: v for k, v in features.items() if k != '技术强度'}
        other_feature_names = list(other_features.keys())
        
        if size - 1 <= len(other_feature_names):
            total_combinations = 0
            for feature_combo in itertools.combinations(other_feature_names, size - 1):
                combo_count = 1
                for feature in feature_combo:
                    combo_count *= other_features[feature]
                total_combinations += combo_count
            
            total_all_tech_strength = total_combinations * tech_strength_count
            print(f"  {size}个特征: {total_all_tech_strength:,}")
