import pandas as pd

# 读取所有策略汇总文件
file_path = r"E:\机器学习\complete_excel_results\所有策略汇总.xlsx"
df = pd.read_excel(file_path)

# 查看策略组合和策略条件描述的前几个样本
print("策略组合和策略条件描述示例:")
for i in range(5):
    print(f"\n策略编号: {df.iloc[i]['策略编号']}")
    print(f"策略组合: {df.iloc[i]['策略组合']}")
    print(f"策略条件描述: {df.iloc[i]['策略条件描述']}")

# 提取所有可能的特征
all_features = set()
for strategy_combo in df['策略组合']:
    features = strategy_combo.split('+')
    for feature in features:
        all_features.add(feature.strip())

print("\n所有可能的特征:")
for feature in sorted(all_features):
    print(feature)

# 检查是否有"成交量是前一日几倍"特征
volume_ratio_feature = "成交量是前一日几倍"
if volume_ratio_feature in all_features:
    print(f"\n'{volume_ratio_feature}'特征存在于策略组合中")
else:
    print(f"\n'{volume_ratio_feature}'特征不存在于策略组合中")

# 查看股票明细_完整_带成交量比.xlsx文件中的列
try:
    detail_file_path = r"E:\机器学习\complete_excel_results\股票明细_完整_带成交量比.xlsx"
    detail_df = pd.read_excel(detail_file_path)
    print("\n股票明细_完整_带成交量比.xlsx文件中的列:")
    print(detail_df.columns.tolist())
except Exception as e:
    print(f"\n读取股票明细_完整_带成交量比.xlsx文件时出错: {e}")
