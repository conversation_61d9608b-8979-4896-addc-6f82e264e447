"""
深度学习模型 - 股票高胜率策略

使用深度神经网络预测股票涨跌，提高预测准确率和胜率。
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import joblib
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
except ImportError:
    # 如果TensorFlow导入失败，尝试导入Keras
    try:
        from keras.models import Sequential
        from keras.layers import Dense, Dropout, BatchNormalization
        from keras.optimizers import Adam
        from keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    except ImportError:
        # 如果Keras也导入失败，使用scikit-learn的MLPClassifier作为替代
        from sklearn.neural_network import MLPClassifier
import matplotlib.pyplot as plt
import seaborn as sns

def print_header(title):
    """打印标题"""
    print("\n" + " " * 24 + title)
    print("=" * 80 + "\n")

def load_data(file_path):
    """加载股票数据"""
    print(f"加载股票数据: {file_path}")
    try:
        df = pd.read_excel(file_path)
        print(f"成功加载股票数据，共 {len(df)} 行记录")
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def preprocess_data(data):
    """预处理数据"""
    print_header("预处理数据")

    # 确保日期列是datetime类型
    if '日期' in data.columns:
        data['日期'] = pd.to_datetime(data['日期'])

    # 1. 处理缺失值
    for col in data.columns:
        if data[col].dtype == np.float64 or data[col].dtype == np.int64:
            # 使用中位数填充缺失值，而不是0，减少极端值的影响
            median_value = data[col].median()
            data[col] = data[col].fillna(median_value)

    # 2. 处理异常值和离群点
    numeric_cols = data.select_dtypes(include=['float64', 'int64']).columns
    for col in numeric_cols:
        if col in ['股票代码', '日期', '星期几', '月份']:  # 跳过非数值特征
            continue

        # 计算Q1、Q3和IQR
        Q1 = data[col].quantile(0.25)
        Q3 = data[col].quantile(0.75)
        IQR = Q3 - Q1

        # 定义异常值边界
        lower_bound = Q1 - 3 * IQR
        upper_bound = Q3 + 3 * IQR

        # 将异常值替换为边界值
        data.loc[data[col] < lower_bound, col] = lower_bound
        data.loc[data[col] > upper_bound, col] = upper_bound

    # 3. 添加时间特征
    if '日期' in data.columns:
        # 添加星期几特征
        data['星期几'] = data['日期'].dt.dayofweek

        # 添加月份特征
        data['月份'] = data['日期'].dt.month

        # 添加季度特征
        data['季度'] = data['日期'].dt.quarter

        # 添加是否月初/月末特征
        data['是否月初'] = (data['日期'].dt.day <= 5).astype(int)
        data['是否月末'] = (data['日期'].dt.day >= 25).astype(int)

    # 3. 添加技术强度变化率
    if '技术强度' in data.columns:
        # 按股票代码分组
        grouped = data.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化技术强度变化率列
        data['技术强度变化率'] = 0.0

        # 对每个股票计算技术强度变化率
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取技术强度列
            strength = group['技术强度'].values

            # 计算技术强度变化率
            for i in range(1, len(group)):
                if strength[i-1] != 0:
                    change_rate = (strength[i] - strength[i-1]) / strength[i-1]
                else:
                    change_rate = 0
                data.loc[group.index[i], '技术强度变化率'] = change_rate

    # 4. 添加连续技术强度变化率
    if '连续技术强度5天数' in data.columns:
        # 按股票代码分组
        grouped = data.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化连续技术强度变化率列
        data['连续技术强度变化率'] = 0.0

        # 对每个股票计算连续技术强度变化率
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取连续技术强度列
            strength = group['连续技术强度5天数'].values

            # 计算连续技术强度变化率
            for i in range(1, len(group)):
                if strength[i-1] != 0:
                    change_rate = (strength[i] - strength[i-1]) / strength[i-1]
                else:
                    change_rate = 0
                data.loc[group.index[i], '连续技术强度变化率'] = change_rate

    # 5. 添加看涨技术指标占比
    if '看涨技术指标数量' in data.columns:
        data['看涨技术指标占比'] = data['看涨技术指标数量'] / 5  # 总共5个技术指标

    # 6. 添加技术指标组合特征
    if all(col in data.columns for col in ['技术指标_均线多头排列', '技术指标_MACD金叉']):
        data['均线多头_MACD金叉'] = data['技术指标_均线多头排列'] & data['技术指标_MACD金叉']

    if all(col in data.columns for col in ['技术指标_RSI反弹', '技术指标_KDJ金叉']):
        data['RSI反弹_KDJ金叉'] = data['技术指标_RSI反弹'] & data['技术指标_KDJ金叉']

    # 7. 添加历史胜率特征
    if all(col in data.columns for col in ['股票代码', '日期', '次日买后日卖收益率']):
        # 按股票代码分组
        grouped = data.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化历史胜率列
        data['历史胜率'] = 0.5  # 默认为50%

        # 对每个股票计算历史胜率
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取收益率列
            returns = group['次日买后日卖收益率'].values

            # 计算历史胜率
            for i in range(1, len(group)):
                if i < 5:  # 不足5天的情况
                    win_rate = np.mean(returns[:i] > 0) if i > 0 else 0.5
                else:  # 5天及以上的情况
                    win_rate = np.mean(returns[i-5:i] > 0)
                data.loc[group.index[i], '历史胜率'] = win_rate

    # 8. 添加更多技术指标特征
    # 8.1 计算移动平均线
    if '当前价格' in data.columns:
        # 按股票代码分组
        grouped = data.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化移动平均线列
        data['MA5'] = 0.0
        data['MA10'] = 0.0
        data['MA20'] = 0.0
        data['MA60'] = 0.0

        # 对每个股票计算移动平均线
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取价格列
            prices = group['当前价格'].values

            # 计算移动平均线
            for i in range(len(group)):
                # MA5
                if i >= 4:
                    data.loc[group.index[i], 'MA5'] = np.mean(prices[i-4:i+1])
                else:
                    data.loc[group.index[i], 'MA5'] = np.mean(prices[:i+1])

                # MA10
                if i >= 9:
                    data.loc[group.index[i], 'MA10'] = np.mean(prices[i-9:i+1])
                else:
                    data.loc[group.index[i], 'MA10'] = np.mean(prices[:i+1])

                # MA20
                if i >= 19:
                    data.loc[group.index[i], 'MA20'] = np.mean(prices[i-19:i+1])
                else:
                    data.loc[group.index[i], 'MA20'] = np.mean(prices[:i+1])

                # MA60
                if i >= 59:
                    data.loc[group.index[i], 'MA60'] = np.mean(prices[i-59:i+1])
                else:
                    data.loc[group.index[i], 'MA60'] = np.mean(prices[:i+1])

        # 计算价格相对于移动平均线的位置
        data['价格相对MA5'] = data['当前价格'] / data['MA5'] - 1
        data['价格相对MA10'] = data['当前价格'] / data['MA10'] - 1
        data['价格相对MA20'] = data['当前价格'] / data['MA20'] - 1
        data['价格相对MA60'] = data['当前价格'] / data['MA60'] - 1

        # 计算移动平均线的斜率
        data['MA5斜率'] = 0.0
        data['MA10斜率'] = 0.0
        data['MA20斜率'] = 0.0

    # 8.2 计算MACD指标
    if '当前价格' in data.columns:
        # 初始化MACD指标列
        data['EMA12'] = 0.0
        data['EMA26'] = 0.0
        data['DIF'] = 0.0
        data['DEA'] = 0.0
        data['MACD'] = 0.0

    # 8.3 计算RSI指标
    if '涨跌幅' in data.columns:
        # 初始化RSI指标列
        data['RSI6'] = 50.0
        data['RSI12'] = 50.0
        data['RSI24'] = 50.0

    # 8.4 计算KDJ指标
    if '当前价格' in data.columns:
        # 按股票代码分组
        grouped = data.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化KDJ指标列
        data['最高价9日'] = 0.0
        data['最低价9日'] = 0.0
        data['RSV'] = 50.0
        data['K值'] = 50.0
        data['D值'] = 50.0
        data['J值'] = 50.0

        # 对每个股票计算KDJ指标
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取价格列
            prices = group['当前价格'].values

            # 计算9日内的最高价和最低价
            for i in range(len(group)):
                if i < 8:
                    data.loc[group.index[i], '最高价9日'] = np.max(prices[:i+1])
                    data.loc[group.index[i], '最低价9日'] = np.min(prices[:i+1])
                else:
                    data.loc[group.index[i], '最高价9日'] = np.max(prices[i-8:i+1])
                    data.loc[group.index[i], '最低价9日'] = np.min(prices[i-8:i+1])

            # 计算RSV、K值、D值和J值
            for i in range(len(group)):
                # 计算RSV
                high = data.loc[group.index[i], '最高价9日']
                low = data.loc[group.index[i], '最低价9日']
                close = prices[i]

                if high != low:
                    rsv = 100 * (close - low) / (high - low)
                else:
                    rsv = 50

                data.loc[group.index[i], 'RSV'] = rsv

                # 计算K值
                if i == 0:
                    k = 50
                else:
                    k = 2/3 * data.loc[group.index[i-1], 'K值'] + 1/3 * rsv

                data.loc[group.index[i], 'K值'] = k

                # 计算D值
                if i == 0:
                    d = 50
                else:
                    d = 2/3 * data.loc[group.index[i-1], 'D值'] + 1/3 * k

                data.loc[group.index[i], 'D值'] = d

                # 计算J值
                j = 3 * k - 2 * d
                data.loc[group.index[i], 'J值'] = j

    # 8.5 计算布林带指标
    if '当前价格' in data.columns:
        # 按股票代码分组
        grouped = data.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化布林带指标列
        data['布林带中轨'] = 0.0
        data['布林带上轨'] = 0.0
        data['布林带下轨'] = 0.0
        data['布林带宽度'] = 0.0
        data['布林带%b'] = 0.5

        # 对每个股票计算布林带指标
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取价格列
            prices = group['当前价格'].values

            # 计算布林带
            for i in range(len(group)):
                # 计算20日移动平均线（中轨）
                if i < 19:
                    ma20 = np.mean(prices[:i+1])
                    std20 = np.std(prices[:i+1])
                else:
                    ma20 = np.mean(prices[i-19:i+1])
                    std20 = np.std(prices[i-19:i+1])

                data.loc[group.index[i], '布林带中轨'] = ma20
                data.loc[group.index[i], '布林带上轨'] = ma20 + 2 * std20
                data.loc[group.index[i], '布林带下轨'] = ma20 - 2 * std20

                # 计算布林带宽度
                if ma20 != 0:
                    data.loc[group.index[i], '布林带宽度'] = (data.loc[group.index[i], '布林带上轨'] - data.loc[group.index[i], '布林带下轨']) / ma20

                # 计算布林带%b
                upper = data.loc[group.index[i], '布林带上轨']
                lower = data.loc[group.index[i], '布林带下轨']
                if upper != lower:
                    data.loc[group.index[i], '布林带%b'] = (prices[i] - lower) / (upper - lower)

    # 8.6 计算成交量指标
    if '成交量' in data.columns:
        # 按股票代码分组
        grouped = data.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化成交量指标列
        data['成交量变化率'] = 0.0
        data['成交量5日均值'] = 0.0
        data['成交量10日均值'] = 0.0
        data['相对成交量'] = 1.0

        # 对每个股票计算成交量指标
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取成交量列
            volumes = group['成交量'].values

            # 计算成交量变化率
            for i in range(1, len(group)):
                if volumes[i-1] != 0:
                    data.loc[group.index[i], '成交量变化率'] = (volumes[i] - volumes[i-1]) / volumes[i-1]

            # 计算成交量均值
            for i in range(len(group)):
                # 5日均值
                if i < 4:
                    data.loc[group.index[i], '成交量5日均值'] = np.mean(volumes[:i+1])
                else:
                    data.loc[group.index[i], '成交量5日均值'] = np.mean(volumes[i-4:i+1])

                # 10日均值
                if i < 9:
                    data.loc[group.index[i], '成交量10日均值'] = np.mean(volumes[:i+1])
                else:
                    data.loc[group.index[i], '成交量10日均值'] = np.mean(volumes[i-9:i+1])

                # 相对成交量
                if data.loc[group.index[i], '成交量5日均值'] != 0:
                    data.loc[group.index[i], '相对成交量'] = volumes[i] / data.loc[group.index[i], '成交量5日均值']

    print("预处理完成")
    return data

def build_deep_learning_model(input_dim):
    """构建深度学习模型"""
    try:
        # 尝试使用TensorFlow/Keras构建模型
        from tensorflow.keras.regularizers import l1_l2

        # 使用L1L2正则化减少过拟合
        regularizer = l1_l2(l1=0.0001, l2=0.0001)

        model = Sequential([
            # 输入层
            Dense(128, activation='relu', input_dim=input_dim,
                  kernel_regularizer=regularizer),
            BatchNormalization(),
            Dropout(0.4),  # 增加Dropout比例

            # 隐藏层1
            Dense(64, activation='relu',
                  kernel_regularizer=regularizer),
            BatchNormalization(),
            Dropout(0.4),

            # 隐藏层2
            Dense(32, activation='relu',
                  kernel_regularizer=regularizer),
            BatchNormalization(),
            Dropout(0.4),

            # 隐藏层3 - 更深的网络，但更窄
            Dense(16, activation='relu',
                  kernel_regularizer=regularizer),
            BatchNormalization(),
            Dropout(0.4),

            # 输出层
            Dense(1, activation='sigmoid')
        ])

        # 编译模型 - 使用更低的学习率
        model.compile(
            optimizer=Adam(learning_rate=0.0005),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        return model, 'keras'
    except Exception as e:
        # 如果TensorFlow/Keras不可用，使用scikit-learn的MLPClassifier
        print(f"TensorFlow/Keras不可用或出错: {e}")
        print("使用scikit-learn的MLPClassifier作为替代")

        # 增加正则化参数alpha
        model = MLPClassifier(
            hidden_layer_sizes=(128, 64, 32, 16),  # 更深的网络
            activation='relu',
            solver='adam',
            alpha=0.001,  # 增加正则化强度
            batch_size=64,
            learning_rate='adaptive',
            learning_rate_init=0.0005,  # 降低学习率
            max_iter=200,  # 增加迭代次数
            early_stopping=True,
            validation_fraction=0.2,
            n_iter_no_change=10,  # 增加早停耐心
            random_state=42
        )

        return model, 'sklearn'

def train_deep_learning_model(data, test_size=0.2, random_state=42):
    """训练深度学习模型"""
    print_header("训练深度学习模型")

    # 1. 特征选择 - 基于领域知识和相关性分析
    # 定义候选特征
    candidate_features = [
        # 基本特征
        '技术强度', '连续技术强度5天数', '技术强度趋势', '价格趋势', '涨跌幅趋势',

        # 技术指标特征 - 这些是数据中已有的技术指标
        '技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
        '技术指标_KDJ金叉', '技术指标_布林带突破', '看涨技术指标数量',

        # 价格和涨跌幅特征
        '当前价格', '涨跌幅', '开盘涨跌',

        # 时间特征
        '星期几', '月份', '季度', '是否月初', '是否月末',

        # 其他可能有用的特征
        '后一天强度', '趋势'
    ]

    # 检查哪些特征在数据中存在
    available_features = [f for f in candidate_features if f in data.columns]
    print(f"可用特征: {available_features}")

    # 2. 处理非数值特征
    numeric_features = []
    for feature in available_features:
        # 检查特征类型
        if data[feature].dtype == 'object':
            print(f"特征 '{feature}' 是非数值类型，将被转换为数值类型")
            # 获取唯一值
            unique_values = data[feature].unique()
            print(f"  唯一值: {unique_values}")

            # 如果唯一值数量较少，使用独热编码
            if len(unique_values) <= 10:
                # 创建独热编码特征
                for value in unique_values:
                    new_feature = f"{feature}_{value}"
                    data[new_feature] = (data[feature] == value).astype(int)
                    numeric_features.append(new_feature)
                    print(f"  创建独热编码特征: {new_feature}")
            else:
                print(f"  唯一值数量过多，特征 '{feature}' 将被忽略")
        else:
            numeric_features.append(feature)

    # 更新可用特征
    available_features = numeric_features
    print(f"处理后的特征: {available_features}")

    # 3. 特征相关性分析 - 去除高度相关的特征
    if len(available_features) > 1:  # 确保至少有两个特征
        try:
            # 计算特征之间的相关性
            correlation_matrix = data[available_features].corr().abs()

            # 找出高度相关的特征对
            high_correlation_features = set()
            for i in range(len(correlation_matrix.columns)):
                for j in range(i):
                    if correlation_matrix.iloc[i, j] > 0.9:  # 相关系数阈值
                        colname = correlation_matrix.columns[i]
                        high_correlation_features.add(colname)

            # 从可用特征中移除高度相关的特征
            for feature in high_correlation_features:
                if feature in available_features:
                    print(f"移除高度相关特征: {feature}")
                    available_features.remove(feature)
        except Exception as e:
            print(f"计算特征相关性时出错: {e}")
            print("跳过特征相关性分析")

    # 最终使用的特征
    features = available_features
    print(f"最终使用的特征: {features}")

    # 检查特征是否存在
    available_features = [f for f in features if f in data.columns]
    print(f"使用特征: {available_features}")

    # 1. 确定目标变量
    if '是否盈利' in data.columns:
        target = '是否盈利'
    elif '次日买后日卖收益率' in data.columns:
        # 创建目标变量
        data['是否盈利'] = (data['次日买后日卖收益率'] > 0).astype(int)
        target = '是否盈利'
    else:
        print("缺少目标变量: 是否盈利 或 次日买后日卖收益率")
        return None

    # 提取特征和目标变量
    X = data[available_features]
    y = data[target]

    # 2. 使用分层抽样进行数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y,
        test_size=test_size,
        random_state=random_state,
        stratify=y  # 确保训练集和测试集中的类别分布一致
    )

    # 3. 特征标准化 - 使用稳健的标准化方法
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # 4. 类别不平衡处理
    # 检查类别分布
    class_counts = np.bincount(y_train)
    if len(class_counts) > 1:
        class_weight = {0: 1.0, 1: class_counts[0] / class_counts[1]}
        print(f"类别权重: {class_weight}")
    else:
        class_weight = None

    # 5. 交叉验证 - 使用K折交叉验证评估模型性能
    from sklearn.model_selection import StratifiedKFold
    from sklearn.metrics import roc_auc_score

    # 初始化交叉验证
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=random_state)
    cv_scores = []

    # 6. 构建深度学习模型
    model, model_type = build_deep_learning_model(input_dim=X_train_scaled.shape[1])

    # 7. 使用交叉验证评估模型性能
    if model_type == 'keras':
        # 使用交叉验证
        for train_idx, val_idx in cv.split(X_train_scaled, y_train):
            # 获取当前折的训练集和验证集
            X_cv_train, X_cv_val = X_train_scaled[train_idx], X_train_scaled[val_idx]
            y_cv_train, y_cv_val = y_train.iloc[train_idx], y_train.iloc[val_idx]

            # 构建模型
            cv_model, _ = build_deep_learning_model(X_train_scaled.shape[1])

            # 编译模型
            cv_model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='binary_crossentropy',
                metrics=['accuracy']
            )

            # 定义回调函数
            callbacks = [
                EarlyStopping(monitor='val_loss', patience=5, restore_best_weights=True)
            ]

            # 训练模型
            cv_model.fit(
                X_cv_train, y_cv_train,
                epochs=50,
                batch_size=64,
                validation_data=(X_cv_val, y_cv_val),
                callbacks=callbacks,
                class_weight=class_weight,
                verbose=0
            )

            # 评估模型
            y_cv_pred = cv_model.predict(X_cv_val)
            cv_score = roc_auc_score(y_cv_val, y_cv_pred)
            cv_scores.append(cv_score)

        print(f"交叉验证AUC分数: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")

        # 8. 在完整训练集上训练最终模型
        # 设置回调函数
        callbacks = [
            EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True),
            ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=5, min_lr=0.0001),
            ModelCheckpoint('best_model.h5', monitor='val_accuracy', save_best_only=True, mode='max')
        ]

        # 训练模型
        history = model.fit(
            X_train_scaled, y_train,
            epochs=100,
            batch_size=64,
            validation_split=0.2,
            callbacks=callbacks,
            class_weight=class_weight,
            verbose=1
        )

        # 评估模型
        y_pred_proba = model.predict(X_test_scaled)
        y_pred = (y_pred_proba > 0.5).astype(int)

        # 创建训练历史记录
        training_history = history.history
    else:
        # 使用交叉验证
        for train_idx, val_idx in cv.split(X_train_scaled, y_train):
            # 获取当前折的训练集和验证集
            X_cv_train, X_cv_val = X_train_scaled[train_idx], X_train_scaled[val_idx]
            y_cv_train, y_cv_val = y_train.iloc[train_idx], y_train.iloc[val_idx]

            # 构建模型
            cv_model, _ = build_deep_learning_model(X_train_scaled.shape[1])

            # 训练模型
            cv_model.fit(X_cv_train, y_cv_train)

            # 评估模型
            y_cv_pred = cv_model.predict_proba(X_cv_val)[:, 1]
            cv_score = roc_auc_score(y_cv_val, y_cv_pred)
            cv_scores.append(cv_score)

        print(f"交叉验证AUC分数: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")

        # 在完整训练集上训练最终模型
        model.fit(X_train_scaled, y_train)

        # 评估模型
        y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
        y_pred = (y_pred_proba > 0.5).astype(int)

        # 创建训练历史记录
        training_history = {
            'loss': [0],
            'accuracy': [model.score(X_train_scaled, y_train)],
            'val_loss': [0],
            'val_accuracy': [model.score(X_test_scaled, y_test)]
        }

    accuracy = accuracy_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred)
    recall = recall_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)

    print(f"测试集准确率: {accuracy:.4f}")
    print(f"测试集精确率: {precision:.4f}")
    print(f"测试集召回率: {recall:.4f}")
    print(f"测试集F1分数: {f1:.4f}")

    # 混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
    plt.title('混淆矩阵')
    plt.xlabel('预测标签')
    plt.ylabel('真实标签')
    plt.savefig('confusion_matrix.png')

    # 学习曲线
    if model_type == 'keras':
        plt.figure(figsize=(12, 4))

        plt.subplot(1, 2, 1)
        plt.plot(history.history['loss'], label='训练损失')
        plt.plot(history.history['val_loss'], label='验证损失')
        plt.title('损失曲线')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()

        plt.subplot(1, 2, 2)
        plt.plot(history.history['accuracy'], label='训练准确率')
        plt.plot(history.history['val_accuracy'], label='验证准确率')
        plt.title('准确率曲线')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy')
        plt.legend()

        plt.tight_layout()
        plt.savefig('learning_curves.png')

    # 保存模型
    model_dir = 'models'
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    # 根据模型类型保存模型
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    if model_type == 'keras':
        model_path = os.path.join(model_dir, f"dl_model_{timestamp}.h5")
        model.save(model_path)
    else:
        model_path = os.path.join(model_dir, f"ml_model_{timestamp}.joblib")
        joblib.dump(model, model_path)

    # 保存模型数据
    model_data = {
        'model': model,  # 直接保存模型对象
        'model_type': model_type,
        'model_path': model_path,
        'scaler': scaler,
        'features': available_features,
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'training_history': training_history
    }

    # 使用一个统一的文件名保存模型数据
    model_data_path = os.path.join(model_dir, "latest_model_data.joblib")
    joblib.dump(model_data, model_data_path)

    print(f"\n模型已保存到: {model_path}")
    print(f"模型数据已保存到: {model_data_path}")

    return model_data

def main():
    """主函数"""
    # 加载数据
    data = load_data('股票明细.xlsx')

    if data is None:
        print("数据加载失败，程序退出")
        return

    # 预处理数据
    processed_data = preprocess_data(data)

    # 训练深度学习模型
    model_data = train_deep_learning_model(processed_data)

    if model_data is None:
        print("模型训练失败，程序退出")
        return

    # 保存模型数据到统一文件
    model_dir = 'models'
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    model_data_path = os.path.join(model_dir, "latest_model_data.joblib")
    joblib.dump(model_data, model_data_path)
    print(f"模型数据已保存到: {model_data_path}")

    print("\n深度学习模型训练完成！")

if __name__ == "__main__":
    main()
