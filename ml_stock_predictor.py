import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import joblib
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer

class MLStockPredictor:
    """
    机器学习股票预测器，用于优化买卖决策
    """
    
    def __init__(self):
        """初始化预测器"""
        # 创建模型目录
        if not os.path.exists('models'):
            os.makedirs('models')
        
        # 模型文件路径
        self.model_file = 'models/stock_predictor_model.pkl'
        
        # 特征重要性文件路径
        self.feature_importance_file = 'models/feature_importance.xlsx'
        
        # 模型性能文件路径
        self.model_performance_file = 'models/model_performance.xlsx'
    
    def prepare_data(self, stock_data, target_type='next_day_rise'):
        """
        准备训练数据
        参数:
            stock_data: 股票历史数据DataFrame
            target_type: 目标变量类型，可选值:
                - 'next_day_rise': 次日上涨
                - 'next_day_significant_rise': 次日显著上涨
                - 'strength_increase': 技术强度增加
                - 'strength_decrease': 技术强度减少
        返回特征X和目标变量y
        """
        print(f"准备{target_type}预测模型的训练数据...")
        
        # 确保日期格式正确
        stock_data['日期'] = pd.to_datetime(stock_data['日期'])
        
        # 按股票代码和日期排序
        stock_data = stock_data.sort_values(['股票代码', '日期'])
        
        # 计算每只股票的次日涨跌幅和技术强度变化
        def calculate_targets(group):
            # 计算次日涨跌幅
            group['次日涨跌幅'] = group['当前价格'].shift(-1) / group['当前价格'] - 1
            
            # 计算技术强度变化
            group['技术强度变化'] = group['技术强度'].shift(-1) - group['技术强度']
            
            return group
        
        data = stock_data.groupby('股票代码', group_keys=False).apply(calculate_targets)
        
        # 删除没有目标变量的记录
        data = data.dropna(subset=['次日涨跌幅', '技术强度变化'])
        
        # 创建目标变量
        if target_type == 'next_day_rise':
            # 次日上涨
            data['目标'] = (data['次日涨跌幅'] > 0).astype(int)
        elif target_type == 'next_day_significant_rise':
            # 次日显著上涨（涨幅超过90%分位数）
            significant_rise_threshold = data['次日涨跌幅'].quantile(0.9)
            data['目标'] = (data['次日涨跌幅'] >= significant_rise_threshold).astype(int)
            print(f"次日显著上涨阈值: {significant_rise_threshold:.4f}")
        elif target_type == 'strength_increase':
            # 技术强度增加
            data['目标'] = (data['技术强度变化'] > 0).astype(int)
        elif target_type == 'strength_decrease':
            # 技术强度减少
            data['目标'] = (data['技术强度变化'] < 0).astype(int)
        else:
            raise ValueError(f"不支持的目标变量类型: {target_type}")
        
        print(f"目标变量分布:")
        print(data['目标'].value_counts())
        print(f"正样本比例: {data['目标'].mean():.4f}")
        
        # 提取特征
        features = self.extract_features(data)
        
        # 选择特征和目标变量
        X = features
        y = data['目标']
        
        return X, y, data
    
    def extract_features(self, data):
        """
        提取特征
        参数:
            data: 股票数据DataFrame
        返回特征DataFrame
        """
        print("提取特征...")
        
        # 基本特征
        features = pd.DataFrame()
        features['技术强度'] = data['技术强度']
        features['涨跌幅'] = data['涨跌幅']
        
        # 提取技术指标特征
        features['均线多头排列'] = data['技术指标'].str.contains('均线多头排列').astype(int)
        features['成交量放大'] = data['技术指标'].str.contains('成交量放大').astype(int)
        features['MACD金叉'] = data['技术指标'].str.contains('MACD金叉').astype(int)
        features['RSI反弹'] = data['技术指标'].str.contains('RSI反弹').astype(int)
        features['KDJ金叉'] = data['技术指标'].str.contains('KDJ金叉').astype(int)
        features['布林带突破'] = data['技术指标'].str.contains('布林带突破').astype(int)
        
        # 趋势特征
        features['趋势_强势上涨'] = (data['趋势'] == 'strong_up').astype(int)
        features['趋势_上涨'] = (data['趋势'] == 'up').astype(int)
        features['趋势_盘整'] = (data['趋势'] == 'neutral').astype(int)
        features['趋势_下跌'] = (data['趋势'] == 'down').astype(int)
        features['趋势_强势下跌'] = (data['趋势'] == 'strong_down').astype(int)
        
        # 价格与目标价/止损价的关系
        features['目标价差比'] = (data['目标价'] - data['当前价格']) / data['当前价格']
        features['止损价差比'] = (data['当前价格'] - data['止损价']) / data['当前价格']
        
        # 添加行业和龙头股特征（如果有）
        if '行业' in data.columns:
            # 对行业进行独热编码
            industry_dummies = pd.get_dummies(data['行业'], prefix='行业')
            features = pd.concat([features, industry_dummies], axis=1)
        
        if '是否行业龙头' in data.columns:
            features['是否行业龙头'] = data['是否行业龙头'].astype(int)
        
        if '是否强势行业' in data.columns:
            features['是否强势行业'] = data['是否强势行业'].astype(int)
        
        # 添加历史数据特征（如果可能）
        try:
            # 按股票代码分组，计算历史特征
            def add_historical_features(group):
                # 计算5日涨跌幅
                group['5日涨跌幅'] = group['当前价格'] / group['当前价格'].shift(5) - 1
                
                # 计算10日涨跌幅
                group['10日涨跌幅'] = group['当前价格'] / group['当前价格'].shift(10) - 1
                
                # 计算5日平均成交量
                if '成交量(手)' in group.columns:
                    group['5日平均成交量'] = group['成交量(手)'].rolling(5).mean()
                    group['成交量变化率'] = group['成交量(手)'] / group['5日平均成交量'] - 1
                
                return group
            
            historical_data = data.groupby('股票代码', group_keys=False).apply(add_historical_features)
            
            # 添加历史特征
            if '5日涨跌幅' in historical_data.columns:
                features['5日涨跌幅'] = historical_data['5日涨跌幅']
            
            if '10日涨跌幅' in historical_data.columns:
                features['10日涨跌幅'] = historical_data['10日涨跌幅']
            
            if '成交量变化率' in historical_data.columns:
                features['成交量变化率'] = historical_data['成交量变化率']
        except Exception as e:
            print(f"添加历史特征失败: {e}")
        
        # 处理缺失值
        features = features.fillna(0)
        
        print(f"提取了 {len(features.columns)} 个特征")
        return features
    
    def train_model(self, X, y, model_type='random_forest'):
        """
        训练模型
        参数:
            X: 特征DataFrame
            y: 目标变量Series
            model_type: 模型类型，可选值:
                - 'random_forest': 随机森林
                - 'gradient_boosting': 梯度提升
        返回训练好的模型
        """
        print(f"训练{model_type}模型...")
        
        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)
        
        print(f"训练集大小: {X_train.shape}")
        print(f"测试集大小: {X_test.shape}")
        
        # 创建模型管道
        if model_type == 'random_forest':
            pipeline = Pipeline([
                ('imputer', SimpleImputer(strategy='mean')),
                ('scaler', StandardScaler()),
                ('classifier', RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced'))
            ])
            
            # 定义网格搜索参数
            param_grid = {
                'classifier__n_estimators': [50, 100, 200],
                'classifier__max_depth': [None, 10, 20, 30],
                'classifier__min_samples_split': [2, 5, 10]
            }
        elif model_type == 'gradient_boosting':
            pipeline = Pipeline([
                ('imputer', SimpleImputer(strategy='mean')),
                ('scaler', StandardScaler()),
                ('classifier', GradientBoostingClassifier(random_state=42))
            ])
            
            # 定义网格搜索参数
            param_grid = {
                'classifier__n_estimators': [50, 100, 200],
                'classifier__learning_rate': [0.01, 0.1, 0.2],
                'classifier__max_depth': [3, 5, 7]
            }
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        # 使用网格搜索找到最佳参数
        print("执行网格搜索以找到最佳参数...")
        grid_search = GridSearchCV(pipeline, param_grid, cv=5, scoring='roc_auc', n_jobs=-1)
        grid_search.fit(X_train, y_train)
        
        print(f"最佳参数: {grid_search.best_params_}")
        print(f"最佳交叉验证得分: {grid_search.best_score_:.4f}")
        
        # 使用最佳模型
        best_model = grid_search.best_estimator_
        
        # 在测试集上评估模型
        y_pred = best_model.predict(X_test)
        y_prob = best_model.predict_proba(X_test)[:, 1]
        
        print("\n测试集上的模型性能:")
        print(f"ROC AUC: {roc_auc_score(y_test, y_prob):.4f}")
        print("\n分类报告:")
        print(classification_report(y_test, y_pred))
        
        print("\n混淆矩阵:")
        cm = confusion_matrix(y_test, y_pred)
        print(cm)
        
        # 保存模型性能
        performance = {
            '训练日期': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '模型类型': model_type,
            '特征数量': X.shape[1],
            '样本数量': X.shape[0],
            '正样本比例': y.mean(),
            '最佳参数': str(grid_search.best_params_),
            '交叉验证AUC': grid_search.best_score_,
            '测试集AUC': roc_auc_score(y_test, y_prob),
            '准确率': (cm[0, 0] + cm[1, 1]) / cm.sum(),
            '精确率': cm[1, 1] / (cm[1, 1] + cm[0, 1]) if (cm[1, 1] + cm[0, 1]) > 0 else 0,
            '召回率': cm[1, 1] / (cm[1, 1] + cm[1, 0]) if (cm[1, 1] + cm[1, 0]) > 0 else 0
        }
        
        # 如果文件存在，则追加；否则创建新文件
        if os.path.exists(self.model_performance_file):
            performance_df = pd.read_excel(self.model_performance_file)
            performance_df = pd.concat([performance_df, pd.DataFrame([performance])], ignore_index=True)
        else:
            performance_df = pd.DataFrame([performance])
        
        performance_df.to_excel(self.model_performance_file, index=False)
        print(f"已将模型性能保存至 {self.model_performance_file}")
        
        # 保存特征重要性
        feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': best_model.named_steps['classifier'].feature_importances_
        }).sort_values('importance', ascending=False)
        
        feature_importance.to_excel(self.feature_importance_file, index=False)
        print(f"已将特征重要性保存至 {self.feature_importance_file}")
        
        # 保存模型
        joblib.dump(best_model, self.model_file)
        print(f"已将模型保存至 {self.model_file}")
        
        return best_model
    
    def predict(self, X):
        """
        使用模型进行预测
        参数:
            X: 特征DataFrame
        返回预测概率
        """
        print("使用模型进行预测...")
        
        # 加载模型
        if os.path.exists(self.model_file):
            model = joblib.load(self.model_file)
        else:
            raise FileNotFoundError(f"模型文件不存在: {self.model_file}")
        
        # 进行预测
        y_prob = model.predict_proba(X)[:, 1]
        
        return y_prob
    
    def train_all_models(self, stock_data):
        """
        训练所有预测模型
        参数:
            stock_data: 股票历史数据DataFrame
        """
        print("训练所有预测模型...")
        
        # 训练次日上涨预测模型
        X_rise, y_rise, _ = self.prepare_data(stock_data, target_type='next_day_rise')
        rise_model = self.train_model(X_rise, y_rise, model_type='random_forest')
        
        # 训练次日显著上涨预测模型
        X_sig_rise, y_sig_rise, _ = self.prepare_data(stock_data, target_type='next_day_significant_rise')
        sig_rise_model = self.train_model(X_sig_rise, y_sig_rise, model_type='gradient_boosting')
        
        # 训练技术强度增加预测模型
        X_strength_inc, y_strength_inc, _ = self.prepare_data(stock_data, target_type='strength_increase')
        strength_inc_model = self.train_model(X_strength_inc, y_strength_inc, model_type='random_forest')
        
        # 训练技术强度减少预测模型
        X_strength_dec, y_strength_dec, _ = self.prepare_data(stock_data, target_type='strength_decrease')
        strength_dec_model = self.train_model(X_strength_dec, y_strength_dec, model_type='random_forest')
        
        print("所有模型训练完成!")
    
    def enhance_stock_selection(self, stock_data, threshold=0.7):
        """
        增强股票选择
        参数:
            stock_data: 当前股票数据DataFrame
            threshold: 预测概率阈值
        返回增强后的股票选择DataFrame
        """
        print("使用机器学习模型增强股票选择...")
        
        # 提取特征
        features = self.extract_features(stock_data)
        
        # 加载模型
        if os.path.exists(self.model_file):
            model = joblib.load(self.model_file)
        else:
            raise FileNotFoundError(f"模型文件不存在: {self.model_file}")
        
        # 进行预测
        stock_data['预测概率'] = model.predict_proba(features)[:, 1]
        
        # 根据预测概率筛选股票
        enhanced_selection = stock_data[stock_data['预测概率'] >= threshold].copy()
        
        # 按预测概率排序
        enhanced_selection = enhanced_selection.sort_values('预测概率', ascending=False)
        
        print(f"筛选出 {len(enhanced_selection)} 只预测概率>={threshold}的股票")
        
        return enhanced_selection
    
    def run(self, stock_data=None, mode='train'):
        """
        运行预测器
        参数:
            stock_data: 股票数据DataFrame
            mode: 运行模式，可选值:
                - 'train': 训练模型
                - 'predict': 使用模型进行预测
        """
        if stock_data is None:
            # 加载股票数据
            try:
                stock_data = pd.read_excel('股票明细.xlsx')
                print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
            except Exception as e:
                print(f"加载股票数据失败: {e}")
                return
        
        if mode == 'train':
            # 训练所有模型
            self.train_all_models(stock_data)
        elif mode == 'predict':
            # 提取最新日期的数据
            latest_date = stock_data['日期'].max()
            latest_data = stock_data[stock_data['日期'] == latest_date]
            
            # 增强股票选择
            enhanced_selection = self.enhance_stock_selection(latest_data)
            
            # 保存增强后的股票选择
            today = datetime.now().strftime('%Y%m%d')
            enhanced_selection.to_excel(f'results/ml_enhanced_stocks_{today}.xlsx', index=False)
            print(f"已将机器学习增强的股票选择保存至 results/ml_enhanced_stocks_{today}.xlsx")
            
            return enhanced_selection
        else:
            raise ValueError(f"不支持的运行模式: {mode}")

if __name__ == "__main__":
    predictor = MLStockPredictor()
    predictor.run(mode='train')
