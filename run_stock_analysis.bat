@echo off
chcp 65001 > nul
echo ===================================================
echo        股票高胜率策略分析工具
echo ===================================================
echo.
echo 1. 整合股票数据
echo 2. 训练机器学习模型
echo 3. 回测高胜率策略
echo 4. 生成股票推荐
echo 5. 全部执行
echo.
set /p choice=请选择操作 (1/2/3/4/5): 

if "%choice%"=="1" (
    echo.
    echo 正在整合股票数据...
    python run_analysis.py --integrate --data_dir "E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0"
) else if "%choice%"=="2" (
    echo.
    echo 正在训练机器学习模型...
    python run_analysis.py --train
) else if "%choice%"=="3" (
    echo.
    set /p start_date=请输入回测开始日期 (格式: YYYY-MM-DD): 
    set /p end_date=请输入回测结束日期 (格式: YYYY-MM-DD): 
    set /p output_file=请输入输出文件名 (默认: 高胜率策略回测结果.txt): 
    
    if "%output_file%"=="" set output_file=高胜率策略回测结果.txt
    
    echo 正在回测高胜率策略...
    python run_analysis.py --backtest --start_date %start_date% --end_date %end_date% --output %output_file%
) else if "%choice%"=="4" (
    echo.
    set /p date=请输入推荐日期 (格式: YYYY-MM-DD): 
    set /p output_file=请输入输出文件名 (默认: 高胜率策略推荐股票.xlsx): 
    
    if "%output_file%"=="" set output_file=高胜率策略推荐股票.xlsx
    
    echo 正在生成股票推荐...
    python run_analysis.py --recommend --date %date% --output %output_file%
) else if "%choice%"=="5" (
    echo.
    echo 正在整合股票数据...
    python run_analysis.py --integrate --data_dir "E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0"
    
    echo.
    echo 正在训练机器学习模型...
    python run_analysis.py --train
    
    echo.
    set /p start_date=请输入回测开始日期 (格式: YYYY-MM-DD): 
    set /p end_date=请输入回测结束日期 (格式: YYYY-MM-DD): 
    set /p backtest_output=请输入回测输出文件名 (默认: 高胜率策略回测结果.txt): 
    
    if "%backtest_output%"=="" set backtest_output=高胜率策略回测结果.txt
    
    echo 正在回测高胜率策略...
    python run_analysis.py --backtest --start_date %start_date% --end_date %end_date% --output %backtest_output%
    
    echo.
    set /p date=请输入推荐日期 (格式: YYYY-MM-DD): 
    set /p recommend_output=请输入推荐输出文件名 (默认: 高胜率策略推荐股票.xlsx): 
    
    if "%recommend_output%"=="" set recommend_output=高胜率策略推荐股票.xlsx
    
    echo 正在生成股票推荐...
    python run_analysis.py --recommend --date %date% --output %recommend_output%
) else (
    echo.
    echo 无效的选择，请重新运行脚本并选择1-5之间的数字
)

echo.
pause
