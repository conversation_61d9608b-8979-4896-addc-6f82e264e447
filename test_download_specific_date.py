import baostock as bs
import pandas as pd
import os
import datetime

def test_download_trading_calendar(start_date, end_date):
    """测试下载交易日历"""
    print(f"测试下载交易日历: {start_date} 到 {end_date}")
    
    # 登录系统
    lg = bs.login()
    print(f"登录状态: {lg.error_code}, {lg.error_msg}")
    
    # 查询交易日历
    rs = bs.query_trade_dates(start_date=start_date, end_date=end_date)
    print(f"查询状态: {rs.error_code}, {rs.error_msg}")
    
    # 打印数据
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    
    # 登出系统
    bs.logout()
    
    if data_list:
        df = pd.DataFrame(data_list, columns=rs.fields)
        print(f"获取到 {len(df)} 条交易日历数据")
        print("列名:", df.columns.tolist())
        print("所有数据:")
        print(df)
        
        # 提取交易日
        trading_days = df[df['is_trading_day'] == '1']['calendar_date'].tolist()
        print(f"交易日: {trading_days}")
        return trading_days
    else:
        print("未获取到任何交易日历数据")
        return []

def test_download_stock_data(stock_code, date):
    """测试下载单只股票单日数据"""
    print(f"测试下载股票数据: {stock_code}, {date}")
    
    # 登录系统
    lg = bs.login()
    print(f"登录状态: {lg.error_code}, {lg.error_msg}")
    
    # 查询历史K线数据
    rs = bs.query_history_k_data_plus(
        stock_code,
        "date,code,open,high,low,close,volume,amount,pctChg",
        start_date=date,
        end_date=date,
        frequency="d",
        adjustflag="3"  # 3表示前复权
    )
    print(f"查询状态: {rs.error_code}, {rs.error_msg}")
    
    # 打印数据
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    
    # 登出系统
    bs.logout()
    
    if data_list:
        df = pd.DataFrame(data_list, columns=rs.fields)
        print(f"获取到 {len(df)} 条K线数据")
        print("数据:")
        print(df)
        return df
    else:
        print("未获取到任何K线数据")
        return None

def test_download_date_range(start_date, end_date):
    """测试下载指定日期范围的数据"""
    print(f"测试下载日期范围: {start_date} 到 {end_date}")
    
    # 获取交易日历
    trading_days = test_download_trading_calendar(start_date, end_date)
    
    if not trading_days:
        print("没有交易日，无法下载数据")
        return
    
    # 测试股票列表
    test_stocks = ["sh.600000", "sh.600036", "sh.601398", "sz.000001", "sz.000002"]
    
    # 下载每个交易日的数据
    for day in trading_days:
        print(f"\n=== 下载日期 {day} 的数据 ===")
        day_data = []
        
        for stock in test_stocks:
            stock_data = test_download_stock_data(stock, day)
            if stock_data is not None and not stock_data.empty:
                day_data.append(stock_data)
        
        if day_data:
            combined_df = pd.concat(day_data, ignore_index=True)
            print(f"日期 {day} 共下载 {len(combined_df)} 条数据")
        else:
            print(f"日期 {day} 没有下载到任何数据")

if __name__ == "__main__":
    # 测试下载2025-02-05到2025-02-16的数据
    test_download_date_range("2025-02-05", "2025-02-16")
