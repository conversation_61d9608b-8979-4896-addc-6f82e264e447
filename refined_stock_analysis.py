import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score, precision_recall_curve
from sklearn.impute import SimpleImputer
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
import matplotlib.pyplot as plt

# 读取数据
print("读取股票数据...")
data = pd.read_excel('股票明细.xlsx')
print(f"数据形状: {data.shape}")

# 检查数据日期范围
data['日期'] = pd.to_datetime(data['日期'])
print("\n数据日期范围:")
print(f"{data['日期'].min()} 至 {data['日期'].max()}")

# 按股票代码分组，计算次日涨幅
def create_next_day_return(group):
    group = group.sort_values('日期')
    group['次日涨幅'] = group['当前价格'].shift(-1) / group['当前价格'] - 1
    return group

# 应用到每个股票组
print("\n计算次日涨幅...")
data_with_next_day = data.groupby('股票代码', group_keys=False).apply(create_next_day_return)

# 删除最后一个日期的数据（因为没有次日数据）
data_with_next_day = data_with_next_day.dropna(subset=['次日涨幅'])

# 分析次日涨幅的分布
print("\n次日涨幅的分布统计:")
print(data_with_next_day['次日涨幅'].describe())

# 找出次日涨幅的不同阈值
percentiles = [50, 75, 90, 95, 99]
thresholds = [data_with_next_day['次日涨幅'].quantile(p/100) for p in percentiles]
print("\n次日涨幅的不同百分位阈值:")
for p, t in zip(percentiles, thresholds):
    print(f"{p}%分位数: {t:.4f}")

# 选择90%分位数作为"显著上涨"的阈值
significant_rise_threshold = thresholds[2]  # 90%分位数
print(f"\n选择{percentiles[2]}%分位数 {significant_rise_threshold:.4f} 作为'显著上涨'的阈值")

# 创建目标变量：次日是否显著上涨
data_with_next_day['目标_次日显著上涨'] = (data_with_next_day['次日涨幅'] >= significant_rise_threshold).astype(int)

print("\n目标变量分布:")
print(data_with_next_day['目标_次日显著上涨'].value_counts())
print(f"显著上涨的比例: {data_with_next_day['目标_次日显著上涨'].mean():.4f}")

# 特征工程 - 只保留有意义的技术指标和趋势特征
print("\n进行特征工程...")

# 提取技术指标特征
data_with_next_day['均线多头排列'] = data_with_next_day['技术指标'].str.contains('均线多头排列').astype(int)
data_with_next_day['成交量放大'] = data_with_next_day['技术指标'].str.contains('成交量放大').astype(int)
data_with_next_day['MACD金叉'] = data_with_next_day['技术指标'].str.contains('MACD金叉').astype(int)
data_with_next_day['RSI反弹'] = data_with_next_day['技术指标'].str.contains('RSI反弹').astype(int)
data_with_next_day['KDJ金叉'] = data_with_next_day['技术指标'].str.contains('KDJ金叉').astype(int)
data_with_next_day['布林带突破'] = data_with_next_day['技术指标'].str.contains('布林带突破').astype(int)

# 趋势特征
data_with_next_day['趋势_强势上涨'] = (data_with_next_day['趋势'] == 'strong_up').astype(int)
data_with_next_day['趋势_上涨'] = (data_with_next_day['趋势'] == 'up').astype(int)
data_with_next_day['趋势_盘整'] = (data_with_next_day['趋势'] == 'neutral').astype(int)
data_with_next_day['趋势_下跌'] = (data_with_next_day['趋势'] == 'down').astype(int)
data_with_next_day['趋势_强势下跌'] = (data_with_next_day['趋势'] == 'strong_down').astype(int)

# 技术强度和涨跌幅也可能有预测价值
# 价格与目标价/止损价的关系
data_with_next_day['目标价差比'] = (data_with_next_day['目标价'] - data_with_next_day['当前价格']) / data_with_next_day['当前价格']
data_with_next_day['止损价差比'] = (data_with_next_day['当前价格'] - data_with_next_day['止损价']) / data_with_next_day['当前价格']

# 选择特征 - 排除股票代码、股票名称、当前价格等无关特征
features = [
    '技术强度', '涨跌幅', '目标价差比', '止损价差比',
    '均线多头排列', '成交量放大', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
    '趋势_强势上涨', '趋势_上涨', '趋势_盘整', '趋势_下跌', '趋势_强势下跌'
]

X = data_with_next_day[features]
y = data_with_next_day['目标_次日显著上涨']

# 处理缺失值
print("\n处理缺失值...")
print("缺失值统计:")
print(X.isnull().sum())

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)

print(f"训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")

# 创建包含预处理和模型的管道
pipeline = Pipeline([
    ('imputer', SimpleImputer(strategy='mean')),  # 使用均值填充缺失值
    ('scaler', StandardScaler()),  # 标准化特征
    ('classifier', RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced'))
])

# 训练模型
print("\n训练随机森林模型...")
pipeline.fit(X_train, y_train)

# 评估模型
y_pred = pipeline.predict(X_test)
y_prob = pipeline.predict_proba(X_test)[:, 1]  # 获取正类的概率

print("\n随机森林模型评估:")
print(f"准确率: {accuracy_score(y_test, y_pred):.4f}")
print("\n分类报告:")
print(classification_report(y_test, y_pred))

# 特征重要性
feature_importance = pd.DataFrame({
    'feature': features,
    'importance': pipeline.named_steps['classifier'].feature_importances_
}).sort_values('importance', ascending=False)

print("\n特征重要性:")
print(feature_importance)

# 分析正样本的特征分布
positive_samples = data_with_next_day[data_with_next_day['目标_次日显著上涨'] == 1]
negative_samples = data_with_next_day[data_with_next_day['目标_次日显著上涨'] == 0]

print("\n正样本(次日显著上涨)的特征统计:")
for feature in feature_importance.head(5)['feature'].tolist():
    pos_mean = positive_samples[feature].mean()
    neg_mean = negative_samples[feature].mean()
    print(f"{feature}: 正样本均值={pos_mean:.4f}, 负样本均值={neg_mean:.4f}, 差异比例={pos_mean/neg_mean if neg_mean != 0 else 'inf':.4f}")

# 找出最可能导致次日显著上涨的条件组合
print("\n寻找最可能导致次日显著上涨的条件组合...")

# 获取前五个最重要的特征
top_features = feature_importance.head(5)['feature'].tolist()
print(f"分析前五个最重要的特征: {top_features}")

# 创建这些特征的组合条件
conditions = {}
for f in top_features:
    # 对于二元特征，使用是否为1作为条件
    if data_with_next_day[f].nunique() <= 2:
        conditions[f] = 1
    # 对于连续特征，使用大于正样本平均值作为条件
    else:
        threshold = positive_samples[f].mean()
        conditions[f] = threshold
        print(f"特征 {f} 的阈值设为: {threshold:.4f}")

# 应用条件组合筛选股票
filtered_data = data_with_next_day.copy()
for feature, threshold in conditions.items():
    if data_with_next_day[feature].nunique() <= 2:
        filtered_data = filtered_data[filtered_data[feature] == threshold]
    else:
        filtered_data = filtered_data[filtered_data[feature] >= threshold]

# 计算满足条件的股票次日显著上涨的概率
success_rate = filtered_data['目标_次日显著上涨'].mean()
print(f"\n满足所有条件的股票数量: {len(filtered_data)}")
print(f"这些股票次日显著上涨的概率: {success_rate:.4f}")
print(f"相比基准提升: {success_rate / data_with_next_day['目标_次日显著上涨'].mean():.2f}倍")

# 尝试不同的条件组合，找出最佳规则
print("\n尝试不同的条件组合，找出最佳规则...")

# 测试不同数量的特征组合
best_combination = None
best_success_rate = 0
best_sample_size = 0

for n_features in range(1, min(6, len(top_features) + 1)):
    selected_features = top_features[:n_features]
    
    # 应用条件组合筛选股票
    filtered = data_with_next_day.copy()
    for feature in selected_features:
        if data_with_next_day[feature].nunique() <= 2:
            filtered = filtered[filtered[feature] == 1]
        else:
            threshold = positive_samples[feature].mean()
            filtered = filtered[filtered[feature] >= threshold]
    
    # 计算满足条件的股票次日显著上涨的概率
    if len(filtered) > 100:  # 确保样本量足够
        rate = filtered['目标_次日显著上涨'].mean()
        improvement = rate / data_with_next_day['目标_次日显著上涨'].mean()
        
        print(f"使用前{n_features}个特征: 样本量={len(filtered)}, 成功率={rate:.4f}, 提升={improvement:.2f}倍")
        
        if rate > best_success_rate:
            best_success_rate = rate
            best_combination = selected_features
            best_sample_size = len(filtered)

print(f"\n最佳特征组合: {best_combination}")
print(f"样本量: {best_sample_size}")
print(f"成功率: {best_success_rate:.4f}")
print(f"相比基准提升: {best_success_rate / data_with_next_day['目标_次日显著上涨'].mean():.2f}倍")

# 总结发现的规则
print("\n总结发现的最佳规则:")
print("基于特征重要性和模型分析，以下是预测股票次日显著上涨的关键规则:")

best_conditions = {}
for feature in best_combination:
    if data_with_next_day[feature].nunique() <= 2:
        best_conditions[feature] = 1
        print(f"- {feature} = 1")
    else:
        threshold = positive_samples[feature].mean()
        best_conditions[feature] = threshold
        print(f"- {feature} >= {threshold:.4f}")

# 应用最佳规则筛选股票
best_filtered = data_with_next_day.copy()
for feature, threshold in best_conditions.items():
    if data_with_next_day[feature].nunique() <= 2:
        best_filtered = best_filtered[best_filtered[feature] == threshold]
    else:
        best_filtered = best_filtered[best_filtered[feature] >= threshold]

# 保存满足条件的股票列表
if len(best_filtered) > 0:
    recent_date = data_with_next_day['日期'].max()
    recent_signals = best_filtered[best_filtered['日期'] == recent_date][['股票代码', '股票名称', '当前价格', '涨跌幅', '技术强度', '技术指标']]
    
    if len(recent_signals) > 0:
        print(f"\n最近日期 {recent_date.date()} 满足条件的股票:")
        print(recent_signals.head(10))
        recent_signals.to_excel('优化推荐股票.xlsx', index=False)
        print("已将推荐股票保存到 '优化推荐股票.xlsx'")
    else:
        print(f"\n最近日期 {recent_date.date()} 没有满足条件的股票")
