"""
测试单日股票数据下载性能
"""

import os
import time
import pandas as pd
import datetime
import threading
import queue
import baostock as bs
import stock_data_manager as sdm

def test_one_day(test_date='2025-02-05', num_threads=30, max_stocks=None, verbose=False):
    """
    测试单日下载性能

    参数:
        test_date: 测试日期，格式为YYYY-MM-DD
        num_threads: 线程数量
        max_stocks: 最大测试股票数量，如果为None则不限制
        verbose: 是否显示详细的错误信息
    """
    print(f"开始测试单日 {test_date} 的下载性能，使用 {num_threads} 个线程")

    # 记录开始时间
    total_start_time = time.time()

    # 登录baostock
    print("登录baostock...")
    bs_login = bs.login()
    print(f"登录状态: {bs_login.error_code}, {bs_login.error_msg}")

    try:
        # 获取股票代码
        # 获取上证和深证所有股票列表
        rs = bs.query_all_stock(day=datetime.datetime.now().strftime('%Y-%m-%d'))
        data_list = []
        while (rs.error_code == '0') & rs.next():
            data_list.append(rs.get_row_data())
        stock_list_df = pd.DataFrame(data_list, columns=rs.fields)
        stock_codes = stock_list_df['code'].tolist()
        print(f"共找到 {len(stock_codes)} 只股票")

        # 限制股票数量
        if max_stocks is not None and max_stocks > 0 and max_stocks < len(stock_codes):
            stock_codes = stock_codes[:max_stocks]
            print(f"限制测试股票数量为 {max_stocks} 只")

        # 下载该日期的所有股票数据
        day_stock_data = []
        error_count = 0
        success_count = 0

        # 创建线程安全的队列
        result_queue = queue.Queue()

        # 定义线程函数
        def download_batch(batch_codes, thread_id):
            batch_results = []
            batch_success = 0
            batch_error = 0
            batch_start_time = time.time()

            for code in batch_codes:
                max_retries = 3
                retry_count = 0
                success = False

                while retry_count < max_retries and not success:
                    try:
                        # 下载单只股票单日数据
                        rs = bs.query_history_k_data_plus(
                            code,
                            "date,code,open,high,low,close,volume,amount,pctChg",
                            start_date=test_date,
                            end_date=test_date,
                            frequency="d",
                            adjustflag="3"  # 3表示前复权
                        )

                        # 检查API调用是否成功
                        if rs.error_code != '0':
                            if retry_count < max_retries - 1:
                                retry_count += 1
                                if verbose:
                                    print(f"线程{thread_id}: 股票 {code} API调用失败: {rs.error_code}, {rs.error_msg}，重试 ({retry_count}/{max_retries})")
                                time.sleep(0.5)  # 短暂暂停后重试
                                continue
                            else:
                                batch_error += 1
                                break

                        # 处理数据
                        data_list = []
                        try:
                            while rs.next():
                                row_data = rs.get_row_data()
                                if row_data and len(row_data) > 0:
                                    data_list.append(row_data)
                        except UnicodeDecodeError as e:
                            if retry_count < max_retries - 1:
                                retry_count += 1
                                if verbose:
                                    print(f"线程{thread_id}: 股票 {code} 解码错误: {e}，重试 ({retry_count}/{max_retries})")
                                time.sleep(0.5)  # 短暂暂停后重试
                                continue
                            else:
                                batch_error += 1
                                break

                        if data_list:
                            # 创建DataFrame
                            df = pd.DataFrame(data_list, columns=rs.fields)
                            # 转换数据类型
                            numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']
                            df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, errors='coerce')
                            batch_results.append(df)
                            batch_success += 1

                        # 标记成功
                        success = True

                    except Exception as e:
                        if retry_count < max_retries - 1:
                            retry_count += 1
                            if verbose:
                                print(f"线程{thread_id}: 股票 {code} 出错: {e}，重试 ({retry_count}/{max_retries})")
                            time.sleep(0.5)  # 短暂暂停后重试
                        else:
                            print(f"线程{thread_id}: 股票 {code} 在 {max_retries} 次尝试后仍然失败: {e}")
                            batch_error += 1
                            break

            # 记录该批次的结束时间
            batch_end_time = time.time()
            batch_elapsed_time = batch_end_time - batch_start_time

            # 将结果放入队列
            result_queue.put((batch_results, batch_success, batch_error, batch_elapsed_time))
            print(f"线程{thread_id}: 完成 {len(batch_codes)} 只股票的下载，成功: {batch_success}，失败: {batch_error}，耗时: {batch_elapsed_time:.2f}秒")

        # 创建并启动线程
        threads = []
        stocks_per_thread = len(stock_codes) // num_threads
        thread_stats = []

        print(f"开始下载日期 {test_date} 的股票数据，共 {len(stock_codes)} 只股票")
        print(f"将使用 {num_threads} 个线程并行下载，每个线程处理约 {stocks_per_thread} 只股票")

        for i in range(num_threads):
            # 计算每个线程处理的股票范围
            start_idx = i * stocks_per_thread
            # 最后一个线程处理剩余的所有股票
            end_idx = len(stock_codes) if i == num_threads - 1 else (i + 1) * stocks_per_thread
            batch_codes = stock_codes[start_idx:end_idx]

            thread = threading.Thread(target=download_batch, args=(batch_codes, i+1))
            thread.daemon = True
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 收集所有线程的结果
        thread_elapsed_times = []
        while not result_queue.empty():
            batch_results, batch_success, batch_error, batch_elapsed_time = result_queue.get()
            day_stock_data.extend(batch_results)
            success_count += batch_success
            error_count += batch_error
            thread_elapsed_times.append(batch_elapsed_time)

        # 记录总结束时间
        total_end_time = time.time()
        total_elapsed_time = total_end_time - total_start_time

        # 打印性能统计
        print("\n性能测试结果:")
        print(f"总耗时: {total_elapsed_time:.2f}秒 ({total_elapsed_time/60:.2f}分钟)")
        print(f"总记录数: {len(day_stock_data)}")
        print(f"成功数: {success_count}")
        print(f"失败数: {error_count}")
        print(f"平均每只股票耗时: {total_elapsed_time/success_count:.4f}秒 (仅计算成功的)")
        print(f"平均线程耗时: {sum(thread_elapsed_times)/len(thread_elapsed_times):.2f}秒")
        print(f"最长线程耗时: {max(thread_elapsed_times):.2f}秒")
        print(f"最短线程耗时: {min(thread_elapsed_times):.2f}秒")

        # 合并并保存该日期的所有股票数据
        if day_stock_data:
            day_combined_df = pd.concat(day_stock_data, ignore_index=True)
            print(f"日期 {test_date} 的股票数据合并完成，共 {len(day_combined_df)} 条记录")

            # 保存到按日期存储的文件
            save_start_time = time.time()
            sdm.save_daily_data(day_combined_df, test_date)
            save_end_time = time.time()
            save_elapsed_time = save_end_time - save_start_time

            print(f"日期 {test_date} 的股票数据已保存，耗时: {save_elapsed_time:.2f}秒")
        else:
            print(f"日期 {test_date} 没有下载到任何股票数据")

    finally:
        # 登出baostock
        bs.logout()
        print("已登出baostock")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='测试单日股票数据下载性能')
    parser.add_argument('--date', type=str, default='2025-02-05', help='测试日期，格式为YYYY-MM-DD')
    parser.add_argument('--threads', type=int, default=30, help='线程数量')
    parser.add_argument('--max_stocks', type=int, default=None, help='最大测试股票数量')

    args = parser.parse_args()

    # 执行测试
    test_one_day(args.date, args.threads, args.max_stocks)
