import pandas as pd
import os
import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置数据目录
base_dir = r'E:\机器学习\complete_excel_results'
details_file = os.path.join(base_dir, '股票明细_完整.xlsx')

print(f"读取股票明细文件: {details_file}")
stock_df = pd.read_excel(details_file)

# 将日期列转换为日期类型
stock_df['日期'] = pd.to_datetime(stock_df['日期'])

# 按股票代码和日期排序
stock_df = stock_df.sort_values(['股票代码', '日期'])

print(f"总记录数: {len(stock_df)}")
print(f"列名: {stock_df.columns.tolist()}")

# 确保字符串类型的列正确处理
string_columns = ['技术指标特征', '趋势组合', '日内股票标记']
for col in string_columns:
    if col in stock_df.columns:
        stock_df[col] = stock_df[col].astype(str)
        print(f"{col} 列已转换为字符串类型")

# 测试组合特征筛选
def test_combined_features(conditions):
    print(f"\n测试组合筛选条件:")
    for condition in conditions:
        print(f"- {condition[0]} {condition[1]} {condition[2]}")
    
    # 应用筛选条件
    filtered_df = stock_df.copy()
    print(f"初始数据集大小: {len(filtered_df)} 条记录")
    
    for col_name, operator, value in conditions:
        if operator == "==":
            filtered_df = filtered_df[filtered_df[col_name] == value]
        elif operator == ">=":
            filtered_df = filtered_df[filtered_df[col_name] >= value]
        elif operator == "<=":
            filtered_df = filtered_df[filtered_df[col_name] <= value]
        
        print(f"应用 {col_name} {operator} {value} 后剩余记录数: {len(filtered_df)}")
    
    print(f"最终筛选结果记录数: {len(filtered_df)}")
    
    if len(filtered_df) > 0:
        print(f"筛选结果示例 (前5行):")
        display_cols = ['股票代码', '股票名称', '日期']
        for col_name, _, _ in conditions:
            if col_name not in display_cols:
                display_cols.append(col_name)
        print(filtered_df[display_cols].head())
    else:
        print("警告: 没有筛选出任何记录")
    
    return filtered_df

# 测试策略1的组合特征
strategy1_conditions = [
    ('技术强度', '==', 28),
    ('连续技术强度5天数', '>=', 355),
    ('连续技术强度10天数', '>=', 579),
    ('技术指标特征', '==', '111111'),
    ('趋势组合', '==', '111111'),
    ('日内股票标记', '==', '664'),
    ('成交量是前一日几倍', '>=', 2.5)
]

result1 = test_combined_features(strategy1_conditions)

# 测试策略2 - 只使用部分特征
strategy2_conditions = [
    ('技术强度', '==', 28),
    ('连续技术强度5天数', '>=', 355),
    ('连续技术强度10天数', '>=', 579)
]

result2 = test_combined_features(strategy2_conditions)

# 测试策略3 - 只使用技术指标特征和趋势组合
strategy3_conditions = [
    ('技术指标特征', '==', '111111'),
    ('趋势组合', '==', '111111')
]

result3 = test_combined_features(strategy3_conditions)

# 测试策略4 - 只使用日内股票标记和成交量
strategy4_conditions = [
    ('日内股票标记', '==', '664'),
    ('成交量是前一日几倍', '>=', 2.5)
]

result4 = test_combined_features(strategy4_conditions)

# 测试策略5 - 只使用技术强度
strategy5_conditions = [
    ('技术强度', '==', 28)
]

result5 = test_combined_features(strategy5_conditions)

print("\n所有测试完成")
