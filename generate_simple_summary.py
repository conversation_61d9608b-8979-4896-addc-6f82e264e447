#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特征组合策略简洁总结
作者: Augment AI
版本: 1.0.0

该脚本生成一个简洁的表格，包含策略组合、总收益率、平均收益率、平均胜率、平均每日交易笔数和总交易笔数。
"""

import pandas as pd
import os

# 创建结果目录
results_dir = 'simple_summary_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data():
    """加载数据"""
    try:
        # 加载总收益数据
        total_return_df = pd.read_csv('total_return_results/top_combinations_total_return.csv')
        return total_return_df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def generate_simple_summary(total_return_df):
    """生成简洁的总结表格"""
    print("生成简洁的总结表格...")
    
    # 提取需要的列
    summary_df = pd.DataFrame({
        '策略组合': total_return_df['特征组合'],
        '特征数量': total_return_df['特征数量'],
        '总收益率(%)': total_return_df['总收益率(%)'],
        '平均收益率(%)': total_return_df['平均每日收益率(%)'],
        '平均胜率(%)': total_return_df['胜率(%)'],
        '平均每日交易笔数': total_return_df['平均每日交易数'],
        '总交易笔数': total_return_df['总交易次数']
    })
    
    # 保存为CSV
    csv_file = f"{results_dir}/simple_summary.csv"
    summary_df.to_csv(csv_file, index=False)
    print(f"简洁总结表格已保存到 {csv_file}")
    
    # 打印表格
    print("\n简洁总结表格:")
    print(summary_df.to_string())
    
    # 生成Markdown格式的表格
    markdown_table = "| 策略组合 | 特征数量 | 总收益率(%) | 平均收益率(%) | 平均胜率(%) | 平均每日交易笔数 | 总交易笔数 |\n"
    markdown_table += "|---------|---------|------------|--------------|------------|----------------|------------|\n"
    
    for _, row in summary_df.iterrows():
        markdown_table += f"| {row['策略组合']} | {row['特征数量']} | {row['总收益率(%)']:.2f} | {row['平均收益率(%)']:.2f} | {row['平均胜率(%)']:.2f} | {row['平均每日交易笔数']:.2f} | {int(row['总交易笔数'])} |\n"
    
    # 保存Markdown表格
    markdown_file = f"{results_dir}/simple_summary.md"
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(markdown_table)
    print(f"Markdown格式的简洁总结表格已保存到 {markdown_file}")
    
    # 生成HTML表格
    html = """
    <html>
    <head>
        <style>
            table {
                border-collapse: collapse;
                width: 100%;
                font-family: Arial, sans-serif;
            }
            th, td {
                border: 1px solid #dddddd;
                text-align: left;
                padding: 8px;
            }
            th {
                background-color: #f2f2f2;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            .highlight {
                background-color: #e6f7ff;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <h2>特征组合策略简洁总结</h2>
        <table>
            <tr>
                <th>排名</th>
                <th>策略组合</th>
                <th>特征数量</th>
                <th>总收益率(%)</th>
                <th>平均收益率(%)</th>
                <th>平均胜率(%)</th>
                <th>平均每日交易笔数</th>
                <th>总交易笔数</th>
            </tr>
    """
    
    # 添加每行数据
    for i, (_, row) in enumerate(summary_df.iterrows()):
        highlight = ' class="highlight"' if i < 3 else ''
        html += f"""
            <tr{highlight}>
                <td>{i+1}</td>
                <td>{row['策略组合']}</td>
                <td>{row['特征数量']}</td>
                <td>{row['总收益率(%)']:.2f}</td>
                <td>{row['平均收益率(%)']:.2f}</td>
                <td>{row['平均胜率(%)']:.2f}</td>
                <td>{row['平均每日交易笔数']:.2f}</td>
                <td>{int(row['总交易笔数'])}</td>
            </tr>
        """
    
    # 关闭HTML
    html += """
        </table>
    </body>
    </html>
    """
    
    # 保存HTML表格
    html_file = f"{results_dir}/simple_summary.html"
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html)
    print(f"HTML格式的简洁总结表格已保存到 {html_file}")
    
    return summary_df

if __name__ == "__main__":
    # 加载数据
    total_return_df = load_data()
    if total_return_df is None:
        exit(1)
    
    # 生成简洁的总结表格
    summary_df = generate_simple_summary(total_return_df)
