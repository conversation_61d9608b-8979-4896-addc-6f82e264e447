"""
完整解决方案 - 解决py_mini_racer依赖问题的打包脚本
"""

import os
import sys
import shutil
import subprocess
import tempfile
import site
import glob
import importlib.util

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'pyinstaller',
        'pandas',
        'numpy',
        'akshare',
        'tqdm',
        'openpyxl',
        'pyarrow',
        'requests',
        'lxml',
        'beautifulsoup4',
        'pywin32',
        'py_mini_racer',
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print("\n需要安装以下依赖:")
        for package in missing_packages:
            print(f"  - {package}")
        
        install = input("\n是否自动安装这些依赖? (y/n): ")
        if install.lower() == 'y':
            for package in missing_packages:
                print(f"\n正在安装 {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"{package} 安装完成")
        else:
            print("\n请手动安装缺失的依赖后再运行此脚本")
            sys.exit(1)

def extract_mini_racer_files():
    """提取py_mini_racer库的所有文件"""
    try:
        import py_mini_racer
        mini_racer_dir = os.path.dirname(py_mini_racer.__file__)
        print(f"py_mini_racer库位置: {mini_racer_dir}")
        
        # 查找所有必要的文件
        required_files = []
        for file_name in ["mini_racer.dll", "snapshot_blob.bin", "icudtl.dat"]:
            file_path = os.path.join(mini_racer_dir, file_name)
            if os.path.exists(file_path):
                required_files.append((file_path, file_name))
                print(f"找到文件: {file_name}")
            else:
                print(f"警告: 未找到文件: {file_name}")
        
        return mini_racer_dir, required_files
    except ImportError:
        print("错误: 未安装py_mini_racer库")
        return None, []

def build_executable(mini_racer_dir, required_files):
    """使用PyInstaller构建可执行文件"""
    print("\n开始构建可执行文件...")
    
    # 创建临时目录存放自定义钩子
    hook_dir = os.path.join(tempfile.gettempdir(), "custom_hooks")
    os.makedirs(hook_dir, exist_ok=True)
    
    # 创建自定义钩子文件
    hook_content = """
# 自定义钩子文件，用于处理py_mini_racer
from PyInstaller.utils.hooks import collect_all

# 收集py_mini_racer的所有依赖
datas, binaries, hiddenimports = collect_all('py_mini_racer')

# 确保添加了所有必要的隐藏导入
hiddenimports += ['py_mini_racer']
"""
    
    with open(os.path.join(hook_dir, "hook-py_mini_racer.py"), "w") as f:
        f.write(hook_content)
    
    print(f"创建了自定义钩子文件: {os.path.join(hook_dir, 'hook-py_mini_racer.py')}")
    
    # 准备命令行参数
    cmd = [
        sys.executable, 
        "-m", 
        "PyInstaller",
        "--clean",
        "--windowed",  # 不显示控制台窗口
        "--onefile",   # 生成单个可执行文件
        "--name", "自动下载程序",
        "--additional-hooks-dir", hook_dir,
        "--hidden-import", "akshare",
        "--hidden-import", "py_mini_racer",
        "--hidden-import", "stock_data_manager",
    ]
    
    # 添加数据文件
    for src_file, _ in required_files:
        cmd.extend(["--add-data", f"{src_file}{os.pathsep}."])
    
    # 添加mini_racer_loader.py
    cmd.extend(["--add-data", f"mini_racer_loader.py{os.pathsep}."])
    
    # 添加主脚本
    cmd.append("download_stock_data_gui.py")
    
    # 执行命令
    print(f"执行命令: {' '.join(cmd)}")
    subprocess.check_call(cmd)
    
    print("\n构建完成!")

def create_release_package(required_files):
    """创建一个包含所有必要文件的发布包"""
    release_dir = "股票数据下载工具_完整解决方案"
    
    # 如果目录已存在，先删除
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    
    # 创建发布目录
    os.makedirs(release_dir)
    
    # 复制可执行文件
    shutil.copy(os.path.join("dist", "自动下载程序.exe"), os.path.join(release_dir, "自动下载程序.exe"))
    
    # 复制必要的文件
    for src_file, file_name in required_files:
        dst_file = os.path.join(release_dir, file_name)
        shutil.copy2(src_file, dst_file)
        print(f"已复制: {file_name}")
    
    # 创建默认数据目录
    os.makedirs(os.path.join(release_dir, "data", "stock_data", "daily"), exist_ok=True)
    
    # 创建说明文件
    with open(os.path.join(release_dir, "使用说明.txt"), "w", encoding="utf-8") as f:
        f.write("""股票数据下载工具使用说明
====================

1. 运行方法:
   双击"自动下载程序.exe"即可启动程序

2. 功能说明:
   - 可以下载指定日期范围内的A股股票历史数据
   - 数据按日期分别存储在Excel文件中
   - 默认保存在程序所在目录的data文件夹中

3. 使用步骤:
   a. 设置数据输出目录(可选)
   b. 设置日期范围
   c. 点击"开始下载"按钮
   d. 等待下载完成

4. 注意事项:
   - 首次运行时可能需要等待较长时间
   - 下载过程中请保持网络连接
   - 如遇到问题，请查看程序日志窗口的提示信息
   - 请确保程序目录中的所有文件都存在，不要删除任何文件
""")
    
    print(f"\n发布包已创建: {os.path.abspath(release_dir)}")
    print("包含以下文件:")
    print(f"  - {release_dir}/自动下载程序.exe")
    for _, file_name in required_files:
        print(f"  - {release_dir}/{file_name}")
    print(f"  - {release_dir}/使用说明.txt")
    print(f"  - {release_dir}/data/ (默认数据目录)")

def main():
    """主函数"""
    print("=" * 50)
    print("股票数据下载工具打包脚本 (完整解决方案)")
    print("=" * 50)
    
    # 检查依赖
    print("\n检查依赖...")
    check_dependencies()
    
    # 提取py_mini_racer文件
    print("\n提取py_mini_racer文件...")
    mini_racer_dir, required_files = extract_mini_racer_files()
    if not mini_racer_dir:
        print("错误: 无法提取py_mini_racer文件")
        sys.exit(1)
    
    # 构建可执行文件
    build_executable(mini_racer_dir, required_files)
    
    # 创建发布包
    create_release_package(required_files)
    
    print("\n打包过程完成!")
    print("=" * 50)

if __name__ == "__main__":
    main()
