"""
直接修复 - 手动复制所有必要的文件
"""

import os
import sys
import shutil
import subprocess
import tempfile

def main():
    print("=" * 50)
    print("直接修复 - 手动复制所有必要的文件")
    print("=" * 50)
    
    # 1. 找到py_mini_racer库的位置
    try:
        import py_mini_racer
        mini_racer_dir = os.path.dirname(py_mini_racer.__file__)
        print(f"py_mini_racer库位置: {mini_racer_dir}")
    except ImportError:
        print("错误: 未安装py_mini_racer库")
        return
    
    # 2. 创建临时目录
    temp_dir = os.path.join(tempfile.gettempdir(), "mini_racer_files")
    os.makedirs(temp_dir, exist_ok=True)
    print(f"创建临时目录: {temp_dir}")
    
    # 3. 复制必要的文件
    required_files = ["mini_racer.dll", "snapshot_blob.bin", "icudtl.dat"]
    for file in required_files:
        src_file = os.path.join(mini_racer_dir, file)
        if os.path.exists(src_file):
            dst_file = os.path.join(temp_dir, file)
            shutil.copy2(src_file, dst_file)
            print(f"已复制: {file}")
        else:
            print(f"警告: 文件不存在: {src_file}")
    
    # 4. 创建发布目录
    release_dir = "股票数据下载工具_直接修复版"
    os.makedirs(release_dir, exist_ok=True)
    print(f"创建发布目录: {release_dir}")
    
    # 5. 复制现有的可执行文件
    if os.path.exists("dist/自动下载程序.exe"):
        shutil.copy2("dist/自动下载程序.exe", os.path.join(release_dir, "自动下载程序.exe"))
        print("已复制可执行文件")
    else:
        print("错误: 可执行文件不存在")
        return
    
    # 6. 复制必要的文件到发布目录
    for file in required_files:
        src_file = os.path.join(temp_dir, file)
        if os.path.exists(src_file):
            dst_file = os.path.join(release_dir, file)
            shutil.copy2(src_file, dst_file)
            print(f"已复制到发布目录: {file}")
    
    # 7. 创建数据目录
    data_dir = os.path.join(release_dir, "data", "stock_data", "daily")
    os.makedirs(data_dir, exist_ok=True)
    print(f"创建数据目录: {data_dir}")
    
    # 8. 创建说明文件
    readme_file = os.path.join(release_dir, "使用说明.txt")
    with open(readme_file, "w", encoding="utf-8") as f:
        f.write("""股票数据下载工具使用说明
====================

1. 运行方法:
   双击"自动下载程序.exe"即可启动程序

2. 功能说明:
   - 可以下载指定日期范围内的A股股票历史数据
   - 数据按日期分别存储在Excel文件中
   - 默认保存在程序所在目录的data文件夹中

3. 使用步骤:
   a. 设置数据输出目录(可选)
   b. 设置日期范围
   c. 点击"开始下载"按钮
   d. 等待下载完成

4. 注意事项:
   - 首次运行时可能需要等待较长时间
   - 下载过程中请保持网络连接
   - 如遇到问题，请查看程序日志窗口的提示信息
   - 请确保程序目录中的所有文件都存在，不要删除任何文件
""")
    print("已创建说明文件")
    
    print("\n完成!")
    print(f"请将整个 {release_dir} 目录发送给客户")
    print("=" * 50)

if __name__ == "__main__":
    main()
