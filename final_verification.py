#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def main():
    print("=== 最终修复验证 ===")
    
    # 读取生成的数据
    df = pd.read_excel('E:/机器学习/complete_excel_results/tech_strength/daily/tech_strength_2025-05-15.xlsx', 
                      dtype={'股票代码': str, '技术指标特征': str, '趋势组合': str})
    
    print(f"数据行数: {len(df)}")
    
    # 检查样本
    sample = df.iloc[0]
    print(f"\n样本股票: {sample['股票代码']}")
    print(f"技术指标特征: {sample['技术指标特征']} (类型: {type(sample['技术指标特征'])})")
    print(f"趋势组合: {sample['趋势组合']} (类型: {type(sample['趋势组合'])})")
    print(f"连续技术强度: 3天={sample['连续技术强度3天数']}, 5天={sample['连续技术强度5天数']}, 10天={sample['连续技术强度10天数']}")
    
    # 检查字段类型
    print(f"\n字段类型:")
    print(f"技术指标特征: {df['技术指标特征'].dtype}")
    print(f"趋势组合: {df['趋势组合'].dtype}")
    
    # 检查多样性
    print(f"\n数据多样性:")
    print(f"技术指标特征唯一值: {df['技术指标特征'].nunique()}")
    print(f"趋势组合唯一值: {df['趋势组合'].nunique()}")
    
    # 检查连续技术强度
    print(f"\n连续技术强度检查 (前5个):")
    for i in range(min(5, len(df))):
        row = df.iloc[i]
        c3, c5, c10 = row['连续技术强度3天数'], row['连续技术强度5天数'], row['连续技术强度10天数']
        order_ok = c3 <= c5 <= c10
        print(f"  {row['股票代码']}: 3天={c3}, 5天={c5}, 10天={c10} {'✅' if order_ok else '❌'}")
    
    # 与原始数据对比
    print(f"\n与原始数据对比:")
    original_df = pd.read_excel('E:/机器学习/complete_excel_results/股票明细_完整.xlsx')
    original_sample = original_df[original_df['日期'] == '2025-05-15'].iloc[0]
    
    print(f"原始数据 - 技术指标特征: {original_sample['技术指标特征']} (类型: {type(original_sample['技术指标特征'])})")
    print(f"生成数据 - 技术指标特征: {sample['技术指标特征']} (类型: {type(sample['技术指标特征'])})")
    
    print(f"原始数据 - 趋势组合: {original_sample['趋势组合']} (类型: {type(original_sample['趋势组合'])})")
    print(f"生成数据 - 趋势组合: {sample['趋势组合']} (类型: {type(sample['趋势组合'])})")
    
    print("\n✅ 验证完成")

if __name__ == "__main__":
    main()
