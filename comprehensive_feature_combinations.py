#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全面特征组合分析系统
作者: Augment AI
版本: 1.0.0

该脚本系统地探索从2个到5个特征的所有组合，并对每个组合的表现进行统计分析。
"""

import pandas as pd
import numpy as np
import os
import itertools
import time
from datetime import datetime
from tqdm import tqdm
import pickle
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict

# 创建结果目录
results_dir = 'combination_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")

        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])

        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")

        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def get_feature_combinations(features, min_features=2, max_features=5):
    """生成特征组合"""
    all_combinations = []
    for r in range(min_features, max_features + 1):
        combinations = list(itertools.combinations(features, r))
        print(f"{r}特征组合数量: {len(combinations)}")
        all_combinations.extend(combinations)

    print(f"总组合数量: {len(all_combinations)}")
    return all_combinations

def evaluate_feature_combination(data, feature_combination, start_date, end_date):
    """评估特征组合的表现"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)

    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]

    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())

    # 初始化结果
    results = {
        'feature_combination': feature_combination,
        'feature_count': len(feature_combination),
        'total_trades': 0,
        'win_count': 0,
        'total_return': 0,
        'daily_returns': [],
        'daily_win_rates': [],
        'daily_trade_counts': []
    }

    # 对每个交易日进行评估
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue

        # 获取次日日期
        next_date = trading_dates[i + 1]

        # 获取当日数据
        daily_data = date_range_data[date_range_data['日期'] == current_date]

        # 应用特征组合条件
        selected = daily_data.copy()
        for feature in feature_combination:
            if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
                         '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势',
                         '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势',
                         '连续技术强度10天数趋势', '连续技术强度3天数价格趋势',
                         '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势',
                         '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势',
                         '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
                # 二元特征，使用==1条件
                selected = selected[selected[feature] == 1]
            elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                # 连续值特征，使用>=中位数条件
                median_value = np.median(daily_data[feature])
                selected = selected[selected[feature] >= median_value]
            elif feature == '看涨技术指标数量':
                # 看涨技术指标数量，使用>=3条件
                selected = selected[selected[feature] >= 3]

        # 如果有推荐的股票，计算表现
        if len(selected) > 0:
            # 获取推荐股票的代码
            recommended_codes = selected['股票代码'].tolist()

            # 获取次日这些股票的数据
            next_day_data = date_range_data[
                (date_range_data['日期'] == next_date) &
                (date_range_data['股票代码'].isin(recommended_codes))
            ]

            # 如果次日有这些股票的数据
            if len(next_day_data) > 0:
                # 计算平均涨跌幅
                avg_return = next_day_data['涨跌幅'].mean()

                # 计算盈利股票数量
                win_stocks = len(next_day_data[next_day_data['涨跌幅'] > 0])
                win_rate = win_stocks / len(next_day_data) * 100

                # 更新结果
                results['total_trades'] += len(next_day_data)
                results['win_count'] += win_stocks
                results['total_return'] += avg_return
                results['daily_returns'].append(avg_return)
                results['daily_win_rates'].append(win_rate)
                results['daily_trade_counts'].append(len(next_day_data))

    # 计算最终统计结果
    if results['total_trades'] > 0:
        results['win_rate'] = results['win_count'] / results['total_trades'] * 100
        results['avg_return'] = results['total_return'] / len(results['daily_returns'])
        results['avg_daily_trades'] = sum(results['daily_trade_counts']) / len(results['daily_trade_counts'])
        results['std_return'] = np.std(results['daily_returns'])
        results['max_return'] = max(results['daily_returns'])
        results['min_return'] = min(results['daily_returns'])
        results['sharpe_ratio'] = results['avg_return'] / results['std_return'] if results['std_return'] > 0 else 0
    else:
        results['win_rate'] = 0
        results['avg_return'] = 0
        results['avg_daily_trades'] = 0
        results['std_return'] = 0
        results['max_return'] = 0
        results['min_return'] = 0
        results['sharpe_ratio'] = 0

    return results

def explore_all_combinations(data, features, start_date, end_date, min_features=2, max_features=5,
                            save_interim=True, interim_frequency=100):
    """探索所有特征组合"""
    print(f"正在探索从{min_features}到{max_features}个特征的所有组合...")

    # 生成特征组合
    combinations = get_feature_combinations(features, min_features, max_features)

    # 初始化结果
    all_results = []

    # 对每个组合进行评估
    for i, combination in enumerate(tqdm(combinations)):
        result = evaluate_feature_combination(data, combination, start_date, end_date)

        # 只保留有交易的结果
        if result['total_trades'] > 0:
            all_results.append(result)

        # 定期保存中间结果
        if save_interim and (i + 1) % interim_frequency == 0:
            # 按总收益率排序
            sorted_results = sorted(all_results, key=lambda x: x['avg_return'], reverse=True)

            # 保存中间结果
            interim_file = f"{results_dir}/interim_results_{i+1}.pkl"
            with open(interim_file, 'wb') as f:
                pickle.dump(sorted_results, f)

            print(f"已评估 {i+1}/{len(combinations)} 个组合，中间结果已保存到 {interim_file}")

    # 按总收益率排序
    all_results = sorted(all_results, key=lambda x: x['avg_return'], reverse=True)

    # 保存最终结果
    final_file = f"{results_dir}/all_combination_results.pkl"
    with open(final_file, 'wb') as f:
        pickle.dump(all_results, f)

    print(f"所有组合评估完成，结果已保存到 {final_file}")

    return all_results

def analyze_results(results):
    """分析特征组合结果"""
    print("分析特征组合结果...")

    # 按特征数量分组
    grouped_by_count = defaultdict(list)
    for result in results:
        grouped_by_count[result['feature_count']].append(result)

    # 对每组特征数量进行统计
    count_stats = {}
    for count, group_results in grouped_by_count.items():
        # 按平均收益率排序
        sorted_results = sorted(group_results, key=lambda x: x['avg_return'], reverse=True)

        # 计算统计量
        count_stats[count] = {
            'combination_count': len(group_results),
            'avg_return_mean': np.mean([r['avg_return'] for r in group_results]),
            'avg_return_std': np.std([r['avg_return'] for r in group_results]),
            'win_rate_mean': np.mean([r['win_rate'] for r in group_results]),
            'win_rate_std': np.std([r['win_rate'] for r in group_results]),
            'sharpe_ratio_mean': np.mean([r['sharpe_ratio'] for r in group_results]),
            'sharpe_ratio_std': np.std([r['sharpe_ratio'] for r in group_results]),
            'top_10_combinations': sorted_results[:10]
        }

    # 保存统计结果
    stats_file = f"{results_dir}/combination_statistics.pkl"
    with open(stats_file, 'wb') as f:
        pickle.dump(count_stats, f)

    print(f"特征组合统计结果已保存到 {stats_file}")

    # 打印统计结果
    print("\n特征组合统计结果:")
    for count, stats in count_stats.items():
        print(f"\n{count}特征组合:")
        print(f"  组合数量: {stats['combination_count']}")
        print(f"  平均收益率: {stats['avg_return_mean']:.4f} ± {stats['avg_return_std']:.4f}")
        print(f"  平均胜率: {stats['win_rate_mean']:.2f}% ± {stats['win_rate_std']:.2f}%")
        print(f"  平均夏普比率: {stats['sharpe_ratio_mean']:.4f} ± {stats['sharpe_ratio_std']:.4f}")

        print("\n  前10名组合:")
        for i, result in enumerate(stats['top_10_combinations']):
            feature_str = ', '.join(result['feature_combination'])
            print(f"    {i+1}. {feature_str}")
            print(f"       平均收益率: {result['avg_return']:.4f}, 胜率: {result['win_rate']:.2f}%, 交易次数: {result['total_trades']}")

    return count_stats

def generate_summary_report(results, count_stats):
    """生成汇总报告"""
    print("生成汇总报告...")

    # 创建汇总数据
    summary_data = []

    # 添加总体最佳组合
    top_overall = results[:20]
    for i, result in enumerate(top_overall):
        feature_str = ', '.join(result['feature_combination'])
        summary_data.append({
            '排名': i + 1,
            '类别': '总体最佳',
            '特征数量': result['feature_count'],
            '特征组合': feature_str,
            '平均收益率': result['avg_return'],
            '胜率': result['win_rate'],
            '夏普比率': result['sharpe_ratio'],
            '交易次数': result['total_trades'],
            '平均每日交易': result['avg_daily_trades']
        })

    # 添加每个特征数量的最佳组合
    for count, stats in count_stats.items():
        top_by_count = stats['top_10_combinations']
        for i, result in enumerate(top_by_count):
            feature_str = ', '.join(result['feature_combination'])
            summary_data.append({
                '排名': i + 1,
                '类别': f'{count}特征最佳',
                '特征数量': count,
                '特征组合': feature_str,
                '平均收益率': result['avg_return'],
                '胜率': result['win_rate'],
                '夏普比率': result['sharpe_ratio'],
                '交易次数': result['total_trades'],
                '平均每日交易': result['avg_daily_trades']
            })

    # 转换为DataFrame
    summary_df = pd.DataFrame(summary_data)

    # 保存汇总报告
    summary_file = f"{results_dir}/combination_summary.csv"
    summary_df.to_csv(summary_file, index=False)

    print(f"汇总报告已保存到 {summary_file}")

    return summary_df

def analyze_feature_importance(results):
    """分析特征重要性"""
    print("分析特征重要性...")

    # 初始化特征计数和平均表现
    feature_counts = defaultdict(int)
    feature_returns = defaultdict(list)
    feature_win_rates = defaultdict(list)

    # 统计每个特征在所有组合中的出现次数和表现
    for result in results:
        for feature in result['feature_combination']:
            feature_counts[feature] += 1
            feature_returns[feature].append(result['avg_return'])
            feature_win_rates[feature].append(result['win_rate'])

    # 计算每个特征的平均表现
    feature_stats = []
    for feature in feature_counts.keys():
        feature_stats.append({
            '特征': feature,
            '出现次数': feature_counts[feature],
            '平均收益率': np.mean(feature_returns[feature]),
            '收益率标准差': np.std(feature_returns[feature]),
            '平均胜率': np.mean(feature_win_rates[feature]),
            '胜率标准差': np.std(feature_win_rates[feature])
        })

    # 转换为DataFrame并按平均收益率排序
    feature_stats_df = pd.DataFrame(feature_stats)
    feature_stats_df = feature_stats_df.sort_values('平均收益率', ascending=False)

    # 保存特征重要性结果
    importance_file = f"{results_dir}/feature_importance.csv"
    feature_stats_df.to_csv(importance_file, index=False)

    print(f"特征重要性分析结果已保存到 {importance_file}")

    # 打印特征重要性
    print("\n特征重要性排名 (按平均收益率):")
    for i, (_, row) in enumerate(feature_stats_df.iterrows()):
        print(f"{i+1}. {row['特征']}: 平均收益率={row['平均收益率']:.4f}, 平均胜率={row['平均胜率']:.2f}%, 出现次数={row['出现次数']}")

    return feature_stats_df

def generate_recommendations(data, top_combinations, date=None):
    """根据最佳组合生成股票推荐"""
    print("生成股票推荐...")

    if date is None:
        date = data['日期'].max()
    else:
        date = pd.to_datetime(date)

    print(f"推荐日期: {date.strftime('%Y-%m-%d')}")

    # 获取当日数据
    daily_data = data[data['日期'] == date]

    # 对每个最佳组合生成推荐
    recommendations = {}
    for i, result in enumerate(top_combinations[:5]):  # 只使用前5个最佳组合
        combination = result['feature_combination']

        # 应用特征组合条件
        selected = daily_data.copy()
        for feature in combination:
            if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
                         '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势',
                         '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势',
                         '连续技术强度10天数趋势', '连续技术强度3天数价格趋势',
                         '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势',
                         '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势',
                         '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
                # 二元特征，使用==1条件
                selected = selected[selected[feature] == 1]
            elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                # 连续值特征，使用>=中位数条件
                median_value = np.median(daily_data[feature])
                selected = selected[selected[feature] >= median_value]
            elif feature == '看涨技术指标数量':
                # 看涨技术指标数量，使用>=3条件
                selected = selected[selected[feature] >= 3]

        # 组合名称
        combination_name = f"组合{i+1}_{'+'.join([f.split('_')[-1] if '_' in f else f for f in combination])}"

        # 保存推荐结果
        recommendations[combination_name] = selected

        # 保存到文件
        if len(selected) > 0:
            output_file = f"{results_dir}/{combination_name}_{date.strftime('%Y%m%d')}.csv"
            selected.to_csv(output_file, index=False)
            print(f"{combination_name}: 推荐 {len(selected)} 只股票，已保存到 {output_file}")

            # 打印前10只股票
            print(f"\n{combination_name} 推荐股票列表 (前10只):")
            for j, (_, stock) in enumerate(selected.iterrows()):
                if j < 10:
                    print(f"{j+1}. {stock['股票代码']} {stock['股票名称']}, 技术强度: {stock['技术强度']}, 连续技术强度5天数: {stock['连续技术强度5天数']}, 看涨技术指标数量: {stock['看涨技术指标数量']}")

            if len(selected) > 10:
                print(f"... 共 {len(selected)} 只股票")
        else:
            print(f"{combination_name}: 无推荐股票")

    return recommendations

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)

    # 获取最新日期
    latest_date = df['日期'].max()
    print(f"最新日期: {latest_date.strftime('%Y-%m-%d')}")

    # 定义要探索的特征 - 选择最重要的8个特征以加快运行速度
    features = [
        '技术强度',
        '连续技术强度5天数',
        '看涨技术指标数量',
        '技术强度趋势',
        '连续技术强度5天数趋势',
        '技术指标_RSI反弹',
        '技术指标_KDJ金叉',
        '涨跌幅趋势'
    ]

    # 探索所有特征组合 - 限制为2-4个特征以加快运行速度
    results = explore_all_combinations(
        df, features, '2025-04-01', '2025-04-30',
        min_features=2, max_features=4,
        save_interim=True, interim_frequency=10
    )

    # 分析结果
    count_stats = analyze_results(results)

    # 生成汇总报告
    summary_df = generate_summary_report(results, count_stats)

    # 分析特征重要性
    feature_importance = analyze_feature_importance(results)

    # 生成股票推荐
    recommendations = generate_recommendations(df, results, latest_date)
