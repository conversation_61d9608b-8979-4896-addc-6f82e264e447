import baostock as bs
import pandas as pd
import datetime

def test_query_trade_dates(start_date, end_date):
    """测试查询交易日历"""
    print(f"测试查询交易日历: {start_date} 到 {end_date}")
    
    # 登录系统
    lg = bs.login()
    print(f"登录状态: {lg.error_code}, {lg.error_msg}")
    
    # 查询交易日历
    rs = bs.query_trade_dates(start_date=start_date, end_date=end_date)
    print(f"查询状态: {rs.error_code}, {rs.error_msg}")
    
    # 打印数据
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    
    # 登出系统
    bs.logout()
    
    if data_list:
        df = pd.DataFrame(data_list, columns=rs.fields)
        print(f"获取到 {len(df)} 条交易日历数据")
        print("列名:", df.columns.tolist())
        print("前5行数据:")
        print(df.head())
    else:
        print("未获取到任何交易日历数据")

def test_query_history_k_data(stock_code, start_date, end_date):
    """测试查询历史K线数据"""
    print(f"测试查询历史K线数据: {stock_code}, {start_date} 到 {end_date}")
    
    # 登录系统
    lg = bs.login()
    print(f"登录状态: {lg.error_code}, {lg.error_msg}")
    
    # 查询历史K线数据
    rs = bs.query_history_k_data_plus(
        stock_code,
        "date,code,open,high,low,close,volume,amount,pctChg",
        start_date=start_date,
        end_date=end_date,
        frequency="d",
        adjustflag="3"  # 3表示前复权
    )
    print(f"查询状态: {rs.error_code}, {rs.error_msg}")
    
    # 打印数据
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    
    # 登出系统
    bs.logout()
    
    if data_list:
        df = pd.DataFrame(data_list, columns=rs.fields)
        print(f"获取到 {len(df)} 条K线数据")
        print("列名:", df.columns.tolist())
        print("前5行数据:")
        print(df.head())
    else:
        print("未获取到任何K线数据")

def test_query_all_stock(date):
    """测试查询所有股票"""
    print(f"测试查询所有股票: {date}")
    
    # 登录系统
    lg = bs.login()
    print(f"登录状态: {lg.error_code}, {lg.error_msg}")
    
    # 查询所有股票
    rs = bs.query_all_stock(day=date)
    print(f"查询状态: {rs.error_code}, {rs.error_msg}")
    
    # 打印数据
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    
    # 登出系统
    bs.logout()
    
    if data_list:
        df = pd.DataFrame(data_list, columns=rs.fields)
        print(f"获取到 {len(df)} 只股票")
        print("列名:", df.columns.tolist())
        print("前5行数据:")
        print(df.head())
    else:
        print("未获取到任何股票数据")

if __name__ == "__main__":
    # 测试日期
    current_date = datetime.datetime.now().strftime('%Y-%m-%d')
    past_date = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime('%Y-%m-%d')
    future_date = (datetime.datetime.now() + datetime.timedelta(days=30)).strftime('%Y-%m-%d')
    
    # 测试交易日历
    print("\n=== 测试过去日期的交易日历 ===")
    test_query_trade_dates(past_date, current_date)
    
    print("\n=== 测试未来日期的交易日历 ===")
    test_query_trade_dates(current_date, future_date)
    
    print("\n=== 测试2025年的交易日历 ===")
    test_query_trade_dates("2025-02-01", "2025-05-22")
    
    # 测试历史K线数据
    print("\n=== 测试过去日期的K线数据 ===")
    test_query_history_k_data("sh.600000", past_date, current_date)
    
    print("\n=== 测试未来日期的K线数据 ===")
    test_query_history_k_data("sh.600000", current_date, future_date)
    
    print("\n=== 测试2025年的K线数据 ===")
    test_query_history_k_data("sh.600000", "2025-02-01", "2025-05-22")
    
    # 测试查询所有股票
    print("\n=== 测试当前日期的所有股票 ===")
    test_query_all_stock(current_date)
    
    print("\n=== 测试2025年的所有股票 ===")
    test_query_all_stock("2025-02-01")
