#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特征组合策略总收益分析
作者: Augment AI
版本: 1.0.0

该脚本计算不同特征组合策略在整个回测周期内的总收益。
"""

import pandas as pd
import numpy as np
import os
import itertools
import time
from datetime import datetime
from tqdm import tqdm
import pickle
from collections import defaultdict

# 创建结果目录
results_dir = 'total_return_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
        
        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")
        
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def calculate_total_return(data, feature_combination, start_date, end_date, initial_capital=100000):
    """计算特征组合策略在整个回测周期内的总收益"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    # 初始化结果
    results = {
        'feature_combination': feature_combination,
        'feature_count': len(feature_combination),
        'initial_capital': initial_capital,
        'current_capital': initial_capital,
        'total_trades': 0,
        'win_count': 0,
        'daily_returns': [],
        'daily_win_rates': [],
        'daily_trade_counts': [],
        'capital_history': [initial_capital],
        'dates': [trading_dates[0]]
    }
    
    # 对每个交易日进行回测
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 获取当日数据
        daily_data = date_range_data[date_range_data['日期'] == current_date]
        
        # 应用特征组合条件
        selected = daily_data.copy()
        for feature in feature_combination:
            if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', 
                         '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势', 
                         '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势', 
                         '连续技术强度10天数趋势', '连续技术强度3天数价格趋势', 
                         '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势', 
                         '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势', 
                         '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
                # 二元特征，使用==1条件
                selected = selected[selected[feature] == 1]
            elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                # 连续值特征，使用>=中位数条件
                median_value = np.median(daily_data[feature])
                selected = selected[selected[feature] >= median_value]
            elif feature == '看涨技术指标数量':
                # 看涨技术指标数量，使用>=3条件
                selected = selected[selected[feature] >= 3]
        
        # 如果有推荐的股票，模拟买入
        if len(selected) > 0:
            # 获取推荐股票的代码
            recommended_codes = selected['股票代码'].tolist()
            
            # 获取次日这些股票的数据
            next_day_data = date_range_data[
                (date_range_data['日期'] == next_date) & 
                (date_range_data['股票代码'].isin(recommended_codes))
            ]
            
            # 如果次日有这些股票的数据
            if len(next_day_data) > 0:
                # 计算平均涨跌幅
                avg_return = next_day_data['涨跌幅'].mean() / 100  # 转换为小数
                
                # 计算盈利股票数量
                win_stocks = len(next_day_data[next_day_data['涨跌幅'] > 0])
                win_rate = win_stocks / len(next_day_data) * 100
                
                # 更新资金
                results['current_capital'] *= (1 + avg_return)
                
                # 更新统计数据
                results['total_trades'] += len(next_day_data)
                results['win_count'] += win_stocks
                results['daily_returns'].append(avg_return)
                results['daily_win_rates'].append(win_rate)
                results['daily_trade_counts'].append(len(next_day_data))
                results['capital_history'].append(results['current_capital'])
                results['dates'].append(next_date)
        else:
            # 如果没有推荐股票，资金保持不变
            results['capital_history'].append(results['current_capital'])
            results['dates'].append(next_date)
            results['daily_returns'].append(0)
            results['daily_win_rates'].append(0)
            results['daily_trade_counts'].append(0)
    
    # 计算最终统计结果
    if results['total_trades'] > 0:
        results['win_rate'] = results['win_count'] / results['total_trades'] * 100
        results['total_return_pct'] = (results['current_capital'] / initial_capital - 1) * 100
        results['avg_daily_return'] = np.mean(results['daily_returns']) * 100
        results['avg_daily_trades'] = np.mean([count for count in results['daily_trade_counts'] if count > 0])
        results['trading_days'] = len([count for count in results['daily_trade_counts'] if count > 0])
        results['total_days'] = len(trading_dates) - 1
        results['trading_frequency'] = results['trading_days'] / results['total_days'] * 100
    else:
        results['win_rate'] = 0
        results['total_return_pct'] = 0
        results['avg_daily_return'] = 0
        results['avg_daily_trades'] = 0
        results['trading_days'] = 0
        results['total_days'] = len(trading_dates) - 1
        results['trading_frequency'] = 0
    
    return results

def analyze_top_combinations(data, start_date, end_date, initial_capital=100000):
    """分析表现最佳的特征组合"""
    print("分析表现最佳的特征组合...")
    
    # 定义要分析的特征组合
    top_combinations = [
        # 2特征组合
        ('技术强度趋势', '技术指标_KDJ金叉'),
        ('连续技术强度5天数趋势', '技术指标_KDJ金叉'),
        ('技术强度趋势', '技术指标_RSI反弹'),
        
        # 3特征组合
        ('技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度趋势', '连续技术强度5天数趋势', '技术指标_KDJ金叉'),
        
        # 4特征组合
        ('技术强度趋势', '连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('连续技术强度5天数', '技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        
        # 5特征组合
        ('技术强度', '连续技术强度5天数', '技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '连续技术强度5天数', '连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '技术强度趋势', '连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉')
    ]
    
    # 计算每个组合的总收益
    results = []
    for combination in tqdm(top_combinations, desc="计算总收益"):
        result = calculate_total_return(data, combination, start_date, end_date, initial_capital)
        results.append(result)
    
    # 按总收益率排序
    results.sort(key=lambda x: x['total_return_pct'], reverse=True)
    
    # 保存结果
    results_file = f"{results_dir}/top_combinations_total_return.pkl"
    with open(results_file, 'wb') as f:
        pickle.dump(results, f)
    
    # 生成CSV报告
    report_data = []
    for result in results:
        feature_str = ', '.join(result['feature_combination'])
        report_data.append({
            '特征组合': feature_str,
            '特征数量': result['feature_count'],
            '总收益率(%)': result['total_return_pct'],
            '最终资金': result['current_capital'],
            '胜率(%)': result['win_rate'],
            '平均每日收益率(%)': result['avg_daily_return'],
            '交易天数': result['trading_days'],
            '总天数': result['total_days'],
            '交易频率(%)': result['trading_frequency'],
            '总交易次数': result['total_trades'],
            '平均每日交易数': result['avg_daily_trades']
        })
    
    report_df = pd.DataFrame(report_data)
    report_file = f"{results_dir}/top_combinations_total_return.csv"
    report_df.to_csv(report_file, index=False)
    
    print(f"分析结果已保存到 {report_file}")
    
    # 打印结果
    print("\n特征组合总收益排名:")
    for i, result in enumerate(results):
        feature_str = ', '.join(result['feature_combination'])
        print(f"{i+1}. {feature_str}")
        print(f"   总收益率: {result['total_return_pct']:.2f}%, 最终资金: {result['current_capital']:.2f}")
        print(f"   胜率: {result['win_rate']:.2f}%, 交易天数: {result['trading_days']}/{result['total_days']}")
        print(f"   平均每日收益率: {result['avg_daily_return']:.2f}%, 平均每日交易数: {result['avg_daily_trades']:.2f}")
        print()
    
    return results

def generate_equity_curves(results, start_date, end_date):
    """生成资金曲线数据"""
    print("生成资金曲线数据...")
    
    # 创建资金曲线数据
    equity_data = []
    
    # 对每个结果生成资金曲线
    for result in results:
        feature_str = '_'.join([f.split('_')[-1] if '_' in f else f for f in result['feature_combination']])
        strategy_name = f"{len(result['feature_combination'])}特征_{feature_str}"
        
        for i, (date, capital) in enumerate(zip(result['dates'], result['capital_history'])):
            equity_data.append({
                '日期': date,
                '策略': strategy_name,
                '资金': capital,
                '收益率(%)': (capital / result['initial_capital'] - 1) * 100
            })
    
    # 转换为DataFrame
    equity_df = pd.DataFrame(equity_data)
    
    # 保存资金曲线数据
    equity_file = f"{results_dir}/equity_curves.csv"
    equity_df.to_csv(equity_file, index=False)
    
    print(f"资金曲线数据已保存到 {equity_file}")
    
    return equity_df

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)
    
    # 设置回测参数
    start_date = '2025-04-01'
    end_date = '2025-04-30'
    initial_capital = 100000
    
    print(f"回测周期: {start_date} 至 {end_date}")
    print(f"初始资金: {initial_capital}")
    
    # 分析表现最佳的特征组合
    results = analyze_top_combinations(df, start_date, end_date, initial_capital)
    
    # 生成资金曲线数据
    equity_df = generate_equity_curves(results, start_date, end_date)
