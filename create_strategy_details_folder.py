#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建策略详细分析目录
作者: Augment AI
版本: 1.0.0

该脚本用于创建策略详细分析目录，并生成示例Excel文件。
"""

import os
import pandas as pd
from datetime import datetime

def create_strategy_detail_excel(strategy_index, output_file):
    """
    创建策略详细分析Excel文件
    
    参数:
        strategy_index (int): 策略编号
        output_file (str): 输出文件路径
    """
    print(f"正在创建策略详细分析Excel文件: {output_file}")
    
    try:
        # 创建Excel写入器
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 创建策略统计信息表格
            stats_data = {
                '统计项': [
                    '策略编号',
                    '特征组合',
                    '特征数量',
                    '总收益率(%)',
                    '平均收益率(%)',
                    '平均胜率(%)',
                    '平均每日交易笔数',
                    '总交易笔数',
                    '交易天数',
                    '总天数',
                    '交易频率(%)'
                ],
                '数值': [
                    strategy_index,
                    '技术强度, 连续技术强度5天数',
                    2,
                    15.5,  # 总收益率
                    0.5,   # 平均收益率
                    80.0,  # 平均胜率
                    5,     # 平均每日交易笔数
                    150,   # 总交易笔数
                    30,    # 交易天数
                    30,    # 总天数
                    100    # 交易频率
                ]
            }
            
            # 转换为DataFrame
            stats_df = pd.DataFrame(stats_data)
            
            # 写入Excel
            stats_df.to_excel(writer, sheet_name='策略统计', index=False)
            
            # 创建策略条件详情表格
            conditions_data = {
                '特征': ['技术强度', '连续技术强度5天数'],
                '条件': ['>= 60', '>= 70'],
                '描述': ['技术强度大于等于60', '连续技术强度5天数大于等于70']
            }
            
            # 转换为DataFrame
            conditions_df = pd.DataFrame(conditions_data)
            
            # 写入Excel
            conditions_df.to_excel(writer, sheet_name='策略条件详情', index=False)
            
            # 创建每日表现数据表格
            daily_data = []
            
            # 生成30天的每日表现数据
            start_date = datetime(2025, 4, 1)
            initial_capital = 1000000
            
            # 生成资金曲线
            capital = initial_capital
            
            for i in range(30):
                date = start_date + pd.Timedelta(days=i)
                
                # 跳过周末
                if date.weekday() >= 5:
                    continue
                    
                # 生成随机数据
                position_value = capital * 0.8
                cash = capital * 0.2
                
                # 更新资金
                capital *= 1.005  # 每日0.5%的收益率
                
                daily_data.append({
                    '日期': date,
                    '现金': cash,
                    '持仓市值': position_value,
                    '总资产': cash + position_value,
                    '日收益率(%)': 0.5,
                    '持仓数量': 5
                })
            
            # 转换为DataFrame
            daily_df = pd.DataFrame(daily_data)
            
            # 写入Excel
            daily_df.to_excel(writer, sheet_name='每日表现', index=False)
            
            # 创建交易记录表格
            trades_data = []
            
            # 生成交易记录
            for i in range(30):
                date = start_date + pd.Timedelta(days=i)
                
                # 跳过周末
                if date.weekday() >= 5:
                    continue
                
                # 生成买入交易
                trades_data.append({
                    '日期': date,
                    '交易时间': '09:30',
                    '股票代码': 'sh.600000',
                    '股票名称': '浦发银行',
                    '操作': '买入',
                    '价格': 10.5,
                    '数量': 1000,
                    '金额': 10500,
                    '涨跌幅(%)': 1.0,
                    '收益': 0,
                    '收益率(%)': 0
                })
                
                # 生成卖出交易
                trades_data.append({
                    '日期': date,
                    '交易时间': '09:30',
                    '股票代码': 'sh.600000',
                    '股票名称': '浦发银行',
                    '操作': '卖出',
                    '价格': 10.8,
                    '数量': 1000,
                    '金额': 10800,
                    '涨跌幅(%)': 2.0,
                    '收益': 300,
                    '收益率(%)': 2.86
                })
            
            # 转换为DataFrame
            trades_df = pd.DataFrame(trades_data)
            
            # 写入Excel
            trades_df.to_excel(writer, sheet_name='交易记录', index=False)
        
        print(f"策略详细分析Excel文件创建完成: {output_file}")
        return True
    except Exception as e:
        print(f"创建策略详细分析Excel文件时出错: {str(e)}")
        return False

def main():
    """主函数"""
    # 设置输出目录
    output_dir = "E:\\机器学习\\complete_excel_results\\strategy_details"
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")
    
    # 生成示例Excel文件
    num_files = 10  # 生成10个示例文件
    
    for i in range(1, num_files + 1):
        output_file = os.path.join(output_dir, f"strategy_{i}.xlsx")
        create_strategy_detail_excel(i, output_file)
    
    print(f"共生成 {num_files} 个示例Excel文件")

if __name__ == "__main__":
    main()
