import pandas as pd
import os
import warnings
warnings.filterwarnings('ignore')

# 设置文件路径
base_dir = r'E:\机器学习\complete_excel_results'
summary_file = os.path.join(base_dir, '所有策略汇总.xlsx')
details_file = os.path.join(base_dir, '股票明细.xlsx')
output_dir = os.path.join(base_dir, 'new_strategy_details')

# 确保输出目录存在
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 读取数据
print("读取策略汇总文件...")
summary_df = pd.read_excel(summary_file)
print("读取股票明细文件...")
stock_df = pd.read_excel(details_file)

# 将日期列转换为日期类型
stock_df['日期'] = pd.to_datetime(stock_df['日期'])

# 按股票代码和日期排序
stock_df = stock_df.sort_values(['股票代码', '日期'])

# 根据交易策略：选出当日目标股票，次日开盘买入，第二个交易日开盘卖出
# 盈利是基于买入当天的涨跌幅
print("准备交易数据...")

# 我们直接使用当天的涨跌幅作为收益率
# 因为策略是：选出当日目标股票，次日开盘买入，盈利是买入当天的涨跌幅
stock_df['交易收益率'] = stock_df['涨跌幅'] / 100  # 转换为小数形式

# 回测函数
def backtest_strategy(strategy_code, strategy_id):
    """
    回测特定策略并返回结果

    参数:
    strategy_code: 策略代码（Python代码字符串）
    strategy_id: 策略ID

    返回:
    dict: 包含策略回测结果的字典
    """
    try:
        # 创建一个本地变量df，以便在eval中使用
        df = stock_df.copy()

        # 执行策略代码，获取符合条件的股票
        strategy_code = strategy_code.strip()
        if not strategy_code.startswith('df['):
            print(f"策略 {strategy_id} 的代码格式不正确: {strategy_code}")
            return None

        # 在Pandas中，条件组合应该使用&而不是and
        # 我们不需要替换，保持原样

        # 执行策略代码
        try:
            # 检查策略代码中是否有语法问题
            if '&' in strategy_code and 'and' in strategy_code:
                print(f"警告: 策略 {strategy_id} 同时包含 & 和 and，可能导致错误")

            # 尝试修复常见的语法问题
            # 确保括号匹配
            if strategy_code.count('[') != strategy_code.count(']'):
                print(f"警告: 策略 {strategy_id} 中的方括号不匹配")

            # 打印调试信息
            print(f"执行策略代码: {strategy_code}")

            # 执行策略代码
            # 在Pandas中，多个条件需要用括号括起来
            if '&' in strategy_code:
                # 修改策略代码，确保每个条件都用括号括起来
                modified_code = strategy_code
                # 提取df[...]中的内容
                code_content = strategy_code[3:-1]  # 去掉df[和最后的]
                # 按&分割条件
                condition_parts = code_content.split('&')
                # 给每个条件加上括号
                bracketed_conditions = []
                for part in condition_parts:
                    part = part.strip()
                    if not part.startswith('(') and not part.endswith(')'):
                        bracketed_conditions.append(f"({part})")
                    else:
                        bracketed_conditions.append(part)
                # 重新组合条件
                new_code_content = ' & '.join(bracketed_conditions)
                modified_code = f"df[{new_code_content}]"
                print(f"修改后的策略代码: {modified_code}")
                selected_stocks = eval(modified_code)
            else:
                selected_stocks = eval(strategy_code)

            print(f"策略 {strategy_id} 选出 {len(selected_stocks)} 只股票")

        except Exception as e:
            print(f"策略 {strategy_id} 执行失败: {e}")
            print(f"策略代码: {strategy_code}")

            # 尝试修复常见错误
            try:
                # 尝试使用更安全的方式执行策略
                print("尝试修复策略代码...")

                # 解析策略代码中的条件
                if '&' in strategy_code:
                    # 提取df[...]中的内容
                    code_content = strategy_code[3:-1]  # 去掉df[和最后的]
                    # 按&分割条件
                    condition_parts = code_content.split('&')

                    # 直接构建条件表达式
                    filtered_df = df.copy()

                    for part in condition_parts:
                        part = part.strip()
                        try:
                            # 提取列名和值
                            if '>=' in part:
                                col_expr, val = part.split('>=')
                                col = col_expr.replace("df['", "").replace("']", "").strip()
                                val = val.strip()
                                filtered_df = filtered_df[filtered_df[col] >= float(val)]
                            elif '<=' in part:
                                col_expr, val = part.split('<=')
                                col = col_expr.replace("df['", "").replace("']", "").strip()
                                val = val.strip()
                                filtered_df = filtered_df[filtered_df[col] <= float(val)]
                            elif '==' in part:
                                col_expr, val = part.split('==')
                                col = col_expr.replace("df['", "").replace("']", "").strip()
                                val = val.strip()
                                # 尝试转换为数字，如果失败则保持原样（可能是字符串比较）
                                try:
                                    val_num = int(val)
                                    filtered_df = filtered_df[filtered_df[col] == val_num]
                                except ValueError:
                                    filtered_df = filtered_df[filtered_df[col] == val]
                            elif '>' in part:
                                col_expr, val = part.split('>')
                                col = col_expr.replace("df['", "").replace("']", "").strip()
                                val = val.strip()
                                filtered_df = filtered_df[filtered_df[col] > float(val)]
                            elif '<' in part:
                                col_expr, val = part.split('<')
                                col = col_expr.replace("df['", "").replace("']", "").strip()
                                val = val.strip()
                                filtered_df = filtered_df[filtered_df[col] < float(val)]
                            print(f"应用条件: {part} - 剩余记录数: {len(filtered_df)}")
                        except Exception as condition_error:
                            print(f"处理条件 '{part}' 时出错: {condition_error}")

                    selected_stocks = filtered_df
                    print(f"修复成功! 策略 {strategy_id} 选出 {len(selected_stocks)} 只股票")
                    return {
                        '总收益率(%)': round(selected_stocks['交易收益率'].sum() * 100, 2),
                        '平均收益率(%)': round(selected_stocks['交易收益率'].mean() * 100, 2),
                        '平均胜率(%)': round((selected_stocks['交易收益率'] > 0).mean() * 100, 2),
                        '平均每日交易笔数': round(len(selected_stocks) / len(selected_stocks['日期'].unique()) if len(selected_stocks['日期'].unique()) > 0 else 0, 2),
                        '总交易笔数': len(selected_stocks),
                        '交易天数': len(selected_stocks['日期'].unique()),
                        '总天数': len(df['日期'].unique()),
                        '交易频率(%)': round(len(selected_stocks['日期'].unique()) / len(df['日期'].unique()) * 100 if len(df['日期'].unique()) > 0 else 0, 2),
                        '选出的股票': selected_stocks
                    }
            except Exception as repair_error:
                print(f"修复尝试失败: {repair_error}")

            return None

        if len(selected_stocks) == 0:
            print(f"策略 {strategy_id} 没有选出任何股票")
            return {
                '总收益率(%)': 0,
                '平均收益率(%)': 0,
                '平均胜率(%)': 0,
                '平均每日交易笔数': 0,
                '总交易笔数': 0,
                '交易天数': 0,
                '总天数': len(df['日期'].unique()),
                '交易频率(%)': 0,
                '选出的股票': pd.DataFrame()
            }

        # 计算策略统计数据
        total_return = selected_stocks['交易收益率'].sum()
        avg_return = selected_stocks['交易收益率'].mean()
        win_rate = (selected_stocks['交易收益率'] > 0).mean() * 100

        # 计算交易统计
        trade_days = len(selected_stocks['日期'].unique())
        total_days = len(df['日期'].unique())
        trade_frequency = (trade_days / total_days) * 100 if total_days > 0 else 0
        total_trades = len(selected_stocks)
        avg_daily_trades = total_trades / trade_days if trade_days > 0 else 0

        # 返回结果
        return {
            '总收益率(%)': round(total_return * 100, 2),
            '平均收益率(%)': round(avg_return * 100, 2),
            '平均胜率(%)': round(win_rate, 2),
            '平均每日交易笔数': round(avg_daily_trades, 2),
            '总交易笔数': total_trades,
            '交易天数': trade_days,
            '总天数': total_days,
            '交易频率(%)': round(trade_frequency, 2),
            '选出的股票': selected_stocks
        }
    except Exception as e:
        print(f"策略 {strategy_id} 回测过程中发生错误: {e}")
        return None

# 回测所有策略
results = []
updated_summary = summary_df.copy()

print(f"开始回测 {len(summary_df)} 个策略...")
for index, row in summary_df.iterrows():
    strategy_id = row['策略编号']
    strategy_code = row['策略代码']

    print(f"回测策略 {strategy_id}...")

    # 回测策略
    result = backtest_strategy(strategy_code, strategy_id)

    if result:
        # 更新汇总表中的统计数据
        updated_summary.loc[index, '总收益率(%)'] = result['总收益率(%)']
        updated_summary.loc[index, '平均收益率(%)'] = result['平均收益率(%)']
        updated_summary.loc[index, '平均胜率(%)'] = result['平均胜率(%)']
        updated_summary.loc[index, '平均每日交易笔数'] = result['平均每日交易笔数']
        updated_summary.loc[index, '总交易笔数'] = result['总交易笔数']
        updated_summary.loc[index, '交易天数'] = result['交易天数']
        updated_summary.loc[index, '总天数'] = result['总天数']
        updated_summary.loc[index, '交易频率(%)'] = result['交易频率(%)']

        # 保存详细分析文件
        detail_file = os.path.join(output_dir, f"strategy_{strategy_id}.xlsx")

        # 如果有选出的股票，保存详细分析
        if '选出的股票' in result and len(result['选出的股票']) > 0:
            # 添加策略信息
            selected_stocks = result['选出的股票'].copy()
            selected_stocks['策略编号'] = strategy_id
            selected_stocks['策略组合'] = row['策略组合']
            selected_stocks['策略条件描述'] = row['策略条件描述']

            # 计算每只股票的收益情况
            selected_stocks['是否盈利'] = selected_stocks['交易收益率'] > 0

            # 保存到Excel
            selected_stocks.to_excel(detail_file, index=False)
            print(f"已保存策略 {strategy_id} 的详细分析到 {detail_file}")
        else:
            print(f"策略 {strategy_id} 没有选出任何股票，不生成详细分析文件")

    results.append(result)

# 保存更新后的汇总表
updated_summary.to_excel(os.path.join(base_dir, '所有策略汇总_已回测.xlsx'), index=False)
print(f"已保存更新后的策略汇总到 {os.path.join(base_dir, '所有策略汇总_已回测.xlsx')}")

print("回测完成！")
