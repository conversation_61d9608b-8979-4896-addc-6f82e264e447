import pandas as pd
import numpy as np
import os
import sys
import joblib
from datetime import datetime
import time
import matplotlib.pyplot as plt
# 使用scikit-learn替代tensorflow

# 导入自定义模块
from enhanced_data_sources import EnhancedDataSources
from sentiment_analyzer import SentimentAnalyzer
from deep_learning_models import DeepLearningModels
from ensemble_learning import EnsembleLearning
from trading_strategy import TradingStrategy

class AdvancedTradingSystem:
    """
    高级交易系统，整合宏观经济数据、行业基本面数据、深度学习模型、集成学习和情感分析
    """

    def __init__(self):
        """初始化高级交易系统"""
        # 创建结果目录
        if not os.path.exists('advanced_results'):
            os.makedirs('advanced_results')

        # 初始化组件
        self.data_sources = EnhancedDataSources()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.dl_models = DeepLearningModels()
        self.ensemble = EnsembleLearning()
        self.strategy = TradingStrategy()

        # 配置参数
        self.max_positions = 10  # 最大持仓数量
        self.investment_per_stock = 100000  # 每只股票投资金额

    def load_stock_data(self):
        """加载股票明细数据"""
        print("加载股票明细数据...")
        try:
            data = pd.read_excel('股票明细.xlsx')
            data['日期'] = pd.to_datetime(data['日期'])
            print(f"成功加载 {len(data)} 条股票数据")
            return data
        except Exception as e:
            print(f"加载股票数据失败: {e}")
            return pd.DataFrame()

    def enrich_data(self, stock_data):
        """
        使用额外数据源丰富股票数据
        参数:
            stock_data: 股票明细数据
        返回丰富后的数据
        """
        print("使用额外数据源丰富股票数据...")

        # 获取最新日期的股票数据
        latest_date = stock_data['日期'].max()
        latest_stock_data = stock_data[stock_data['日期'] == latest_date]

        print(f"最新日期: {latest_date.date()}, 股票数量: {len(latest_stock_data)}")

        # 获取股票代码列表
        stock_codes = latest_stock_data['股票代码'].unique().tolist()

        # 获取宏观经济数据
        macro_data = self.data_sources.get_macro_economic_data()

        # 获取行业基本面数据
        industry_data = self.data_sources.get_industry_fundamentals()

        # 获取股票基本面数据
        stock_fundamentals = self.data_sources.get_stock_fundamentals(stock_codes)

        # 获取市场情感数据
        market_sentiment = self.sentiment_analyzer.get_market_sentiment()

        # 获取股票情感数据
        stock_sentiment = self.sentiment_analyzer.get_stock_sentiment(stock_codes)

        # 合并数据
        enriched_data = latest_stock_data.copy()

        # 添加宏观经济数据
        if not macro_data.empty:
            for col in macro_data.columns:
                if col != '日期':
                    enriched_data[col] = macro_data.iloc[0][col]

        # 添加行业基本面数据
        if not industry_data.empty:
            # 创建行业映射
            industry_mapping = {}
            for _, row in industry_data.iterrows():
                industry = row['行业']
                for col in industry_data.columns:
                    if col not in ['日期', '行业']:
                        if industry not in industry_mapping:
                            industry_mapping[industry] = {}
                        industry_mapping[industry][col] = row[col]

            # 添加行业数据
            for i, row in enriched_data.iterrows():
                industry = row.get('行业', None)
                if industry and industry in industry_mapping:
                    for col, value in industry_mapping[industry].items():
                        enriched_data.at[i, col] = value

        # 添加股票基本面数据
        if not stock_fundamentals.empty:
            enriched_data = pd.merge(
                enriched_data,
                stock_fundamentals[['股票代码'] + [col for col in stock_fundamentals.columns if col not in ['日期', '股票代码', '股票名称', '所属行业']]],
                on='股票代码',
                how='left'
            )

        # 添加市场情感数据
        if not market_sentiment.empty:
            for col in market_sentiment.columns:
                if col != '日期':
                    enriched_data[f'市场_{col}'] = market_sentiment.iloc[0][col]

        # 添加股票情感数据
        if not stock_sentiment.empty:
            enriched_data = pd.merge(
                enriched_data,
                stock_sentiment[['股票代码'] + [col for col in stock_sentiment.columns if col not in ['日期', '股票代码']]],
                on='股票代码',
                how='left'
            )

        print(f"数据丰富完成，特征数量从 {len(stock_data.columns)} 增加到 {len(enriched_data.columns)}")

        # 保存丰富后的数据
        enriched_data.to_excel('advanced_results/enriched_stock_data.xlsx', index=False)
        print("已将丰富后的数据保存至 advanced_results/enriched_stock_data.xlsx")

        return enriched_data

    def train_models(self, stock_data):
        """
        训练所有模型
        参数:
            stock_data: 股票历史数据
        """
        print("训练所有模型...")

        # 训练深度学习模型
        print("\n训练深度学习模型...")
        self.dl_models.train_all_models(stock_data)

        # 训练集成模型
        print("\n训练集成模型...")

        # 准备特征和目标变量
        features = [col for col in stock_data.columns if col not in ['日期', '股票代码', '股票名称', '次日涨跌方向', '次日收益率']]

        # 计算次日涨跌方向
        stock_data['次日涨跌方向'] = 0

        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 计算次日涨跌方向
            group['次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)

            # 更新原始数据
            stock_data.loc[group.index, '次日涨跌方向'] = group['次日涨跌方向']

        # 删除没有次日数据的记录
        stock_data = stock_data.dropna(subset=['次日涨跌方向'])

        # 训练分类集成模型
        self.ensemble.run(stock_data, features, '次日涨跌方向', 'classification')

        # 计算次日收益率
        stock_data['次日收益率'] = 0

        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 计算次日收益率
            group['次日收益率'] = group['当前价格'].shift(-1) / group['当前价格'] - 1

            # 更新原始数据
            stock_data.loc[group.index, '次日收益率'] = group['次日收益率']

        # 删除没有次日数据的记录
        stock_data = stock_data.dropna(subset=['次日收益率'])

        # 训练回归集成模型
        self.ensemble.run(stock_data, features, '次日收益率', 'regression')

        print("所有模型训练完成!")

    def predict_with_advanced_models(self, enriched_data):
        """
        使用高级模型进行预测
        参数:
            enriched_data: 丰富后的股票数据
        返回预测结果
        """
        print("使用高级模型进行预测...")

        # 使用深度学习集成模型进行预测
        dl_predictions = self.dl_models.predict_with_ensemble(enriched_data)

        if dl_predictions.empty:
            print("深度学习模型预测失败")
            return pd.DataFrame()

        # 加载集成模型
        try:
            classification_ensemble = joblib.load('ensemble_models/classification_ensemble_model.pkl')
            regression_ensemble = joblib.load('ensemble_models/regression_ensemble_model.pkl')
        except Exception as e:
            print(f"加载集成模型失败: {e}")
            return dl_predictions

        # 准备特征
        features = [col for col in enriched_data.columns if col not in ['日期', '股票代码', '股票名称', '次日涨跌方向', '次日收益率']]

        # 使用集成模型进行预测
        try:
            X = enriched_data[features].values

            # 分类预测
            classification_pred = classification_ensemble.predict_proba(X)[:, 1]

            # 回归预测
            regression_pred = regression_ensemble.predict(X)

            # 添加预测结果
            dl_predictions['集成模型涨跌概率'] = classification_pred
            dl_predictions['集成模型收益率预测'] = regression_pred

            # 计算综合预测
            dl_predictions['综合涨跌概率'] = (dl_predictions['集成涨跌概率'] + dl_predictions['集成模型涨跌概率']) / 2
            dl_predictions['综合收益率预测'] = (dl_predictions['集成收益率预测'] + dl_predictions['集成模型收益率预测']) / 2

            # 更新预测信号
            dl_predictions['预测涨跌'] = '上涨' if dl_predictions['综合涨跌概率'] > 0.5 else '下跌'
            dl_predictions['预测信号强度'] = abs(dl_predictions['综合涨跌概率'] - 0.5) * 2
        except Exception as e:
            print(f"集成模型预测失败: {e}")

        # 按预测信号强度排序
        dl_predictions = dl_predictions.sort_values('预测信号强度', ascending=False)

        # 保存预测结果
        dl_predictions.to_excel('advanced_results/advanced_predictions.xlsx', index=False)
        print("已将预测结果保存至 advanced_results/advanced_predictions.xlsx")

        return dl_predictions

    def select_stocks_to_trade(self, predictions, holdings):
        """
        选择要交易的股票
        参数:
            predictions: 预测结果
            holdings: 当前持仓
        返回要买入和卖出的股票
        """
        print("选择要交易的股票...")

        # 选择要买入的股票
        stocks_to_buy = predictions[
            (predictions['预测涨跌'] == '上涨') &
            (predictions['预测信号强度'] >= 0.7) &
            (predictions['综合收益率预测'] >= 0.01)  # 预期收益率至少1%
        ].head(self.max_positions - len(holdings))

        # 如果没有满足条件的股票，降低标准
        if len(stocks_to_buy) == 0:
            stocks_to_buy = predictions[
                (predictions['预测涨跌'] == '上涨') &
                (predictions['预测信号强度'] >= 0.6)
            ].head(self.max_positions - len(holdings))

        # 选择要卖出的股票
        stocks_to_sell = pd.DataFrame()

        if not holdings.empty:
            # 获取持仓股票的预测结果
            holdings_predictions = predictions[predictions['股票代码'].isin(holdings['股票代码'])]

            # 选择预测下跌的持仓股票
            stocks_to_sell = holdings[
                holdings['股票代码'].isin(
                    holdings_predictions[
                        (holdings_predictions['预测涨跌'] == '下跌') &
                        (holdings_predictions['预测信号强度'] >= 0.6)
                    ]['股票代码']
                )
            ]

            # 添加持有超过5天的股票
            holdings['买入日期'] = pd.to_datetime(holdings['买入日期'])
            today = datetime.now().date()
            holdings['持有天数'] = holdings['买入日期'].apply(lambda x: (today - x.date()).days)

            stocks_to_sell = pd.concat([
                stocks_to_sell,
                holdings[holdings['持有天数'] >= 5]
            ]).drop_duplicates()

            # 添加盈利超过10%的股票
            stocks_to_sell = pd.concat([
                stocks_to_sell,
                holdings[holdings['盈亏比例'] >= 0.1]
            ]).drop_duplicates()

            # 添加亏损超过5%的股票
            stocks_to_sell = pd.concat([
                stocks_to_sell,
                holdings[holdings['盈亏比例'] <= -0.05]
            ]).drop_duplicates()

        print(f"选择 {len(stocks_to_buy)} 只股票买入，{len(stocks_to_sell)} 只股票卖出")

        return stocks_to_buy, stocks_to_sell

    def execute_trades(self, stocks_to_buy, stocks_to_sell):
        """
        执行交易
        参数:
            stocks_to_buy: 要买入的股票
            stocks_to_sell: 要卖出的股票
        """
        print("执行交易...")

        # 执行卖出订单
        self.strategy.execute_sell_orders(stocks_to_sell)

        # 执行买入订单
        self.strategy.execute_buy_orders(stocks_to_buy, self.investment_per_stock)

        print("交易执行完成!")

    def update_models(self, stock_data):
        """
        更新模型（在线学习）
        参数:
            stock_data: 最新股票数据
        """
        print("更新模型...")

        # 准备特征和目标变量
        features = [col for col in stock_data.columns if col not in ['日期', '股票代码', '股票名称', '次日涨跌方向', '次日收益率']]

        # 计算次日涨跌方向和收益率
        stock_data['次日涨跌方向'] = 0
        stock_data['次日收益率'] = 0

        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 计算次日涨跌方向和收益率
            group['次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)
            group['次日收益率'] = group['当前价格'].shift(-1) / group['当前价格'] - 1

            # 更新原始数据
            stock_data.loc[group.index, '次日涨跌方向'] = group['次日涨跌方向']
            stock_data.loc[group.index, '次日收益率'] = group['次日收益率']

        # 删除没有次日数据的记录
        stock_data = stock_data.dropna(subset=['次日涨跌方向', '次日收益率'])

        # 如果有足够的数据，更新模型
        if len(stock_data) >= 100:
            # 更新分类集成模型
            try:
                classification_ensemble = joblib.load('ensemble_models/classification_ensemble_model.pkl')
                X = stock_data[features].values
                y = stock_data['次日涨跌方向'].values
                self.ensemble.online_learning_update(classification_ensemble, X, y, 'classification')
            except Exception as e:
                print(f"更新分类集成模型失败: {e}")

            # 更新回归集成模型
            try:
                regression_ensemble = joblib.load('ensemble_models/regression_ensemble_model.pkl')
                X = stock_data[features].values
                y = stock_data['次日收益率'].values
                self.ensemble.online_learning_update(regression_ensemble, X, y, 'regression')
            except Exception as e:
                print(f"更新回归集成模型失败: {e}")
        else:
            print(f"数据不足，无法更新模型，当前数据量: {len(stock_data)}")

        print("模型更新完成!")

    def run(self, train_models=False):
        """
        运行高级交易系统
        参数:
            train_models: 是否训练模型
        """
        print("开始运行高级交易系统...")

        # 加载股票数据
        stock_data = self.load_stock_data()
        if stock_data.empty:
            print("无法获取股票数据，程序终止")
            return

        # 如果需要，训练模型
        if train_models:
            self.train_models(stock_data)

        # 丰富数据
        enriched_data = self.enrich_data(stock_data)

        # 使用高级模型进行预测
        predictions = self.predict_with_advanced_models(enriched_data)

        if predictions.empty:
            print("预测失败，程序终止")
            return

        # 更新持仓信息
        holdings = self.strategy.load_holdings()

        # 选择要交易的股票
        stocks_to_buy, stocks_to_sell = self.select_stocks_to_trade(predictions, holdings)

        # 执行交易
        self.execute_trades(stocks_to_buy, stocks_to_sell)

        # 更新模型
        self.update_models(stock_data)

        print("高级交易系统运行完成!")

        # 输出当前持仓概况
        holdings = self.strategy.load_holdings()
        if len(holdings) > 0:
            total_value = holdings['当前市值'].sum()
            total_profit = holdings['盈亏金额'].sum()
            total_cost = total_value - total_profit
            total_profit_ratio = total_profit / total_cost if total_cost > 0 else 0

            print("\n当前持仓概况:")
            print(f"持仓股票数: {len(holdings)}")
            print(f"持仓总市值: {total_value:.2f}")
            print(f"总盈亏金额: {total_profit:.2f}")
            print(f"总盈亏比例: {total_profit_ratio*100:.2f}%")

            # 输出每只股票的持仓情况
            print("\n持仓明细:")
            for _, row in holdings.iterrows():
                print(f"{row['股票名称']}({row['股票代码']}): 持仓={row['持仓数量']}, 市值={row['当前市值']:.2f}, 盈亏={row['盈亏金额']:.2f}({row['盈亏比例']*100:.2f}%), 技术强度={row['技术强度']}")
        else:
            print("\n当前无持仓")

if __name__ == "__main__":
    # 解析命令行参数
    train_models = False
    if len(sys.argv) > 1 and sys.argv[1] == '--train':
        train_models = True

    # 运行高级交易系统
    system = AdvancedTradingSystem()
    system.run(train_models=train_models)
