import os
import pandas as pd

# 读取主Excel文件
excel_path = r'E:\机器学习\complete_excel_results\所有策略汇总_20250516_122946.xlsx'
print(f"正在读取Excel文件: {excel_path}")

# 检查文件是否存在
if not os.path.exists(excel_path):
    print(f"文件不存在: {excel_path}")
    exit(1)

# 读取所有工作表
try:
    all_sheets = pd.read_excel(excel_path, sheet_name=None)
    print(f"成功读取Excel文件，包含 {len(all_sheets)} 个工作表")

    # 打印每个工作表的信息
    for sheet_name, df in all_sheets.items():
        print(f"\n工作表: {sheet_name}")
        print(f"列名: {df.columns.tolist()}")
        print(f"行数: {len(df)}")

        # 打印前5行数据的部分列
        if len(df) > 0:
            if sheet_name == '策略汇总':
                print("\n前5行数据 (部分列):")
                columns_to_show = ['策略编号', '策略组合', '特征数量', '总收益率(%)', '平均胜率(%)']
                print(df[columns_to_show].head().to_string())
            elif sheet_name == '策略条件':
                print("\n前5行数据 (部分列):")
                columns_to_show = ['策略编号', '策略组合', '策略条件描述']
                print(df[columns_to_show].head().to_string())

    # 检查是否有strategy_details目录
    strategy_details_dir = os.path.join(os.path.dirname(excel_path), 'strategy_details')
    print(f"\n检查策略详细分析目录: {strategy_details_dir}")

    if os.path.exists(strategy_details_dir):
        print(f"目录存在: {strategy_details_dir}")
        if os.path.isdir(strategy_details_dir):
            print("这是一个目录")
            files = os.listdir(strategy_details_dir)
            print(f"共有 {len(files)} 个策略详细分析文件")
            if len(files) > 0:
                print(f"前5个文件: {files[:5]}")
        else:
            print("这不是一个目录")
    else:
        print("目录不存在")

        # 尝试在其他位置查找
        parent_dir = os.path.dirname(os.path.dirname(excel_path))
        print(f"\n尝试在父目录中查找: {parent_dir}")
        strategy_files = []

        for item in os.listdir(parent_dir):
            item_path = os.path.join(parent_dir, item)
            if os.path.isdir(item_path) and "strategy" in item.lower():
                print(f"找到可能的策略目录: {item}")

                # 检查是否包含策略详细分析文件
                try:
                    sub_files = os.listdir(item_path)
                    strategy_files = [f for f in sub_files if "strategy_" in f.lower()]
                    if strategy_files:
                        print(f"  包含策略文件，共 {len(strategy_files)} 个文件")
                        print(f"  前5个文件: {strategy_files[:5]}")
                        strategy_details_dir = item_path

                        # 读取第一个策略详细分析文件
                        if strategy_files:
                            first_file = os.path.join(strategy_details_dir, strategy_files[0])
                            print(f"\n读取第一个策略详细分析文件: {strategy_files[0]}")

                            try:
                                strategy_sheets = pd.read_excel(first_file, sheet_name=None)
                                print(f"包含 {len(strategy_sheets)} 个工作表")

                                # 打印每个工作表的信息
                                for sheet_name, df in strategy_sheets.items():
                                    print(f"\n工作表: {sheet_name}")
                                    print(f"列名: {df.columns.tolist()}")
                                    print(f"行数: {len(df)}")
                            except Exception as e:
                                print(f"读取策略详细分析文件时出错: {str(e)}")
                except Exception as e:
                    print(f"  无法读取目录: {str(e)}")
except Exception as e:
    print(f"读取Excel文件时出错: {str(e)}")
