@echo off
echo 正在检查并安装必要的依赖库...

REM 检查Python是否已安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python未安装，请先安装Python 3.8或更高版本。
    echo 可以从 https://www.python.org/downloads/ 下载安装。
    pause
    exit /b
)

REM 检查并安装akshare
python -c "import akshare" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装akshare库...
    pip install akshare
) else (
    echo akshare库已安装。
)

REM 检查并安装pandas
python -c "import pandas" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装pandas库...
    pip install pandas
) else (
    echo pandas库已安装。
)

REM 检查并安装其他依赖
python -c "import numpy" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装numpy库...
    pip install numpy
) else (
    echo numpy库已安装。
)

python -c "import openpyxl" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装openpyxl库...
    pip install openpyxl
) else (
    echo openpyxl库已安装。
)

echo 所有依赖库已安装完成，正在启动程序...
python download_stock_data_gui.py

if %errorlevel% neq 0 (
    echo 程序运行出错，请查看上面的错误信息。
    pause
) else (
    echo 程序已正常退出。
)
