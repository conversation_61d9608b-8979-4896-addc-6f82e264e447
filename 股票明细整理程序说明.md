# 股票明细整理程序说明

## 程序概述

股票明细整理程序是一个用于处理和整合股票数据的工具，它可以从多个来源读取股票数据，合并这些数据，计算各种技术指标和特征，并将结果保存到一个统一的Excel文件中。

## 主要功能

1. **数据读取与合并**：
   - 读取现有的股票明细表
   - 读取历史股票数据
   - 读取多个选股结果文件夹中的数据
   - 合并所有数据，保留唯一记录

2. **特征计算**：
   - 计算技术指标特征（6位编码）
   - 计算买入日和卖出日开盘涨跌幅
   - 计算日内股票标记（3位编码）
   - 计算连续技术强度（3天、5天、10天）
   - 计算趋势组合（6位编码）
   - 计算成交量比例


4. **数据验证**：
   - 验证各个特征列的计算结果
   - 检查非空记录比例
   - 检查值分布

5. **数据保存**：
   - 将处理后的数据保存到Excel文件



2. 运行程序：
   ```
   python merge_new_data.py
   ```

3. 程序会自动执行以下步骤：
   - 读取现有数据
   - 读取历史数据
   - 读取选股结果
   - 合并数据
   - 计算特征
   - 保存结果
   - 验证结果


## 性能优化

程序采用了多种优化策略，以提高处理速度：

1. **只处理需要更新的记录**：
   - 标记新数据中的记录为"需要更新"
   - 只对这些记录进行特征计算

2. **批量处理**：
   - 使用pandas的向量化操作代替循环
   - 例如，使用`transform`、`groupby`、`map`等函数

3. **数据过滤**：
   - 只处理需要的股票数据
   - 减少内存使用和计算量

4. **高效的数据结构**：
   - 使用字典映射加速查询
   - 减少重复计算

## 注意事项

1. 程序会尝试关闭正在运行的Excel进程，以确保能够成功保存数据。

2. 处理大量数据时可能需要较长时间，特别是在计算趋势和价格变化时。

3. 程序会跳过无法读取的Excel文件（如临时文件）。

4. 如果历史数据不完整，某些特征可能无法计算，程序会使用默认值填充。

## 输出结果

程序处理完成后，会输出各个特征列的验证结果，包括：

- 非空记录数量和比例
- 编码长度
- 值分布

这些信息可以帮助验证数据的质量和完整性。

## 数据字典

详细的字段说明请参考《股票数据字典.md》文档，其中包含了每个字段的含义、计算方法和使用场景。
