import pandas as pd
import os
import argparse

# 设置文件路径
base_dir = r'E:\机器学习\complete_excel_results'
summary_file = os.path.join(base_dir, '所有策略汇总.xlsx')
updated_summary_file = os.path.join(base_dir, '所有策略汇总_已回测.xlsx')
details_dir = os.path.join(base_dir, 'new_strategy_details')

def update_summary_from_details():
    """从详细分析文件更新汇总表"""
    # 读取原始汇总表
    print("读取原始汇总表...")
    try:
        summary_df = pd.read_excel(summary_file)
        print(f"成功读取原始汇总表，共 {len(summary_df)} 条记录")
    except Exception as e:
        print(f"读取原始汇总表时出错: {e}")
        return False
    
    # 检查是否已经存在更新后的汇总表
    if os.path.exists(updated_summary_file):
        try:
            updated_summary_df = pd.read_excel(updated_summary_file)
            print(f"成功读取更新后的汇总表，共 {len(updated_summary_df)} 条记录")
            
            # 检查两个表的记录数是否一致
            if len(summary_df) != len(updated_summary_df):
                print(f"警告: 原始汇总表有 {len(summary_df)} 条记录，但更新后的汇总表有 {len(updated_summary_df)} 条记录")
                print("将使用原始汇总表作为基础")
                updated_summary_df = summary_df.copy()
        except Exception as e:
            print(f"读取更新后的汇总表时出错: {e}")
            print("将使用原始汇总表作为基础")
            updated_summary_df = summary_df.copy()
    else:
        print("更新后的汇总表不存在，将使用原始汇总表作为基础")
        updated_summary_df = summary_df.copy()
    
    # 获取所有详细分析文件
    detail_files = [f for f in os.listdir(details_dir) if f.startswith('strategy_') and f.endswith('.xlsx')]
    print(f"找到 {len(detail_files)} 个详细分析文件")
    
    # 添加新的财务指标列（如果不存在）
    for col in ['初始资金', '最终资金', '盈利', '累计收益率(%)', '年化收益率(%)']:
        if col not in updated_summary_df.columns:
            updated_summary_df[col] = None
    
    # 从每个详细分析文件中提取数据并更新汇总表
    updated_count = 0
    for file in detail_files:
        try:
            # 从文件名中提取策略ID
            strategy_id = int(file.split('_')[1].split('.')[0])
            
            # 读取详细分析文件中的策略汇总参数
            detail_file = os.path.join(details_dir, file)
            summary_sheet = pd.read_excel(detail_file, sheet_name='策略汇总参数')
            
            if len(summary_sheet) > 0:
                # 查找策略在汇总表中的索引
                strategy_idx = updated_summary_df[updated_summary_df['策略编号'] == strategy_id].index
                
                if len(strategy_idx) > 0:
                    # 更新汇总表中的数据
                    for col in summary_sheet.columns:
                        if col in updated_summary_df.columns and col != '策略编号':
                            updated_summary_df.loc[strategy_idx[0], col] = summary_sheet.iloc[0][col]
                    
                    updated_count += 1
                    print(f"已更新策略 {strategy_id} 的数据")
                else:
                    print(f"警告: 在汇总表中找不到策略 {strategy_id}")
        except Exception as e:
            print(f"处理文件 {file} 时出错: {e}")
    
    # 保存更新后的汇总表
    try:
        updated_summary_df.to_excel(updated_summary_file, index=False)
        print(f"已保存更新后的汇总表到 {updated_summary_file}")
        print(f"共更新了 {updated_count} 个策略的数据")
        return True
    except Exception as e:
        print(f"保存更新后的汇总表时出错: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='从详细分析文件更新汇总表')
    args = parser.parse_args()
    
    update_summary_from_details()

if __name__ == "__main__":
    main()
