@echo off
echo ===================================================
echo        Stock High Win Rate Strategy Analysis Tool
echo ===================================================
echo.

echo Step 1: Installing dependencies...
pip install pandas numpy joblib scikit-learn cx_Freeze

echo.
echo Step 2: Creating necessary directories...
if not exist "models" mkdir models
if not exist "backtest_results" mkdir backtest_results

echo.
echo Step 3: Building executable...
python setup.py build

echo.
echo Step 4: Creating startup scripts...
(
echo @echo off
echo echo Starting Stock High Win Rate Strategy Analysis Tool (GUI version)...
echo start build\exe.win-amd64-3.8\stock_analyzer_GUI.exe --gui
) > run_gui.bat

(
echo @echo off
echo echo Starting Stock High Win Rate Strategy Analysis Tool (Console version)...
echo build\exe.win-amd64-3.8\stock_analyzer.exe --data stock_data.xlsx --start_date 2025-04-25 --end_date 2025-05-08 --output analysis_result.txt
echo echo Analysis complete, results saved to analysis_result.txt
echo pause
) > run_console.bat

echo.
echo ===================================================
echo                  Build Complete!
echo ===================================================
echo.
echo Please run the following files to start the program:
echo 1. run_gui.bat - Start GUI version
echo 2. run_console.bat - Start Console version
echo.
echo Press any key to exit...
pause > nul
