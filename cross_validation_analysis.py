import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime
import matplotlib.pyplot as plt

def analyze_cross_validation():
    """
    分析同时被机器学习模型推荐且技术强度为100的股票胜率
    """
    print("开始分析交叉验证策略的胜率...")
    
    # 创建结果目录
    if not os.path.exists('cross_validation_results'):
        os.makedirs('cross_validation_results')
    
    # 获取所有推荐股票文件
    recommendation_files = glob.glob('recommendation_results/*_推荐股票.xlsx')
    
    # 创建结果DataFrame
    results = pd.DataFrame(columns=[
        '日期', 
        '推荐股票数', 
        '技术强度100股票数', 
        '交叉验证股票数',
        '推荐股票胜率', 
        '技术强度100胜率', 
        '交叉验证胜率',
        '推荐股票收益率', 
        '技术强度100收益率', 
        '交叉验证收益率'
    ])
    
    # 分析每个日期的推荐股票
    for file_path in recommendation_files:
        # 从文件名中提取日期
        file_name = os.path.basename(file_path)
        date_str = file_name.split('_')[0]
        date = datetime.strptime(date_str, '%Y-%m-%d')
        
        print(f"\n分析日期: {date_str}")
        
        # 读取推荐股票
        recommendations = pd.read_excel(file_path)
        
        # 计算推荐股票的胜率和收益率
        total_count = len(recommendations)
        up_count = sum(recommendations['实际是否盈利'])
        win_rate = up_count / total_count if total_count > 0 else 0
        avg_return = recommendations['两日收益率'].mean()
        
        # 筛选技术强度为100的股票
        strength_100 = recommendations[recommendations['技术强度'] == 100]
        strength_100_count = len(strength_100)
        strength_100_up_count = sum(strength_100['实际是否盈利'])
        strength_100_win_rate = strength_100_up_count / strength_100_count if strength_100_count > 0 else 0
        strength_100_avg_return = strength_100['两日收益率'].mean() if strength_100_count > 0 else 0
        
        # 筛选预测概率前20%且技术强度为100的股票（交叉验证）
        top_percent = 0.2
        top_n = max(int(len(recommendations) * top_percent), 1)
        top_recommendations = recommendations.head(top_n)
        cross_validation = top_recommendations[top_recommendations['技术强度'] == 100]
        
        cross_validation_count = len(cross_validation)
        cross_validation_up_count = sum(cross_validation['实际是否盈利'])
        cross_validation_win_rate = cross_validation_up_count / cross_validation_count if cross_validation_count > 0 else 0
        cross_validation_avg_return = cross_validation['两日收益率'].mean() if cross_validation_count > 0 else 0
        
        print(f"推荐股票数: {total_count}")
        print(f"推荐股票胜率: {win_rate:.4f} ({win_rate*100:.2f}%)")
        print(f"推荐股票平均收益率: {avg_return:.4f}%")
        
        print(f"技术强度为100的股票数: {strength_100_count}")
        if strength_100_count > 0:
            print(f"技术强度为100的股票胜率: {strength_100_win_rate:.4f} ({strength_100_win_rate*100:.2f}%)")
            print(f"技术强度为100的股票平均收益率: {strength_100_avg_return:.4f}%")
        
        print(f"交叉验证股票数: {cross_validation_count}")
        if cross_validation_count > 0:
            print(f"交叉验证股票胜率: {cross_validation_win_rate:.4f} ({cross_validation_win_rate*100:.2f}%)")
            print(f"交叉验证股票平均收益率: {cross_validation_avg_return:.4f}%")
            print(f"交叉验证股票: {', '.join(cross_validation['股票名称'].tolist())}")
        
        # 添加到结果
        new_row = pd.DataFrame({
            '日期': [date],
            '推荐股票数': [total_count],
            '技术强度100股票数': [strength_100_count],
            '交叉验证股票数': [cross_validation_count],
            '推荐股票胜率': [win_rate],
            '技术强度100胜率': [strength_100_win_rate if strength_100_count > 0 else np.nan],
            '交叉验证胜率': [cross_validation_win_rate if cross_validation_count > 0 else np.nan],
            '推荐股票收益率': [avg_return],
            '技术强度100收益率': [strength_100_avg_return if strength_100_count > 0 else np.nan],
            '交叉验证收益率': [cross_validation_avg_return if cross_validation_count > 0 else np.nan]
        })
        results = pd.concat([results, new_row], ignore_index=True)
        
        # 保存交叉验证股票
        if cross_validation_count > 0:
            cross_validation.to_excel(f'cross_validation_results/{date_str}_交叉验证股票.xlsx', index=False)
    
    # 保存结果
    results.to_excel('cross_validation_results/交叉验证结果.xlsx', index=False)
    
    # 计算整体统计
    valid_results = results.dropna(subset=['交叉验证胜率'])
    if len(valid_results) > 0:
        # 计算加权平均胜率
        overall_win_rate = results['推荐股票数'].sum() / results['推荐股票数'].sum()
        overall_strength_100_win_rate = (results['技术强度100股票数'] * results['技术强度100胜率']).sum() / results['技术强度100股票数'].sum()
        overall_cross_validation_win_rate = (valid_results['交叉验证股票数'] * valid_results['交叉验证胜率']).sum() / valid_results['交叉验证股票数'].sum()
        
        # 计算平均收益率
        overall_avg_return = results['推荐股票收益率'].mean()
        overall_strength_100_avg_return = results.dropna(subset=['技术强度100收益率'])['技术强度100收益率'].mean()
        overall_cross_validation_avg_return = valid_results['交叉验证收益率'].mean()
        
        print("\n整体统计:")
        print(f"推荐股票总数: {results['推荐股票数'].sum()}")
        print(f"推荐股票整体胜率: {overall_win_rate:.4f} ({overall_win_rate*100:.2f}%)")
        print(f"推荐股票平均收益率: {overall_avg_return:.4f}%")
        
        print(f"技术强度为100的股票总数: {results['技术强度100股票数'].sum()}")
        print(f"技术强度为100的股票整体胜率: {overall_strength_100_win_rate:.4f} ({overall_strength_100_win_rate*100:.2f}%)")
        print(f"技术强度为100的股票平均收益率: {overall_strength_100_avg_return:.4f}%")
        
        print(f"交叉验证股票总数: {valid_results['交叉验证股票数'].sum()}")
        print(f"交叉验证股票整体胜率: {overall_cross_validation_win_rate:.4f} ({overall_cross_validation_win_rate*100:.2f}%)")
        print(f"交叉验证股票平均收益率: {overall_cross_validation_avg_return:.4f}%")
        
        # 绘制胜率比较图表
        plt.figure(figsize=(12, 6))
        plt.plot(results['日期'], results['推荐股票胜率'], marker='o', linestyle='-', label='模型推荐股票')
        plt.plot(results['日期'], results['技术强度100胜率'], marker='x', linestyle='--', label='技术强度100股票')
        plt.plot(valid_results['日期'], valid_results['交叉验证胜率'], marker='*', linestyle='-.', color='red', label='交叉验证股票')
        plt.title('不同策略胜率比较')
        plt.xlabel('日期')
        plt.ylabel('胜率')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('cross_validation_results/胜率比较.png')
        
        # 绘制收益率比较图表
        plt.figure(figsize=(12, 6))
        plt.plot(results['日期'], results['推荐股票收益率'], marker='o', linestyle='-', label='模型推荐股票')
        plt.plot(results['日期'], results['技术强度100收益率'], marker='x', linestyle='--', label='技术强度100股票')
        plt.plot(valid_results['日期'], valid_results['交叉验证收益率'], marker='*', linestyle='-.', color='red', label='交叉验证股票')
        plt.title('不同策略收益率比较')
        plt.xlabel('日期')
        plt.ylabel('收益率 (%)')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('cross_validation_results/收益率比较.png')
        
        # 绘制交叉验证股票数量图表
        plt.figure(figsize=(12, 6))
        plt.bar(valid_results['日期'], valid_results['交叉验证股票数'])
        plt.title('交叉验证股票数量')
        plt.xlabel('日期')
        plt.ylabel('股票数量')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('cross_validation_results/交叉验证股票数量.png')
    
    print("\n分析完成！结果已保存至 cross_validation_results 目录")
    
    return results

if __name__ == "__main__":
    analyze_cross_validation()
