import pandas as pd

# 读取数据
df = pd.read_excel('E:/机器学习/complete_excel_results/股票明细_完整.xlsx')

# 打印总记录数
print(f'总记录数: {len(df)}')

# 检查各个条件的记录数
print(f'技术强度为42的记录数: {len(df[df["技术强度"] == 42])}')
print(f'连续技术强度5天数>=140的记录数: {len(df[df["连续技术强度5天数"] >= 140])}')
print(f'趋势组合为111000的记录数: {len(df[df["趋势组合"] == "111000"])}')

# 检查同时满足所有条件的记录数
combined_filter = (df["技术强度"] == 42) & (df["连续技术强度5天数"] >= 140) & (df["趋势组合"] == "111000")
print(f'同时满足三个条件的记录数: {len(df[combined_filter])}')

# 如果有满足条件的记录，打印前5条
if len(df[combined_filter]) > 0:
    print('\n前5条满足条件的记录:')
    print(df[combined_filter].head())
else:
    print('\n没有满足所有条件的记录')
    
    # 分析每个条件的分布
    print('\n技术强度的唯一值:')
    print(df["技术强度"].unique())
    
    print('\n连续技术强度5天数的分布:')
    print(df["连续技术强度5天数"].describe())
    
    print('\n趋势组合的唯一值:')
    print(df["趋势组合"].unique())
    
    # 检查是否有接近条件的记录
    print('\n检查接近条件的记录:')
    
    # 技术强度为42，但其他条件不满足
    filter1 = (df["技术强度"] == 42)
    if len(df[filter1]) > 0:
        print(f'\n技术强度为42的记录示例:')
        print(df[filter1].head())
    
    # 连续技术强度5天数>=140，但其他条件不满足
    filter2 = (df["连续技术强度5天数"] >= 140)
    if len(df[filter2]) > 0:
        print(f'\n连续技术强度5天数>=140的记录示例:')
        print(df[filter2].head())
    
    # 趋势组合为111000，但其他条件不满足
    filter3 = (df["趋势组合"] == "111000")
    if len(df[filter3]) > 0:
        print(f'\n趋势组合为111000的记录示例:')
        print(df[filter3].head())
    
    # 技术强度为42且连续技术强度5天数>=140
    filter4 = (df["技术强度"] == 42) & (df["连续技术强度5天数"] >= 140)
    if len(df[filter4]) > 0:
        print(f'\n技术强度为42且连续技术强度5天数>=140的记录示例:')
        print(df[filter4].head())
    
    # 技术强度为42且趋势组合为111000
    filter5 = (df["技术强度"] == 42) & (df["趋势组合"] == "111000")
    if len(df[filter5]) > 0:
        print(f'\n技术强度为42且趋势组合为111000的记录示例:')
        print(df[filter5].head())
    
    # 连续技术强度5天数>=140且趋势组合为111000
    filter6 = (df["连续技术强度5天数"] >= 140) & (df["趋势组合"] == "111000")
    if len(df[filter6]) > 0:
        print(f'\n连续技术强度5天数>=140且趋势组合为111000的记录示例:')
        print(df[filter6].head())
