import pandas as pd
import os

def check_strategy_range():
    """检查汇总表中策略的实际范围"""
    
    # 检查汇总表
    summary_files = [
        'E:/机器学习/complete_excel_results/所有策略汇总_已回测.xlsx',
        'E:/机器学习/complete_excel_results/所有策略汇总.xlsx',
        'E:/机器学习/complete_excel_results/所有策略汇总.parquet'
    ]
    
    for file_path in summary_files:
        if os.path.exists(file_path):
            try:
                if file_path.endswith('.parquet'):
                    df = pd.read_parquet(file_path)
                    print(f"✅ 读取 {os.path.basename(file_path)}: {len(df)}条记录")
                else:
                    df = pd.read_excel(file_path)
                    print(f"✅ 读取 {os.path.basename(file_path)}: {len(df)}条记录")
                
                if '策略编号' in df.columns:
                    min_id = df['策略编号'].min()
                    max_id = df['策略编号'].max()
                    print(f"   策略ID范围: {min_id} - {max_id}")
                    
                    # 检查7913附近的策略
                    nearby = df[(df['策略编号'] >= 7910) & (df['策略编号'] <= 7915)]
                    if not nearby.empty:
                        print(f"   7910-7915范围内的策略:")
                        for _, row in nearby.iterrows():
                            print(f"     策略{row['策略编号']}: {row.get('策略条件描述', 'N/A')[:50]}...")
                    else:
                        print(f"   7910-7915范围内没有策略")
                    
                    # 检查最后几个策略
                    last_10 = df.nlargest(10, '策略编号')
                    print(f"   最后10个策略:")
                    for _, row in last_10.iterrows():
                        print(f"     策略{row['策略编号']}: {row.get('策略条件描述', 'N/A')[:50]}...")
                
                return df
                
            except Exception as e:
                print(f"❌ 读取{file_path}失败: {e}")
    
    print("❌ 没有找到可用的汇总表文件")
    return None

if __name__ == "__main__":
    df = check_strategy_range()
    
    if df is not None:
        print(f"\n📊 汇总表统计:")
        print(f"   总策略数: {len(df)}")
        print(f"   策略ID范围: {df['策略编号'].min()} - {df['策略编号'].max()}")
        
        # 检查是否有重复的策略ID
        duplicates = df[df.duplicated('策略编号', keep=False)]
        if not duplicates.empty:
            print(f"   ⚠️  发现重复的策略ID: {duplicates['策略编号'].unique()}")
        else:
            print(f"   ✅ 没有重复的策略ID")
        
        # 检查策略ID是否连续
        all_ids = set(df['策略编号'])
        expected_ids = set(range(df['策略编号'].min(), df['策略编号'].max() + 1))
        missing_ids = expected_ids - all_ids
        if missing_ids:
            print(f"   ⚠️  缺失的策略ID: {sorted(list(missing_ids))[:10]}...")
        else:
            print(f"   ✅ 策略ID连续")
