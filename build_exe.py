"""
打包脚本，将create_strategy_excel.py打包为独立的.exe文件

使用方法：
1. 安装PyInstaller：pip install pyinstaller
2. 运行此脚本：python build_exe.py
3. 在dist目录下找到生成的.exe文件
"""

import os
import subprocess
import shutil
import sys

def build_exe():
    """打包create_strategy_excel.py为独立的.exe文件"""
    print("开始打包create_strategy_excel.py为独立的.exe文件...")
    
    # 检查create_strategy_excel.py是否存在
    if not os.path.exists('create_strategy_excel.py'):
        print("错误：找不到create_strategy_excel.py文件")
        return False
    
    # 检查是否安装了PyInstaller
    try:
        import PyInstaller
        print(f"已安装PyInstaller版本：{PyInstaller.__version__}")
    except ImportError:
        print("错误：未安装PyInstaller，请先安装：pip install pyinstaller")
        return False
    
    # 创建打包命令
    cmd = [
        'pyinstaller',
        '--onefile',  # 打包为单个文件
        '--noconsole',  # 不显示控制台窗口
        '--name', 'CreateStrategyExcel',  # 指定输出文件名
        '--icon', 'NONE',  # 不使用图标
        'create_strategy_excel.py'  # 要打包的脚本
    ]
    
    # 执行打包命令
    print("执行打包命令：", ' '.join(cmd))
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # 检查打包结果
    if result.returncode == 0:
        print("打包成功！")
        exe_path = os.path.join('dist', 'CreateStrategyExcel.exe')
        if os.path.exists(exe_path):
            print(f"生成的.exe文件路径：{os.path.abspath(exe_path)}")
            return True
        else:
            print(f"错误：找不到生成的.exe文件：{exe_path}")
            return False
    else:
        print("打包失败！")
        print("错误信息：")
        print(result.stderr)
        return False

def create_gui_wrapper():
    """创建GUI包装器，使用tkinter创建一个简单的GUI界面"""
    print("创建GUI包装器...")
    
    gui_code = """
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
import os
import sys

class CreateStrategyExcelGUI:
    def __init__(self, root):
        self.root = root
        root.title("策略Excel文件生成器")
        root.geometry("600x500")
        root.resizable(True, True)
        
        # 设置样式
        style = ttk.Style()
        style.configure("TButton", padding=6, relief="flat", background="#ccc")
        style.configure("TLabel", padding=6, font=('Helvetica', 10))
        style.configure("TEntry", padding=6)
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 策略ID
        ttk.Label(main_frame, text="策略ID:").grid(column=0, row=0, sticky=tk.W, pady=5)
        self.strategy_id = tk.StringVar()
        ttk.Entry(main_frame, width=10, textvariable=self.strategy_id).grid(column=1, row=0, sticky=tk.W, pady=5)
        
        # 策略描述
        ttk.Label(main_frame, text="策略描述:").grid(column=0, row=1, sticky=tk.W, pady=5)
        self.strategy_desc = tk.StringVar()
        self.strategy_desc_entry = ttk.Entry(main_frame, width=50, textvariable=self.strategy_desc)
        self.strategy_desc_entry.grid(column=1, row=1, columnspan=3, sticky=tk.W+tk.E, pady=5)
        
        # 输出目录
        ttk.Label(main_frame, text="输出目录:").grid(column=0, row=2, sticky=tk.W, pady=5)
        self.output_dir = tk.StringVar(value=r"E:\\机器学习\\complete_excel_results\\new_strategy_details")
        self.output_dir_entry = ttk.Entry(main_frame, width=50, textvariable=self.output_dir)
        self.output_dir_entry.grid(column=1, row=2, columnspan=2, sticky=tk.W+tk.E, pady=5)
        ttk.Button(main_frame, text="浏览...", command=self.browse_output_dir).grid(column=3, row=2, sticky=tk.W, pady=5)
        
        # 批量生成设置
        ttk.Label(main_frame, text="批量生成设置:").grid(column=0, row=3, sticky=tk.W, pady=5)
        
        # 起始ID
        ttk.Label(main_frame, text="起始ID:").grid(column=0, row=4, sticky=tk.W, pady=5)
        self.start_id = tk.StringVar(value="1")
        ttk.Entry(main_frame, width=10, textvariable=self.start_id).grid(column=1, row=4, sticky=tk.W, pady=5)
        
        # 终止ID
        ttk.Label(main_frame, text="终止ID:").grid(column=2, row=4, sticky=tk.W, pady=5)
        self.end_id = tk.StringVar(value="10")
        ttk.Entry(main_frame, width=10, textvariable=self.end_id).grid(column=3, row=4, sticky=tk.W, pady=5)
        
        # 日志区域
        ttk.Label(main_frame, text="日志:").grid(column=0, row=5, sticky=tk.W, pady=5)
        self.log_text = tk.Text(main_frame, width=70, height=15)
        self.log_text.grid(column=0, row=6, columnspan=4, sticky=tk.W+tk.E+tk.N+tk.S, pady=5)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.grid(column=4, row=6, sticky=tk.N+tk.S)
        self.log_text['yscrollcommand'] = scrollbar.set
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(column=0, row=7, columnspan=4, sticky=tk.W+tk.E, pady=10)
        
        # 生成单个文件按钮
        ttk.Button(button_frame, text="生成单个文件", command=self.create_single_file).pack(side=tk.LEFT, padx=5)
        
        # 批量生成文件按钮
        ttk.Button(button_frame, text="批量生成文件", command=self.create_batch_files).pack(side=tk.LEFT, padx=5)
        
        # 清空日志按钮
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        
        # 退出按钮
        ttk.Button(button_frame, text="退出", command=root.destroy).pack(side=tk.RIGHT, padx=5)
        
        # 设置列和行的权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)
        
        # 初始化日志
        self.log("策略Excel文件生成器已启动")
        self.log(f"默认输出目录: {self.output_dir.get()}")
    
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(initialdir=self.output_dir.get())
        if directory:
            self.output_dir.set(directory)
            self.log(f"已选择输出目录: {directory}")
    
    def log(self, message):
        """添加日志"""
        self.log_text.insert(tk.END, message + "\\n")
        self.log_text.see(tk.END)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("日志已清空")
    
    def create_single_file(self):
        """生成单个文件"""
        strategy_id = self.strategy_id.get().strip()
        strategy_desc = self.strategy_desc.get().strip()
        output_dir = self.output_dir.get().strip()
        
        # 验证输入
        if not strategy_id:
            messagebox.showerror("错误", "请输入策略ID")
            return
        
        try:
            strategy_id = int(strategy_id)
        except ValueError:
            messagebox.showerror("错误", "策略ID必须是整数")
            return
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                self.log(f"已创建输出目录: {output_dir}")
            except Exception as e:
                messagebox.showerror("错误", f"无法创建输出目录: {str(e)}")
                return
        
        # 构建命令
        cmd = [
            sys.executable,
            "create_strategy_excel.py",
            "--id", str(strategy_id)
        ]
        
        if strategy_desc:
            cmd.extend(["--desc", strategy_desc])
        
        cmd.extend(["--output_dir", output_dir])
        
        self.log(f"执行命令: {' '.join(cmd)}")
        
        # 执行命令
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                self.log(f"成功生成策略 {strategy_id} 的Excel文件")
                self.log(result.stdout)
                messagebox.showinfo("成功", f"已成功生成策略 {strategy_id} 的Excel文件")
            else:
                self.log(f"生成策略 {strategy_id} 的Excel文件失败")
                self.log(f"错误信息: {result.stderr}")
                messagebox.showerror("错误", f"生成Excel文件失败: {result.stderr}")
        except Exception as e:
            self.log(f"执行命令时出错: {str(e)}")
            messagebox.showerror("错误", f"执行命令时出错: {str(e)}")
    
    def create_batch_files(self):
        """批量生成文件"""
        start_id = self.start_id.get().strip()
        end_id = self.end_id.get().strip()
        strategy_desc = self.strategy_desc.get().strip()
        output_dir = self.output_dir.get().strip()
        
        # 验证输入
        if not start_id or not end_id:
            messagebox.showerror("错误", "请输入起始ID和终止ID")
            return
        
        try:
            start_id = int(start_id)
            end_id = int(end_id)
        except ValueError:
            messagebox.showerror("错误", "起始ID和终止ID必须是整数")
            return
        
        if start_id > end_id:
            messagebox.showerror("错误", "起始ID不能大于终止ID")
            return
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                self.log(f"已创建输出目录: {output_dir}")
            except Exception as e:
                messagebox.showerror("错误", f"无法创建输出目录: {str(e)}")
                return
        
        # 确认是否继续
        if end_id - start_id > 20:
            if not messagebox.askyesno("确认", f"您将生成 {end_id - start_id + 1} 个文件，这可能需要一些时间。是否继续？"):
                return
        
        # 批量生成文件
        success_count = 0
        fail_count = 0
        
        for strategy_id in range(start_id, end_id + 1):
            # 构建命令
            cmd = [
                sys.executable,
                "create_strategy_excel.py",
                "--id", str(strategy_id)
            ]
            
            if strategy_desc:
                cmd.extend(["--desc", strategy_desc])
            
            cmd.extend(["--output_dir", output_dir])
            
            self.log(f"执行命令: {' '.join(cmd)}")
            
            # 执行命令
            try:
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    self.log(f"成功生成策略 {strategy_id} 的Excel文件")
                    success_count += 1
                else:
                    self.log(f"生成策略 {strategy_id} 的Excel文件失败")
                    self.log(f"错误信息: {result.stderr}")
                    fail_count += 1
            except Exception as e:
                self.log(f"执行命令时出错: {str(e)}")
                fail_count += 1
        
        # 显示结果
        self.log(f"批量生成完成: 成功 {success_count} 个，失败 {fail_count} 个")
        messagebox.showinfo("完成", f"批量生成完成: 成功 {success_count} 个，失败 {fail_count} 个")

def main():
    root = tk.Tk()
    app = CreateStrategyExcelGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
"""
    
    # 保存GUI包装器代码
    with open('create_strategy_excel_gui.py', 'w', encoding='utf-8') as f:
        f.write(gui_code)
    
    print("已创建GUI包装器：create_strategy_excel_gui.py")
    
    # 打包GUI包装器
    cmd = [
        'pyinstaller',
        '--onefile',  # 打包为单个文件
        '--noconsole',  # 不显示控制台窗口
        '--name', 'CreateStrategyExcelGUI',  # 指定输出文件名
        '--icon', 'NONE',  # 不使用图标
        '--add-data', 'create_strategy_excel.py;.',  # 添加数据文件
        'create_strategy_excel_gui.py'  # 要打包的脚本
    ]
    
    # 执行打包命令
    print("执行打包命令：", ' '.join(cmd))
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # 检查打包结果
    if result.returncode == 0:
        print("打包成功！")
        exe_path = os.path.join('dist', 'CreateStrategyExcelGUI.exe')
        if os.path.exists(exe_path):
            print(f"生成的GUI.exe文件路径：{os.path.abspath(exe_path)}")
            return True
        else:
            print(f"错误：找不到生成的GUI.exe文件：{exe_path}")
            return False
    else:
        print("打包失败！")
        print("错误信息：")
        print(result.stderr)
        return False

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--gui':
        create_gui_wrapper()
    else:
        build_exe()
