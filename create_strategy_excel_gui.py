"""
策略Excel文件生成器GUI版本

这个脚本提供了一个图形界面，用于创建策略Excel文件。
用户可以指定策略ID、策略描述和输出目录，生成符合回测程序要求的Excel文件。
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
import os
import sys
import pandas as pd
from datetime import datetime

class CreateStrategyExcelGUI:
    def __init__(self, root):
        self.root = root
        root.title("策略Excel文件生成器")
        root.geometry("600x500")
        root.resizable(True, True)
        
        # 设置样式
        style = ttk.Style()
        style.configure("TButton", padding=6, relief="flat", background="#ccc")
        style.configure("TLabel", padding=6, font=('Helvetica', 10))
        style.configure("TEntry", padding=6)
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 策略ID
        ttk.Label(main_frame, text="策略ID:").grid(column=0, row=0, sticky=tk.W, pady=5)
        self.strategy_id = tk.StringVar()
        ttk.Entry(main_frame, width=10, textvariable=self.strategy_id).grid(column=1, row=0, sticky=tk.W, pady=5)
        
        # 策略描述
        ttk.Label(main_frame, text="策略描述:").grid(column=0, row=1, sticky=tk.W, pady=5)
        self.strategy_desc = tk.StringVar()
        self.strategy_desc_entry = ttk.Entry(main_frame, width=50, textvariable=self.strategy_desc)
        self.strategy_desc_entry.grid(column=1, row=1, columnspan=3, sticky=tk.W+tk.E, pady=5)
        
        # 输出目录
        ttk.Label(main_frame, text="输出目录:").grid(column=0, row=2, sticky=tk.W, pady=5)
        self.output_dir = tk.StringVar(value=r"E:\机器学习\complete_excel_results\new_strategy_details")
        self.output_dir_entry = ttk.Entry(main_frame, width=50, textvariable=self.output_dir)
        self.output_dir_entry.grid(column=1, row=2, columnspan=2, sticky=tk.W+tk.E, pady=5)
        ttk.Button(main_frame, text="浏览...", command=self.browse_output_dir).grid(column=3, row=2, sticky=tk.W, pady=5)
        
        # 批量生成设置
        ttk.Label(main_frame, text="批量生成设置:").grid(column=0, row=3, sticky=tk.W, pady=5)
        
        # 起始ID
        ttk.Label(main_frame, text="起始ID:").grid(column=0, row=4, sticky=tk.W, pady=5)
        self.start_id = tk.StringVar(value="1")
        ttk.Entry(main_frame, width=10, textvariable=self.start_id).grid(column=1, row=4, sticky=tk.W, pady=5)
        
        # 终止ID
        ttk.Label(main_frame, text="终止ID:").grid(column=2, row=4, sticky=tk.W, pady=5)
        self.end_id = tk.StringVar(value="10")
        ttk.Entry(main_frame, width=10, textvariable=self.end_id).grid(column=3, row=4, sticky=tk.W, pady=5)
        
        # 日志区域
        ttk.Label(main_frame, text="日志:").grid(column=0, row=5, sticky=tk.W, pady=5)
        self.log_text = tk.Text(main_frame, width=70, height=15)
        self.log_text.grid(column=0, row=6, columnspan=4, sticky=tk.W+tk.E+tk.N+tk.S, pady=5)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.grid(column=4, row=6, sticky=tk.N+tk.S)
        self.log_text['yscrollcommand'] = scrollbar.set
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(column=0, row=7, columnspan=4, sticky=tk.W+tk.E, pady=10)
        
        # 生成单个文件按钮
        ttk.Button(button_frame, text="生成单个文件", command=self.create_single_file).pack(side=tk.LEFT, padx=5)
        
        # 批量生成文件按钮
        ttk.Button(button_frame, text="批量生成文件", command=self.create_batch_files).pack(side=tk.LEFT, padx=5)
        
        # 清空日志按钮
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        
        # 退出按钮
        ttk.Button(button_frame, text="退出", command=root.destroy).pack(side=tk.RIGHT, padx=5)
        
        # 设置列和行的权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)
        
        # 初始化日志
        self.log("策略Excel文件生成器已启动")
        self.log(f"默认输出目录: {self.output_dir.get()}")
    
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(initialdir=self.output_dir.get())
        if directory:
            self.output_dir.set(directory)
            self.log(f"已选择输出目录: {directory}")
    
    def log(self, message):
        """添加日志"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("日志已清空")
    
    def create_strategy_excel(self, strategy_id, strategy_desc=None, output_dir=None):
        """创建策略Excel文件"""
        # 设置默认输出目录
        if output_dir is None:
            output_dir = r'E:\机器学习\complete_excel_results\new_strategy_details'
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 创建文件路径
        file_path = os.path.join(output_dir, f"strategy_{strategy_id}.xlsx")
        
        # 如果没有提供策略描述，使用默认描述
        if strategy_desc is None or strategy_desc.strip() == "":
            strategy_desc = f"技术强度 等于 28 AND 连续技术强度5天数 大于等于 355 AND 连续技术强度10天数 大于等于 579 AND 技术指标特征 为 111111（满足: 均线多头排列, 成交量放大, MACD金叉, RSI反弹, KDJ金叉, 布林带突破） AND 趋势组合 为 111111（满足: 3天技术强度上升, 3天价格上升, 5天技术强度上升, 5天价格上升, 10天技术强度上升, 10天价格上升） AND 日内股票标记 为 664（开盘上涨, 日内最高点强势） AND 成交量是前一日几倍 大于等于 2.5"
        
        # 创建一个Excel写入器
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 1. 策略汇总参数
            strategy_summary = pd.DataFrame({
                '策略编号': [strategy_id],
                '策略组合': [f'策略{strategy_id}'],
                '特征数量': [len(strategy_desc.split('AND'))],
                '平均收益率(%)': [0],
                '平均胜率(%)': [0],
                '平均每日交易笔数': [0],
                '总交易笔数': [0],
                '交易天数': [0],
                '总天数': [30],  # 假设总天数为30
                '交易频率(%)': [0],
                '初始资金(元)': [10000],
                '最终资金(元)': [10000],
                '盈利(元)': [0],
                '累计收益率(%)': [0],
                '年化收益率(%)': [0],
            })
            strategy_summary.to_excel(writer, sheet_name='策略汇总参数', index=False)

            # 2. 策略参数
            strategy_params = pd.DataFrame({
                '策略编号': [strategy_id],
                '策略条件描述': [strategy_desc],
            })
            strategy_params.to_excel(writer, sheet_name='策略参数', index=False)
            
            # 3. 空的股票选择结果 - 添加一行假数据，确保文件能被正确读取
            empty_df = pd.DataFrame({
                '股票代码': ['000000'],
                '股票名称': ['空数据'],
                '选股日期': [pd.Timestamp('2025-01-01')],
                '技术强度': [0],
                '涨跌幅': [0],
                '买入日期': [pd.Timestamp('2025-01-02')],
                '买入日涨跌幅': [0],
                '卖出日期': [pd.Timestamp('2025-01-03')],
                '卖出日股票涨跌幅': [0],
                '是否盈利': [False],
                '策略编号': [strategy_id]
            })
            empty_df.to_excel(writer, sheet_name='选股明细', index=False)
            
            # 4. 空的每日收益明细 - 添加一行假数据，确保文件能被正确读取
            empty_daily_df = pd.DataFrame({
                '日期': [pd.Timestamp('2025-01-01')],
                '日平均涨幅(%)': [0],
                '当日胜率(%)': [0],
                '日收益率': [0],
                '日收益金额': [0],
                '累计资金': [10000],
                '交易股票数量': [0],
                '策略编号': [strategy_id]
            })
            empty_daily_df.to_excel(writer, sheet_name='每日收益明细', index=False)
            
            # 5. 说明
            info_df = pd.DataFrame({
                '说明': [
                    f'策略编号: {strategy_id}',
                    f'创建时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                    f'策略条件描述: {strategy_desc}',
                    '该策略没有筛选出符合条件的股票',
                    '这是一个空的Excel文件，用于确保程序能够继续执行下一个组合'
                ]
            })
            info_df.to_excel(writer, sheet_name='说明', index=False)
        
        # 验证文件是否成功保存
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            if file_size > 0:
                return file_path, True
            else:
                return file_path, False
        else:
            return file_path, False
    
    def create_single_file(self):
        """生成单个文件"""
        strategy_id = self.strategy_id.get().strip()
        strategy_desc = self.strategy_desc.get().strip()
        output_dir = self.output_dir.get().strip()
        
        # 验证输入
        if not strategy_id:
            messagebox.showerror("错误", "请输入策略ID")
            return
        
        try:
            strategy_id = int(strategy_id)
        except ValueError:
            messagebox.showerror("错误", "策略ID必须是整数")
            return
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                self.log(f"已创建输出目录: {output_dir}")
            except Exception as e:
                messagebox.showerror("错误", f"无法创建输出目录: {str(e)}")
                return
        
        # 创建Excel文件
        self.log(f"开始创建策略 {strategy_id} 的Excel文件...")
        try:
            file_path, success = self.create_strategy_excel(strategy_id, strategy_desc, output_dir)
            if success:
                self.log(f"成功创建策略 {strategy_id} 的Excel文件: {file_path}")
                messagebox.showinfo("成功", f"已成功创建策略 {strategy_id} 的Excel文件")
            else:
                self.log(f"创建策略 {strategy_id} 的Excel文件失败")
                messagebox.showerror("错误", f"创建Excel文件失败")
        except Exception as e:
            self.log(f"创建Excel文件时出错: {str(e)}")
            messagebox.showerror("错误", f"创建Excel文件时出错: {str(e)}")
    
    def create_batch_files(self):
        """批量生成文件"""
        start_id = self.start_id.get().strip()
        end_id = self.end_id.get().strip()
        strategy_desc = self.strategy_desc.get().strip()
        output_dir = self.output_dir.get().strip()
        
        # 验证输入
        if not start_id or not end_id:
            messagebox.showerror("错误", "请输入起始ID和终止ID")
            return
        
        try:
            start_id = int(start_id)
            end_id = int(end_id)
        except ValueError:
            messagebox.showerror("错误", "起始ID和终止ID必须是整数")
            return
        
        if start_id > end_id:
            messagebox.showerror("错误", "起始ID不能大于终止ID")
            return
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                self.log(f"已创建输出目录: {output_dir}")
            except Exception as e:
                messagebox.showerror("错误", f"无法创建输出目录: {str(e)}")
                return
        
        # 确认是否继续
        if end_id - start_id > 20:
            if not messagebox.askyesno("确认", f"您将生成 {end_id - start_id + 1} 个文件，这可能需要一些时间。是否继续？"):
                return
        
        # 批量生成文件
        success_count = 0
        fail_count = 0
        
        self.log(f"开始批量创建策略 {start_id} 到 {end_id} 的Excel文件...")
        
        for strategy_id in range(start_id, end_id + 1):
            try:
                file_path, success = self.create_strategy_excel(strategy_id, strategy_desc, output_dir)
                if success:
                    self.log(f"成功创建策略 {strategy_id} 的Excel文件")
                    success_count += 1
                else:
                    self.log(f"创建策略 {strategy_id} 的Excel文件失败")
                    fail_count += 1
            except Exception as e:
                self.log(f"创建策略 {strategy_id} 的Excel文件时出错: {str(e)}")
                fail_count += 1
        
        # 显示结果
        self.log(f"批量创建完成: 成功 {success_count} 个，失败 {fail_count} 个")
        messagebox.showinfo("完成", f"批量创建完成: 成功 {success_count} 个，失败 {fail_count} 个")

def main():
    root = tk.Tk()
    app = CreateStrategyExcelGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
