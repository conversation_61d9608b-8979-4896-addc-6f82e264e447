#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
真实股票回测模块
作者: Augment AI
版本: 1.0.0

该模块提供基于真实历史数据的股票策略回测功能。
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from tqdm import tqdm

class RealStockBacktester:
    """真实股票策略回测器"""
    
    def __init__(self, stock_data_file, output_dir):
        """
        初始化回测器
        
        参数:
            stock_data_file (str): 股票历史数据文件路径
            output_dir (str): 输出目录
        """
        self.stock_data_file = stock_data_file
        self.output_dir = output_dir
        self.stock_data = None
        self.trading_dates = None
        self.results_dir = os.path.join(output_dir, 'backtest_results')
        
        # 创建结果目录
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
            
        # 加载股票数据
        self.load_stock_data()
        
    def load_stock_data(self):
        """加载股票历史数据"""
        try:
            # 读取股票数据
            self.stock_data = pd.read_excel(self.stock_data_file)
            
            # 确保日期列是日期类型
            if '日期' in self.stock_data.columns:
                self.stock_data['日期'] = pd.to_datetime(self.stock_data['日期'])
                
            # 获取所有交易日期
            if '日期' in self.stock_data.columns:
                self.trading_dates = sorted(self.stock_data['日期'].unique())
                
            print(f"成功加载股票数据，共{len(self.stock_data)}条记录，{len(self.trading_dates)}个交易日")
            
        except Exception as e:
            print(f"加载股票数据时出错: {str(e)}")
            self.stock_data = None
            self.trading_dates = None
            
    def backtest_strategy(self, strategy_conditions, start_date=None, end_date=None):
        """
        回测策略
        
        参数:
            strategy_conditions (list): 策略条件列表，每个条件是一个字典，包含feature、condition和description
            start_date (str): 回测开始日期，格式：YYYY-MM-DD
            end_date (str): 回测结束日期，格式：YYYY-MM-DD
            
        返回:
            dict: 回测结果
        """
        if self.stock_data is None or self.trading_dates is None:
            print("股票数据未加载，无法进行回测")
            return None
            
        # 转换日期格式
        if start_date:
            start_date = pd.to_datetime(start_date)
        else:
            start_date = self.trading_dates[0]
            
        if end_date:
            end_date = pd.to_datetime(end_date)
        else:
            end_date = self.trading_dates[-1]
            
        # 筛选日期范围内的数据
        mask = (self.stock_data['日期'] >= start_date) & (self.stock_data['日期'] <= end_date)
        data = self.stock_data[mask].copy()
        
        # 如果数据为空，返回空结果
        if len(data) == 0:
            print("选定日期范围内没有数据")
            return None
            
        # 获取日期范围内的交易日
        trading_dates = sorted(data['日期'].unique())
        
        # 初始化结果
        results = {
            'daily_performance': [],
            'trades': [],
            'summary': {}
        }
        
        # 初始化资金
        initial_capital = 1000000  # 初始资金100万
        current_capital = initial_capital
        
        # 初始化持仓
        positions = {}
        
        # 遍历每个交易日
        for i, date in enumerate(trading_dates[:-1]):  # 最后一天不买入
            # 当前日期的数据
            current_day_data = data[data['日期'] == date]
            
            # 下一个交易日
            next_date = trading_dates[i + 1]
            next_day_data = data[data['日期'] == next_date]
            
            # 卖出昨日持仓
            if positions:
                for stock_code, position in list(positions.items()):
                    # 获取今日该股票的数据
                    next_stock_data = next_day_data[next_day_data['股票代码'] == stock_code]
                    
                    if len(next_stock_data) > 0:
                        # 获取今日开盘价
                        open_price = next_stock_data['开盘价'].values[0]
                        
                        # 计算卖出收益
                        sell_value = position['quantity'] * open_price
                        profit = sell_value - position['cost']
                        profit_rate = profit / position['cost'] * 100
                        
                        # 更新资金
                        current_capital += sell_value
                        
                        # 记录交易
                        results['trades'].append({
                            '日期': next_date,
                            '股票代码': stock_code,
                            '操作': '卖出',
                            '价格': open_price,
                            '数量': position['quantity'],
                            '金额': sell_value,
                            '收益': profit,
                            '收益率(%)': profit_rate
                        })
                        
                        # 从持仓中移除
                        del positions[stock_code]
                    else:
                        # 如果今日没有该股票的数据，假设以昨日收盘价卖出
                        sell_price = position['price']
                        sell_value = position['quantity'] * sell_price
                        profit = sell_value - position['cost']
                        profit_rate = profit / position['cost'] * 100
                        
                        # 更新资金
                        current_capital += sell_value
                        
                        # 记录交易
                        results['trades'].append({
                            '日期': next_date,
                            '股票代码': stock_code,
                            '操作': '卖出(无数据)',
                            '价格': sell_price,
                            '数量': position['quantity'],
                            '金额': sell_value,
                            '收益': profit,
                            '收益率(%)': profit_rate
                        })
                        
                        # 从持仓中移除
                        del positions[stock_code]
            
            # 应用策略条件筛选股票
            filtered_data = current_day_data.copy()
            
            for condition in strategy_conditions:
                feature = condition['feature']
                cond = condition['condition']
                
                # 确保特征存在
                if feature not in filtered_data.columns:
                    print(f"特征 {feature} 不存在于数据中")
                    continue
                    
                # 应用条件
                if '>=' in cond:
                    threshold = float(cond.split('>=')[1].strip())
                    filtered_data = filtered_data[filtered_data[feature] >= threshold]
                elif '<=' in cond:
                    threshold = float(cond.split('<=')[1].strip())
                    filtered_data = filtered_data[filtered_data[feature] <= threshold]
                elif '==' in cond:
                    threshold = float(cond.split('==')[1].strip())
                    filtered_data = filtered_data[filtered_data[feature] == threshold]
                elif '>' in cond:
                    threshold = float(cond.split('>')[1].strip())
                    filtered_data = filtered_data[filtered_data[feature] > threshold]
                elif '<' in cond:
                    threshold = float(cond.split('<')[1].strip())
                    filtered_data = filtered_data[filtered_data[feature] < threshold]
            
            # 获取符合条件的股票
            selected_stocks = filtered_data.copy()
            
            # 买入股票
            if len(selected_stocks) > 0:
                # 计算每只股票的买入金额
                max_stocks = 10  # 最多买入10只股票
                num_stocks = min(len(selected_stocks), max_stocks)
                per_stock_value = current_capital / num_stocks
                
                # 买入股票
                for _, stock in selected_stocks.head(max_stocks).iterrows():
                    stock_code = stock['股票代码']
                    close_price = stock['收盘价']
                    
                    # 计算买入数量（整百股）
                    quantity = int(per_stock_value / close_price / 100) * 100
                    
                    if quantity > 0:
                        # 计算买入金额
                        cost = quantity * close_price
                        
                        # 更新资金
                        current_capital -= cost
                        
                        # 添加到持仓
                        positions[stock_code] = {
                            'quantity': quantity,
                            'price': close_price,
                            'cost': cost,
                            'buy_date': date
                        }
                        
                        # 记录交易
                        results['trades'].append({
                            '日期': date,
                            '股票代码': stock_code,
                            '操作': '买入',
                            '价格': close_price,
                            '数量': quantity,
                            '金额': cost,
                            '收益': 0,
                            '收益率(%)': 0
                        })
            
            # 计算当日总资产
            total_position_value = sum([position['quantity'] * position['price'] for position in positions.values()])
            total_assets = current_capital + total_position_value
            
            # 计算当日收益率
            if i == 0:
                daily_return = 0
            else:
                prev_assets = results['daily_performance'][-1]['总资产']
                daily_return = (total_assets - prev_assets) / prev_assets * 100
            
            # 记录每日表现
            results['daily_performance'].append({
                '日期': date,
                '现金': current_capital,
                '持仓市值': total_position_value,
                '总资产': total_assets,
                '日收益率(%)': daily_return,
                '持仓数量': len(positions)
            })
        
        # 计算汇总统计
        if results['daily_performance']:
            # 计算总收益率
            initial_assets = initial_capital
            final_assets = results['daily_performance'][-1]['总资产']
            total_return = (final_assets - initial_assets) / initial_assets * 100
            
            # 计算年化收益率
            days = (trading_dates[-1] - trading_dates[0]).days
            annual_return = total_return * 365 / days if days > 0 else 0
            
            # 计算最大回撤
            max_drawdown = 0
            peak = initial_assets
            
            for day in results['daily_performance']:
                if day['总资产'] > peak:
                    peak = day['总资产']
                drawdown = (peak - day['总资产']) / peak * 100
                max_drawdown = max(max_drawdown, drawdown)
            
            # 计算胜率
            if results['trades']:
                win_trades = [trade for trade in results['trades'] if trade['操作'].startswith('卖出') and trade['收益'] > 0]
                win_rate = len(win_trades) / len([trade for trade in results['trades'] if trade['操作'].startswith('卖出')]) * 100 if len([trade for trade in results['trades'] if trade['操作'].startswith('卖出')]) > 0 else 0
            else:
                win_rate = 0
            
            # 计算平均每日交易笔数
            daily_trades = {}
            for trade in results['trades']:
                date = trade['日期']
                if date not in daily_trades:
                    daily_trades[date] = 0
                daily_trades[date] += 1
            
            avg_daily_trades = sum(daily_trades.values()) / len(trading_dates) if trading_dates else 0
            
            # 汇总统计
            results['summary'] = {
                '初始资金': initial_capital,
                '最终资金': final_assets,
                '总收益率(%)': total_return,
                '年化收益率(%)': annual_return,
                '最大回撤(%)': max_drawdown,
                '胜率(%)': win_rate,
                '总交易笔数': len(results['trades']),
                '平均每日交易笔数': avg_daily_trades,
                '交易天数': len(daily_trades),
                '总天数': len(trading_dates),
                '交易频率(%)': len(daily_trades) / len(trading_dates) * 100 if trading_dates else 0
            }
        
        return results
    
    def save_backtest_results(self, results, strategy_name):
        """
        保存回测结果
        
        参数:
            results (dict): 回测结果
            strategy_name (str): 策略名称
        """
        if results is None:
            print("回测结果为空，无法保存")
            return
            
        # 创建策略目录
        strategy_dir = os.path.join(self.results_dir, strategy_name)
        if not os.path.exists(strategy_dir):
            os.makedirs(strategy_dir)
            
        # 保存每日表现
        if results['daily_performance']:
            daily_df = pd.DataFrame(results['daily_performance'])
            daily_file = os.path.join(strategy_dir, '每日表现.xlsx')
            daily_df.to_excel(daily_file, index=False)
            
        # 保存交易记录
        if results['trades']:
            trades_df = pd.DataFrame(results['trades'])
            trades_file = os.path.join(strategy_dir, '交易记录.xlsx')
            trades_df.to_excel(trades_file, index=False)
            
        # 保存汇总统计
        if results['summary']:
            summary_data = {
                '统计项': list(results['summary'].keys()),
                '数值': list(results['summary'].values())
            }
            summary_df = pd.DataFrame(summary_data)
            summary_file = os.path.join(strategy_dir, '汇总统计.xlsx')
            summary_df.to_excel(summary_file, index=False)
            
        # 绘制资金曲线
        if results['daily_performance']:
            self.plot_equity_curve(results['daily_performance'], os.path.join(strategy_dir, '资金曲线.png'))
            
        print(f"回测结果已保存到: {strategy_dir}")
        
    def plot_equity_curve(self, daily_performance, output_file):
        """
        绘制资金曲线
        
        参数:
            daily_performance (list): 每日表现数据
            output_file (str): 输出文件路径
        """
        # 创建DataFrame
        df = pd.DataFrame(daily_performance)
        
        # 设置日期为索引
        df['日期'] = pd.to_datetime(df['日期'])
        df.set_index('日期', inplace=True)
        
        # 创建图表
        plt.figure(figsize=(12, 6))
        
        # 绘制总资产曲线
        plt.plot(df.index, df['总资产'], label='总资产')
        
        # 设置图表标题和标签
        plt.title('策略资金曲线')
        plt.xlabel('日期')
        plt.ylabel('资金')
        plt.grid(True)
        plt.legend()
        
        # 保存图表
        plt.savefig(output_file)
        plt.close()
        
    def backtest_multiple_strategies(self, strategies, start_date=None, end_date=None, callback=None):
        """
        回测多个策略
        
        参数:
            strategies (list): 策略列表，每个策略是一个字典，包含name和conditions
            start_date (str): 回测开始日期，格式：YYYY-MM-DD
            end_date (str): 回测结束日期，格式：YYYY-MM-DD
            callback (function): 回调函数，用于更新进度和日志
            
        返回:
            list: 回测结果列表
        """
        results = []
        
        for i, strategy in enumerate(strategies):
            # 更新进度
            if callback:
                progress = (i + 1) / len(strategies) * 100
                callback("progress", progress)
                callback("info", f"正在回测策略: {strategy['name']} ({i+1}/{len(strategies)})")
                
            # 回测策略
            result = self.backtest_strategy(strategy['conditions'], start_date, end_date)
            
            if result:
                # 保存回测结果
                self.save_backtest_results(result, strategy['name'])
                
                # 添加策略信息
                result['strategy'] = strategy
                
                # 添加到结果列表
                results.append(result)
                
                # 更新日志
                if callback:
                    callback("info", f"策略 {strategy['name']} 回测完成，总收益率: {result['summary']['总收益率(%)']:.2f}%，胜率: {result['summary']['胜率(%)']:.2f}%")
            else:
                # 更新日志
                if callback:
                    callback("warning", f"策略 {strategy['name']} 回测失败")
        
        return results
