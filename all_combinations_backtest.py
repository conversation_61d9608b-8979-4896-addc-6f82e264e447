#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
所有特征组合回测系统
作者: Augment AI
版本: 1.0.0

该脚本对所有可能的特征组合进行回测，并将结果保存到Excel文件中。
"""

import pandas as pd
import numpy as np
import os
import itertools
import time
from datetime import datetime
from tqdm import tqdm
import pickle

# 创建结果目录
results_dir = 'all_combinations_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
        
        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")
        
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def backtest_feature_combination(data, feature_combination, start_date, end_date, initial_capital=100000):
    """回测特征组合策略"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    # 初始化结果
    results = {
        'feature_combination': feature_combination,
        'feature_count': len(feature_combination),
        'initial_capital': initial_capital,
        'current_capital': initial_capital,
        'total_trades': 0,
        'win_count': 0,
        'daily_returns': [],
        'daily_win_rates': [],
        'daily_trade_counts': [],
        'capital_history': [initial_capital],
        'dates': [trading_dates[0]]
    }
    
    # 对每个交易日进行回测
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 获取当日数据
        daily_data = date_range_data[date_range_data['日期'] == current_date]
        
        # 应用特征组合条件
        selected = daily_data.copy()
        for feature in feature_combination:
            if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', 
                         '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势', 
                         '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势', 
                         '连续技术强度10天数趋势', '连续技术强度3天数价格趋势', 
                         '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势', 
                         '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势', 
                         '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
                # 二元特征，使用==1条件
                selected = selected[selected[feature] == 1]
            elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                # 连续值特征，使用>=中位数条件
                median_value = np.median(daily_data[feature])
                selected = selected[selected[feature] >= median_value]
            elif feature == '看涨技术指标数量':
                # 看涨技术指标数量，使用>=3条件
                selected = selected[selected[feature] >= 3]
        
        # 如果有推荐的股票，模拟买入
        if len(selected) > 0:
            # 获取推荐股票的代码
            recommended_codes = selected['股票代码'].tolist()
            
            # 获取次日这些股票的数据
            next_day_data = date_range_data[
                (date_range_data['日期'] == next_date) & 
                (date_range_data['股票代码'].isin(recommended_codes))
            ]
            
            # 如果次日有这些股票的数据
            if len(next_day_data) > 0:
                # 计算平均涨跌幅
                avg_return = next_day_data['涨跌幅'].mean() / 100  # 转换为小数
                
                # 计算盈利股票数量
                win_stocks = len(next_day_data[next_day_data['涨跌幅'] > 0])
                win_rate = win_stocks / len(next_day_data) * 100
                
                # 更新资金
                results['current_capital'] *= (1 + avg_return)
                
                # 更新统计数据
                results['total_trades'] += len(next_day_data)
                results['win_count'] += win_stocks
                results['daily_returns'].append(avg_return * 100)  # 转换为百分比
                results['daily_win_rates'].append(win_rate)
                results['daily_trade_counts'].append(len(next_day_data))
                results['capital_history'].append(results['current_capital'])
                results['dates'].append(next_date)
            else:
                # 如果次日没有这些股票的数据，资金保持不变
                results['capital_history'].append(results['current_capital'])
                results['dates'].append(next_date)
                results['daily_returns'].append(0)
                results['daily_win_rates'].append(0)
                results['daily_trade_counts'].append(0)
        else:
            # 如果没有推荐股票，资金保持不变
            results['capital_history'].append(results['current_capital'])
            results['dates'].append(next_date)
            results['daily_returns'].append(0)
            results['daily_win_rates'].append(0)
            results['daily_trade_counts'].append(0)
    
    # 计算最终统计结果
    if results['total_trades'] > 0:
        results['win_rate'] = results['win_count'] / results['total_trades'] * 100
        results['total_return_pct'] = (results['current_capital'] / initial_capital - 1) * 100
        results['avg_daily_return'] = np.mean([r for r in results['daily_returns'] if r != 0])
        results['avg_daily_trades'] = np.mean([count for count in results['daily_trade_counts'] if count > 0])
        results['trading_days'] = len([count for count in results['daily_trade_counts'] if count > 0])
        results['total_days'] = len(trading_dates) - 1
        results['trading_frequency'] = results['trading_days'] / results['total_days'] * 100
    else:
        results['win_rate'] = 0
        results['total_return_pct'] = 0
        results['avg_daily_return'] = 0
        results['avg_daily_trades'] = 0
        results['trading_days'] = 0
        results['total_days'] = len(trading_dates) - 1
        results['trading_frequency'] = 0
    
    return results

def backtest_all_combinations(data, features, start_date, end_date, min_features=2, max_features=5, save_interim=True, interim_frequency=10):
    """回测所有特征组合"""
    print(f"回测从{min_features}到{max_features}个特征的所有组合...")
    
    # 初始化结果
    all_results = []
    
    # 对每个特征数量进行回测
    for r in range(min_features, max_features + 1):
        # 生成r个特征的所有组合
        combinations = list(itertools.combinations(features, r))
        print(f"{r}特征组合数量: {len(combinations)}")
        
        # 回测每个组合
        for i, combination in enumerate(tqdm(combinations, desc=f"{r}特征组合")):
            result = backtest_feature_combination(data, combination, start_date, end_date)
            
            # 只保留有交易的结果
            if result['total_trades'] > 0:
                all_results.append(result)
            
            # 定期保存中间结果
            if save_interim and (i + 1) % interim_frequency == 0:
                # 按总收益率排序
                sorted_results = sorted(all_results, key=lambda x: x['total_return_pct'], reverse=True)
                
                # 保存中间结果
                interim_file = f"{results_dir}/interim_results_{r}features_{i+1}.pkl"
                with open(interim_file, 'wb') as f:
                    pickle.dump(sorted_results, f)
                
                print(f"已回测 {r}特征组合 {i+1}/{len(combinations)} 个，中间结果已保存到 {interim_file}")
                
                # 生成中间Excel报告
                interim_excel = f"{results_dir}/interim_results_{r}features_{i+1}.xlsx"
                create_excel_report(sorted_results, interim_excel)
                print(f"中间Excel报告已保存到 {interim_excel}")
        
        # 保存每个特征数量的结果
        feature_count_results = [r for r in all_results if r['feature_count'] == r]
        if feature_count_results:
            # 按总收益率排序
            sorted_results = sorted(feature_count_results, key=lambda x: x['total_return_pct'], reverse=True)
            
            # 保存结果
            feature_count_file = f"{results_dir}/{r}feature_results.pkl"
            with open(feature_count_file, 'wb') as f:
                pickle.dump(sorted_results, f)
            
            # 生成Excel报告
            feature_count_excel = f"{results_dir}/{r}feature_results.xlsx"
            create_excel_report(sorted_results, feature_count_excel)
            
            print(f"{r}特征组合回测完成，结果已保存到 {feature_count_file} 和 {feature_count_excel}")
    
    # 按总收益率排序
    all_results = sorted(all_results, key=lambda x: x['total_return_pct'], reverse=True)
    
    # 保存最终结果
    final_file = f"{results_dir}/all_combination_results.pkl"
    with open(final_file, 'wb') as f:
        pickle.dump(all_results, f)
    
    print(f"所有组合回测完成，结果已保存到 {final_file}")
    
    return all_results

def create_excel_report(results, output_file):
    """创建Excel报告"""
    print(f"创建Excel报告: {output_file}")
    
    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 创建汇总表格
        create_summary_sheet(results, writer)
        
        # 创建按特征数量分组的统计表格
        create_grouped_stats_sheet(results, writer)
    
    print(f"Excel报告已保存到: {output_file}")

def create_summary_sheet(results, writer):
    """创建汇总表格"""
    # 创建汇总数据
    summary_data = []
    
    for result in results:
        feature_str = ', '.join(result['feature_combination'])
        summary_data.append({
            '策略组合': feature_str,
            '特征数量': result['feature_count'],
            '总收益率(%)': result['total_return_pct'],
            '平均收益率(%)': result['avg_daily_return'],
            '平均胜率(%)': result['win_rate'],
            '平均每日交易笔数': result['avg_daily_trades'],
            '总交易笔数': result['total_trades'],
            '交易天数': result['trading_days'],
            '总天数': result['total_days'],
            '交易频率(%)': result['trading_frequency']
        })
    
    # 转换为DataFrame
    summary_df = pd.DataFrame(summary_data)
    
    # 写入Excel
    summary_df.to_excel(writer, sheet_name='策略汇总', index=False)

def create_grouped_stats_sheet(results, writer):
    """创建按特征数量分组的统计表格"""
    # 创建汇总数据
    summary_data = []
    
    for result in results:
        summary_data.append({
            '特征数量': result['feature_count'],
            '总收益率(%)': result['total_return_pct'],
            '平均收益率(%)': result['avg_daily_return'],
            '平均胜率(%)': result['win_rate'],
            '平均每日交易笔数': result['avg_daily_trades'],
            '总交易笔数': result['total_trades']
        })
    
    # 转换为DataFrame
    summary_df = pd.DataFrame(summary_data)
    
    # 按特征数量分组
    grouped = summary_df.groupby('特征数量').agg({
        '总收益率(%)': ['mean', 'std', 'max', 'min'],
        '平均收益率(%)': ['mean', 'std'],
        '平均胜率(%)': ['mean', 'std'],
        '平均每日交易笔数': 'mean',
        '总交易笔数': 'mean'
    })
    
    # 写入Excel
    grouped.to_excel(writer, sheet_name='特征数量统计')

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)
    
    # 设置回测参数
    start_date = '2025-03-01'  # 使用更长的历史数据
    end_date = '2025-05-15'
    
    print(f"回测周期: {start_date} 至 {end_date}")
    
    # 定义要探索的特征
    features = [
        '技术强度',
        '连续技术强度5天数',
        '连续技术强度3天数',
        '连续技术强度10天数',
        '看涨技术指标数量',
        '涨跌幅趋势',
        '技术强度趋势',
        '连续技术强度5天数趋势',
        '连续技术强度5天数价格趋势',
        '连续技术强度5天数涨跌幅趋势',
        '技术指标_均线多头排列',
        '技术指标_MACD金叉',
        '技术指标_RSI反弹',
        '技术指标_KDJ金叉',
        '技术指标_布林带突破',
        '开盘涨跌'
    ]
    
    # 回测所有特征组合
    results = backtest_all_combinations(
        df, features, start_date, end_date, 
        min_features=2, max_features=5,
        save_interim=True, interim_frequency=10
    )
    
    # 创建最终Excel报告
    output_file = f"{results_dir}/所有特征组合回测结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    create_excel_report(results, output_file)
