"""
趋势股整理程序

提供图形界面，方便用户计算趋势股和迁移趋势股数据
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import datetime
import pandas as pd

# 添加当前目录到Python路径，确保能够导入模块
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

# 导入模块
try:
    import tech_strength_manager as tsm
    import stock_data_manager as sdm
    from calculate_tech_strength import process_stock_data
    from migrate_tech_strength import migrate_tech_strength_data
except ImportError as e:
    print(f"导入模块时出错: {e}")
    sys.exit(1)

class TechStrengthGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("趋势股整理程序")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 设置默认目录
        self.data_dir_var = tk.StringVar(value=sdm.base_dir)
        self.output_dir_var = tk.StringVar(value=tsm.base_dir)

        # 创建主框架
        main_frame = ttk.Frame(root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建选项卡
        tab_control = ttk.Notebook(main_frame)

        # 创建计算技术强度选项卡
        calculate_tab = ttk.Frame(tab_control)
        tab_control.add(calculate_tab, text="计算趋势股")

        # 创建迁移数据选项卡
        migrate_tab = ttk.Frame(tab_control)
        tab_control.add(migrate_tab, text="迁移数据")

        # 创建设置选项卡
        settings_tab = ttk.Frame(tab_control)
        tab_control.add(settings_tab, text="设置")

        tab_control.pack(fill=tk.BOTH, expand=True)

        # 设置计算技术强度选项卡
        self.setup_calculate_tab(calculate_tab)

        # 设置迁移数据选项卡
        self.setup_migrate_tab(migrate_tab)

        # 设置设置选项卡
        self.setup_settings_tab(settings_tab)

        # 创建日志框架
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # 创建日志文本框
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, width=80, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 创建滚动条
        scrollbar = ttk.Scrollbar(self.log_text, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

        # 更新目录
        self.update_directories()

        # 记录日志
        self.log("趋势股整理程序已启动")
        self.log(f"股票数据目录: {sdm.base_dir}")
        self.log(f"技术强度数据目录: {tsm.base_dir}")

    def setup_calculate_tab(self, parent):
        """设置计算趋势股选项卡"""
        # 创建日期范围框架
        date_frame = ttk.LabelFrame(parent, text="日期范围", padding=10)
        date_frame.pack(fill=tk.X, pady=5)

        # 开始日期
        ttk.Label(date_frame, text="开始日期:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.start_date_var = tk.StringVar(value=(datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%Y-%m-%d"))
        start_date_entry = ttk.Entry(date_frame, textvariable=self.start_date_var, width=15)
        start_date_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # 结束日期
        ttk.Label(date_frame, text="结束日期:").grid(row=0, column=2, sticky=tk.W, pady=5)
        self.end_date_var = tk.StringVar(value=datetime.datetime.now().strftime("%Y-%m-%d"))
        end_date_entry = ttk.Entry(date_frame, textvariable=self.end_date_var, width=15)
        end_date_entry.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)

        # 按钮框架
        button_frame = ttk.Frame(parent, padding=10)
        button_frame.pack(fill=tk.X, pady=5)

        # 计算按钮
        calculate_button = ttk.Button(button_frame, text="计算趋势股", command=self.calculate_tech_strength)
        calculate_button.pack(side=tk.LEFT, padx=5)

        # 查看结果按钮
        view_button = ttk.Button(button_frame, text="查看结果", command=self.view_tech_strength)
        view_button.pack(side=tk.LEFT, padx=5)

    def setup_migrate_tab(self, parent):
        """设置迁移数据选项卡"""
        # 创建文件选择框架
        file_frame = ttk.LabelFrame(parent, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, pady=5)

        # 输入文件
        ttk.Label(file_frame, text="输入文件:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.input_file_var = tk.StringVar()
        input_file_entry = ttk.Entry(file_frame, textvariable=self.input_file_var, width=50)
        input_file_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)

        # 浏览按钮
        browse_button = ttk.Button(file_frame, text="浏览...", command=self.browse_input_file)
        browse_button.grid(row=0, column=2, padx=5, pady=5)

        # 按钮框架
        button_frame = ttk.Frame(parent, padding=10)
        button_frame.pack(fill=tk.X, pady=5)

        # 迁移按钮
        migrate_button = ttk.Button(button_frame, text="迁移数据", command=self.migrate_tech_strength)
        migrate_button.pack(side=tk.LEFT, padx=5)

    def setup_settings_tab(self, parent):
        """设置设置选项卡"""
        # 创建目录设置框架
        dir_frame = ttk.LabelFrame(parent, text="目录设置", padding=10)
        dir_frame.pack(fill=tk.X, pady=5)

        # 股票数据目录
        ttk.Label(dir_frame, text="股票数据目录:").grid(row=0, column=0, sticky=tk.W, pady=5)
        data_dir_entry = ttk.Entry(dir_frame, textvariable=self.data_dir_var, width=50)
        data_dir_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)

        # 浏览按钮
        data_dir_button = ttk.Button(dir_frame, text="浏览...", command=self.browse_data_dir)
        data_dir_button.grid(row=0, column=2, padx=5, pady=5)

        # 技术强度数据目录
        ttk.Label(dir_frame, text="技术强度数据目录:").grid(row=1, column=0, sticky=tk.W, pady=5)
        output_dir_entry = ttk.Entry(dir_frame, textvariable=self.output_dir_var, width=50)
        output_dir_entry.grid(row=1, column=1, sticky=tk.W+tk.E, padx=5, pady=5)

        # 浏览按钮
        output_dir_button = ttk.Button(dir_frame, text="浏览...", command=self.browse_output_dir)
        output_dir_button.grid(row=1, column=2, padx=5, pady=5)

        # 按钮框架
        button_frame = ttk.Frame(parent, padding=10)
        button_frame.pack(fill=tk.X, pady=5)

        # 应用按钮
        apply_button = ttk.Button(button_frame, text="应用", command=self.apply_settings)
        apply_button.pack(side=tk.LEFT, padx=5)

    def log(self, message, level="INFO"):
        """记录日志"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"{timestamp} [{level}] {message}\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)

        # 根据日志级别设置颜色
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_message)+1}c", "end-1c")
            self.log_text.tag_config("error", foreground="red")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_message)+1}c", "end-1c")
            self.log_text.tag_config("success", foreground="green")
        elif level == "WARNING":
            self.log_text.tag_add("warning", f"end-{len(log_message)+1}c", "end-1c")
            self.log_text.tag_config("warning", foreground="orange")

    def update_directories(self):
        """更新目录"""
        # 更新股票数据目录
        sdm.set_base_dir(self.data_dir_var.get())

        # 更新技术强度数据目录
        tsm.set_base_dir(self.output_dir_var.get())

    def browse_input_file(self):
        """浏览输入文件"""
        file_path = filedialog.askopenfilename(
            initialdir=self.output_dir_var.get(),
            title="选择输入文件",
            filetypes=(("Excel文件", "*.xlsx"), ("所有文件", "*.*"))
        )

        if file_path:
            self.input_file_var.set(file_path)
            self.log(f"已选择输入文件: {file_path}")

    def browse_data_dir(self):
        """浏览股票数据目录"""
        directory = filedialog.askdirectory(
            initialdir=self.data_dir_var.get(),
            title="选择股票数据目录"
        )

        if directory:
            self.data_dir_var.set(directory)
            self.log(f"已选择股票数据目录: {directory}")

    def browse_output_dir(self):
        """浏览技术强度数据目录"""
        directory = filedialog.askdirectory(
            initialdir=self.output_dir_var.get(),
            title="选择技术强度数据目录"
        )

        if directory:
            self.output_dir_var.set(directory)
            self.log(f"已选择技术强度数据目录: {directory}")

    def apply_settings(self):
        """应用设置"""
        try:
            # 更新目录
            self.update_directories()

            self.log(f"已应用设置", "SUCCESS")
            self.log(f"股票数据目录: {sdm.base_dir}")
            self.log(f"技术强度数据目录: {tsm.base_dir}")

            # 检查股票数据目录
            self.check_data_directories()

            messagebox.showinfo("成功", "设置已应用")
        except Exception as e:
            self.log(f"应用设置时出错: {e}", "ERROR")
            messagebox.showerror("错误", f"应用设置时出错: {e}")

    def check_data_directories(self):
        """检查数据目录"""
        # 检查股票数据目录
        self.log(f"检查股票数据目录: {sdm.daily_data_dir}")
        if os.path.exists(sdm.daily_data_dir):
            files = os.listdir(sdm.daily_data_dir)
            stock_data_files = [f for f in files if f.startswith("stock_data_") and f.endswith(".xlsx")]
            self.log(f"股票数据目录中有 {len(stock_data_files)} 个股票数据文件")
            if stock_data_files:
                self.log(f"股票数据文件示例: {stock_data_files[:3]}")

                # 获取可用日期
                available_dates = sdm.get_available_dates()
                self.log(f"可用日期数量: {len(available_dates)}")
                if available_dates:
                    self.log(f"可用日期范围: {available_dates[0].strftime('%Y-%m-%d')} 到 {available_dates[-1].strftime('%Y-%m-%d')}")

                    # 加载一个日期的数据进行检查
                    sample_date = available_dates[-1]
                    self.log(f"加载示例日期 {sample_date.strftime('%Y-%m-%d')} 的数据...")
                    sample_data = sdm.load_daily_data(sample_date)
                    if not sample_data.empty:
                        self.log(f"成功加载示例数据，共 {len(sample_data)} 条记录")
                        self.log(f"数据列: {sample_data.columns.tolist()}")
                    else:
                        self.log(f"示例数据为空", "WARNING")
                else:
                    self.log(f"没有找到可用日期", "WARNING")
            else:
                self.log(f"股票数据目录中没有股票数据文件", "WARNING")
        else:
            self.log(f"股票数据目录不存在", "WARNING")

        # 检查技术强度数据目录
        self.log(f"检查技术强度数据目录: {tsm.daily_tech_strength_dir}")
        if os.path.exists(tsm.daily_tech_strength_dir):
            files = os.listdir(tsm.daily_tech_strength_dir)
            tech_strength_files = [f for f in files if f.startswith("tech_strength_") and f.endswith(".xlsx")]
            self.log(f"技术强度数据目录中有 {len(tech_strength_files)} 个技术强度数据文件")
            if tech_strength_files:
                self.log(f"技术强度数据文件示例: {tech_strength_files[:3]}")

                # 获取可用日期
                available_dates = tsm.get_available_dates()
                self.log(f"可用日期数量: {len(available_dates)}")
                if available_dates:
                    self.log(f"可用日期范围: {available_dates[0].strftime('%Y-%m-%d')} 到 {available_dates[-1].strftime('%Y-%m-%d')}")
                else:
                    self.log(f"没有找到可用日期", "WARNING")
            else:
                self.log(f"技术强度数据目录中没有技术强度数据文件", "WARNING")
        else:
            self.log(f"技术强度数据目录不存在", "WARNING")
            # 创建目录
            os.makedirs(tsm.daily_tech_strength_dir, exist_ok=True)
            self.log(f"已创建技术强度数据目录: {tsm.daily_tech_strength_dir}")

    def calculate_tech_strength(self):
        """计算趋势股"""
        try:
            # 获取日期范围
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()

            # 更新目录
            self.update_directories()

            self.log(f"开始计算趋势股，日期范围: {start_date} 到 {end_date}")

            # 创建线程
            thread = threading.Thread(
                target=self._calculate_tech_strength_thread,
                args=(start_date, end_date)
            )
            thread.daemon = True
            thread.start()
        except Exception as e:
            self.log(f"计算趋势股时出错: {e}", "ERROR")
            messagebox.showerror("错误", f"计算趋势股时出错: {e}")

    def _calculate_tech_strength_thread(self, start_date, end_date):
        """计算趋势股线程"""
        try:
            # 重定向标准输出到StringIO对象
            import io
            import sys
            output = io.StringIO()
            original_stdout = sys.stdout
            sys.stdout = output

            # 计算技术强度
            success = process_stock_data(start_date, end_date)

            # 恢复标准输出
            sys.stdout = original_stdout

            # 获取输出内容
            output_str = output.getvalue()

            # 记录输出内容
            for line in output_str.split('\n'):
                if line.strip():
                    self.log(line.strip())

            if success:
                self.log(f"趋势股计算成功", "SUCCESS")
                messagebox.showinfo("成功", "趋势股计算成功")
            else:
                self.log(f"趋势股计算失败", "ERROR")
                messagebox.showerror("错误", "趋势股计算失败，请查看日志获取详细信息")
        except Exception as e:
            self.log(f"计算趋势股时出错: {e}", "ERROR")
            import traceback
            error_traceback = traceback.format_exc()
            for line in error_traceback.split('\n'):
                if line.strip():
                    self.log(line.strip(), "ERROR")
            messagebox.showerror("错误", f"计算趋势股时出错: {e}")

    def migrate_tech_strength(self):
        """迁移技术强度数据"""
        try:
            # 获取输入文件
            input_file = self.input_file_var.get()

            if not input_file:
                self.log(f"请选择输入文件", "ERROR")
                messagebox.showerror("错误", "请选择输入文件")
                return

            # 更新目录
            self.update_directories()

            self.log(f"开始迁移技术强度数据，输入文件: {input_file}")

            # 创建线程
            thread = threading.Thread(
                target=self._migrate_tech_strength_thread,
                args=(input_file,)
            )
            thread.daemon = True
            thread.start()
        except Exception as e:
            self.log(f"迁移技术强度数据时出错: {e}", "ERROR")
            messagebox.showerror("错误", f"迁移技术强度数据时出错: {e}")

    def _migrate_tech_strength_thread(self, input_file):
        """迁移技术强度数据线程"""
        try:
            # 迁移技术强度数据
            success = migrate_tech_strength_data(input_file)

            if success:
                self.log(f"技术强度数据迁移成功", "SUCCESS")
                messagebox.showinfo("成功", "技术强度数据迁移成功")
            else:
                self.log(f"技术强度数据迁移失败", "ERROR")
                messagebox.showerror("错误", "技术强度数据迁移失败")
        except Exception as e:
            self.log(f"迁移技术强度数据时出错: {e}", "ERROR")
            messagebox.showerror("错误", f"迁移技术强度数据时出错: {e}")

    def view_tech_strength(self):
        """查看技术强度数据"""
        try:
            # 更新目录
            self.update_directories()

            # 获取可用日期
            available_dates = tsm.get_available_dates()

            if not available_dates:
                self.log(f"没有找到可用的技术强度数据", "ERROR")
                messagebox.showerror("错误", "没有找到可用的技术强度数据")
                return

            # 创建查看窗口
            view_window = tk.Toplevel(self.root)
            view_window.title("趋势股数据")
            view_window.geometry("800x600")

            # 创建日期选择框架
            date_frame = ttk.Frame(view_window, padding=10)
            date_frame.pack(fill=tk.X)

            # 日期选择
            ttk.Label(date_frame, text="选择日期:").pack(side=tk.LEFT, padx=5)
            date_var = tk.StringVar(value=available_dates[-1].strftime("%Y-%m-%d"))
            date_combo = ttk.Combobox(date_frame, textvariable=date_var, width=15)
            date_combo['values'] = [d.strftime("%Y-%m-%d") for d in available_dates]
            date_combo.pack(side=tk.LEFT, padx=5)

            # 查看按钮
            view_button = ttk.Button(date_frame, text="查看", command=lambda: self._load_tech_strength_data(date_var.get(), data_text))
            view_button.pack(side=tk.LEFT, padx=5)

            # 创建数据文本框
            data_frame = ttk.Frame(view_window, padding=10)
            data_frame.pack(fill=tk.BOTH, expand=True)

            data_text = tk.Text(data_frame, wrap=tk.WORD, width=80, height=20)
            data_text.pack(fill=tk.BOTH, expand=True)

            # 创建滚动条
            scrollbar = ttk.Scrollbar(data_text, command=data_text.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            data_text.config(yscrollcommand=scrollbar.set)

            # 加载最新日期的数据
            self._load_tech_strength_data(date_var.get(), data_text)
        except Exception as e:
            self.log(f"查看技术强度数据时出错: {e}", "ERROR")
            messagebox.showerror("错误", f"查看技术强度数据时出错: {e}")

    def _load_tech_strength_data(self, date_str, text_widget):
        """加载技术强度数据"""
        try:
            # 清空文本框
            text_widget.delete(1.0, tk.END)

            # 加载数据
            df = tsm.load_daily_tech_strength(date_str)

            if df.empty:
                text_widget.insert(tk.END, f"日期 {date_str} 没有技术强度数据")
                return

            # 显示数据
            text_widget.insert(tk.END, f"日期: {date_str}\n")
            text_widget.insert(tk.END, f"记录数: {len(df)}\n\n")

            # 显示技术强度分布
            text_widget.insert(tk.END, "技术强度分布:\n")
            strength_counts = df['技术强度'].value_counts().sort_index()
            for strength, count in strength_counts.items():
                if strength == -100:
                    text_widget.insert(tk.END, f"技术强度 {strength} (不满足条件): {count} 只股票\n")
                else:
                    text_widget.insert(tk.END, f"技术强度 {strength}: {count} 只股票\n")

            # 显示技术强度为100的股票
            text_widget.insert(tk.END, "\n技术强度为100的股票:\n")
            strength_100 = df[df['技术强度'] == 100]
            if len(strength_100) > 0:
                for i, row in strength_100.iterrows():
                    text_widget.insert(tk.END, f"{row['股票代码']} {row['股票名称']}\n")
            else:
                text_widget.insert(tk.END, "没有技术强度为100的股票\n")

            # 显示技术强度为-100的股票数量
            text_widget.insert(tk.END, f"\n不满足条件的股票数量: {len(df[df['技术强度'] == -100])}\n")
        except Exception as e:
            text_widget.insert(tk.END, f"加载技术强度数据时出错: {e}")

def main():
    root = tk.Tk()
    app = TechStrengthGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
