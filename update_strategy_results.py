#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
更新策略回测结果
作者: Augment AI
版本: 1.0.0

该脚本用于基于真实股票数据进行回测，并更新策略汇总Excel文件中的统计结果。
"""

import os
import pandas as pd
import numpy as np
import glob
from datetime import datetime, timedelta
import time

def backtest_strategy(strategy_info, stock_data, start_date=None, end_date=None):
    """
    回测策略

    参数:
        strategy_info (Series): 策略信息
        stock_data (DataFrame): 股票历史数据
        start_date (str): 回测开始日期，格式：YYYY-MM-DD
        end_date (str): 回测结束日期，格式：YYYY-MM-DD

    返回:
        dict: 回测结果
    """
    print(f"正在回测策略 {strategy_info['策略编号']}: {strategy_info['策略条件描述']}")

    # 转换日期格式
    if start_date:
        start_date = pd.to_datetime(start_date)
    else:
        start_date = stock_data['日期'].min()

    if end_date:
        end_date = pd.to_datetime(end_date)
    else:
        end_date = stock_data['日期'].max()

    # 筛选日期范围内的数据
    mask = (stock_data['日期'] >= start_date) & (stock_data['日期'] <= end_date)
    data = stock_data[mask].copy()

    # 如果数据为空，返回空结果
    if len(data) == 0:
        print("选定日期范围内没有数据")
        return None

    # 获取日期范围内的交易日
    trading_dates = sorted(data['日期'].unique())

    # 初始化结果
    results = {
        'daily_performance': [],
        'trades': [],
        'summary': {}
    }

    # 初始化资金
    initial_capital = 1000000  # 初始资金100万
    current_capital = initial_capital

    # 初始化持仓
    positions = {}

    # 解析策略条件
    conditions_str = strategy_info['策略条件描述']
    conditions_list = conditions_str.split(' AND ')

    # 遍历每个交易日
    for i, date in enumerate(trading_dates[:-1]):  # 最后一天不买入
        # 当前日期的数据
        current_day_data = data[data['日期'] == date]

        # 下一个交易日
        next_date = trading_dates[i + 1]
        next_day_data = data[data['日期'] == next_date]

        # 卖出昨日持仓（次日早盘卖出）
        if positions:
            for stock_code, position in list(positions.items()):
                # 获取今日该股票的数据
                next_stock_data = next_day_data[next_day_data['证券代码'] == stock_code]

                if len(next_stock_data) > 0:
                    # 使用开盘价作为卖出价格
                    sell_price = next_stock_data['开盘价'].values[0]

                    # 计算卖出收益
                    sell_value = position['quantity'] * sell_price
                    profit = sell_value - position['cost']
                    profit_rate = profit / position['cost'] * 100

                    # 更新资金
                    current_capital += sell_value

                    # 记录交易
                    results['trades'].append({
                        '日期': next_date,
                        '交易时间': '09:30',  # 早盘开盘时间
                        '股票代码': stock_code,
                        '操作': '卖出',
                        '价格': sell_price,
                        '数量': position['quantity'],
                        '金额': sell_value,
                        '收益': profit,
                        '收益率(%)': profit_rate
                    })

                    # 从持仓中移除
                    del positions[stock_code]
                else:
                    # 如果今日没有该股票的数据，假设以昨日价格卖出
                    sell_price = position['price']
                    sell_value = position['quantity'] * sell_price
                    profit = sell_value - position['cost']
                    profit_rate = profit / position['cost'] * 100

                    # 更新资金
                    current_capital += sell_value

                    # 记录交易
                    results['trades'].append({
                        '日期': next_date,
                        '交易时间': '09:30',  # 早盘开盘时间
                        '股票代码': stock_code,
                        '操作': '卖出(无数据)',
                        '价格': sell_price,
                        '数量': position['quantity'],
                        '金额': sell_value,
                        '收益': profit,
                        '收益率(%)': profit_rate
                    })

                    # 从持仓中移除
                    del positions[stock_code]

        # 应用策略条件筛选股票
        filtered_data = current_day_data.copy()

        for condition in conditions_list:
            if '大于等于' in condition:
                feature, threshold = condition.split('大于等于')
                feature = feature.strip()
                threshold = float(threshold.strip())
                if feature in filtered_data.columns:
                    filtered_data = filtered_data[filtered_data[feature] >= threshold]
            elif '为 1（是）' in condition:
                feature = condition.split('为')[0].strip()
                if feature in filtered_data.columns:
                    filtered_data = filtered_data[filtered_data[feature] == 1]

        # 获取符合条件的股票
        selected_stocks = filtered_data.copy()

        # 买入股票（次日早盘买入）
        if len(selected_stocks) > 0:
            # 计算每只股票的买入金额
            max_stocks = 10  # 最多买入10只股票
            num_stocks = min(len(selected_stocks), max_stocks)
            per_stock_value = current_capital / num_stocks

            # 买入股票
            for _, stock in selected_stocks.head(max_stocks).iterrows():
                stock_code = stock['证券代码']

                # 获取次日该股票的数据
                next_stock_data = next_day_data[next_day_data['证券代码'] == stock_code]

                if len(next_stock_data) > 0:
                    # 使用次日开盘价作为买入价格
                    buy_price = next_stock_data['开盘价'].values[0]

                    # 计算买入数量（整百股）
                    quantity = int(per_stock_value / buy_price / 100) * 100

                    if quantity > 0:
                        # 计算买入金额
                        cost = quantity * buy_price

                        # 更新资金
                        current_capital -= cost

                        # 添加到持仓
                        positions[stock_code] = {
                            'quantity': quantity,
                            'price': buy_price,
                            'cost': cost,
                            'buy_date': next_date
                        }

                        # 记录交易
                        results['trades'].append({
                            '日期': next_date,
                            '交易时间': '09:30',  # 早盘开盘时间
                            '股票代码': stock_code,
                            '操作': '买入',
                            '价格': buy_price,
                            '数量': quantity,
                            '金额': cost,
                            '收益': 0,
                            '收益率(%)': 0
                        })

        # 计算当日总资产
        total_position_value = sum([position['quantity'] * position['price'] for position in positions.values()])
        total_assets = current_capital + total_position_value

        # 计算当日收益率
        if i == 0:
            daily_return = 0
        else:
            prev_assets = results['daily_performance'][-1]['总资产']
            daily_return = (total_assets - prev_assets) / prev_assets * 100

        # 记录每日表现
        results['daily_performance'].append({
            '日期': date,
            '现金': current_capital,
            '持仓市值': total_position_value,
            '总资产': total_assets,
            '日收益率(%)': daily_return,
            '持仓数量': len(positions)
        })

    # 计算汇总统计
    if results['daily_performance']:
        # 计算总收益率
        initial_assets = initial_capital
        final_assets = results['daily_performance'][-1]['总资产']
        total_return = (final_assets - initial_assets) / initial_assets * 100

        # 计算年化收益率
        days = (trading_dates[-1] - trading_dates[0]).days
        annual_return = total_return * 365 / days if days > 0 else 0

        # 计算最大回撤
        max_drawdown = 0
        peak = initial_assets

        for day in results['daily_performance']:
            if day['总资产'] > peak:
                peak = day['总资产']
            drawdown = (peak - day['总资产']) / peak * 100
            max_drawdown = max(max_drawdown, drawdown)

        # 计算胜率
        if results['trades']:
            win_trades = [trade for trade in results['trades'] if trade['操作'].startswith('卖出') and trade['收益'] > 0]
            win_rate = len(win_trades) / len([trade for trade in results['trades'] if trade['操作'].startswith('卖出')]) * 100 if len([trade for trade in results['trades'] if trade['操作'].startswith('卖出')]) > 0 else 0
        else:
            win_rate = 0

        # 计算平均每日交易笔数
        daily_trades = {}
        for trade in results['trades']:
            date = trade['日期']
            if date not in daily_trades:
                daily_trades[date] = 0
            daily_trades[date] += 1

        avg_daily_trades = sum(daily_trades.values()) / len(trading_dates) if trading_dates else 0

        # 汇总统计
        results['summary'] = {
            '初始资金': initial_capital,
            '最终资金': final_assets,
            '总收益率(%)': total_return,
            '年化收益率(%)': annual_return,
            '最大回撤(%)': max_drawdown,
            '胜率(%)': win_rate,
            '总交易笔数': len(results['trades']),
            '平均每日交易笔数': avg_daily_trades,
            '交易天数': len(daily_trades),
            '总天数': len(trading_dates),
            '交易频率(%)': len(daily_trades) / len(trading_dates) * 100 if trading_dates else 0
        }

    print(f"回测完成，总收益率: {results['summary']['总收益率(%)']}%，胜率: {results['summary']['胜率(%)']}%")
    return results
