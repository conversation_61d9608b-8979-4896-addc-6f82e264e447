股票高胜率策略分析工具打包说明
==================================================

本文档将指导您如何将股票高胜率策略分析工具打包为可执行程序(.exe)。

一、准备工作
--------------------------------------------------

1. 确保您的系统已安装Python 3.6或更高版本
2. 确保您的系统已安装以下依赖项：
   - pandas
   - numpy
   - joblib
   - scikit-learn
   - cx_Freeze

二、打包步骤
--------------------------------------------------

1. 运行build_exe.bat脚本
   - 双击build_exe.bat文件
   - 或在命令行中运行：.\build_exe.bat

2. 等待打包完成
   - 脚本会自动安装依赖项
   - 创建必要的目录
   - 构建可执行文件
   - 创建启动脚本

3. 打包完成后，您将看到以下文件：
   - build目录：包含构建的可执行文件
   - run_gui.bat：启动图形界面版的脚本
   - run_console.bat：启动命令行版的脚本

三、使用说明
--------------------------------------------------

1. 图形界面版
   - 运行run_gui.bat
   - 在界面上设置参数
   - 点击"运行分析"按钮

2. 命令行版
   - 运行run_console.bat
   - 或直接运行：build\exe.win32-3.8\股票分析器.exe --data 股票明细.xlsx --start_date 2025-04-25 --end_date 2025-05-08 --output 分析结果.txt

四、注意事项
--------------------------------------------------

1. 确保股票明细.xlsx文件存在于程序目录中
2. 确保models目录存在且包含模型文件
3. 如果打包过程中出现错误，请检查依赖项是否正确安装
4. 打包后的程序可能需要较长时间启动，请耐心等待

五、常见问题
--------------------------------------------------

1. Q: 打包后的程序无法运行，提示缺少DLL文件
   A: 请确保您的系统已安装所有依赖项，并重新运行build_exe.bat

2. Q: 打包后的程序运行时提示找不到模型文件
   A: 请确保models目录存在且包含模型文件，并重新运行build_exe.bat

3. Q: 打包后的程序运行时提示找不到股票明细.xlsx
   A: 请确保股票明细.xlsx文件存在于程序目录中，并重新运行build_exe.bat

如有其他问题，请联系开发者。
