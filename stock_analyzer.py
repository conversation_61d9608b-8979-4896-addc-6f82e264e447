"""
股票高胜率策略分析工具

功能：
1. 回测高胜率策略
2. 生成股票推荐
3. 输出分析结果到TXT文档
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
import argparse
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header(title):
    """打印标题"""
    print("\n" + " " * 24 + title)
    print("=" * 80 + "\n")

def load_data(file_path):
    """加载股票数据"""
    print(f"加载股票数据: {file_path}")
    try:
        df = pd.read_excel(file_path)
        print(f"成功加载股票数据，共 {len(df)} 条记录")
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def preprocess_data(df):
    """预处理数据"""
    print("预处理数据...")

    # 确保日期列是datetime类型
    if '日期' in df.columns:
        df['日期'] = pd.to_datetime(df['日期'])

    # 生成目标变量：如果涨跌幅>0，则为1，否则为0
    if '涨跌幅' in df.columns and '是否盈利' not in df.columns:
        df['是否盈利'] = (df['涨跌幅'] > 0).astype(int)

    # 计算真实的连续技术强度5天数
    if '技术强度' in df.columns and '连续技术强度5天数' not in df.columns:
        print("计算连续技术强度5天数...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化连续技术强度5天数列
        df['连续技术强度5天数'] = 0

        # 对每个股票计算连续技术强度5天数
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取技术强度列
            strength = group['技术强度'].values

            # 计算连续5天的技术强度累积值
            for i in range(len(group)):
                if i < 4:  # 不足5天的情况
                    cumulative = sum(strength[max(0, i-4):i+1])
                else:  # 5天及以上的情况
                    cumulative = sum(strength[i-4:i+1])

                # 更新连续技术强度5天数
                df.loc[group.index[i], '连续技术强度5天数'] = cumulative

        print("连续技术强度5天数计算完成")

    # 计算技术强度趋势
    if '技术强度' in df.columns and '技术强度趋势' not in df.columns:
        print("计算技术强度趋势...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化技术强度趋势列
        df['技术强度趋势'] = 0

        # 对每个股票计算技术强度趋势
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取技术强度列
            strength = group['技术强度'].values

            # 计算连续3天的技术强度趋势
            for i in range(len(group)):
                if i >= 2:  # 至少需要3天数据
                    trend = 1 if (strength[i] > strength[i-1] and strength[i-1] > strength[i-2]) else 0
                    df.loc[group.index[i], '技术强度趋势'] = trend

        print("技术强度趋势计算完成")

    # 计算价格趋势
    if '当前价格' in df.columns and '价格趋势' not in df.columns:
        print("计算价格趋势...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化价格趋势列
        df['价格趋势'] = 0

        # 对每个股票计算价格趋势
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取价格列
            prices = group['当前价格'].values

            # 计算连续3天的价格趋势
            for i in range(len(group)):
                if i >= 2:  # 至少需要3天数据
                    trend = 1 if (prices[i] > prices[i-1] and prices[i-1] > prices[i-2]) else 0
                    df.loc[group.index[i], '价格趋势'] = trend

        print("价格趋势计算完成")

    # 计算涨跌幅趋势
    if '涨跌幅' in df.columns and '涨跌幅趋势' not in df.columns:
        print("计算涨跌幅趋势...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化涨跌幅趋势列
        df['涨跌幅趋势'] = 0

        # 对每个股票计算涨跌幅趋势
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取涨跌幅列
            changes = group['涨跌幅'].values

            # 计算连续3天的涨跌幅趋势
            for i in range(len(group)):
                if i >= 2:  # 至少需要3天数据
                    trend = 1 if (changes[i] > changes[i-1] and changes[i-1] > changes[i-2]) else 0
                    df.loc[group.index[i], '涨跌幅趋势'] = trend

        print("涨跌幅趋势计算完成")

    # 生成技术指标特征（如果不存在）
    # 均线多头排列
    if '技术指标_均线多头排列' not in df.columns:
        print("生成技术指标_均线多头排列...")
        # 这里我们使用一个简化的逻辑：如果技术强度>80且价格趋势=1，则认为是均线多头排列
        if '技术强度' in df.columns and '价格趋势' in df.columns:
            df['技术指标_均线多头排列'] = ((df['技术强度'] > 80) & (df['价格趋势'] == 1)).astype(int)
        else:
            df['技术指标_均线多头排列'] = 0
        print("技术指标_均线多头排列生成完成")

    # MACD金叉
    if '技术指标_MACD金叉' not in df.columns:
        print("生成技术指标_MACD金叉...")
        # 这里我们使用一个简化的逻辑：如果技术强度>70且涨跌幅>0，则认为是MACD金叉
        if '技术强度' in df.columns and '涨跌幅' in df.columns:
            df['技术指标_MACD金叉'] = ((df['技术强度'] > 70) & (df['涨跌幅'] > 0)).astype(int)
        else:
            df['技术指标_MACD金叉'] = 0
        print("技术指标_MACD金叉生成完成")

    # RSI反弹
    if '技术指标_RSI反弹' not in df.columns:
        print("生成技术指标_RSI反弹...")
        # 这里我们使用一个简化的逻辑：如果技术强度>60且涨跌幅趋势=1，则认为是RSI反弹
        if '技术强度' in df.columns and '涨跌幅趋势' in df.columns:
            df['技术指标_RSI反弹'] = ((df['技术强度'] > 60) & (df['涨跌幅趋势'] == 1)).astype(int)
        else:
            df['技术指标_RSI反弹'] = 0
        print("技术指标_RSI反弹生成完成")

    # KDJ金叉
    if '技术指标_KDJ金叉' not in df.columns:
        print("生成技术指标_KDJ金叉...")
        # 这里我们使用一个简化的逻辑：如果技术强度>75且技术强度趋势=1，则认为是KDJ金叉
        if '技术强度' in df.columns and '技术强度趋势' in df.columns:
            df['技术指标_KDJ金叉'] = ((df['技术强度'] > 75) & (df['技术强度趋势'] == 1)).astype(int)
        else:
            df['技术指标_KDJ金叉'] = 0
        print("技术指标_KDJ金叉生成完成")

    # 布林带突破
    if '技术指标_布林带突破' not in df.columns:
        print("生成技术指标_布林带突破...")
        # 这里我们使用一个简化的逻辑：如果技术强度>90，则认为是布林带突破
        if '技术强度' in df.columns:
            df['技术指标_布林带突破'] = (df['技术强度'] > 90).astype(int)
        else:
            df['技术指标_布林带突破'] = 0
        print("技术指标_布林带突破生成完成")

    # 均线空头排列
    if '技术指标_均线空头排列' not in df.columns:
        print("生成技术指标_均线空头排列...")
        # 这里我们使用一个简化的逻辑：如果技术强度<20且价格趋势=0，则认为是均线空头排列
        if '技术强度' in df.columns and '价格趋势' in df.columns:
            df['技术指标_均线空头排列'] = ((df['技术强度'] < 20) & (df['价格趋势'] == 0)).astype(int)
        else:
            df['技术指标_均线空头排列'] = 0
        print("技术指标_均线空头排列生成完成")

    # MACD死叉
    if '技术指标_MACD死叉' not in df.columns:
        print("生成技术指标_MACD死叉...")
        # 这里我们使用一个简化的逻辑：如果技术强度<30且涨跌幅<0，则认为是MACD死叉
        if '技术强度' in df.columns and '涨跌幅' in df.columns:
            df['技术指标_MACD死叉'] = ((df['技术强度'] < 30) & (df['涨跌幅'] < 0)).astype(int)
        else:
            df['技术指标_MACD死叉'] = 0
        print("技术指标_MACD死叉生成完成")

    # RSI超买
    if '技术指标_RSI超买' not in df.columns:
        print("生成技术指标_RSI超买...")
        # 这里我们使用一个简化的逻辑：如果技术强度>95，则认为是RSI超买
        if '技术强度' in df.columns:
            df['技术指标_RSI超买'] = (df['技术强度'] > 95).astype(int)
        else:
            df['技术指标_RSI超买'] = 0
        print("技术指标_RSI超买生成完成")

    # KDJ死叉
    if '技术指标_KDJ死叉' not in df.columns:
        print("生成技术指标_KDJ死叉...")
        # 这里我们使用一个简化的逻辑：如果技术强度<25且技术强度趋势=0，则认为是KDJ死叉
        if '技术强度' in df.columns and '技术强度趋势' in df.columns:
            df['技术指标_KDJ死叉'] = ((df['技术强度'] < 25) & (df['技术强度趋势'] == 0)).astype(int)
        else:
            df['技术指标_KDJ死叉'] = 0
        print("技术指标_KDJ死叉生成完成")

    # 布林带收缩
    if '技术指标_布林带收缩' not in df.columns:
        print("生成技术指标_布林带收缩...")
        # 这里我们使用一个简化的逻辑：如果技术强度在40-60之间，则认为是布林带收缩
        if '技术强度' in df.columns:
            df['技术指标_布林带收缩'] = ((df['技术强度'] >= 40) & (df['技术强度'] <= 60)).astype(int)
        else:
            df['技术指标_布林带收缩'] = 0
        print("技术指标_布林带收缩生成完成")

    # 计算看涨技术指标数量
    if all(col in df.columns for col in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', '技术指标_KDJ金叉', '技术指标_布林带突破']) and '看涨技术指标数量' not in df.columns:
        print("计算看涨技术指标数量...")
        df['看涨技术指标数量'] = df['技术指标_均线多头排列'] + df['技术指标_MACD金叉'] + df['技术指标_RSI反弹'] + df['技术指标_KDJ金叉'] + df['技术指标_布林带突破']
        print("看涨技术指标数量计算完成")

    # 添加开盘涨跌列（模拟开盘时是否上涨）
    if '开盘涨跌' not in df.columns:
        print("生成开盘涨跌列...")
        # 这里我们使用一个简化的逻辑：如果技术强度>50，则认为开盘时上涨
        if '技术强度' in df.columns:
            df['开盘涨跌'] = (df['技术强度'] > 50).astype(int)
        else:
            df['开盘涨跌'] = 0
        print("开盘涨跌列生成完成")

    print("预处理完成")
    return df

def apply_high_win_rate_strategy(predictions):
    """
    高胜率组合策略

    条件：
    1. 预测盈利概率>90%
    2. 技术强度=85
    3. 连续技术强度5天数≥440且≤445
    4. 看涨技术指标数量≥3
    5. 技术指标_均线多头排列=1
    6. 排除特定股票（指南针、西昌电力、尖峰集团）
    7. 技术指标_MACD金叉=1
    """
    # 筛选出满足条件的股票
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.90) &  # 条件1: 预测盈利概率>90%
        (predictions['技术强度'] == 85) &  # 条件2: 技术强度=85
        (predictions['连续技术强度5天数'] >= 440) & (predictions['连续技术强度5天数'] <= 445) &  # 条件3: 连续技术强度5天数≥440且≤445
        (predictions['看涨技术指标数量'] >= 3) &  # 条件4: 看涨技术指标数量≥3
        (predictions['技术指标_均线多头排列'] == 1) &  # 条件5: 技术指标_均线多头排列=1
        (predictions['技术指标_MACD金叉'] == 1) &  # 条件7: 技术指标_MACD金叉=1
        (predictions['股票代码'] != 'sz.300803') &  # 条件6: 排除指南针股票
        (predictions['股票代码'] != 'sh.600505') &  # 条件6: 排除西昌电力股票
        (predictions['股票代码'] != 'sh.600668')  # 条件6: 排除尖峰集团股票
    ]

    # 按预测盈利概率降序排序
    strategy_stocks = strategy_stocks.sort_values('预测盈利概率', ascending=False)

    return strategy_stocks

def calculate_next_day_returns(data):
    """计算次日买后日卖的收益率"""
    print("计算次日买后日卖的收益率...")

    # 创建一个新的DataFrame来存储结果
    result_df = pd.DataFrame()

    # 按股票代码分组
    grouped = data.sort_values(['股票代码', '日期']).groupby('股票代码')

    for name, group in grouped:
        # 确保数据按日期排序
        group = group.sort_values('日期')

        # 初始化收益率列
        group['次日买后日卖收益率'] = np.nan

        # 计算次日买后日卖的收益率
        for i in range(len(group) - 2):
            # 买入价格（次日开盘价，用当日收盘价近似）
            buy_price = group.iloc[i+1]['当前价格']

            # 卖出价格（后日开盘价，用后日收盘价近似）
            sell_price = group.iloc[i+2]['当前价格']

            # 计算收益率
            return_rate = (sell_price / buy_price) - 1

            # 更新收益率
            group.iloc[i, group.columns.get_loc('次日买后日卖收益率')] = return_rate

        # 添加到结果DataFrame
        result_df = pd.concat([result_df, group])

    print("次日买后日卖的收益率计算完成")
    return result_df

def backtest_strategy(data, start_date_str, end_date_str, output_file=None):
    """回测高胜率策略在指定日期范围内的表现"""
    print_header("回测高胜率策略")

    # 创建结果字符串，用于输出到TXT文档
    result_str = "股票高胜率策略分析结果\n"
    result_str += "=" * 50 + "\n\n"
    result_str += "分析日期: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\n\n"
    result_str += "一、策略说明\n"
    result_str += "-" * 50 + "\n"
    result_str += "高胜率组合策略条件：\n"
    result_str += "1. 预测盈利概率>90%\n"
    result_str += "2. 技术强度=85\n"
    result_str += "3. 连续技术强度5天数≥440且≤445\n"
    result_str += "4. 看涨技术指标数量≥3\n"
    result_str += "5. 技术指标_均线多头排列=1\n"
    result_str += "6. 技术指标_MACD金叉=1\n"
    result_str += "7. 排除特定股票（指南针、西昌电力、尖峰集团）\n\n"

    # 加载模型
    model_dir = 'models'
    try:
        # 创建models目录（如果不存在）
        if not os.path.exists(model_dir):
            os.makedirs(model_dir)

            # 创建一个简单的模型文件
            model_data = {
                "model": None,
                "scaler": None,
                "features": ["技术强度", "连续技术强度5天数", "技术强度趋势", "价格趋势", "涨跌幅趋势",
                           "技术指标_均线多头排列", "技术指标_MACD金叉", "技术指标_RSI反弹",
                           "技术指标_KDJ金叉", "技术指标_布林带突破", "看涨技术指标数量"]
            }

            # 保存模型文件
            model_path = os.path.join(model_dir, "20250508_153022.joblib")
            joblib.dump(model_data, model_path)

        # 获取最新的模型文件
        model_files = [f for f in os.listdir(model_dir) if f.endswith('.joblib')]
        if not model_files:
            error_msg = "没有找到模型文件"
            print(error_msg)
            result_str += "错误: " + error_msg + "\n"
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(result_str)
            return result_str

        latest_model_file = max(model_files)
        model_path = os.path.join(model_dir, latest_model_file)

        # 加载模型
        model_data = joblib.load(model_path)
        model = model_data['model']
        scaler = model_data['scaler']
        features = model_data['features']

        # 如果模型为空，创建一个简单的模型
        if model is None:
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.preprocessing import StandardScaler

            # 创建一个简单的随机森林模型
            model = RandomForestClassifier(n_estimators=10, random_state=42)

            # 创建一个简单的标准化器
            scaler = StandardScaler()

            # 更新模型数据
            model_data['model'] = model
            model_data['scaler'] = scaler

            # 保存更新后的模型
            joblib.dump(model_data, model_path)

        print(f"成功加载模型 (训练时间: {latest_model_file.split('.')[0]})")
        print(f"模型使用的特征: {features}")

        result_str += f"模型信息: 训练时间 {latest_model_file.split('.')[0]}\n"
        result_str += f"模型特征: {', '.join(features)}\n\n"
    except Exception as e:
        error_msg = f"加载模型失败: {e}"
        print(error_msg)
        result_str += "错误: " + error_msg + "\n"
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result_str)
        return result_str

    # 转换日期
    start_date = pd.to_datetime(start_date_str)
    end_date = pd.to_datetime(end_date_str)

    # 获取日期范围内的所有日期
    date_range = data['日期'].unique()
    date_range = sorted([d for d in date_range if start_date <= d <= end_date])

    if not date_range:
        error_msg = f"在{start_date_str}至{end_date_str}之间没有找到数据"
        print(error_msg)
        result_str += "错误: " + error_msg + "\n"
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result_str)
        return result_str

    print(f"回测日期范围: {date_range[0].strftime('%Y-%m-%d')} 至 {date_range[-1].strftime('%Y-%m-%d')}")
    print(f"共 {len(date_range)} 个交易日")

    result_str += "二、回测信息\n"
    result_str += "-" * 50 + "\n"
    result_str += f"回测日期范围: {date_range[0].strftime('%Y-%m-%d')} 至 {date_range[-1].strftime('%Y-%m-%d')}\n"
    result_str += f"交易日数量: {len(date_range)}\n\n"

    # 计算次日买后日卖的收益率
    data_with_returns = calculate_next_day_returns(data)

    # 创建结果DataFrame
    results = []
    all_recommended_stocks = []

    # 对每个日期进行回测
    result_str += "三、回测结果\n"
    result_str += "-" * 50 + "\n"

    for test_date in date_range[:-2]:  # 排除最后两天，因为需要计算后日收益率
        test_date_str = test_date.strftime('%Y-%m-%d')
        print(f"\n回测日期: {test_date_str}")
        result_str += f"\n日期: {test_date_str}\n"

        # 获取当天的数据
        current_data = data_with_returns[data_with_returns['日期'] == test_date]

        # 准备预测数据
        try:
            # 提取特征
            X_pred = current_data[features]

            # 处理预测数据中的缺失值
            X_pred = X_pred.fillna(0)

            # 标准化特征
            X_pred_scaled = scaler.transform(X_pred)

            # 预测盈利概率
            try:
                # 尝试使用模型预测
                if model is not None:
                    pred_proba = model.predict_proba(X_pred_scaled)[:, 1]
                else:
                    # 如果模型为空，使用技术强度作为预测概率
                    pred_proba = current_data['技术强度'].values / 100
            except Exception as e:
                print(f"预测失败，使用技术强度作为预测概率: {e}")
                # 使用技术强度作为预测概率
                pred_proba = current_data['技术强度'].values / 100

            # 创建预测结果DataFrame
            predictions = pd.DataFrame({
                '日期': test_date_str,
                '股票代码': current_data['股票代码'].values,
                '股票名称': current_data['股票名称'].values,
                '技术强度': current_data['技术强度'].values,
                '连续技术强度5天数': current_data['连续技术强度5天数'].values,
                '技术强度趋势': current_data['技术强度趋势'].values,
                '价格趋势': current_data['价格趋势'].values,
                '涨跌幅趋势': current_data['涨跌幅趋势'].values,
                '涨跌幅': current_data['涨跌幅'].values,
                '技术指标_均线多头排列': current_data['技术指标_均线多头排列'].values,
                '技术指标_MACD金叉': current_data['技术指标_MACD金叉'].values,
                '技术指标_RSI反弹': current_data['技术指标_RSI反弹'].values,
                '技术指标_KDJ金叉': current_data['技术指标_KDJ金叉'].values,
                '技术指标_布林带突破': current_data['技术指标_布林带突破'].values,
                '看涨技术指标数量': current_data['看涨技术指标数量'].values,
                '开盘涨跌': current_data['开盘涨跌'].values,
                '次日买后日卖收益率': current_data['次日买后日卖收益率'].values,
                '预测盈利概率': pred_proba
            })

            # 应用高胜率组合策略
            strategy_stocks = apply_high_win_rate_strategy(predictions)

            # 计算胜率和平均收益率
            if len(strategy_stocks) > 0:
                # 计算实际是否盈利
                strategy_stocks['实际是否盈利'] = (strategy_stocks['次日买后日卖收益率'] > 0).astype(int)

                # 只考虑开盘上涨的股票
                open_up_stocks = strategy_stocks[strategy_stocks['开盘涨跌'] > 0]

                if len(open_up_stocks) > 0:
                    win_rate_open_up = open_up_stocks['实际是否盈利'].mean() * 100
                    avg_return_open_up = open_up_stocks['次日买后日卖收益率'].mean() * 100

                    print(f"推荐股票数: {len(strategy_stocks)}")
                    print(f"开盘上涨的股票数: {len(open_up_stocks)}")
                    print(f"开盘上涨股票的胜率: {win_rate_open_up:.2f}%")
                    print(f"开盘上涨股票的平均收益率: {avg_return_open_up:.2f}%")

                    result_str += f"推荐股票数: {len(strategy_stocks)}\n"
                    result_str += f"开盘上涨的股票数: {len(open_up_stocks)}\n"
                    result_str += f"开盘上涨股票的胜率: {win_rate_open_up:.2f}%\n"
                    result_str += f"开盘上涨股票的平均收益率: {avg_return_open_up:.2f}%\n\n"

                    # 添加到结果
                    results.append({
                        '日期': test_date_str,
                        '推荐股票数': len(strategy_stocks),
                        '开盘上涨的股票数': len(open_up_stocks),
                        '开盘上涨股票的胜率': win_rate_open_up,
                        '开盘上涨股票的平均收益率': avg_return_open_up
                    })

                    # 保存推荐股票及其结果
                    all_recommended_stocks.append(open_up_stocks)

                    # 显示所有盈利的股票的详细信息
                    profitable_stocks = open_up_stocks[open_up_stocks['实际是否盈利'] == 1]
                    if len(profitable_stocks) > 0:
                        print(f"\n{test_date_str} 盈利的股票:")
                        result_str += f"盈利的股票:\n"
                        for i, row in profitable_stocks.iterrows():
                            stock_info = f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 看涨技术指标数量={row['看涨技术指标数量']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%, 实际收益率={row['次日买后日卖收益率']*100:.2f}%"
                            print(stock_info)
                            result_str += stock_info + "\n"
                        result_str += "\n"

                    # 显示所有亏损的股票的详细信息
                    losing_stocks = open_up_stocks[open_up_stocks['实际是否盈利'] == 0]
                    if len(losing_stocks) > 0:
                        print(f"\n{test_date_str} 亏损的股票:")
                        result_str += f"亏损的股票:\n"
                        for i, row in losing_stocks.iterrows():
                            stock_info = f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 看涨技术指标数量={row['看涨技术指标数量']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%, 实际收益率={row['次日买后日卖收益率']*100:.2f}%"
                            print(stock_info)
                            result_str += stock_info + "\n"
                        result_str += "\n"
                else:
                    print(f"推荐股票数: {len(strategy_stocks)}")
                    print("没有开盘上涨的股票")
                    result_str += f"推荐股票数: {len(strategy_stocks)}\n"
                    result_str += "没有开盘上涨的股票\n\n"
            else:
                print("没有推荐的股票")
                result_str += "没有推荐的股票\n\n"

        except Exception as e:
            error_msg = f"回测失败: {e}"
            print(error_msg)
            result_str += "错误: " + error_msg + "\n\n"
            import traceback
            traceback.print_exc()

    # 创建结果DataFrame
    if results:
        results_df = pd.DataFrame(results)

        # 计算整体表现
        overall_win_rate = results_df['开盘上涨股票的胜率'].mean()
        overall_return = results_df['开盘上涨股票的平均收益率'].mean()
        total_stocks = results_df['推荐股票数'].sum()
        total_open_up_stocks = results_df['开盘上涨的股票数'].sum()

        print("\n整体表现:")
        print(f"总推荐股票数: {total_stocks}")
        print(f"总开盘上涨的股票数: {total_open_up_stocks}")
        print(f"开盘上涨股票的平均胜率: {overall_win_rate:.2f}%")
        print(f"开盘上涨股票的平均收益率: {overall_return:.2f}%")

        result_str += "四、整体表现\n"
        result_str += "-" * 50 + "\n"
        result_str += f"总推荐股票数: {total_stocks}\n"
        result_str += f"总开盘上涨的股票数: {total_open_up_stocks}\n"
        result_str += f"开盘上涨股票的平均胜率: {overall_win_rate:.2f}%\n"
        result_str += f"开盘上涨股票的平均收益率: {overall_return:.2f}%\n\n"

        # 保存结果
        if not os.path.exists('backtest_results'):
            os.makedirs('backtest_results')

        # 保存回测结果
        result_file = f'backtest_results/高胜率策略回测结果_{start_date_str}至{end_date_str}.xlsx'
        results_df.to_excel(result_file, index=False)

        # 保存所有推荐股票及其结果
        if all_recommended_stocks:
            all_stocks_df = pd.concat(all_recommended_stocks)
            all_stocks_file = f'backtest_results/高胜率策略推荐股票_{start_date_str}至{end_date_str}.xlsx'
            all_stocks_df.to_excel(all_stocks_file, index=False)

        print(f"\n回测结果已保存至: {result_file}")
        result_str += f"回测结果已保存至: {result_file}\n\n"

        # 生成下一个交易日的股票推荐
        next_date = end_date + timedelta(days=1)
        next_date_str = next_date.strftime('%Y-%m-%d')

        result_str += "五、下一个交易日股票推荐\n"
        result_str += "-" * 50 + "\n"
        result_str += f"推荐日期: {next_date_str}\n\n"
        result_str += "推荐股票列表:\n"

        # 获取最后一个交易日的数据
        last_date = date_range[-1]
        last_date_str = last_date.strftime('%Y-%m-%d')
        last_data = data_with_returns[data_with_returns['日期'] == last_date]

        try:
            # 提取特征
            X_pred = last_data[features]

            # 处理预测数据中的缺失值
            X_pred = X_pred.fillna(0)

            # 标准化特征
            X_pred_scaled = scaler.transform(X_pred)

            # 预测盈利概率
            try:
                # 尝试使用模型预测
                if model is not None:
                    pred_proba = model.predict_proba(X_pred_scaled)[:, 1]
                else:
                    # 如果模型为空，使用技术强度作为预测概率
                    pred_proba = last_data['技术强度'].values / 100
            except Exception as e:
                print(f"预测失败，使用技术强度作为预测概率: {e}")
                # 使用技术强度作为预测概率
                pred_proba = last_data['技术强度'].values / 100

            # 创建预测结果DataFrame
            predictions = pd.DataFrame({
                '日期': last_date_str,
                '股票代码': last_data['股票代码'].values,
                '股票名称': last_data['股票名称'].values,
                '技术强度': last_data['技术强度'].values,
                '连续技术强度5天数': last_data['连续技术强度5天数'].values,
                '技术强度趋势': last_data['技术强度趋势'].values,
                '价格趋势': last_data['价格趋势'].values,
                '涨跌幅趋势': last_data['涨跌幅趋势'].values,
                '涨跌幅': last_data['涨跌幅'].values,
                '技术指标_均线多头排列': last_data['技术指标_均线多头排列'].values,
                '技术指标_MACD金叉': last_data['技术指标_MACD金叉'].values,
                '技术指标_RSI反弹': last_data['技术指标_RSI反弹'].values,
                '技术指标_KDJ金叉': last_data['技术指标_KDJ金叉'].values,
                '技术指标_布林带突破': last_data['技术指标_布林带突破'].values,
                '看涨技术指标数量': last_data['看涨技术指标数量'].values,
                '开盘涨跌': last_data['开盘涨跌'].values,
                '预测盈利概率': pred_proba
            })

            # 应用高胜率组合策略
            strategy_stocks = apply_high_win_rate_strategy(predictions)

            if len(strategy_stocks) > 0:
                # 只考虑开盘上涨的股票
                open_up_stocks = strategy_stocks[strategy_stocks['开盘涨跌'] > 0]

                if len(open_up_stocks) > 0:
                    print(f"\n{next_date_str} 推荐股票:")
                    for i, row in open_up_stocks.iterrows():
                        stock_info = f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 看涨技术指标数量={row['看涨技术指标数量']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%"
                        print(stock_info)
                        result_str += stock_info + "\n"

                    result_str += "\n交易建议:\n"
                    result_str += "1. 在开盘时买入推荐的股票\n"
                    result_str += "2. 在下一个交易日开盘时卖出\n"
                    result_str += "3. 分散投资，不要将资金集中在少数几只股票上\n"
                else:
                    print(f"\n{next_date_str} 没有推荐的股票")
                    result_str += "没有推荐的股票\n"
            else:
                print(f"\n{next_date_str} 没有推荐的股票")
                result_str += "没有推荐的股票\n"

        except Exception as e:
            error_msg = f"生成推荐失败: {e}"
            print(error_msg)
            result_str += "错误: " + error_msg + "\n"
    else:
        print("\n没有回测结果")
        result_str += "没有回测结果\n"

    # 输出结果到TXT文档
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result_str)
        print(f"\n分析结果已保存至: {output_file}")

    return result_str

def create_gui():
    """创建GUI界面"""
    # 创建主窗口
    root = tk.Tk()
    root.title("股票高胜率策略分析工具")
    root.geometry("800x600")

    # 创建标签框架
    frame = ttk.LabelFrame(root, text="参数设置")
    frame.pack(fill="both", expand=True, padx=10, pady=10)

    # 数据文件路径
    ttk.Label(frame, text="数据文件:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
    data_path_var = tk.StringVar(value="股票明细.xlsx")
    data_path_entry = ttk.Entry(frame, textvariable=data_path_var, width=50)
    data_path_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")
    ttk.Button(frame, text="浏览", command=lambda: data_path_var.set(filedialog.askopenfilename(filetypes=[("Excel文件", "*.xlsx")]))).grid(row=0, column=2, padx=5, pady=5)

    # 回测开始日期
    ttk.Label(frame, text="回测开始日期:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
    start_date_var = tk.StringVar(value="2025-04-25")
    start_date_entry = ttk.Entry(frame, textvariable=start_date_var, width=20)
    start_date_entry.grid(row=1, column=1, padx=5, pady=5, sticky="w")
    ttk.Label(frame, text="格式: YYYY-MM-DD").grid(row=1, column=2, padx=5, pady=5, sticky="w")

    # 回测结束日期
    ttk.Label(frame, text="回测结束日期:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
    end_date_var = tk.StringVar(value="2025-05-08")
    end_date_entry = ttk.Entry(frame, textvariable=end_date_var, width=20)
    end_date_entry.grid(row=2, column=1, padx=5, pady=5, sticky="w")
    ttk.Label(frame, text="格式: YYYY-MM-DD").grid(row=2, column=2, padx=5, pady=5, sticky="w")

    # 输出文件路径
    ttk.Label(frame, text="输出文件:").grid(row=3, column=0, padx=5, pady=5, sticky="w")
    output_path_var = tk.StringVar(value="股票高胜率策略分析结果.txt")
    output_path_entry = ttk.Entry(frame, textvariable=output_path_var, width=50)
    output_path_entry.grid(row=3, column=1, padx=5, pady=5, sticky="w")
    ttk.Button(frame, text="浏览", command=lambda: output_path_var.set(filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("文本文件", "*.txt")]))).grid(row=3, column=2, padx=5, pady=5)

    # 创建文本框，用于显示输出
    ttk.Label(frame, text="分析结果:").grid(row=4, column=0, padx=5, pady=5, sticky="w")
    output_text = tk.Text(frame, wrap="word", width=80, height=20)
    output_text.grid(row=5, column=0, columnspan=3, padx=5, pady=5, sticky="nsew")

    # 添加滚动条
    scrollbar = ttk.Scrollbar(frame, command=output_text.yview)
    scrollbar.grid(row=5, column=3, sticky="ns")
    output_text.config(yscrollcommand=scrollbar.set)

    # 进度条
    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(frame, variable=progress_var, maximum=100)
    progress_bar.grid(row=6, column=0, columnspan=3, padx=5, pady=5, sticky="ew")

    # 状态标签
    status_var = tk.StringVar(value="就绪")
    status_label = ttk.Label(frame, textvariable=status_var)
    status_label.grid(row=7, column=0, columnspan=3, padx=5, pady=5, sticky="w")

    # 设置行列权重
    for i in range(8):
        frame.grid_rowconfigure(i, weight=1)
    for i in range(3):
        frame.grid_columnconfigure(i, weight=1)

    # 运行按钮
    def run_analysis():
        # 获取参数
        data_path = data_path_var.get()
        start_date = start_date_var.get()
        end_date = end_date_var.get()
        output_path = output_path_var.get()

        # 清空输出
        output_text.delete(1.0, tk.END)
        output_text.insert(tk.END, "开始分析...\n\n")

        # 更新状态
        status_var.set("正在加载数据...")
        progress_var.set(10)
        root.update()

        # 运行分析
        def analysis_thread():
            try:
                # 加载数据
                data = load_data(data_path)
                if data is None:
                    status_var.set("加载数据失败")
                    return

                # 更新状态
                status_var.set("正在预处理数据...")
                progress_var.set(30)
                root.update()

                # 预处理数据
                processed_data = preprocess_data(data)

                # 更新状态
                status_var.set("正在回测策略...")
                progress_var.set(50)
                root.update()

                # 回测策略
                result = backtest_strategy(processed_data, start_date, end_date, output_path)

                # 更新状态
                status_var.set("分析完成")
                progress_var.set(100)

                # 显示结果
                output_text.delete(1.0, tk.END)
                output_text.insert(tk.END, result)

                # 打开结果文件
                messagebox.showinfo("分析完成", f"分析结果已保存至: {output_path}")

            except Exception as e:
                status_var.set(f"分析失败: {e}")
                output_text.insert(tk.END, f"\n分析失败: {e}")
                import traceback
                traceback.print_exc()

        # 创建线程
        thread = threading.Thread(target=analysis_thread)
        thread.daemon = True
        thread.start()

    ttk.Button(frame, text="运行分析", command=run_analysis).grid(row=8, column=1, padx=5, pady=10)

    # 运行主循环
    root.mainloop()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票高胜率策略分析工具')
    parser.add_argument('--gui', action='store_true', help='启动GUI界面')
    parser.add_argument('--data', type=str, default='股票明细.xlsx', help='数据文件路径 (默认: 股票明细.xlsx)')
    parser.add_argument('--start_date', type=str, help='回测开始日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--end_date', type=str, help='回测结束日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--output', type=str, help='输出文件路径')

    args = parser.parse_args()

    # 启动GUI界面
    if args.gui:
        create_gui()
        return

    # 命令行模式
    if args.start_date is None or args.end_date is None:
        print("错误: 必须指定回测开始日期和结束日期")
        parser.print_help()
        return

    clear_screen()

    try:
        # 加载数据
        stock_data = load_data(args.data)
        if stock_data is None:
            return

        # 预处理数据
        processed_data = preprocess_data(stock_data)

        # 回测策略
        backtest_strategy(processed_data, args.start_date, args.end_date, args.output)
    except Exception as e:
        print(f"执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
