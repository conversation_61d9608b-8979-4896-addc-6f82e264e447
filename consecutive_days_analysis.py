import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

def analyze_consecutive_days():
    """分析连续多日技术强度为100的股票胜率"""
    print("开始分析连续多日技术强度为100的股票胜率...")

    # 创建结果目录
    if not os.path.exists('analysis_results'):
        os.makedirs('analysis_results')

    # 加载股票数据
    print("\n加载股票数据...")
    try:
        stock_data = pd.read_excel('股票明细.xlsx')
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")

        # 转换日期格式
        if isinstance(stock_data['日期'].iloc[0], str):
            stock_data['日期'] = pd.to_datetime(stock_data['日期'])

        # 获取所有日期并排序
        all_dates = sorted(stock_data['日期'].unique())
        print(f"数据集中的日期范围: {all_dates[0]} 到 {all_dates[-1]}")

        # 按股票代码和日期排序
        stock_data = stock_data.sort_values(['股票代码', '日期'])

        # 创建结果DataFrame
        results = pd.DataFrame(columns=['连续天数', '股票数量', '上涨数量', '胜率', '平均涨幅'])

        # 分析连续1-5天技术强度为100的股票
        for consecutive_days in range(1, 6):
            print(f"\n分析连续{consecutive_days}天技术强度为100的股票...")

            # 创建前N日技术强度列
            for i in range(1, consecutive_days):
                stock_data[f'前{i}日技术强度'] = stock_data.groupby('股票代码')['技术强度'].shift(i)

            # 筛选条件
            filter_conditions = [(stock_data['技术强度'] == 100)]
            for i in range(1, consecutive_days):
                filter_conditions.append(stock_data[f'前{i}日技术强度'] == 100)

            # 筛选连续N天技术强度为100的记录
            filtered_data = stock_data.copy()
            for condition in filter_conditions:
                filtered_data = filtered_data[condition]

            print(f"找到 {len(filtered_data)} 条连续{consecutive_days}天技术强度为100的记录")

            if len(filtered_data) == 0:
                print(f"没有找到连续{consecutive_days}天技术强度为100的记录")
                results = results.append({
                    '连续天数': consecutive_days,
                    '股票数量': 0,
                    '上涨数量': 0,
                    '胜率': 0,
                    '平均涨幅': 0
                }, ignore_index=True)
                continue

            # 计算次日涨跌方向和收益率
            filtered_data['次日涨跌方向'] = 0
            filtered_data['次日收益率'] = 0

            # 按股票代码分组处理
            for code, group in filtered_data.groupby('股票代码'):
                # 确保数据按日期排序
                group = group.sort_values('日期')

                # 计算次日涨跌方向和收益率
                group['次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)
                group['次日收益率'] = (group['当前价格'].shift(-1) / group['当前价格'] - 1) * 100

                # 更新原始数据
                filtered_data.loc[group.index, '次日涨跌方向'] = group['次日涨跌方向']
                filtered_data.loc[group.index, '次日收益率'] = group['次日收益率']

            # 删除没有次日数据的记录
            filtered_data = filtered_data.dropna(subset=['次日涨跌方向', '次日收益率'])

            print(f"处理后的数据集大小: {len(filtered_data)} 条记录")

            if len(filtered_data) == 0:
                print(f"处理后没有有效记录")
                new_row = pd.DataFrame({
                    '连续天数': [consecutive_days],
                    '股票数量': [0],
                    '上涨数量': [0],
                    '胜率': [0],
                    '平均涨幅': [0]
                })
                results = pd.concat([results, new_row], ignore_index=True)
                continue

            # 分析上涨概率
            up_count = filtered_data['次日涨跌方向'].sum()
            total_count = len(filtered_data)
            up_probability = up_count / total_count if total_count > 0 else 0

            # 分析平均收益率
            avg_return = filtered_data['次日收益率'].mean()

            print(f"总记录数: {total_count}")
            print(f"上涨记录数: {up_count}")
            print(f"上涨概率: {up_probability:.4f} ({up_probability*100:.2f}%)")
            print(f"平均次日收益率: {avg_return:.4f}%")

            # 添加到结果
            new_row = pd.DataFrame({
                '连续天数': [consecutive_days],
                '股票数量': [total_count],
                '上涨数量': [up_count],
                '胜率': [up_probability],
                '平均涨幅': [avg_return]
            })
            results = pd.concat([results, new_row], ignore_index=True)

            # 保存详细数据
            filtered_data.to_excel(f'analysis_results/连续{consecutive_days}天技术强度100_详细数据.xlsx', index=False)

        # 保存汇总结果
        results.to_excel('analysis_results/连续天数胜率分析.xlsx', index=False)

        print("\n汇总结果:")
        print(results)

        # 绘制胜率图表
        plt.figure(figsize=(10, 6))
        plt.bar(results['连续天数'], results['胜率'] * 100)
        plt.xlabel('连续天数')
        plt.ylabel('胜率 (%)')
        plt.title('连续N天技术强度为100的股票次日上涨胜率')
        plt.xticks(results['连续天数'])
        plt.grid(axis='y', linestyle='--', alpha=0.7)

        for i, v in enumerate(results['胜率']):
            plt.text(i + 1, v * 100 + 2, f"{v*100:.1f}%", ha='center')

        plt.savefig('analysis_results/胜率分析.png')

        # 绘制平均涨幅图表
        plt.figure(figsize=(10, 6))
        plt.bar(results['连续天数'], results['平均涨幅'])
        plt.xlabel('连续天数')
        plt.ylabel('平均涨幅 (%)')
        plt.title('连续N天技术强度为100的股票次日平均涨幅')
        plt.xticks(results['连续天数'])
        plt.grid(axis='y', linestyle='--', alpha=0.7)

        for i, v in enumerate(results['平均涨幅']):
            plt.text(i + 1, v + 0.2, f"{v:.2f}%", ha='center')

        plt.savefig('analysis_results/平均涨幅分析.png')

        # 分析5月8日的数据
        print("\n分析5月8日的数据...")
        target_date = pd.to_datetime('2025-05-08')

        # 如果没有找到这个日期，尝试找到最后一个交易日
        if target_date not in all_dates:
            print(f"未找到5月8日的数据，使用最后一个交易日")
            target_date = all_dates[-1]

        print(f"目标日期: {target_date}")

        # 获取目标日期的数据
        target_data = stock_data[stock_data['日期'] == target_date]

        # 创建验证结果DataFrame
        validation_results = pd.DataFrame(columns=['连续天数', '股票数量', '上涨数量', '胜率', '平均涨幅'])

        # 分析连续1-5天技术强度为100的股票
        for consecutive_days in range(1, 6):
            print(f"\n分析5月8日连续{consecutive_days}天技术强度为100的股票...")

            # 获取前N天的日期
            previous_dates = []
            current_date = target_date
            for i in range(1, consecutive_days):
                idx = all_dates.index(current_date)
                if idx > 0:
                    current_date = all_dates[idx - 1]
                    previous_dates.append(current_date)
                else:
                    break

            if len(previous_dates) < consecutive_days - 1:
                print(f"没有足够的历史数据来分析连续{consecutive_days}天")
                new_row = pd.DataFrame({
                    '连续天数': [consecutive_days],
                    '股票数量': [0],
                    '上涨数量': [0],
                    '胜率': [0],
                    '平均涨幅': [0]
                })
                validation_results = pd.concat([validation_results, new_row], ignore_index=True)
                continue

            # 筛选目标日期技术强度为100的股票
            filtered_stocks = target_data[target_data['技术强度'] == 100]['股票代码'].unique()

            # 检查前N-1天是否也是技术强度为100
            for i, prev_date in enumerate(previous_dates):
                prev_data = stock_data[stock_data['日期'] == prev_date]
                prev_strong_stocks = prev_data[prev_data['技术强度'] == 100]['股票代码'].unique()
                filtered_stocks = np.intersect1d(filtered_stocks, prev_strong_stocks)

            print(f"找到 {len(filtered_stocks)} 只连续{consecutive_days}天技术强度为100的股票")

            if len(filtered_stocks) == 0:
                print(f"没有找到连续{consecutive_days}天技术强度为100的股票")
                new_row = pd.DataFrame({
                    '连续天数': [consecutive_days],
                    '股票数量': [0],
                    '上涨数量': [0],
                    '胜率': [0],
                    '平均涨幅': [0]
                })
                validation_results = pd.concat([validation_results, new_row], ignore_index=True)
                continue

            # 获取5月9日的数据
            next_date = pd.to_datetime('2025-05-09')
            if next_date not in all_dates:
                print(f"未找到5月9日的数据，使用下一个交易日")
                idx = all_dates.index(target_date)
                if idx < len(all_dates) - 1:
                    next_date = all_dates[idx + 1]
                else:
                    print(f"没有下一个交易日的数据")
                    new_row = pd.DataFrame({
                        '连续天数': [consecutive_days],
                        '股票数量': [len(filtered_stocks)],
                        '上涨数量': [0],
                        '胜率': [0],
                        '平均涨幅': [0]
                    })
                    validation_results = pd.concat([validation_results, new_row], ignore_index=True)
                    continue

            print(f"下一个交易日: {next_date}")

            # 获取下一个交易日的数据
            next_data = stock_data[stock_data['日期'] == next_date]

            # 获取目标股票在目标日期和下一个交易日的价格
            target_prices = target_data[target_data['股票代码'].isin(filtered_stocks)][['股票代码', '当前价格']]
            next_prices = next_data[next_data['股票代码'].isin(filtered_stocks)][['股票代码', '当前价格']]

            # 合并数据
            merged_prices = pd.merge(
                target_prices,
                next_prices,
                on='股票代码',
                suffixes=('_target', '_next')
            )

            # 计算涨跌幅
            merged_prices['涨跌幅'] = (merged_prices['当前价格_next'] / merged_prices['当前价格_target'] - 1) * 100
            merged_prices['涨跌'] = merged_prices['涨跌幅'] > 0

            # 计算胜率和平均涨幅
            up_count = sum(merged_prices['涨跌'])
            total_count = len(merged_prices)
            success_rate = up_count / total_count if total_count > 0 else 0
            avg_return = merged_prices['涨跌幅'].mean()

            print(f"总股票数: {total_count}")
            print(f"上涨股票数: {up_count}")
            print(f"胜率: {success_rate:.4f} ({success_rate*100:.2f}%)")
            print(f"平均涨跌幅: {avg_return:.4f}%")

            # 添加到验证结果
            new_row = pd.DataFrame({
                '连续天数': [consecutive_days],
                '股票数量': [total_count],
                '上涨数量': [up_count],
                '胜率': [success_rate],
                '平均涨幅': [avg_return]
            })
            validation_results = pd.concat([validation_results, new_row], ignore_index=True)

            # 保存详细数据
            merged_prices.to_excel(f'analysis_results/5月8日连续{consecutive_days}天技术强度100_验证结果.xlsx', index=False)

        # 保存验证结果
        validation_results.to_excel('analysis_results/5月8日连续天数验证结果.xlsx', index=False)

        print("\n5月8日验证结果:")
        print(validation_results)

        # 绘制验证胜率图表
        plt.figure(figsize=(10, 6))
        plt.bar(validation_results['连续天数'], validation_results['胜率'] * 100)
        plt.xlabel('连续天数')
        plt.ylabel('胜率 (%)')
        plt.title('5月8日连续N天技术强度为100的股票次日上涨胜率')
        plt.xticks(validation_results['连续天数'])
        plt.grid(axis='y', linestyle='--', alpha=0.7)

        for i, v in enumerate(validation_results['胜率']):
            plt.text(i + 1, v * 100 + 2, f"{v*100:.1f}%", ha='center')

        plt.savefig('analysis_results/5月8日胜率验证.png')

        print("\n分析完成！结果已保存至 analysis_results 目录")

        return results, validation_results

    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    analyze_consecutive_days()
