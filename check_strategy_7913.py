import pandas as pd
import os
import glob

def check_strategy_7913():
    """检查策略7913的筛选条件和数据匹配情况"""
    
    # 1. 检查汇总表中是否有策略7913
    print("=== 检查汇总表 ===")
    summary_files = [
        'E:/机器学习/complete_excel_results/所有策略汇总_已回测.xlsx',
        'E:/机器学习/complete_excel_results/所有策略汇总.parquet'
    ]
    
    strategy_7913_found = False
    strategy_7913_condition = None
    
    for file_path in summary_files:
        if os.path.exists(file_path):
            try:
                if file_path.endswith('.parquet'):
                    df = pd.read_parquet(file_path)
                    print(f"读取Parquet汇总表: {len(df)}条记录")
                else:
                    df = pd.read_excel(file_path)
                    print(f"读取Excel汇总表: {len(df)}条记录")
                
                print(f"策略ID范围: {df['策略编号'].min()} - {df['策略编号'].max()}")
                
                # 检查策略7913
                strategy_7913 = df[df['策略编号'] == 7913]
                if not strategy_7913.empty:
                    strategy_7913_found = True
                    strategy_7913_condition = strategy_7913['策略条件描述'].values[0]
                    print(f"✅ 找到策略7913: {strategy_7913_condition}")
                    break
                else:
                    print(f"❌ 策略7913不在{file_path}中")
                    
            except Exception as e:
                print(f"读取{file_path}失败: {e}")
    
    if not strategy_7913_found:
        print("❌ 策略7913不在任何汇总表中")
        return
    
    # 2. 检查技术强度数据
    print("\n=== 检查技术强度数据 ===")
    tech_strength_dir = 'E:/机器学习/complete_excel_results/tech_strength/daily'
    
    if os.path.exists(tech_strength_dir):
        files = glob.glob(os.path.join(tech_strength_dir, '*.xlsx'))
        print(f"找到 {len(files)} 个技术强度文件")
        
        if files:
            # 检查第一个文件的数据结构
            sample_file = files[0]
            try:
                sample_df = pd.read_excel(sample_file)
                print(f"技术强度数据列: {list(sample_df.columns)}")
                print(f"样本数据量: {len(sample_df)}")
                
                # 检查技术强度的值分布
                if '技术强度' in sample_df.columns:
                    tech_values = sample_df['技术强度'].value_counts().head(10)
                    print(f"技术强度值分布(前10):\n{tech_values}")
                
            except Exception as e:
                print(f"读取技术强度样本文件失败: {e}")
    else:
        print("❌ 技术强度数据目录不存在")
    
    # 3. 解析策略7913的条件
    if strategy_7913_condition:
        print(f"\n=== 解析策略7913条件 ===")
        print(f"条件: {strategy_7913_condition}")
        
        # 简单解析条件
        conditions = []
        if "AND" in strategy_7913_condition:
            condition_parts = strategy_7913_condition.split("AND")
            for part in condition_parts:
                part = part.strip()
                conditions.append(part)
        else:
            conditions.append(strategy_7913_condition.strip())
        
        print(f"解析出 {len(conditions)} 个条件:")
        for i, condition in enumerate(conditions, 1):
            print(f"  {i}. {condition}")
        
        # 4. 检查数据中是否有匹配的值
        print(f"\n=== 检查数据匹配情况 ===")
        if files:
            # 随机检查几个文件
            import random
            sample_files = random.sample(files, min(3, len(files)))
            
            for file_path in sample_files:
                try:
                    df = pd.read_excel(file_path)
                    file_name = os.path.basename(file_path)
                    print(f"\n检查文件: {file_name}")
                    print(f"  数据量: {len(df)}")
                    
                    # 检查每个条件
                    for condition in conditions:
                        if "技术强度" in condition and "等于" in condition:
                            # 提取技术强度值
                            try:
                                value = int(condition.split("等于")[1].strip())
                                matching_count = len(df[df['技术强度'] == value])
                                print(f"  技术强度={value}: {matching_count}条记录")
                            except:
                                print(f"  无法解析条件: {condition}")
                        
                        elif "连续技术强度" in condition:
                            # 检查连续技术强度列
                            for col in df.columns:
                                if "连续技术强度" in col:
                                    print(f"  {col}: 范围 {df[col].min()}-{df[col].max()}")
                        
                        elif "日内股票标记" in condition:
                            if '日内股票标记' in df.columns:
                                values = df['日内股票标记'].value_counts().head(5)
                                print(f"  日内股票标记分布:\n{values}")
                        
                        elif "成交量是前一日几倍" in condition:
                            if '成交量是前一日几倍' in df.columns:
                                values = df['成交量是前一日几倍'].describe()
                                print(f"  成交量倍数统计:\n{values}")
                
                except Exception as e:
                    print(f"  检查文件{file_path}失败: {e}")

if __name__ == "__main__":
    check_strategy_7913()
