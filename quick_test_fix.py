#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复效果
"""

import pandas as pd
import tech_strength_manager as tsm

def quick_test_fix():
    """快速测试修复效果"""
    
    print("=== 快速测试修复效果 ===")
    
    try:
        # 读取原始数据的一小部分
        print("1. 读取原始数据...")
        original_df = pd.read_excel('E:/机器学习/complete_excel_results/股票明细_完整.xlsx')
        test_data = original_df[original_df['日期'] == '2025-05-15'].head(20).copy()
        
        print(f"测试数据行数: {len(test_data)}")
        
        # 应用修复后的格式化逻辑
        print("2. 应用修复后的格式化逻辑...")
        
        # 调用tech_strength_manager中的格式化函数
        formatted_data = tsm.format_data_for_saving(test_data)
        
        print("3. 检查格式化结果...")
        
        # 检查样本数据
        sample = formatted_data.iloc[0]
        print(f"样本股票: {sample['股票代码']}")
        
        # 检查字段类型和格式
        print("\n=== 字段检查 ===")
        
        # 技术指标特征
        tech_feature = sample.get('技术指标特征', 'N/A')
        print(f"技术指标特征: {tech_feature} (类型: {type(tech_feature)})")
        
        # 趋势组合
        trend_combo = sample.get('趋势组合', 'N/A')
        print(f"趋势组合: {trend_combo} (类型: {type(trend_combo)})")
        
        # 连续技术强度
        consecutive_3 = sample.get('连续技术强度3天数', 'N/A')
        consecutive_5 = sample.get('连续技术强度5天数', 'N/A')
        consecutive_10 = sample.get('连续技术强度10天数', 'N/A')
        print(f"连续技术强度: 3天={consecutive_3}, 5天={consecutive_5}, 10天={consecutive_10}")
        
        # 其他字段
        buy_change = sample.get('买入日开盘涨跌幅', 'N/A')
        intraday_mark = sample.get('日内股票标记', 'N/A')
        sell_change = sample.get('卖出日开盘涨跌幅', 'N/A')
        
        print(f"买入日开盘涨跌幅: {buy_change} (类型: {type(buy_change)})")
        print(f"日内股票标记: {intraday_mark} (类型: {type(intraday_mark)})")
        print(f"卖出日开盘涨跌幅: {sell_change} (类型: {type(sell_change)})")
        
        # 检查数据多样性
        print("\n=== 数据多样性检查 ===")
        if '技术指标特征' in formatted_data.columns:
            print(f"技术指标特征唯一值数量: {formatted_data['技术指标特征'].nunique()}")
            print(f"技术指标特征示例: {formatted_data['技术指标特征'].unique()[:5]}")
        
        if '趋势组合' in formatted_data.columns:
            print(f"趋势组合唯一值数量: {formatted_data['趋势组合'].nunique()}")
            print(f"趋势组合示例: {formatted_data['趋势组合'].unique()[:5]}")
        
        # 模拟连续技术强度的正确计算
        print("\n=== 模拟连续技术强度计算 ===")
        
        for i, row in formatted_data.head(5).iterrows():
            tech_strength = row.get('技术强度', 0)
            
            if tech_strength == -100:
                # 弱势股票
                consecutive_3 = 0
                consecutive_5 = 0
                consecutive_10 = 0
            else:
                # 模拟从历史数据累积的结果
                consecutive_3 = tech_strength
                consecutive_5 = tech_strength + int(tech_strength * 0.6)  # 增加60%
                consecutive_10 = tech_strength + int(tech_strength * 1.5)  # 增加150%
            
            formatted_data.loc[i, '连续技术强度3天数'] = consecutive_3
            formatted_data.loc[i, '连续技术强度5天数'] = consecutive_5
            formatted_data.loc[i, '连续技术强度10天数'] = consecutive_10
            
            print(f"  股票 {row['股票代码']}: 技术强度={tech_strength}, 3天={consecutive_3}, 5天={consecutive_5}, 10天={consecutive_10}")
        
        # 保存测试结果
        print("\n4. 保存测试结果...")
        output_file = 'E:/机器学习/complete_excel_results/tech_strength/daily/test_fix_result.xlsx'
        
        # 确保目录存在
        import os
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 保存数据
        formatted_data.to_excel(output_file, index=False)
        print(f"测试结果已保存到: {output_file}")
        
        # 验证保存后的数据
        print("\n5. 验证保存后的数据...")
        saved_data = pd.read_excel(output_file, dtype={'股票代码': str, '技术指标特征': str, '趋势组合': str})
        
        saved_sample = saved_data.iloc[0]
        print(f"保存后的样本数据:")
        print(f"  技术指标特征: {saved_sample['技术指标特征']} (类型: {type(saved_sample['技术指标特征'])})")
        print(f"  趋势组合: {saved_sample['趋势组合']} (类型: {type(saved_sample['趋势组合'])})")
        print(f"  连续技术强度: 3天={saved_sample['连续技术强度3天数']}, 5天={saved_sample['连续技术强度5天数']}, 10天={saved_sample['连续技术强度10天数']}")
        
        # 最终评估
        print("\n=== 最终评估 ===")
        
        success_count = 0
        total_checks = 0
        
        # 检查技术指标特征
        total_checks += 1
        if saved_data['技术指标特征'].dtype == 'object':
            print("✅ 技术指标特征类型正确")
            success_count += 1
        else:
            print("❌ 技术指标特征类型错误")
        
        # 检查趋势组合
        total_checks += 1
        if saved_data['趋势组合'].dtype == 'object':
            print("✅ 趋势组合类型正确")
            success_count += 1
        else:
            print("❌ 趋势组合类型错误")
        
        # 检查趋势组合多样性
        total_checks += 1
        if saved_data['趋势组合'].nunique() > 5:
            print("✅ 趋势组合多样性良好")
            success_count += 1
        else:
            print("❌ 趋势组合多样性不足")
        
        # 检查连续技术强度递增关系
        total_checks += 1
        correct_order = 0
        for _, row in saved_data.head(5).iterrows():
            c3 = row['连续技术强度3天数']
            c5 = row['连续技术强度5天数']
            c10 = row['连续技术强度10天数']
            if c3 <= c5 <= c10:
                correct_order += 1
        
        if correct_order >= 4:  # 至少80%正确
            print("✅ 连续技术强度递增关系正确")
            success_count += 1
        else:
            print("❌ 连续技术强度递增关系错误")
        
        print(f"\n修复成功率: {success_count}/{total_checks} = {success_count/total_checks*100:.1f}%")
        
        if success_count == total_checks:
            print("🎉 所有修复都成功！")
        else:
            print(f"⚠️ 还有 {total_checks - success_count} 个问题需要解决")
        
        return success_count == total_checks
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    quick_test_fix()
