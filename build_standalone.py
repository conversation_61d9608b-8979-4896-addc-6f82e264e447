"""
打包脚本 - 创建包含所有依赖的独立可执行文件
"""

import os
import subprocess
import sys
import platform
import shutil

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'pyinstaller',
        'pandas',
        'numpy',
        'akshare',
        'tqdm',
        'openpyxl',
        'pyarrow',  # 用于parquet文件支持
        'requests',
        'lxml',
        'beautifulsoup4',
        'pywin32',  # Windows特定依赖
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print("\n需要安装以下依赖:")
        for package in missing_packages:
            print(f"  - {package}")
        
        install = input("\n是否自动安装这些依赖? (y/n): ")
        if install.lower() == 'y':
            for package in missing_packages:
                print(f"\n正在安装 {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"{package} 安装完成")
        else:
            print("\n请手动安装缺失的依赖后再运行此脚本")
            sys.exit(1)

def create_spec_file():
    """创建自定义spec文件"""
    spec_content = """# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['download_stock_data_gui.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'akshare',
        'pandas',
        'numpy',
        'tqdm',
        'openpyxl',
        'pyarrow',
        'requests',
        'lxml',
        'bs4',
        'win32api',
        'win32con',
        'tkinter',
        'queue',
        'threading',
        'datetime',
        'time',
        'os',
        'glob',
        'stock_data_manager',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='自动下载程序',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='stock_icon.ico',  # 如果有图标文件，请确保路径正确
)
"""
    
    # 检查是否有图标文件
    if not os.path.exists('stock_icon.ico'):
        # 修改spec文件，移除图标设置
        spec_content = spec_content.replace("    icon='stock_icon.ico',", "    icon=None,")
    
    with open('download_stock_data.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("已创建自定义spec文件")

def build_executable():
    """使用PyInstaller构建可执行文件"""
    print("\n开始构建可执行文件...")
    
    # 使用spec文件构建
    subprocess.check_call([
        sys.executable, 
        "-m", 
        "PyInstaller", 
        "download_stock_data.spec",
        "--clean"
    ])
    
    print("\n构建完成!")
    
    # 检查是否成功创建了可执行文件
    if os.path.exists(os.path.join('dist', '自动下载程序.exe')):
        print(f"\n可执行文件已创建: {os.path.abspath(os.path.join('dist', '自动下载程序.exe'))}")
        
        # 创建一个包含必要文件的发布目录
        create_release_package()
    else:
        print("\n错误: 未能创建可执行文件")

def create_release_package():
    """创建一个包含所有必要文件的发布包"""
    release_dir = "股票数据下载工具"
    
    # 如果目录已存在，先删除
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    
    # 创建发布目录
    os.makedirs(release_dir)
    
    # 复制可执行文件
    shutil.copy(os.path.join('dist', '自动下载程序.exe'), os.path.join(release_dir, '自动下载程序.exe'))
    
    # 创建默认数据目录
    os.makedirs(os.path.join(release_dir, 'data', 'stock_data', 'daily'), exist_ok=True)
    
    # 创建说明文件
    with open(os.path.join(release_dir, '使用说明.txt'), 'w', encoding='utf-8') as f:
        f.write("""股票数据下载工具使用说明
====================

1. 运行方法:
   双击"自动下载程序.exe"即可启动程序

2. 功能说明:
   - 可以下载指定日期范围内的A股股票历史数据
   - 数据按日期分别存储在Excel文件中
   - 默认保存在程序所在目录的data文件夹中

3. 使用步骤:
   a. 设置数据输出目录(可选)
   b. 设置日期范围
   c. 点击"开始下载"按钮
   d. 等待下载完成

4. 注意事项:
   - 首次运行时可能需要等待较长时间
   - 下载过程中请保持网络连接
   - 如遇到问题，请查看程序日志窗口的提示信息
""")
    
    print(f"\n发布包已创建: {os.path.abspath(release_dir)}")
    print("包含以下文件:")
    print(f"  - {release_dir}/自动下载程序.exe")
    print(f"  - {release_dir}/使用说明.txt")
    print(f"  - {release_dir}/data/ (默认数据目录)")

def main():
    """主函数"""
    print("=" * 50)
    print("股票数据下载工具打包脚本")
    print("=" * 50)
    
    # 检查操作系统
    if platform.system() != 'Windows':
        print("警告: 此脚本设计用于Windows系统，在其他系统上可能无法正常工作")
    
    # 检查依赖
    print("\n检查依赖...")
    check_dependencies()
    
    # 创建spec文件
    print("\n创建打包配置...")
    create_spec_file()
    
    # 构建可执行文件
    build_executable()
    
    print("\n打包过程完成!")
    print("=" * 50)

if __name__ == "__main__":
    main()
