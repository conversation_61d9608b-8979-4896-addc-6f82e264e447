"""
分析策略1在特定日期的表现
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys
from stock_predictor import (
    load_data, preprocess_data, load_model, 
    apply_strategy_1, print_header
)

def analyze_strategy1_conditions(date_str, data_file_path='股票明细.xlsx'):
    """
    分析策略1的条件在特定日期的满足情况
    
    参数:
    date_str: 日期字符串，格式为'YYYY-MM-DD'
    data_file_path: 数据文件路径
    """
    print_header(f"分析策略1在 {date_str} 的条件满足情况")
    
    # 加载模型
    model, scaler, features = load_model()
    if model is None:
        print("无法加载模型，请先训练模型")
        return
    
    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return
    
    # 预处理数据
    processed_data = preprocess_data(stock_data)
    
    # 转换日期
    date = pd.to_datetime(date_str)
    
    # 获取指定日期的数据
    date_data = processed_data[processed_data['日期'] == date]
    
    if len(date_data) == 0:
        print(f"在 {date_str} 没有找到数据")
        return
    
    print(f"日期 {date_str} 的数据记录数: {len(date_data)}")
    
    # 提取特征
    X = date_data[features]
    
    # 处理缺失值
    valid_indices = ~X.isnull().any(axis=1)
    X = X[valid_indices]
    date_data_filtered = date_data.loc[valid_indices.index[valid_indices]]
    
    if len(X) == 0:
        print(f"预测数据不足，无法进行分析")
        return
    
    # 标准化特征
    X_scaled = scaler.transform(X)
    
    # 预测盈利概率
    pred_proba = model.predict_proba(X_scaled)[:, 1]
    
    # 创建预测结果DataFrame
    predictions = pd.DataFrame({
        '股票代码': date_data_filtered['股票代码'],
        '股票名称': date_data_filtered['股票名称'],
        '涨跌幅': date_data_filtered['涨跌幅'] if '涨跌幅' in date_data_filtered.columns else 0,
        '技术强度': date_data_filtered['技术强度'],
        '连续技术强度天数': date_data_filtered['连续技术强度天数'],
        '连续技术强度5天数': date_data_filtered['连续技术强度5天数'],
        '预测盈利概率': pred_proba,
        '实际盈利': date_data_filtered['是否盈利'],
        '实际收益率': date_data_filtered['两日收益率']
    })
    
    # 分析策略1的条件
    # 条件1: 预测盈利概率>78%
    condition1 = predictions['预测盈利概率'] > 0.78
    # 条件2: 技术强度≥70
    condition2 = predictions['技术强度'] >= 70
    # 条件3: 连续技术强度5天数≥400
    condition3 = predictions['连续技术强度5天数'] >= 400
    
    # 满足各个条件的股票数量
    print(f"\n策略1条件满足情况:")
    print(f"条件1 (预测盈利概率>78%): {condition1.sum()} 只股票")
    print(f"条件2 (技术强度≥70): {condition2.sum()} 只股票")
    print(f"条件3 (连续技术强度5天数≥400): {condition3.sum()} 只股票")
    print(f"同时满足条件1和条件2: {(condition1 & condition2).sum()} 只股票")
    print(f"同时满足条件1和条件3: {(condition1 & condition3).sum()} 只股票")
    print(f"同时满足条件2和条件3: {(condition2 & condition3).sum()} 只股票")
    print(f"同时满足所有条件: {(condition1 & condition2 & condition3).sum()} 只股票")
    
    # 查看接近满足条件的股票
    print("\n接近满足条件的股票:")
    
    # 预测盈利概率>75%且技术强度≥70且连续技术强度5天数≥350
    close_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &
        (predictions['技术强度'] >= 70) &
        (predictions['连续技术强度5天数'] >= 350)
    ]
    
    if len(close_stocks) > 0:
        print(f"找到 {len(close_stocks)} 只接近满足条件的股票:")
        for i, row in close_stocks.iterrows():
            print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
    else:
        print("没有找到接近满足条件的股票")
    
    # 查看预测盈利概率最高的10只股票
    print("\n预测盈利概率最高的10只股票:")
    top_proba = predictions.sort_values('预测盈利概率', ascending=False).head(10)
    for i, row in top_proba.iterrows():
        print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
    
    # 查看技术强度最高的10只股票
    print("\n技术强度最高的10只股票:")
    top_strength = predictions.sort_values('技术强度', ascending=False).head(10)
    for i, row in top_strength.iterrows():
        print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
    
    # 查看连续技术强度5天数最高的10只股票
    print("\n连续技术强度5天数最高的10只股票:")
    top_cumulative = predictions.sort_values('连续技术强度5天数', ascending=False).head(10)
    for i, row in top_cumulative.iterrows():
        print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        date_str = sys.argv[1]
    else:
        date_str = "2025-05-07"
    
    analyze_strategy1_conditions(date_str)
