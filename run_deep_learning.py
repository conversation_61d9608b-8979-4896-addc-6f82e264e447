"""
运行深度学习模型和策略
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import joblib
import argparse
import deep_learning_model
import deep_learning_strategy

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header(title):
    """打印标题"""
    print("\n" + " " * 24 + title)
    print("=" * 80 + "\n")

def train_deep_learning_model(data_file):
    """训练深度学习模型"""
    print_header("训练深度学习模型")

    # 加载数据
    data = deep_learning_model.load_data(data_file)
    if data is None:
        print("数据加载失败，程序退出")
        return None

    # 预处理数据
    processed_data = deep_learning_model.preprocess_data(data)

    # 训练深度学习模型
    model_data = deep_learning_model.train_deep_learning_model(processed_data)

    return model_data

def backtest_deep_learning_strategy(data_file, model_data_path, start_date, end_date, output_file):
    """回测深度学习高胜率策略"""
    print_header("回测深度学习高胜率策略")

    # 加载数据
    data = deep_learning_model.load_data(data_file)
    if data is None:
        print("数据加载失败，程序退出")
        return None

    # 预处理数据
    processed_data = deep_learning_model.preprocess_data(data)

    # 加载模型数据
    model_data = deep_learning_strategy.load_model_data(model_data_path)
    if model_data is None:
        print("模型数据加载失败，程序退出")
        return None

    # 回测深度学习高胜率策略
    result = deep_learning_strategy.backtest_deep_learning_strategy(processed_data, model_data, start_date, end_date, output_file)

    return result

def generate_recommendations(data_file, model_data_path, date, output_file):
    """生成股票推荐"""
    print_header("生成股票推荐")

    # 加载数据
    data = deep_learning_model.load_data(data_file)
    if data is None:
        print("数据加载失败，程序退出")
        return None

    # 预处理数据
    processed_data = deep_learning_model.preprocess_data(data)

    # 加载模型数据
    model_data = deep_learning_strategy.load_model_data(model_data_path)
    if model_data is None:
        print("模型数据加载失败，程序退出")
        return None

    # 加载深度学习模型
    model = deep_learning_strategy.load_deep_learning_model(model_data)
    if model is None:
        print("模型加载失败，程序退出")
        return None

    model_type = model_data['model_type']

    # 获取指定日期的数据
    date_obj = pd.to_datetime(date)
    current_data = processed_data[processed_data['日期'] == date_obj]

    if len(current_data) == 0:
        print(f"没有找到 {date} 的数据")
        return None

    # 预处理特征
    X_pred_scaled = deep_learning_strategy.preprocess_features(current_data, model_data['features'], model_data['scaler'])

    # 预测盈利概率
    if model_type == 'keras':
        pred_proba = model.predict(X_pred_scaled)
    else:
        pred_proba = model.predict_proba(X_pred_scaled)[:, 1].reshape(-1, 1)

    # 创建预测结果DataFrame
    predictions = pd.DataFrame({
        '日期': date,
        '股票代码': current_data['股票代码'].values,
        '股票名称': current_data['股票名称'].values,
        '技术强度': current_data['技术强度'].values,
        '连续技术强度5天数': current_data['连续技术强度5天数'].values,
        '技术强度趋势': current_data['技术强度趋势'].values,
        '价格趋势': current_data['价格趋势'].values,
        '涨跌幅趋势': current_data['涨跌幅趋势'].values,
        '涨跌幅': current_data['涨跌幅'].values,
        '技术指标_均线多头排列': current_data['技术指标_均线多头排列'].values,
        '技术指标_MACD金叉': current_data['技术指标_MACD金叉'].values,
        '技术指标_RSI反弹': current_data['技术指标_RSI反弹'].values,
        '技术指标_KDJ金叉': current_data['技术指标_KDJ金叉'].values,
        '技术指标_布林带突破': current_data['技术指标_布林带突破'].values,
        '看涨技术指标数量': current_data['看涨技术指标数量'].values,
        '开盘涨跌': current_data['开盘涨跌'].values,
        '历史胜率': current_data['历史胜率'].values if '历史胜率' in current_data.columns else 0.5,
        '预测盈利概率': pred_proba.flatten()
    })

    # 应用深度学习高胜率策略
    strategy_stocks = deep_learning_strategy.apply_deep_learning_strategy(predictions)

    # 保存推荐结果
    if len(strategy_stocks) > 0:
        strategy_stocks.to_excel(output_file, index=False)
        print(f"推荐股票数: {len(strategy_stocks)}")
        print(f"推荐结果已保存到: {output_file}")

        # 显示推荐股票
        print("\n推荐股票:")
        for i, row in strategy_stocks.iterrows():
            print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 看涨技术指标数量={row['看涨技术指标数量']}, 历史胜率={row['历史胜率']:.2f}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
    else:
        print("没有推荐的股票")

    return strategy_stocks

def find_latest_model_data():
    """查找最新的模型数据"""
    model_dir = 'models'
    if not os.path.exists(model_dir):
        return None

    # 使用统一的文件名
    model_data_path = os.path.join(model_dir, "latest_model_data.joblib")
    if os.path.exists(model_data_path):
        return model_data_path

    # 兼容旧版本
    model_data_files = [f for f in os.listdir(model_dir) if (f.startswith('dl_model_data_') or f.startswith('ml_model_data_')) and f.endswith('.joblib')]
    if not model_data_files:
        return None

    latest_model_data_file = max(model_data_files)
    model_data_path = os.path.join(model_dir, latest_model_data_file)

    return model_data_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='深度学习股票高胜率策略')
    parser.add_argument('--train', action='store_true', help='训练深度学习模型')
    parser.add_argument('--backtest', action='store_true', help='回测深度学习高胜率策略')
    parser.add_argument('--recommend', action='store_true', help='生成股票推荐')
    parser.add_argument('--data_file', type=str, default='股票明细.xlsx', help='股票数据文件')
    parser.add_argument('--model_data', type=str, help='模型数据文件')
    parser.add_argument('--start_date', type=str, help='回测开始日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--end_date', type=str, help='回测结束日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--date', type=str, help='推荐日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--output', type=str, help='输出文件')

    args = parser.parse_args()

    clear_screen()

    try:
        # 训练深度学习模型
        if args.train:
            model_data = train_deep_learning_model(args.data_file)
            if model_data is None:
                print("模型训练失败，程序退出")
                return

        # 回测深度学习高胜率策略
        if args.backtest:
            if not args.start_date or not args.end_date:
                print("回测需要指定开始日期和结束日期")
                return

            model_data_path = args.model_data if args.model_data else find_latest_model_data()
            if model_data_path is None:
                print("没有找到模型数据文件，请先训练模型或指定模型数据文件")
                return

            output_file = args.output if args.output else f"深度学习高胜率策略回测结果_{args.start_date}至{args.end_date}.txt"
            backtest_deep_learning_strategy(args.data_file, model_data_path, args.start_date, args.end_date, output_file)

        # 生成股票推荐
        if args.recommend:
            if not args.date:
                print("推荐需要指定日期")
                return

            model_data_path = args.model_data if args.model_data else find_latest_model_data()
            if model_data_path is None:
                print("没有找到模型数据文件，请先训练模型或指定模型数据文件")
                return

            output_file = args.output if args.output else f"深度学习高胜率策略推荐股票_{args.date}.xlsx"
            generate_recommendations(args.data_file, model_data_path, args.date, output_file)

    except Exception as e:
        print(f"执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
