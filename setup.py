"""
Packaging script to convert the backtesting program to an executable file
"""

import sys
from cx_Freeze import setup, Executable

# Dependencies
build_exe_options = {
    "packages": ["os", "pandas", "numpy", "joblib", "sklearn", "argparse", "tkinter"],
    "excludes": [],
    "include_files": ["stock_data.xlsx", "models/"]
}

# Create two executable files: one with GUI, one command line version
base_gui = None
base_console = None
if sys.platform == "win32":
    base_gui = "Win32GUI"  # Use Windows GUI
    base_console = "Console"  # Use console

executables = [
    # GUI version
    Executable(
        "stock_analyzer.py",  # Main program file
        base=base_gui,
        target_name="stock_analyzer_GUI.exe",  # Executable file name
        icon=None,  # Icon file
        shortcut_name="Stock High Win Rate Strategy Analysis Tool",
        shortcut_dir="DesktopFolder"
    ),
    # Command line version
    Executable(
        "stock_analyzer.py",  # Main program file
        base=base_console,
        target_name="stock_analyzer.exe",  # Executable file name
        icon=None  # Icon file
    )
]

# Setup
setup(
    name="Stock High Win Rate Strategy Analysis Tool",
    version="1.0",
    description="Machine Learning Based Stock High Win Rate Strategy Analysis Tool",
    author="AI Assistant",
    options={"build_exe": build_exe_options},
    executables=executables
)
