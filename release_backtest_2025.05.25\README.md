# 策略回测系统 v2025.05.25 修复版

## 📋 程序说明
这是一个股票策略回测系统GUI程序，可以对多种交易策略进行历史数据回测分析。

## 🔧 修复内容
- ✅ 修复了买入日期和卖出日期涨跌幅更新问题
- ✅ 优化了股票代码格式处理逻辑
- ✅ 改进了数据关联准确性
- ✅ 精简了日志输出，提升运行效率

## 🚀 使用方法
1. 双击 `策略回测系统_v2025.05.25_修复版.exe` 启动程序
2. 在GUI界面中选择要回测的策略ID
3. 点击"开始回测"按钮执行回测
4. 查看实时日志输出和进度
5. 回测完成后查看生成的Excel结果文件

## 📁 数据要求
程序需要以下数据文件：
- **技术强度数据**：E:\机器学习\complete_excel_results\tech_strength\daily\*.xlsx
- **历史股票数据**：E:\机器学习\complete_excel_results\stock_data\daily\*.xlsx  
- **策略配置文件**：complete_excel_results\所有策略汇总.xlsx

## 📊 输出结果
- **策略汇总表**：所有策略汇总_已回测.xlsx
- **详细分析**：new_strategy_details\strategy_X.xlsx

## 🎯 功能特点
- 支持单个策略回测和批量策略回测
- 实时显示回测进度和日志
- 自动生成详细的Excel分析报告
- 包含买入日、卖出日涨跌幅等关键指标
- 计算连续交易日累计涨幅
- 提供完整的策略绩效统计

## 🔧 技术支持
如有问题，请检查：
1. 数据文件路径是否正确
2. Excel文件是否可以正常打开
3. 系统是否有足够的内存和磁盘空间（建议8GB以上内存）
4. 确保没有其他程序占用Excel文件

## 📅 版本信息
- **版本号**：v2025.05.25 修复版
- **构建时间**：2025-05-25 22:58:55
- **Python版本**：3.13.1
- **主要修复**：股票代码格式处理和数据关联逻辑

## 🆕 更新日志
### v2025.05.25 修复版
- 修复买入日期和卖出日期涨跌幅个别未更新的问题
- 优化股票代码格式处理，保持原始前缀格式
- 改进数据关联逻辑，提高数据匹配准确性
- 精简日志输出，减少冗余信息
- 提升程序运行效率和稳定性
