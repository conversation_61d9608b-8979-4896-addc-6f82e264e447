import pandas as pd
import numpy as np
from datetime import datetime
import os
from stock_crawler import StockCrawler

class EnhancedStockSelector:
    """
    增强版股票选择器，结合爬虫数据和技术强度分析
    """
    
    def __init__(self):
        """初始化选择器"""
        # 创建数据目录
        if not os.path.exists('results'):
            os.makedirs('results')
    
    def load_stock_data(self):
        """
        加载股票明细数据
        返回DataFrame
        """
        print("加载股票明细数据...")
        try:
            data = pd.read_excel('股票明细.xlsx')
            data['日期'] = pd.to_datetime(data['日期'])
            print(f"成功加载 {len(data)} 条股票数据")
            return data
        except Exception as e:
            print(f"加载股票明细数据失败: {e}")
            return pd.DataFrame()
    
    def load_crawler_data(self):
        """
        加载爬虫获取的数据
        如果没有今日数据，则运行爬虫获取
        返回DataFrame
        """
        print("加载爬虫数据...")
        today = datetime.now().strftime('%Y%m%d')
        file_path = f'data/stocks_with_strong_industries_{today}.xlsx'
        
        if os.path.exists(file_path):
            try:
                crawler_data = pd.read_excel(file_path)
                print(f"成功加载今日爬虫数据，共 {len(crawler_data)} 条记录")
                return crawler_data
            except Exception as e:
                print(f"加载爬虫数据失败: {e}")
                print("将重新运行爬虫获取数据...")
        else:
            print("未找到今日爬虫数据，将运行爬虫获取...")
        
        # 运行爬虫获取数据
        crawler = StockCrawler()
        crawler_data = crawler.run()
        return crawler_data
    
    def merge_data(self, stock_data, crawler_data):
        """
        合并股票明细数据和爬虫数据
        参数:
            stock_data: 股票明细DataFrame
            crawler_data: 爬虫数据DataFrame
        返回合并后的DataFrame
        """
        print("合并股票明细数据和爬虫数据...")
        
        # 获取最新日期的股票数据
        latest_date = stock_data['日期'].max()
        latest_stock_data = stock_data[stock_data['日期'] == latest_date]
        
        print(f"最新日期: {latest_date.date()}, 股票数量: {len(latest_stock_data)}")
        
        # 标准化股票代码格式
        latest_stock_data['标准代码'] = latest_stock_data['股票代码'].apply(
            lambda x: x.split('.')[1].lower() + '.' + x.split('.')[0].lower()
        )
        
        # 确保爬虫数据中的股票代码格式一致
        if '标准代码' not in crawler_data.columns:
            crawler_data['标准代码'] = crawler_data['股票代码']
        
        # 合并数据
        merged_data = pd.merge(
            latest_stock_data,
            crawler_data[['标准代码', '行业', '是否行业龙头', '是否强势行业']],
            on='标准代码',
            how='left'
        )
        
        # 填充缺失值
        merged_data['是否行业龙头'] = merged_data['是否行业龙头'].fillna(False)
        merged_data['是否强势行业'] = merged_data['是否强势行业'].fillna(False)
        
        print(f"合并后的数据共 {len(merged_data)} 条记录")
        return merged_data
    
    def select_stocks(self, merged_data, tech_strength=100, max_stocks=10):
        """
        选择推荐股票
        参数:
            merged_data: 合并后的DataFrame
            tech_strength: 技术强度阈值
            max_stocks: 最大推荐股票数量
        返回推荐股票DataFrame
        """
        print(f"选择技术强度={tech_strength}的推荐股票...")
        
        # 筛选技术强度符合条件的股票
        strength_stocks = merged_data[merged_data['技术强度'] == tech_strength]
        print(f"技术强度={tech_strength}的股票共 {len(strength_stocks)} 只")
        
        # 计算每只股票的综合得分
        strength_stocks['综合得分'] = 0
        
        # 基础分：技术强度=100得50分
        strength_stocks['综合得分'] += 50
        
        # 行业龙头加分：20分
        strength_stocks.loc[strength_stocks['是否行业龙头'], '综合得分'] += 20
        
        # 强势行业加分：15分
        strength_stocks.loc[strength_stocks['是否强势行业'], '综合得分'] += 15
        
        # 涨跌幅加分：最高10分
        max_change = strength_stocks['涨跌幅'].max()
        if max_change > 0:
            strength_stocks['涨跌幅得分'] = strength_stocks['涨跌幅'] / max_change * 10
            strength_stocks['综合得分'] += strength_stocks['涨跌幅得分']
        
        # 技术指标加分：每个技术指标1分，最高6分
        for indicator in ['均线多头排列', '成交量放大', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破']:
            strength_stocks[f'{indicator}得分'] = strength_stocks['技术指标'].str.contains(indicator).astype(int)
            strength_stocks['综合得分'] += strength_stocks[f'{indicator}得分']
        
        # 按综合得分排序
        strength_stocks = strength_stocks.sort_values('综合得分', ascending=False)
        
        # 确保行业分散
        selected_stocks = pd.DataFrame()
        selected_industries = set()
        
        # 第一轮：选择每个行业得分最高的股票
        for industry, group in strength_stocks.groupby('行业'):
            if len(selected_stocks) < max_stocks:
                top_stock = group.iloc[0:1]
                selected_stocks = pd.concat([selected_stocks, top_stock])
                selected_industries.add(industry)
        
        # 第二轮：从剩余股票中选择得分最高的股票，直到达到最大数量
        remaining_stocks = strength_stocks[~strength_stocks.index.isin(selected_stocks.index)]
        remaining_slots = max_stocks - len(selected_stocks)
        
        if remaining_slots > 0 and len(remaining_stocks) > 0:
            additional_stocks = remaining_stocks.head(remaining_slots)
            selected_stocks = pd.concat([selected_stocks, additional_stocks])
        
        print(f"最终选择 {len(selected_stocks)} 只推荐股票")
        
        # 保存推荐股票
        today = datetime.now().strftime('%Y%m%d')
        selected_stocks.to_excel(f'results/recommended_stocks_{today}.xlsx', index=False)
        print(f"已将推荐股票保存至 results/recommended_stocks_{today}.xlsx")
        
        return selected_stocks
    
    def run(self):
        """运行股票选择器"""
        print("开始运行增强版股票选择器...")
        
        # 加载股票明细数据
        stock_data = self.load_stock_data()
        if len(stock_data) == 0:
            print("无法获取股票明细数据，程序终止")
            return
        
        # 加载爬虫数据
        crawler_data = self.load_crawler_data()
        if len(crawler_data) == 0:
            print("无法获取爬虫数据，程序终止")
            return
        
        # 合并数据
        merged_data = self.merge_data(stock_data, crawler_data)
        
        # 选择推荐股票
        recommended_stocks = self.select_stocks(merged_data)
        
        print("增强版股票选择器运行完成!")
        
        # 输出推荐股票
        print("\n今日推荐股票:")
        for i, (_, row) in enumerate(recommended_stocks.iterrows(), 1):
            print(f"{i}. {row['股票名称']}({row['股票代码']}): 行业={row['行业']}, 综合得分={row['综合得分']:.2f}, {'行业龙头 ' if row['是否行业龙头'] else ''}{'强势行业 ' if row['是否强势行业'] else ''}")
        
        return recommended_stocks

if __name__ == "__main__":
    selector = EnhancedStockSelector()
    result = selector.run()
