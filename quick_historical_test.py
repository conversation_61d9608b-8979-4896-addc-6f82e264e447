#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def quick_test():
    print("=== 快速历史数据测试 ===")
    
    # 测试单个文件读取
    tech_system_path = r"E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0"
    
    # 测试读取5月15日的数据
    date_str = "2025-05-15"
    subdir = f"选股结果_{date_str}"
    filename = f"强势股选股结果_{date_str}.xlsx"
    file_path = os.path.join(tech_system_path, subdir, filename)
    
    print(f"测试文件路径: {file_path}")
    print(f"文件存在: {os.path.exists(file_path)}")
    
    if os.path.exists(file_path):
        try:
            df = pd.read_excel(file_path)
            print(f"成功读取文件，行数: {len(df)}")
            print(f"列名: {df.columns.tolist()}")
            
            if '股票代码' in df.columns and '技术强度' in df.columns:
                # 处理股票代码格式
                df['股票代码_clean'] = df['股票代码'].astype(str).apply(
                    lambda x: x.split('.')[-1] if '.' in x else (x[2:] if x.startswith(('sh', 'sz')) else x)
                )
                
                print(f"前5个股票的技术强度:")
                for i in range(min(5, len(df))):
                    row = df.iloc[i]
                    print(f"  {row['股票代码']} -> {row['股票代码_clean']}: 技术强度 {row['技术强度']}")
                
                # 测试连续技术强度计算逻辑
                print(f"\n模拟连续技术强度计算:")
                
                # 假设我们有3天的数据
                sample_stock = df.iloc[0]['股票代码_clean']
                sample_strength = df.iloc[0]['技术强度']
                
                # 模拟历史数据
                historical_strengths = [
                    (pd.to_datetime('2025-05-13'), sample_strength - 10),
                    (pd.to_datetime('2025-05-14'), sample_strength - 5),
                    (pd.to_datetime('2025-05-15'), sample_strength)
                ]
                
                # 计算3天累积
                total_3_days = sum([strength for _, strength in historical_strengths])
                print(f"  股票 {sample_stock} 的3天累积技术强度: {total_3_days}")
                
                # 计算5天累积（假设前面还有2天）
                total_5_days = total_3_days + (sample_strength - 20) + (sample_strength - 15)
                print(f"  股票 {sample_stock} 的5天累积技术强度: {total_5_days}")
                
                # 计算10天累积（假设前面还有5天）
                total_10_days = total_5_days + sum([sample_strength - 25 - i*5 for i in range(5)])
                print(f"  股票 {sample_stock} 的10天累积技术强度: {total_10_days}")
                
                print(f"\n✅ 连续技术强度应该显示递增关系: 3天={total_3_days} < 5天={total_5_days} < 10天={total_10_days}")
                
        except Exception as e:
            print(f"读取文件时出错: {e}")
    
    # 测试目录结构
    print(f"\n=== 测试目录结构 ===")
    if os.path.exists(tech_system_path):
        subdirs = [d for d in os.listdir(tech_system_path) if d.startswith('选股结果_')]
        print(f"找到 {len(subdirs)} 个历史数据目录")
        print(f"最近5个目录: {sorted(subdirs)[-5:]}")
        
        # 统计总数据量
        total_records = 0
        valid_files = 0
        
        for subdir in subdirs[:5]:  # 只检查前5个目录
            date_str = subdir.replace('选股结果_', '')
            filename = f"强势股选股结果_{date_str}.xlsx"
            file_path = os.path.join(tech_system_path, subdir, filename)
            
            if os.path.exists(file_path):
                try:
                    df = pd.read_excel(file_path)
                    total_records += len(df)
                    valid_files += 1
                    print(f"  {date_str}: {len(df)} 条记录")
                except:
                    pass
        
        print(f"\n前5个文件统计: {valid_files} 个有效文件, 共 {total_records} 条记录")
        estimated_total = total_records * len(subdirs) // 5
        print(f"估计总数据量: 约 {estimated_total} 条记录")
        
    print(f"\n✅ 快速测试完成")

if __name__ == "__main__":
    quick_test()
