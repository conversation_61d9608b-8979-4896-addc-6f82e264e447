import pandas as pd
import os
import glob

print("=== 数据关联失败原因分析 ===")

# 1. 检查技术强度数据
tech_strength_dir = r'E:\机器学习\complete_excel_results\tech_strength\daily'
print(f"\n1. 检查技术强度数据目录: {tech_strength_dir}")

if os.path.exists(tech_strength_dir):
    tech_files = glob.glob(os.path.join(tech_strength_dir, "tech_strength_strong_*_smart.xlsx"))
    print(f"找到 {len(tech_files)} 个技术强度文件")
    
    if tech_files:
        # 检查第一个文件的结构
        first_file = tech_files[0]
        print(f"\n检查文件: {os.path.basename(first_file)}")
        
        try:
            tech_df = pd.read_excel(first_file)
            print(f"技术强度数据列名: {tech_df.columns.tolist()}")
            print(f"数据形状: {tech_df.shape}")
            
            if not tech_df.empty:
                print("前3行数据:")
                print(tech_df.head(3))
                
                # 检查股票代码格式
                if '股票代码' in tech_df.columns:
                    sample_codes = tech_df['股票代码'].head(5).tolist()
                    print(f"股票代码样例: {sample_codes}")
                    
                    # 检查是否有前缀
                    has_prefix = any(code.startswith(('sh.', 'sz.', 'sh', 'sz')) for code in sample_codes if isinstance(code, str))
                    print(f"股票代码是否有前缀: {has_prefix}")
                    
        except Exception as e:
            print(f"读取技术强度文件出错: {e}")
else:
    print("技术强度数据目录不存在")

# 2. 检查历史数据
print(f"\n2. 检查历史数据")
stock_data_dir = r'E:\机器学习\complete_excel_results\stock_data\daily'
print(f"历史数据目录: {stock_data_dir}")

if os.path.exists(stock_data_dir):
    history_files = glob.glob(os.path.join(stock_data_dir, "stock_data_*.xlsx")) + glob.glob(os.path.join(stock_data_dir, "stock_data_*.parquet"))
    print(f"找到 {len(history_files)} 个历史数据文件")
    
    if history_files:
        # 检查第一个文件的结构
        first_file = history_files[0]
        print(f"\n检查文件: {os.path.basename(first_file)}")
        
        try:
            if first_file.endswith('.parquet'):
                history_df = pd.read_parquet(first_file)
            else:
                history_df = pd.read_excel(first_file)
                
            print(f"历史数据列名: {history_df.columns.tolist()}")
            print(f"数据形状: {history_df.shape}")
            
            if not history_df.empty:
                print("前3行数据:")
                print(history_df.head(3))
                
                # 检查股票代码格式
                if '证券代码' in history_df.columns:
                    sample_codes = history_df['证券代码'].head(5).tolist()
                    print(f"股票代码样例: {sample_codes}")
                    
                    # 检查是否有前缀
                    has_prefix = any(str(code).startswith(('sh.', 'sz.', 'sh', 'sz')) for code in sample_codes if pd.notna(code))
                    print(f"股票代码是否有前缀: {has_prefix}")
                    
        except Exception as e:
            print(f"读取历史数据文件出错: {e}")
else:
    print("历史数据目录不存在")

# 3. 模拟关联键创建过程
print(f"\n3. 模拟关联键创建过程")

# 假设的技术强度数据
tech_sample = {
    '股票代码': ['sh.000001', 'sz.000002', '000001', '000002'],
    '日期': ['2025-05-15', '2025-05-15', '2025-05-16', '2025-05-16'],
    '技术强度': [28, 28, 28, 28]
}
tech_df_sample = pd.DataFrame(tech_sample)

# 假设的历史数据
history_sample = {
    '证券代码': ['000001', '000002', '000001', '000002'],
    '日期': ['2025-05-15', '2025-05-15', '2025-05-16', '2025-05-16'],
    '涨跌幅': [2.5, -1.2, 3.1, 0.8]
}
history_df_sample = pd.DataFrame(history_sample)

print("技术强度数据样例:")
print(tech_df_sample)

print("\n历史数据样例:")
print(history_df_sample)

# 模拟股票代码处理
def remove_prefix(code):
    if isinstance(code, str):
        if code.startswith('sh.') or code.startswith('sz.'):
            return code[3:]
        elif code.startswith('sh') or code.startswith('sz'):
            return code[2:]
    return code

tech_df_sample['历史股票代码'] = tech_df_sample['股票代码'].apply(remove_prefix)
print(f"\n处理后的技术强度股票代码:")
print(tech_df_sample[['股票代码', '历史股票代码']])

# 创建关联键
tech_df_sample['买入关联键'] = tech_df_sample['历史股票代码'] + '_' + tech_df_sample['日期']
history_df_sample['关联键'] = history_df_sample['证券代码'].astype(str) + '_' + history_df_sample['日期']

print(f"\n技术强度关联键:")
print(tech_df_sample['买入关联键'].tolist())

print(f"\n历史数据关联键:")
print(history_df_sample['关联键'].tolist())

# 检查匹配情况
matched_keys = set(tech_df_sample['买入关联键']) & set(history_df_sample['关联键'])
print(f"\n匹配的关联键: {matched_keys}")
print(f"匹配率: {len(matched_keys)}/{len(tech_df_sample)} = {len(matched_keys)/len(tech_df_sample)*100:.1f}%")

# 创建映射并测试
history_key_to_pctChg = dict(zip(history_df_sample['关联键'], history_df_sample['涨跌幅']))
tech_df_sample['买入日涨跌幅'] = tech_df_sample['买入关联键'].map(history_key_to_pctChg)

print(f"\n关联结果:")
print(tech_df_sample[['股票代码', '买入关联键', '买入日涨跌幅']])

print(f"\n=== 分析完成 ===")
