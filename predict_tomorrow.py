"""
预测明天的股票走势

根据历史数据预测明天100%上涨的股票
"""

import os
import sys
from datetime import datetime

# 添加当前目录到系统路径，以便导入trading_strategies包
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from trading_strategies import (
        predict_next_day_stocks,
        predict_all_strategies_next_day,
        train_and_save_model
    )
except ImportError:
    print("无法导入trading_strategies包，请确保该包已正确安装")
    sys.exit(1)

def clear_screen():
    """清除屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """打印界面标题"""
    print("=" * 80)
    print("                        预测明天的股票走势")
    print("=" * 80)
    print()

def print_menu():
    """打印主菜单"""
    print("\n主菜单:")
    print("1. 训练模型")
    print("2. 使用策略1预测明天的股票")
    print("3. 使用策略A预测明天的股票")
    print("4. 使用策略B预测明天的股票")
    print("5. 使用策略C预测明天的股票")
    print("6. 使用所有策略预测明天的股票")
    print("0. 退出")
    print()

def train_model():
    """训练模型功能"""
    clear_screen()
    print_header()
    print("训练模型")
    print("-" * 80)
    
    print("开始训练模型，这可能需要几分钟时间...")
    result = train_and_save_model(data_file_path='股票明细.xlsx', model_dir='trained_models')
    
    if result['success']:
        print("\n模型训练成功！")
        print(f"训练用时: {result['duration']:.2f} 秒")
        print(f"使用记录数: {result['valid_record_count']}")
        print(f"模型文件: {result['model_file']}")
        
        print("\n特征重要性:")
        for i, row in result['feature_importance'].iterrows():
            if i < 10:  # 只显示前10个特征
                print(f"{row['feature']}: {row['importance']:.4f}")
    else:
        print("\n模型训练失败！")
        print(f"错误信息: {result['error']}")
    
    input("\n按Enter键返回主菜单...")

def display_recommendations(recommended_stocks, risk_description):
    """显示推荐股票和风险说明"""
    if recommended_stocks is None:
        print("\n生成推荐失败！")
        return
    
    if len(recommended_stocks) == 0:
        print("\n没有找到符合条件的股票")
        return
    
    print("\n风险说明:")
    print(f"策略: {risk_description['strategy_description']}")
    print(f"预期胜率: {risk_description['expected_win_rate']}")
    print(f"预期收益率: {risk_description['expected_return']}")
    print(f"买入风险: {risk_description['buy_risk']}")
    print(f"卖出风险: {risk_description['sell_risk']}")
    print(f"重要提示: {risk_description['important_note']}")
    print(f"交易策略: {risk_description['trading_strategy']}")
    
    print("\n推荐股票:")
    print("-" * 80)
    print(f"{'股票代码':<10} {'股票名称':<15} {'技术强度':<8} {'连续技术强度5天数':<15} {'预测盈利概率':<12}")
    print("-" * 80)
    
    for i, row in recommended_stocks.iterrows():
        print(f"{row['股票代码']:<10} {row['股票名称']:<15} {row['技术强度']:<8} {row['连续技术强度5天数']:<15} {row['预测盈利概率']*100:.2f}%")
    
    print("-" * 80)
    print(f"共 {len(recommended_stocks)} 只推荐股票")
    print(f"结果已保存至: {risk_description['result_file']}")

def predict_strategy(strategy_name):
    """预测明天的股票走势"""
    clear_screen()
    print_header()
    
    strategy_descriptions = {
        'strategy_1': "策略1：100%高胜率策略",
        'strategy_A': "策略A：最高胜率策略",
        'strategy_B': "策略B：最高收益率策略",
        'strategy_C': "策略C：平衡策略（胜率和交易机会的平衡）"
    }
    
    print(f"使用{strategy_descriptions[strategy_name]}预测明天的股票")
    print("-" * 80)
    
    print(f"\n正在使用{strategy_descriptions[strategy_name]}预测明天的股票...")
    
    try:
        recommended_stocks, risk_description = predict_next_day_stocks(
            strategy_name=strategy_name,
            data_file_path='股票明细.xlsx',
            use_saved_model=True
        )
        
        display_recommendations(recommended_stocks, risk_description)
    except Exception as e:
        print(f"\n预测失败: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按Enter键返回主菜单...")

def predict_all_strategies():
    """使用所有策略预测明天的股票"""
    clear_screen()
    print_header()
    print("使用所有策略预测明天的股票")
    print("-" * 80)
    
    print("\n正在使用所有策略预测明天的股票...")
    
    try:
        strategy_results, risk_descriptions = predict_all_strategies_next_day(
            data_file_path='股票明细.xlsx',
            use_saved_model=True
        )
        
        if strategy_results is None:
            print("\n预测失败！")
            input("\n按Enter键返回主菜单...")
            return
        
        print("\n各策略推荐股票数量:")
        for strategy_name, stocks in strategy_results.items():
            if stocks is not None:
                print(f"{risk_descriptions[strategy_name]['strategy_description']}: {len(stocks)} 只股票")
        
        while True:
            print("\n请选择要查看的策略:")
            print("1. 策略1：100%高胜率策略")
            print("2. 策略A：最高胜率策略")
            print("3. 策略B：最高收益率策略")
            print("4. 策略C：平衡策略")
            print("0. 返回主菜单")
            
            choice = input("\n请输入选项 (0-4): ")
            
            if choice == '0':
                break
            elif choice == '1':
                display_recommendations(strategy_results['strategy_1'], risk_descriptions['strategy_1'])
            elif choice == '2':
                display_recommendations(strategy_results['strategy_A'], risk_descriptions['strategy_A'])
            elif choice == '3':
                display_recommendations(strategy_results['strategy_B'], risk_descriptions['strategy_B'])
            elif choice == '4':
                display_recommendations(strategy_results['strategy_C'], risk_descriptions['strategy_C'])
            else:
                print("无效的选项，请重新输入")
            
            input("\n按Enter键继续...")
    
    except Exception as e:
        print(f"\n预测失败: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按Enter键返回主菜单...")

def main():
    """主函数"""
    while True:
        clear_screen()
        print_header()
        print_menu()
        
        choice = input("请输入选项 (0-6): ")
        
        if choice == '0':
            print("\n感谢使用！再见！")
            break
        elif choice == '1':
            train_model()
        elif choice == '2':
            predict_strategy('strategy_1')
        elif choice == '3':
            predict_strategy('strategy_A')
        elif choice == '4':
            predict_strategy('strategy_B')
        elif choice == '5':
            predict_strategy('strategy_C')
        elif choice == '6':
            predict_all_strategies()
        else:
            print("无效的选项，请重新输入")
            input("\n按Enter键继续...")

if __name__ == "__main__":
    main()
