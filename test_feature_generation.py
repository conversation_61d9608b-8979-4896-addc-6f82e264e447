import pandas as pd
import itertools
import os
import numpy as np

# 测试特征生成函数
def get_available_features():
    """
    获取可用于组合的特征列表
    """
    # 根据调整后的股票数据字典定义可用特征
    features = [
        # 技术强度相关特征
        '技术强度',
        '连续技术强度3天数',
        '连续技术强度5天数',
        '连续技术强度10天数',
        
        # 编码特征
        '技术指标特征',
        '趋势组合',
        '日内股票标记',
        
        # 交易相关特征
        '成交量是前一日几倍'
    ]
    
    return features

# 生成技术指标特征的全部有意义组合（1=满足，2=不满足）
def generate_tech_indicator_values():
    values = []
    # 单个指标满足
    for i in range(6):
        value = ['2'] * 6
        value[i] = '1'
        values.append(''.join(value))
    
    # 两个指标满足的常见组合
    common_pairs = [(0,1), (0,2), (0,4), (1,2), (2,4), (3,4)]  # 常见的技术指标组合
    for i, j in common_pairs:
        value = ['2'] * 6
        value[i] = '1'
        value[j] = '1'
        values.append(''.join(value))
    
    # 三个指标满足的强势组合
    strong_triplets = [(0,1,2), (0,2,4), (1,2,4), (0,1,4)]
    for i, j, k in strong_triplets:
        value = ['2'] * 6
        value[i] = '1'
        value[j] = '1'
        value[k] = '1'
        values.append(''.join(value))
    
    # 四个及以上指标满足
    values.extend(['111122', '111112', '111121', '111111'])
    
    return values

# 测试函数
print("测试特征生成...")

features = get_available_features()
print(f"可用特征数量: {len(features)}")
print(f"特征列表: {features}")

tech_indicator_values = generate_tech_indicator_values()
print(f"\n技术指标特征值数量: {len(tech_indicator_values)}")
print(f"前10个技术指标特征值: {tech_indicator_values[:10]}")

# 计算组合数量
tech_strength_values = [28, 42, 57, 71, 85, 100]
continuous_strength_3d_values = [85, 126, 156, 199]
continuous_strength_5d_values = [140, 210, 285, 355]
continuous_strength_10d_values = [226, 438, 579]
volume_ratio_values = [0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5]

print(f"\n各特征的值数量:")
print(f"技术强度: {len(tech_strength_values)}")
print(f"连续技术强度3天数: {len(continuous_strength_3d_values)}")
print(f"连续技术强度5天数: {len(continuous_strength_5d_values)}")
print(f"连续技术强度10天数: {len(continuous_strength_10d_values)}")
print(f"成交量是前一日几倍: {len(volume_ratio_values)}")
print(f"技术指标特征: {len(tech_indicator_values)}")

print("\n测试完成！")
