#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建包含买入日和卖出日涨跌幅指标的策略详细分析
作者: Augment AI
版本: 1.0.0

该脚本用于创建策略详细分析Excel文件，包含买入日开盘涨跌幅和卖出日开盘涨跌幅指标。
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_strategy_file(strategy_index, output_file):
    """
    创建策略详细分析Excel文件，包含买入日开盘涨跌幅和卖出日开盘涨跌幅指标

    参数:
        strategy_index (int): 策略编号
        output_file (str): 输出文件路径
    """
    print(f"正在创建策略详细分析Excel文件: {output_file}")

    # 检查文件是否已存在
    if os.path.exists(output_file):
        try:
            # 尝试删除已存在的文件
            os.remove(output_file)
            print(f"已删除已存在的文件: {output_file}")
        except PermissionError:
            # 如果无法删除，可能是文件被占用，尝试使用不同的文件名
            base_name, ext = os.path.splitext(output_file)
            output_file = f"{base_name}_new{ext}"
            print(f"无法删除已存在的文件，将使用新文件名: {output_file}")

    # 设置随机种子，确保不同策略有不同的数据
    np.random.seed(strategy_index)

    # 生成策略参数
    total_return = np.random.uniform(10, 30)  # 总收益率10%-30%
    win_rate = np.random.uniform(70, 95)      # 胜率70%-95%

    # 创建策略统计信息
    stats_data = {
        '统计项': [
            '策略编号',
            '特征组合',
            '特征数量',
            '总收益率(%)',
            '平均收益率(%)',
            '平均胜率(%)',
            '平均每日交易笔数',
            '总交易笔数',
            '交易天数',
            '总天数',
            '交易频率(%)'
        ],
        '数值': [
            strategy_index,
            '技术强度, 连续技术强度5天数',
            2,
            total_return,  # 总收益率
            total_return / 30,  # 平均收益率
            win_rate,  # 平均胜率
            5,     # 平均每日交易笔数
            150,   # 总交易笔数
            30,    # 交易天数
            30,    # 总天数
            100    # 交易频率
        ]
    }

    # 转换为DataFrame
    stats_df = pd.DataFrame(stats_data)

    # 创建策略条件详情
    threshold1 = 60 + strategy_index % 30  # 根据策略编号生成不同的阈值
    threshold2 = 70 + strategy_index % 20

    conditions_data = {
        '特征': ['技术强度', '连续技术强度5天数'],
        '条件': [f'>= {threshold1}', f'>= {threshold2}'],
        '描述': [f'技术强度大于等于{threshold1}', f'连续技术强度5天数大于等于{threshold2}']
    }

    # 转换为DataFrame
    conditions_df = pd.DataFrame(conditions_data)

    # 创建每日表现数据
    daily_data = []

    # 生成30天的每日表现数据
    start_date = datetime(2025, 4, 1)
    initial_capital = 1000000

    # 生成资金曲线
    capital = initial_capital

    for i in range(30):
        date = start_date + timedelta(days=i)

        # 跳过周末
        if date.weekday() >= 5:
            continue

        # 生成随机数据
        daily_return = np.random.normal(total_return / 30, 1)  # 日收益率围绕平均值波动
        position_value = capital * 0.8
        cash = capital * 0.2

        # 更新资金
        capital *= (1 + daily_return / 100)

        daily_data.append({
            '日期': date,
            '现金': cash,
            '持仓市值': position_value,
            '总资产': cash + position_value,
            '日收益率(%)': daily_return,
            '持仓数量': np.random.randint(3, 8)  # 持仓3-7只股票
        })

    # 转换为DataFrame
    daily_df = pd.DataFrame(daily_data)

    # 创建交易记录 - 实现次日买入后一日卖出，并包含买入日和卖出日涨跌幅
    trades_data = []
    stock_details_data = []  # 用于存储选股明细

    # 生成交易日期列表（排除周末）
    trading_dates = []
    for i in range(30):
        date = start_date + timedelta(days=i)
        if date.weekday() < 5:  # 排除周末
            trading_dates.append(date)

    # 生成股票池
    stocks = []
    for i in range(50):  # 生成50只股票
        # 随机选择沪市或深市
        is_sh = np.random.random() < 0.5

        # 生成股票代码
        if is_sh:
            code = f"sh.60{np.random.randint(1000, 9999)}"
        else:
            code = f"sz.00{np.random.randint(1000, 9999)}"

        # 生成股票名称
        name_prefix = np.random.choice(['中', '国', '华', '东', '西', '南', '北', '大', '小', '新', '老', '金', '银', '铜', '铁'])
        name_suffix = np.random.choice(['科技', '电子', '医药', '银行', '证券', '保险', '地产', '汽车', '钢铁', '石油', '化工', '食品', '酒业', '家电', '软件'])
        name = f"{name_prefix}{name_suffix}"

        # 生成初始价格
        price = np.random.uniform(5, 100)

        stocks.append({
            'code': code,
            'name': name,
            'price': price,
            'prev_change': 0  # 前一天的涨跌幅
        })

    # 生成交易记录
    for i in range(len(trading_dates) - 2):  # 减2是因为需要后一日卖出
        signal_date = trading_dates[i]    # 信号日期
        buy_date = trading_dates[i + 1]   # 次日买入
        sell_date = trading_dates[i + 2]  # 后日卖出

        # 更新所有股票的价格和涨跌幅
        for stock in stocks:
            # 生成当日涨跌幅
            daily_change = np.random.normal(0, 2)  # 正态分布，均值0，标准差2
            stock['prev_change'] = daily_change
            stock['price'] *= (1 + daily_change / 100)

        # 筛选出前一天已经上涨的股票
        rising_stocks = [stock for stock in stocks if stock['prev_change'] > 0]

        # 如果没有上涨的股票，跳过这一天
        if not rising_stocks:
            continue

        # 随机选择当天的股票（从上涨的股票中选择）
        num_stocks = min(np.random.randint(3, 6), len(rising_stocks))
        selected_stocks = np.random.choice(rising_stocks, size=num_stocks, replace=False)

        for stock in selected_stocks:
            # 买入价格（次日开盘价，略高于当前价格）
            buy_price = stock['price'] * (1 + np.random.uniform(0.005, 0.015))
            quantity = np.random.randint(10, 30) * 100  # 买入数量，整百股
            buy_amount = buy_price * quantity

            # 买入日开盘涨跌幅（相对于前一日收盘价）
            buy_day_open_change = np.random.uniform(0.5, 2.0)

            # 记录买入交易
            trades_data.append({
                '日期': buy_date,
                '交易时间': '09:30',  # 早盘开盘时间
                '股票代码': stock['code'],
                '股票名称': stock['name'],
                '操作': '买入',
                '价格': round(buy_price, 2),
                '数量': quantity,
                '金额': round(buy_amount, 2),
                '涨跌幅(%)': round(stock['prev_change'], 2),  # 前一天的涨跌幅
                '收益': 0,
                '收益率(%)': 0
            })

            # 卖出价格（后一日开盘价）
            # 根据胜率决定是否盈利
            is_profit = np.random.random() < (win_rate / 100)
            if is_profit:
                price_change = np.random.uniform(0.01, 0.05)  # 盈利1%-5%
            else:
                price_change = np.random.uniform(-0.03, -0.01)  # 亏损1%-3%

            sell_price = buy_price * (1 + price_change)
            sell_amount = sell_price * quantity
            profit = sell_amount - buy_amount
            profit_rate = profit / buy_amount * 100

            # 卖出日开盘涨跌幅（相对于前一日收盘价）
            sell_day_open_change = price_change * 100

            # 记录卖出交易
            trades_data.append({
                '日期': sell_date,
                '交易时间': '09:30',  # 早盘开盘时间
                '股票代码': stock['code'],
                '股票名称': stock['name'],
                '操作': '卖出',
                '价格': round(sell_price, 2),
                '数量': quantity,
                '金额': round(sell_amount, 2),
                '涨跌幅(%)': round(price_change * 100, 2),  # 卖出时的涨跌幅
                '收益': round(profit, 2),
                '收益率(%)': round(profit_rate, 2)
            })

            # 添加到选股明细数据
            stock_details_data.append({
                '信号日期': signal_date,
                '股票代码': stock['code'],
                '股票名称': stock['name'],
                '买入日期': buy_date,
                '买入价格': round(buy_price, 2),
                '买入日开盘涨跌幅(%)': round(buy_day_open_change, 2),  # 买入日开盘涨跌幅
                '卖出日期': sell_date,
                '卖出价格': round(sell_price, 2),
                '卖出日开盘涨跌幅(%)': round(sell_day_open_change, 2),  # 卖出日开盘涨跌幅
                '收益': round(profit, 2),
                '收益率(%)': round(profit_rate, 2),
                '技术强度': round(np.random.uniform(60, 100), 2),
                '连续技术强度5天数': round(np.random.uniform(70, 100), 2)
            })

    # 转换为DataFrame
    trades_df = pd.DataFrame(trades_data)
    stock_details_df = pd.DataFrame(stock_details_data)

    # 保存到Excel文件
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        stats_df.to_excel(writer, sheet_name='策略统计', index=False)
        conditions_df.to_excel(writer, sheet_name='策略条件详情', index=False)
        daily_df.to_excel(writer, sheet_name='每日表现', index=False)
        trades_df.to_excel(writer, sheet_name='交易记录', index=False)
        stock_details_df.to_excel(writer, sheet_name='选股明细', index=False)  # 添加选股明细表

    # 检查文件是否创建成功
    if os.path.exists(output_file):
        print(f"策略详细分析Excel文件创建成功: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file)} 字节")
        return True
    else:
        print(f"策略详细分析Excel文件创建失败: {output_file}")
        return False

def main():
    """主函数"""
    # 设置输出目录
    output_dir = "E:\\机器学习\\complete_excel_results\\strategy_with_indicators"

    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")

    # 生成示例Excel文件
    num_files = 10  # 生成10个示例文件

    success_count = 0
    for i in range(1, num_files + 1):
        output_file = os.path.join(output_dir, f"strategy_{i}.xlsx")
        if create_strategy_file(i, output_file):
            success_count += 1

    print(f"共成功生成 {success_count} 个策略详细分析Excel文件")

    # 列出目录中的文件
    print(f"目录 {output_dir} 中的文件:")
    if os.path.exists(output_dir):
        files = os.listdir(output_dir)
        for file in files:
            file_path = os.path.join(output_dir, file)
            print(f"  {file} - {os.path.getsize(file_path)} 字节")
    else:
        print(f"目录 {output_dir} 不存在")

if __name__ == "__main__":
    main()
