import re

# 搜索backtest_local.py中所有可能的前缀处理代码
with open('backtest_local.py', 'r', encoding='utf-8') as f:
    lines = f.readlines()

print("=== 搜索股票代码前缀处理相关代码 ===")

# 搜索关键词
keywords = [
    'remove_prefix',
    'startswith',
    'sh.',
    'sz.',
    '[3:]',
    '[2:]',
    '前缀',
    'prefix'
]

found_lines = []

for i, line in enumerate(lines, 1):
    line_lower = line.lower()
    for keyword in keywords:
        if keyword.lower() in line_lower:
            found_lines.append((i, line.strip()))
            break

if found_lines:
    print(f"找到 {len(found_lines)} 行包含前缀处理相关代码:")
    for line_num, line_content in found_lines:
        print(f"  第{line_num}行: {line_content}")
else:
    print("没有找到前缀处理相关代码")

print("\n=== 搜索完成 ===")
