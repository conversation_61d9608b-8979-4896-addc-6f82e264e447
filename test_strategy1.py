import os
import sys
import subprocess
import pandas as pd
import time

def run_strategy_test(strategy_id=1):
    """运行指定策略的回测并检查结果"""
    print(f"开始测试策略 {strategy_id}...")

    # 执行回测
    cmd = f"python backtest_single.py --id {strategy_id} --preload"
    print(f"执行命令: {cmd}")

    start_time = time.time()
    process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    stdout, stderr = process.communicate()

    if process.returncode != 0:
        print(f"回测失败，返回码: {process.returncode}")
        print(f"错误信息: {stderr}")
        return False

    end_time = time.time()
    print(f"回测完成，耗时: {end_time - start_time:.2f}秒")

    # 检查结果文件
    base_dir = "E:\\机器学习\\complete_excel_results"
    result_file = os.path.join(base_dir, "new_strategy_details", f"strategy_{strategy_id}.xlsx")

    if not os.path.exists(result_file):
        print(f"结果文件不存在: {result_file}")
        return False

    print(f"结果文件存在: {result_file}")
    file_size = os.path.getsize(result_file)
    print(f"文件大小: {file_size} 字节")

    # 读取结果文件
    try:
        stock_details = pd.read_excel(result_file, sheet_name="选股明细")
        print(f"选股明细行数: {len(stock_details)}")

        # 检查是否包含我们需要的列
        required_columns = [
            '买入后连续2个交易日累计涨幅',
            '买入后连续3个交易日累计涨幅'
        ]

        missing_columns = [col for col in required_columns if col not in stock_details.columns]
        if missing_columns:
            print(f"警告: 缺少以下列: {', '.join(missing_columns)}")
            return False

        print("所有需要的列都存在")

        # 检查涨幅计算是否正确
        if len(stock_details) > 0:
            print("\n涨幅数据示例:")
            for idx, row in stock_details.head(5).iterrows():
                print(f"股票: {row['股票代码']} {row['股票名称']}, 买入日期: {row['买入日期']}")
                print(f"  买入日涨跌幅: {row['买入日涨跌幅']:.2f}%")
                print(f"  买入后连续2个交易日累计涨幅: {row['买入后连续2个交易日累计涨幅']:.2f}%")
                print(f"  买入后连续3个交易日累计涨幅: {row['买入后连续3个交易日累计涨幅']:.2f}%")

                # 计算买入日起的累计涨幅（使用复利计算方法）
                two_day_cumulative = (1 + row['买入日涨跌幅']/100) * (1 + row['买入后连续2个交易日累计涨幅']/100) - 1
                two_day_cumulative = two_day_cumulative * 100  # 转换为百分比

                three_day_cumulative = (1 + row['买入日涨跌幅']/100) * (1 + row['买入后连续3个交易日累计涨幅']/100) - 1
                three_day_cumulative = three_day_cumulative * 100  # 转换为百分比
                print(f"  计算得出 - 买入日起2日累计涨幅(含买入日): {two_day_cumulative:.2f}%")
                print(f"  计算得出 - 买入日起3日累计涨幅(含买入日): {three_day_cumulative:.2f}%")

                print("")

            # 检查整体数据
            print("\n整体数据统计:")
            total_count = len(stock_details)

            # 计算平均涨幅
            avg_2day_cumulative = stock_details['买入后连续2个交易日累计涨幅'].mean()
            avg_3day_cumulative = stock_details['买入后连续3个交易日累计涨幅'].mean()

            print(f"平均买入后连续2个交易日累计涨幅: {avg_2day_cumulative:.2f}%")
            print(f"平均买入后连续3个交易日累计涨幅: {avg_3day_cumulative:.2f}%")

            # 计算胜率
            win_rate_2day = (stock_details['买入后连续2个交易日累计涨幅'] > 0).mean() * 100
            win_rate_3day = (stock_details['买入后连续3个交易日累计涨幅'] > 0).mean() * 100

            print(f"买入后连续2个交易日上涨概率: {win_rate_2day:.2f}%")
            print(f"买入后连续3个交易日上涨概率: {win_rate_3day:.2f}%")

            # 检查是否有异常值
            max_2day = stock_details['买入后连续2个交易日累计涨幅'].max()
            min_2day = stock_details['买入后连续2个交易日累计涨幅'].min()
            max_3day = stock_details['买入后连续3个交易日累计涨幅'].max()
            min_3day = stock_details['买入后连续3个交易日累计涨幅'].min()

            print(f"买入后连续2个交易日累计涨幅范围: {min_2day:.2f}% 至 {max_2day:.2f}%")
            print(f"买入后连续3个交易日累计涨幅范围: {min_3day:.2f}% 至 {max_3day:.2f}%")

            # 验证通过
            print("验证通过: 连续2日和3日涨幅数据已正确计算")
            return True
        else:
            print("选股明细为空，无法验证涨幅计算")
            return False

    except Exception as e:
        print(f"读取结果文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    strategy_id = 1
    if len(sys.argv) > 1:
        strategy_id = int(sys.argv[1])

    success = run_strategy_test(strategy_id)
    print(f"测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
