#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特征组合回测结果Excel报告生成器
作者: Augment AI
版本: 1.0.0

该脚本将所有特征组合的回测结果生成到Excel文件中，包括详细的统计数据和图表。
"""

import pandas as pd
import numpy as np
import os
import time
from datetime import datetime
import pickle
import matplotlib.pyplot as plt
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.chart import LineChart, Reference, BarChart
from openpyxl.chart.label import DataLabelList
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.styles.differential import DifferentialStyle
from openpyxl.formatting.rule import Rule

# 创建结果目录
results_dir = 'excel_report_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
        
        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")
        
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def backtest_feature_combination(data, feature_combination, start_date, end_date, initial_capital=100000):
    """回测特征组合策略"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    # 初始化结果
    results = {
        'feature_combination': feature_combination,
        'feature_count': len(feature_combination),
        'initial_capital': initial_capital,
        'current_capital': initial_capital,
        'total_trades': 0,
        'win_count': 0,
        'daily_returns': [],
        'daily_win_rates': [],
        'daily_trade_counts': [],
        'capital_history': [initial_capital],
        'dates': [trading_dates[0]]
    }
    
    # 对每个交易日进行回测
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 获取当日数据
        daily_data = date_range_data[date_range_data['日期'] == current_date]
        
        # 应用特征组合条件
        selected = daily_data.copy()
        for feature in feature_combination:
            if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', 
                         '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势', 
                         '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势', 
                         '连续技术强度10天数趋势', '连续技术强度3天数价格趋势', 
                         '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势', 
                         '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势', 
                         '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
                # 二元特征，使用==1条件
                selected = selected[selected[feature] == 1]
            elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                # 连续值特征，使用>=中位数条件
                median_value = np.median(daily_data[feature])
                selected = selected[selected[feature] >= median_value]
            elif feature == '看涨技术指标数量':
                # 看涨技术指标数量，使用>=3条件
                selected = selected[selected[feature] >= 3]
        
        # 如果有推荐的股票，模拟买入
        if len(selected) > 0:
            # 获取推荐股票的代码
            recommended_codes = selected['股票代码'].tolist()
            
            # 获取次日这些股票的数据
            next_day_data = date_range_data[
                (date_range_data['日期'] == next_date) & 
                (date_range_data['股票代码'].isin(recommended_codes))
            ]
            
            # 如果次日有这些股票的数据
            if len(next_day_data) > 0:
                # 计算平均涨跌幅
                avg_return = next_day_data['涨跌幅'].mean() / 100  # 转换为小数
                
                # 计算盈利股票数量
                win_stocks = len(next_day_data[next_day_data['涨跌幅'] > 0])
                win_rate = win_stocks / len(next_day_data) * 100
                
                # 更新资金
                results['current_capital'] *= (1 + avg_return)
                
                # 更新统计数据
                results['total_trades'] += len(next_day_data)
                results['win_count'] += win_stocks
                results['daily_returns'].append(avg_return * 100)  # 转换为百分比
                results['daily_win_rates'].append(win_rate)
                results['daily_trade_counts'].append(len(next_day_data))
                results['capital_history'].append(results['current_capital'])
                results['dates'].append(next_date)
            else:
                # 如果次日没有这些股票的数据，资金保持不变
                results['capital_history'].append(results['current_capital'])
                results['dates'].append(next_date)
                results['daily_returns'].append(0)
                results['daily_win_rates'].append(0)
                results['daily_trade_counts'].append(0)
        else:
            # 如果没有推荐股票，资金保持不变
            results['capital_history'].append(results['current_capital'])
            results['dates'].append(next_date)
            results['daily_returns'].append(0)
            results['daily_win_rates'].append(0)
            results['daily_trade_counts'].append(0)
    
    # 计算最终统计结果
    if results['total_trades'] > 0:
        results['win_rate'] = results['win_count'] / results['total_trades'] * 100
        results['total_return_pct'] = (results['current_capital'] / initial_capital - 1) * 100
        results['avg_daily_return'] = np.mean([r for r in results['daily_returns'] if r != 0])
        results['avg_daily_trades'] = np.mean([count for count in results['daily_trade_counts'] if count > 0])
        results['trading_days'] = len([count for count in results['daily_trade_counts'] if count > 0])
        results['total_days'] = len(trading_dates) - 1
        results['trading_frequency'] = results['trading_days'] / results['total_days'] * 100
    else:
        results['win_rate'] = 0
        results['total_return_pct'] = 0
        results['avg_daily_return'] = 0
        results['avg_daily_trades'] = 0
        results['trading_days'] = 0
        results['total_days'] = len(trading_dates) - 1
        results['trading_frequency'] = 0
    
    return results

def backtest_top_combinations(data, start_date, end_date):
    """回测顶级特征组合"""
    print("回测顶级特征组合...")
    
    # 定义顶级特征组合
    top_combinations = [
        # 2特征组合
        ('技术强度趋势', '技术指标_KDJ金叉'),
        ('连续技术强度5天数趋势', '技术指标_KDJ金叉'),
        ('技术强度趋势', '技术指标_RSI反弹'),
        ('技术强度趋势', '连续技术强度5天数趋势'),
        ('技术指标_RSI反弹', '技术指标_KDJ金叉'),
        
        # 3特征组合
        ('技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度趋势', '连续技术强度5天数趋势', '技术指标_KDJ金叉'),
        ('技术强度', '技术强度趋势', '技术指标_KDJ金叉'),
        ('连续技术强度5天数', '技术强度趋势', '技术指标_KDJ金叉'),
        
        # 4特征组合
        ('技术强度趋势', '连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('连续技术强度5天数', '技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '连续技术强度5天数', '技术强度趋势', '技术指标_KDJ金叉'),
        
        # 5特征组合
        ('技术强度', '连续技术强度5天数', '技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '连续技术强度5天数', '连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '技术强度趋势', '连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉')
    ]
    
    # 回测每个组合
    results = []
    for combination in top_combinations:
        print(f"回测组合: {', '.join(combination)}")
        result = backtest_feature_combination(data, combination, start_date, end_date)
        results.append(result)
    
    # 按总收益率排序
    results.sort(key=lambda x: x['total_return_pct'], reverse=True)
    
    return results

def create_excel_report(results, output_file):
    """创建Excel报告"""
    print(f"创建Excel报告: {output_file}")
    
    # 创建工作簿
    wb = Workbook()
    
    # 创建汇总表格
    create_summary_sheet(wb, results)
    
    # 创建每个策略的详细表格
    for i, result in enumerate(results):
        create_strategy_sheet(wb, result, i+1)
    
    # 创建比较图表
    create_comparison_charts(wb, results)
    
    # 保存工作簿
    wb.save(output_file)
    print(f"Excel报告已保存到: {output_file}")

def create_summary_sheet(wb, results):
    """创建汇总表格"""
    # 获取或创建工作表
    if 'Sheet' in wb.sheetnames:
        ws = wb['Sheet']
        ws.title = '策略汇总'
    else:
        ws = wb.create_sheet('策略汇总')
    
    # 设置标题
    ws['A1'] = '特征组合策略回测结果汇总'
    ws['A1'].font = Font(size=16, bold=True)
    ws.merge_cells('A1:J1')
    ws['A1'].alignment = Alignment(horizontal='center')
    
    # 设置表头
    headers = ['排名', '策略组合', '特征数量', '总收益率(%)', '平均收益率(%)', 
               '平均胜率(%)', '平均每日交易笔数', '总交易笔数', '交易天数', '总天数']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')
        cell.fill = PatternFill(start_color='E0E0E0', end_color='E0E0E0', fill_type='solid')
    
    # 填充数据
    for i, result in enumerate(results, 1):
        feature_str = ', '.join(result['feature_combination'])
        row_data = [
            i,
            feature_str,
            result['feature_count'],
            result['total_return_pct'],
            result['avg_daily_return'],
            result['win_rate'],
            result['avg_daily_trades'],
            result['total_trades'],
            result['trading_days'],
            result['total_days']
        ]
        
        for col, value in enumerate(row_data, 1):
            cell = ws.cell(row=i+3, column=col, value=value)
            if col in [4, 5, 6]:  # 百分比列
                cell.number_format = '0.00%' if col == 4 else '0.00'
    
    # 设置列宽
    ws.column_dimensions['A'].width = 6
    ws.column_dimensions['B'].width = 50
    ws.column_dimensions['C'].width = 10
    for col in ['D', 'E', 'F', 'G', 'H', 'I', 'J']:
        ws.column_dimensions[col].width = 15
    
    # 添加条件格式
    # 总收益率条件格式
    red_fill = PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid')
    green_fill = PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid')
    
    ws.conditional_formatting.add(f'D4:D{len(results)+3}',
                                 Rule(type='colorScale',
                                      colorScale={'cfvo': [{'type': 'min'}, {'type': 'max'}],
                                                 'color': ['FF9999', '99FF99']}))
    
    # 胜率条件格式
    ws.conditional_formatting.add(f'F4:F{len(results)+3}',
                                 Rule(type='colorScale',
                                      colorScale={'cfvo': [{'type': 'min'}, {'type': 'max'}],
                                                 'color': ['FF9999', '99FF99']}))

def create_strategy_sheet(wb, result, index):
    """为每个策略创建详细表格"""
    # 创建策略名称
    feature_str = '_'.join([f.split('_')[-1] if '_' in f else f for f in result['feature_combination']])
    sheet_name = f"{index}_{feature_str[:20]}"  # 限制长度以避免Excel限制
    
    # 创建工作表
    ws = wb.create_sheet(sheet_name)
    
    # 设置标题
    ws['A1'] = f"策略 {index}: {', '.join(result['feature_combination'])}"
    ws['A1'].font = Font(size=14, bold=True)
    ws.merge_cells('A1:E1')
    
    # 添加策略统计信息
    stats_data = [
        ['特征数量', result['feature_count']],
        ['总收益率(%)', result['total_return_pct']],
        ['平均收益率(%)', result['avg_daily_return']],
        ['平均胜率(%)', result['win_rate']],
        ['平均每日交易笔数', result['avg_daily_trades']],
        ['总交易笔数', result['total_trades']],
        ['交易天数', result['trading_days']],
        ['总天数', result['total_days']],
        ['交易频率(%)', result['trading_frequency']]
    ]
    
    for i, (label, value) in enumerate(stats_data, 3):
        ws.cell(row=i, column=1, value=label).font = Font(bold=True)
        cell = ws.cell(row=i, column=2, value=value)
        if 'rate' in label.lower() or 'ratio' in label.lower() or '%' in label:
            cell.number_format = '0.00'
    
    # 添加每日表现数据
    ws['A13'] = '每日表现'
    ws['A13'].font = Font(size=12, bold=True)
    ws.merge_cells('A13:E13')
    
    # 设置表头
    daily_headers = ['日期', '资金', '收益率(%)', '胜率(%)', '交易笔数']
    for col, header in enumerate(daily_headers, 1):
        cell = ws.cell(row=14, column=col, value=header)
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')
        cell.fill = PatternFill(start_color='E0E0E0', end_color='E0E0E0', fill_type='solid')
    
    # 填充每日数据
    for i, (date, capital) in enumerate(zip(result['dates'], result['capital_history']), 15):
        ws.cell(row=i, column=1, value=date)
        ws.cell(row=i, column=2, value=capital)
        
        if i-15 < len(result['daily_returns']):
            ws.cell(row=i, column=3, value=result['daily_returns'][i-15])
            ws.cell(row=i, column=4, value=result['daily_win_rates'][i-15])
            ws.cell(row=i, column=5, value=result['daily_trade_counts'][i-15])
    
    # 设置列宽
    ws.column_dimensions['A'].width = 15
    ws.column_dimensions['B'].width = 15
    ws.column_dimensions['C'].width = 12
    ws.column_dimensions['D'].width = 12
    ws.column_dimensions['E'].width = 12
    
    # 创建资金曲线图表
    chart = LineChart()
    chart.title = "策略资金曲线"
    chart.style = 2
    chart.x_axis.title = "日期"
    chart.y_axis.title = "资金"
    
    # 添加数据
    data = Reference(ws, min_col=2, min_row=14, max_row=14+len(result['capital_history']), max_col=2)
    cats = Reference(ws, min_col=1, min_row=15, max_row=14+len(result['dates']))
    chart.add_data(data, titles_from_data=True)
    chart.set_categories(cats)
    
    # 添加图表到工作表
    ws.add_chart(chart, "G3")

def create_comparison_charts(wb, results):
    """创建比较图表"""
    # 创建工作表
    ws = wb.create_sheet('策略比较图表')
    
    # 设置标题
    ws['A1'] = '策略比较图表'
    ws['A1'].font = Font(size=16, bold=True)
    ws.merge_cells('A1:J1')
    ws['A1'].alignment = Alignment(horizontal='center')
    
    # 准备数据
    strategy_names = [f"{i+1}. {', '.join(r['feature_combination'][:2])}..." for i, r in enumerate(results)]
    total_returns = [r['total_return_pct'] for r in results]
    avg_returns = [r['avg_daily_return'] for r in results]
    win_rates = [r['win_rate'] for r in results]
    
    # 填充数据
    ws['A3'] = '策略'
    ws['B3'] = '总收益率(%)'
    ws['C3'] = '平均收益率(%)'
    ws['D3'] = '平均胜率(%)'
    
    for i, (name, total_return, avg_return, win_rate) in enumerate(zip(strategy_names, total_returns, avg_returns, win_rates), 4):
        ws.cell(row=i, column=1, value=name)
        ws.cell(row=i, column=2, value=total_return)
        ws.cell(row=i, column=3, value=avg_return)
        ws.cell(row=i, column=4, value=win_rate)
    
    # 创建总收益率图表
    chart1 = BarChart()
    chart1.title = "策略总收益率比较"
    chart1.style = 10
    chart1.x_axis.title = "策略"
    chart1.y_axis.title = "总收益率(%)"
    
    data = Reference(ws, min_col=2, min_row=3, max_row=3+len(results), max_col=2)
    cats = Reference(ws, min_col=1, min_row=4, max_row=3+len(results))
    chart1.add_data(data, titles_from_data=True)
    chart1.set_categories(cats)
    
    # 添加图表到工作表
    ws.add_chart(chart1, "F3")
    
    # 创建胜率图表
    chart2 = BarChart()
    chart2.title = "策略平均胜率比较"
    chart2.style = 10
    chart2.x_axis.title = "策略"
    chart2.y_axis.title = "平均胜率(%)"
    
    data = Reference(ws, min_col=4, min_row=3, max_row=3+len(results), max_col=4)
    cats = Reference(ws, min_col=1, min_row=4, max_row=3+len(results))
    chart2.add_data(data, titles_from_data=True)
    chart2.set_categories(cats)
    
    # 添加图表到工作表
    ws.add_chart(chart2, "F20")
    
    # 设置列宽
    ws.column_dimensions['A'].width = 40
    for col in ['B', 'C', 'D']:
        ws.column_dimensions[col].width = 15

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)
    
    # 设置回测参数
    start_date = '2025-03-01'  # 使用更长的历史数据
    end_date = '2025-05-15'
    
    print(f"回测周期: {start_date} 至 {end_date}")
    
    # 回测顶级特征组合
    results = backtest_top_combinations(df, start_date, end_date)
    
    # 创建Excel报告
    output_file = f"{results_dir}/特征组合回测结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    create_excel_report(results, output_file)
