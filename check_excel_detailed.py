import pandas as pd
import os

# 检查策略1的Excel文件的详细内容
excel_file = r'E:\机器学习\complete_excel_results\new_strategy_details\strategy_1.xlsx'

if os.path.exists(excel_file):
    print(f"文件存在: {excel_file}")
    print(f"文件大小: {os.path.getsize(excel_file)} 字节")
    
    try:
        # 获取所有sheet名称
        xl_file = pd.ExcelFile(excel_file)
        print(f"Sheet名称: {xl_file.sheet_names}")
        
        # 检查每个sheet的内容
        for sheet_name in xl_file.sheet_names:
            print(f"\n{'='*50}")
            print(f"Sheet: {sheet_name}")
            print(f"{'='*50}")
            
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            print(f"数据形状: {df.shape}")
            print(f"列名: {df.columns.tolist()}")
            
            if not df.empty:
                print("\n所有数据:")
                print(df.to_string())
                
                # 检查空值情况
                null_counts = df.isnull().sum()
                if null_counts.sum() > 0:
                    print(f"\n空值统计:")
                    for col, count in null_counts.items():
                        if count > 0:
                            print(f"  {col}: {count} 个空值 ({count/len(df)*100:.1f}%)")
                
                # 特别检查选股明细sheet的关键字段
                if sheet_name == '选股明细':
                    print(f"\n选股明细关键字段分析:")
                    key_fields = ['买入日涨跌幅', '卖出日股票涨跌幅', '买入日开盘涨跌幅', '卖出日开盘涨跌幅']
                    for field in key_fields:
                        if field in df.columns:
                            non_zero_count = (df[field] != 0).sum()
                            zero_count = (df[field] == 0).sum()
                            null_count = df[field].isnull().sum()
                            print(f"  {field}:")
                            print(f"    非零值: {non_zero_count} 个")
                            print(f"    零值: {zero_count} 个")
                            print(f"    空值: {null_count} 个")
                            if non_zero_count > 0:
                                print(f"    非零值范围: {df[field][df[field] != 0].min():.2f} 到 {df[field][df[field] != 0].max():.2f}")
                        else:
                            print(f"  {field}: 列不存在")
                            
                    # 检查股票代码格式
                    if '股票代码' in df.columns:
                        print(f"\n股票代码格式分析:")
                        sample_codes = df['股票代码'].head(10).tolist()
                        print(f"  样例股票代码: {sample_codes}")
                        
                        # 检查是否有前缀
                        has_prefix = df['股票代码'].str.contains(r'^(sh\.|sz\.)', na=False).sum()
                        print(f"  有前缀的股票代码数量: {has_prefix}/{len(df)}")
                        
                    # 检查日期格式
                    date_fields = ['选股日期', '买入日期', '卖出日期']
                    for field in date_fields:
                        if field in df.columns:
                            print(f"\n{field}格式分析:")
                            sample_dates = df[field].head(5).tolist()
                            print(f"  样例日期: {sample_dates}")
                            
            else:
                print("数据为空")
                
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()
else:
    print(f"文件不存在: {excel_file}")
