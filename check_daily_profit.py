import pandas as pd
import os

# 检查策略1的Excel文件中的每日收益明细
excel_file = r'E:\机器学习\complete_excel_results\new_strategy_details\strategy_1.xlsx'

if os.path.exists(excel_file):
    print(f"检查文件: {excel_file}")
    
    try:
        # 获取所有sheet名称
        xl_file = pd.ExcelFile(excel_file)
        print(f"Sheet名称: {xl_file.sheet_names}")
        
        # 检查每日收益明细
        if '每日收益明细' in xl_file.sheet_names:
            print(f"\n=== 每日收益明细 ===")
            daily_df = pd.read_excel(excel_file, sheet_name='每日收益明细')
            print(f"数据形状: {daily_df.shape}")
            print(f"列名: {daily_df.columns.tolist()}")
            
            if not daily_df.empty:
                print(f"\n每日收益明细数据:")
                print(daily_df.to_string())
                
                # 特别检查当日胜率列
                if '当日胜率(%)' in daily_df.columns:
                    print(f"\n当日胜率分析:")
                    win_rates = daily_df['当日胜率(%)']
                    print(f"  当日胜率值: {win_rates.tolist()}")
                    print(f"  最小值: {win_rates.min()}")
                    print(f"  最大值: {win_rates.max()}")
                    print(f"  平均值: {win_rates.mean():.2f}")
                    
                    # 检查是否有异常值
                    over_100 = win_rates[win_rates > 100]
                    if len(over_100) > 0:
                        print(f"  警告: 发现 {len(over_100)} 个超过100%的胜率值: {over_100.tolist()}")
                else:
                    print("没有找到'当日胜率(%)'列")
            else:
                print("每日收益明细数据为空")
        else:
            print("没有找到'每日收益明细'sheet")
            
        # 同时检查选股明细，看看原始数据
        if '选股明细' in xl_file.sheet_names:
            print(f"\n=== 选股明细（用于验证胜率计算）===")
            detail_df = pd.read_excel(excel_file, sheet_name='选股明细')
            
            if not detail_df.empty and '买入日期' in detail_df.columns and '买入日涨跌幅' in detail_df.columns:
                # 按买入日期分组计算胜率
                print(f"\n按买入日期分组的胜率计算验证:")
                
                buy_date_groups = detail_df.groupby('买入日期')
                for date, group in buy_date_groups:
                    total_stocks = len(group)
                    winning_stocks = (group['买入日涨跌幅'] > 0).sum()
                    win_rate = (winning_stocks / total_stocks) * 100 if total_stocks > 0 else 0
                    
                    print(f"  {date}: {winning_stocks}/{total_stocks} = {win_rate:.2f}%")
                    print(f"    涨跌幅: {group['买入日涨跌幅'].tolist()}")
                    print(f"    是否盈利: {(group['买入日涨跌幅'] > 0).tolist()}")
            else:
                print("选股明细数据不完整，无法验证胜率计算")
                
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()
else:
    print(f"文件不存在: {excel_file}")

print(f"\n=== 检查完成 ===")
