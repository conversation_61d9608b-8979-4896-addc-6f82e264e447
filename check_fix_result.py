#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查修复结果的脚本
"""

import pandas as pd

def check_fix_result():
    """检查修复结果"""
    
    print("=== 修复后的数据检查 ===")
    
    # 读取生成的数据
    df = pd.read_excel('E:/机器学习/complete_excel_results/tech_strength/daily/tech_strength_2025-05-15.xlsx', 
                      dtype={'股票代码': str, '技术指标特征': str, '趋势组合': str})
    
    # 检查样本股票
    sample = df.iloc[0]
    print(f"股票代码: {sample['股票代码']}")
    print(f"技术强度: {sample['技术强度']}")
    print(f"趋势: {sample['趋势']}")
    print(f"技术指标特征: {sample['技术指标特征']} (类型: {type(sample['技术指标特征'])})")
    print(f"趋势组合: {sample['趋势组合']} (类型: {type(sample['趋势组合'])})")
    print(f"连续技术强度3天数: {sample['连续技术强度3天数']}")
    print(f"连续技术强度5天数: {sample['连续技术强度5天数']}")
    print(f"连续技术强度10天数: {sample['连续技术强度10天数']}")
    
    print("\n=== 趋势组合分布检查 ===")
    print("趋势组合唯一值数量:", df['趋势组合'].nunique())
    print("趋势组合前10个值:")
    print(df['趋势组合'].value_counts().head(10))
    
    print("\n=== 连续技术强度检查 ===")
    print("前5个股票的连续技术强度:")
    for i in range(5):
        row = df.iloc[i]
        print(f"  股票 {row['股票代码']}: 3天={row['连续技术强度3天数']}, 5天={row['连续技术强度5天数']}, 10天={row['连续技术强度10天数']}")
    
    print("\n=== 字段类型检查 ===")
    print(f"技术指标特征类型: {df['技术指标特征'].dtype}")
    print(f"趋势组合类型: {df['趋势组合'].dtype}")
    print(f"买入日开盘涨跌幅类型: {df['买入日开盘涨跌幅'].dtype}")
    print(f"日内股票标记类型: {df['日内股票标记'].dtype}")
    print(f"卖出日开盘涨跌幅类型: {df['卖出日开盘涨跌幅'].dtype}")
    
    # 检查是否有前导0
    print("\n=== 前导0检查 ===")
    print("技术指标特征中有前导0的值:", any(val.startswith('0') for val in df['技术指标特征'] if isinstance(val, str)))
    print("趋势组合中有前导0的值:", any(val.startswith('0') for val in df['趋势组合'] if isinstance(val, str)))
    
    # 与原始数据对比
    print("\n=== 与原始数据对比 ===")
    original_df = pd.read_excel('E:/机器学习/complete_excel_results/股票明细_完整.xlsx')
    original_filtered = original_df[original_df['日期'] == '2025-05-15']
    
    # 查找匹配的股票
    sample_code = sample['股票代码']
    original_match = original_filtered[original_filtered['股票代码'] == sample_code]
    
    if not original_match.empty:
        original_stock = original_match.iloc[0]
        print(f"股票 {sample_code}:")
        print(f"  生成数据 - 趋势组合: {sample['趋势组合']}, 连续技术强度3天数: {sample['连续技术强度3天数']}")
        print(f"  原始数据 - 趋势组合: {original_stock['趋势组合']}, 连续技术强度3天数: {original_stock['连续技术强度3天数']}")
        
        # 检查是否修复成功
        if str(sample['趋势组合']) == str(original_stock['趋势组合']):
            print("  ✅ 趋势组合修复成功！")
        else:
            print("  ❌ 趋势组合仍有问题")
            
        if sample['连续技术强度3天数'] == original_stock['连续技术强度3天数']:
            print("  ✅ 连续技术强度3天数修复成功！")
        else:
            print("  ❌ 连续技术强度3天数仍有问题")

if __name__ == "__main__":
    check_fix_result()
