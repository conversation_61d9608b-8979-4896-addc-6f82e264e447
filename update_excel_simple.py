#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
从Excel文件更新策略汇总结果 - 简化版
作者: Augment AI
版本: 1.0.0

该脚本用于从Excel文件中读取真实数据，并更新策略汇总Excel文件中的统计结果。
使用默认位置和文件名，无需用户输入。
"""

import os
import pandas as pd
import glob
from datetime import datetime

def update_strategy_summary(main_excel_file, detail_excel_files):
    """
    更新策略汇总Excel文件中的统计结果

    参数:
        main_excel_file (str): 主Excel文件路径
        detail_excel_files (list): 详细Excel文件路径列表

    返回:
        str: 更新后的Excel文件路径
    """
    print(f"正在更新策略汇总Excel文件: {main_excel_file}")

    try:
        # 读取Excel文件
        summary_df = pd.read_excel(main_excel_file, sheet_name='策略汇总')
        conditions_df = pd.read_excel(main_excel_file, sheet_name='策略条件')

        # 读取详细Excel文件中的统计数据
        stats_data = {}
        for file_path in detail_excel_files:
            try:
                # 从文件名中提取策略编号
                file_name = os.path.basename(file_path)
                if file_name.startswith('strategy_') and file_name.endswith('.xlsx'):
                    strategy_index = int(file_name[9:-5])  # 提取strategy_X.xlsx中的X

                    # 读取策略统计表
                    stats_df = pd.read_excel(file_path, sheet_name='策略统计')

                    # 提取统计数据
                    stats = {}
                    for _, row in stats_df.iterrows():
                        stats[row['统计项']] = row['数值']

                    stats_data[strategy_index] = stats
                    print(f"成功读取策略 {strategy_index} 的统计数据")
            except Exception as e:
                print(f"读取文件 {file_path} 时出错: {str(e)}")

        # 更新策略汇总表
        for i, row in summary_df.iterrows():
            strategy_index = row['策略编号']
            if strategy_index in stats_data:
                stats = stats_data[strategy_index]

                # 更新统计数据
                if '总收益率(%)' in stats:
                    summary_df.at[i, '总收益率(%)'] = stats['总收益率(%)']
                if '平均收益率(%)' in stats:
                    summary_df.at[i, '平均收益率(%)'] = stats['平均收益率(%)']
                if '平均胜率(%)' in stats:
                    summary_df.at[i, '平均胜率(%)'] = stats['平均胜率(%)']
                if '平均每日交易笔数' in stats:
                    summary_df.at[i, '平均每日交易笔数'] = stats['平均每日交易笔数']
                if '总交易笔数' in stats:
                    summary_df.at[i, '总交易笔数'] = stats['总交易笔数']
                if '交易天数' in stats:
                    summary_df.at[i, '交易天数'] = stats['交易天数']
                if '总天数' in stats:
                    summary_df.at[i, '总天数'] = stats['总天数']
                if '交易频率(%)' in stats:
                    summary_df.at[i, '交易频率(%)'] = stats['交易频率(%)']

        # 更新策略条件表
        for i, row in conditions_df.iterrows():
            strategy_index = row['策略编号']
            if strategy_index in stats_data:
                stats = stats_data[strategy_index]

                # 更新统计数据
                if '总收益率(%)' in stats:
                    conditions_df.at[i, '总收益率(%)'] = stats['总收益率(%)']
                if '平均胜率(%)' in stats:
                    conditions_df.at[i, '平均胜率(%)'] = stats['平均胜率(%)']

        # 创建新的Excel文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(os.path.dirname(main_excel_file), f"所有策略汇总_更新_{timestamp}.xlsx")

        # 写入Excel文件
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            summary_df.to_excel(writer, sheet_name='策略汇总', index=False)
            conditions_df.to_excel(writer, sheet_name='策略条件', index=False)

            # 如果有特征数量统计表，也复制过来
            try:
                stats_df = pd.read_excel(main_excel_file, sheet_name='特征数量统计')
                stats_df.to_excel(writer, sheet_name='特征数量统计', index=False)
            except:
                pass

        print(f"策略汇总Excel文件更新完成: {output_file}")
        return output_file
    except Exception as e:
        print(f"更新策略汇总Excel文件时出错: {str(e)}")
        return None

def main():
    """主函数"""
    # 使用默认位置和文件名
    excel_dir = "E:\\机器学习\\complete_excel_results"
    main_excel_file = "E:\\机器学习\\complete_excel_results\\所有策略汇总.xlsx"
    detail_dir = "E:\\机器学习\\complete_excel_results\\strategy_with_indicators"

    print(f"使用默认参数:")
    print(f"主Excel文件: {main_excel_file}")
    print(f"详细Excel文件目录: {detail_dir}")

    # 检查文件和目录是否存在
    if not os.path.exists(main_excel_file):
        print(f"主Excel文件不存在: {main_excel_file}")
        return

    if not os.path.exists(detail_dir):
        print(f"详细Excel文件目录不存在: {detail_dir}")
        return

    # 查找所有详细Excel文件
    detail_excel_files = glob.glob(os.path.join(detail_dir, "strategy_*.xlsx"))
    print(f"找到 {len(detail_excel_files)} 个详细Excel文件")

    if not detail_excel_files:
        print("未找到详细Excel文件")
        return

    # 更新策略汇总Excel文件
    updated_file = update_strategy_summary(main_excel_file, detail_excel_files)
    if updated_file:
        print(f"策略汇总Excel文件更新完成，结果已保存到: {updated_file}")
    else:
        print("更新策略汇总Excel文件失败")

if __name__ == "__main__":
    main()
