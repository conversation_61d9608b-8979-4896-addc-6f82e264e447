import pandas as pd
import numpy as np
import os
import sys
import joblib
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score

def test_consecutive_strength():
    """测试连续两日技术强度为100的股票预测效果"""
    print("开始测试连续两日技术强度为100的股票预测效果...")

    # 创建结果目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')

    # 加载股票数据
    print("\n加载股票数据...")
    try:
        stock_data = pd.read_excel('股票明细.xlsx')
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")

        # 按股票代码和日期排序
        stock_data = stock_data.sort_values(['股票代码', '日期'])

        # 找出连续两日技术强度为100的股票
        print("\n筛选连续两日技术强度为100的股票...")

        # 创建前一日技术强度列
        stock_data['前一日技术强度'] = stock_data.groupby('股票代码')['技术强度'].shift(1)

        # 筛选连续两日技术强度为100的记录
        consecutive_strength = stock_data[
            (stock_data['技术强度'] == 100) &
            (stock_data['前一日技术强度'] == 100)
        ]

        print(f"找到 {len(consecutive_strength)} 条连续两日技术强度为100的记录")

        if len(consecutive_strength) == 0:
            print("没有找到连续两日技术强度为100的记录，尝试放宽条件...")
            # 放宽条件，找出技术强度为100的记录
            single_strength = stock_data[stock_data['技术强度'] == 100]
            print(f"找到 {len(single_strength)} 条技术强度为100的记录")

            if len(single_strength) > 0:
                # 使用单日技术强度为100的记录
                filtered_data = single_strength
                print("使用单日技术强度为100的记录进行分析")
            else:
                print("没有找到技术强度为100的记录，使用全部数据")
                filtered_data = stock_data
        else:
            filtered_data = consecutive_strength
            print("使用连续两日技术强度为100的记录进行分析")

        # 计算次日涨跌方向和收益率
        filtered_data['次日涨跌方向'] = 0
        filtered_data['次日收益率'] = 0

        # 按股票代码分组处理
        for code, group in filtered_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 计算次日涨跌方向和收益率
            group['次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)
            group['次日收益率'] = group['当前价格'].shift(-1) / group['当前价格'] - 1

            # 更新原始数据
            filtered_data.loc[group.index, '次日涨跌方向'] = group['次日涨跌方向']
            filtered_data.loc[group.index, '次日收益率'] = group['次日收益率']

        # 删除没有次日数据的记录
        filtered_data = filtered_data.dropna(subset=['次日涨跌方向', '次日收益率'])

        print(f"处理后的数据集大小: {len(filtered_data)} 条记录")

        if len(filtered_data) < 10:
            print("数据集太小，无法进行有效的训练和测试")
            return

        # 分析上涨概率
        up_count = filtered_data['次日涨跌方向'].sum()
        total_count = len(filtered_data)
        up_probability = up_count / total_count

        print(f"\n基于技术强度的上涨概率分析:")
        print(f"总记录数: {total_count}")
        print(f"上涨记录数: {up_count}")
        print(f"上涨概率: {up_probability:.4f} ({up_probability*100:.2f}%)")

        # 分析平均收益率
        avg_return = filtered_data['次日收益率'].mean()
        print(f"平均次日收益率: {avg_return:.4f} ({avg_return*100:.2f}%)")

        # 训练随机森林模型
        print("\n训练随机森林模型...")
        features = ['技术强度']  # 只使用技术强度作为特征
        X = filtered_data[features].values
        y_dir = filtered_data['次日涨跌方向'].values

        X_train, X_test, y_train, y_test = train_test_split(X, y_dir, test_size=0.2, random_state=42)

        rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
        rf_model.fit(X_train, y_train)

        y_pred = rf_model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        print(f"随机森林模型准确率: {accuracy:.4f}")

        # 保存模型
        joblib.dump(rf_model, 'test_results/strength_rf_model.pkl')

        # 获取最新日期的数据
        latest_date = stock_data['日期'].max()
        previous_date = stock_data[stock_data['日期'] < latest_date]['日期'].max()

        print(f"\n获取最新日期 {latest_date} 的数据...")

        # 找出最新日期技术强度为100且前一日也为100的股票
        latest_data = stock_data[stock_data['日期'] == latest_date]
        previous_data = stock_data[stock_data['日期'] == previous_date]

        # 合并数据，找出连续两日技术强度为100的股票
        merged_data = pd.merge(
            latest_data[['股票代码', '股票名称', '当前价格', '技术强度', '日期']],
            previous_data[['股票代码', '技术强度']],
            on='股票代码',
            suffixes=('', '_prev')
        )

        # 筛选连续两日技术强度为100的股票
        consecutive_stocks = merged_data[
            (merged_data['技术强度'] == 100) &
            (merged_data['技术强度_prev'] == 100)
        ]

        print(f"找到 {len(consecutive_stocks)} 只连续两日技术强度为100的股票")

        if len(consecutive_stocks) == 0:
            print("没有找到连续两日技术强度为100的股票，尝试使用单日技术强度为100的股票...")
            single_day_stocks = latest_data[latest_data['技术强度'] == 100]
            print(f"找到 {len(single_day_stocks)} 只单日技术强度为100的股票")

            if len(single_day_stocks) > 0:
                prediction_data = single_day_stocks
            else:
                print("没有找到技术强度为100的股票，使用最新日期的前10只股票")
                prediction_data = latest_data.head(10)
        else:
            prediction_data = consecutive_stocks

        # 使用模型进行预测
        print("\n使用模型进行预测...")
        test_X = prediction_data[features].values

        # 由于所有样本都是上涨的，模型可能只有一个类别
        try:
            # 尝试获取预测概率
            test_pred_proba = rf_model.predict_proba(test_X)
            if test_pred_proba.shape[1] > 1:
                test_pred = test_pred_proba[:, 1]  # 正常情况，取第二列（上涨概率）
            else:
                # 只有一个类别，全部设为1.0（如果是正类）或0.0（如果是负类）
                classes = rf_model.classes_
                if 1 in classes:
                    test_pred = np.ones(len(test_X))  # 如果唯一的类是正类，全部设为1.0
                else:
                    test_pred = np.zeros(len(test_X))  # 如果唯一的类是负类，全部设为0.0
        except Exception as e:
            print(f"预测概率时出错: {e}")
            # 直接使用predict
            test_pred_class = rf_model.predict(test_X)
            test_pred = np.array([1.0 if p == 1 else 0.0 for p in test_pred_class])

        # 创建预测结果
        predictions = pd.DataFrame({
            '股票代码': prediction_data['股票代码'],
            '股票名称': prediction_data['股票名称'],
            '当前价格': prediction_data['当前价格'],
            '技术强度': prediction_data['技术强度'],
            '预测涨跌概率': test_pred,
            '预测涨跌': ['上涨' if p > 0.5 else '下跌' for p in test_pred],
            '预测信号强度': [abs(p - 0.5) * 2 for p in test_pred],
            '历史上涨概率': up_probability
        })

        # 按预测涨跌概率降序排序
        predictions = predictions.sort_values('预测涨跌概率', ascending=False)

        print("\n预测结果:")
        print(predictions)

        # 保存预测结果
        predictions.to_excel('test_results/consecutive_strength_predictions.xlsx', index=False)

        print("\n测试完成！结果已保存至 test_results 目录")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_consecutive_strength()
