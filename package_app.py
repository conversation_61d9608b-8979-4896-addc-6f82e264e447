"""
打包脚本 - 将股票数据下载程序打包为独立的可执行文件
"""

import os
import sys
import shutil
import subprocess
import platform

def clean_build_folders():
    """清理构建文件夹"""
    folders_to_clean = ['build', 'dist', '__pycache__']
    for folder in folders_to_clean:
        if os.path.exists(folder):
            print(f"清理文件夹: {folder}")
            shutil.rmtree(folder)
    
    # 清理spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        print(f"删除spec文件: {spec_file}")
        os.remove(spec_file)

def create_executable():
    """创建可执行文件"""
    print("开始打包程序...")
    
    # 确定入口文件
    entry_file = "download_stock_data_gui.py"
    if not os.path.exists(entry_file):
        print(f"错误: 入口文件 {entry_file} 不存在")
        return False
    
    # 确定输出文件名
    output_name = "自动下载程序"
    
    # 构建PyInstaller命令
    cmd = [
        "pyinstaller",
        "--name", output_name,
        "--onefile",  # 打包成单个可执行文件
        "--windowed",  # 不显示控制台窗口
        "--clean",  # 在构建之前清理PyInstaller缓存
        "--add-data", f"stock_data_manager.py{os.pathsep}.",  # 添加模块文件
        "--add-data", f"download_stock_data_akshare.py{os.pathsep}.",  # 添加模块文件
        "--icon", "NONE",  # 不使用图标
        entry_file
    ]
    
    # 添加隐藏导入
    hidden_imports = [
        "pandas",
        "numpy",
        "akshare",
        "baostock",
        "openpyxl",
        "tkinter",
        "datetime",
        "os",
        "sys",
        "time",
        "traceback",
        "glob"
    ]
    
    for imp in hidden_imports:
        cmd.extend(["--hidden-import", imp])
    
    # 执行PyInstaller命令
    print(f"执行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # 检查结果
    if result.returncode != 0:
        print(f"打包失败，错误信息:")
        print(result.stderr)
        return False
    
    print("打包成功!")
    
    # 检查生成的文件
    exe_path = os.path.join("dist", f"{output_name}.exe")
    if os.path.exists(exe_path):
        print(f"可执行文件已生成: {exe_path}")
        # 获取文件大小
        size_mb = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"文件大小: {size_mb:.2f} MB")
        return True
    else:
        print(f"错误: 可执行文件 {exe_path} 未生成")
        return False

def create_readme():
    """创建说明文档"""
    readme_content = """# 股票数据自动下载程序

## 使用说明

1. 双击运行"自动下载程序.exe"
2. 在界面上选择开始日期和结束日期
3. 选择数据保存位置（默认为程序所在目录下的complete_excel_results文件夹）
4. 点击"开始下载"按钮开始下载数据
5. 下载完成后，数据将保存在指定目录的stock_data/daily文件夹下，按日期分别存储

## 注意事项

1. 程序会自动检查日期是否为交易日
2. 如果某日期的数据文件已存在，程序会自动跳过该日期
3. 下载过程中请保持网络连接稳定
4. 下载大量数据可能需要较长时间，请耐心等待

## 系统要求

- Windows 7/8/10/11
- 不需要安装Python或其他依赖库
- 需要网络连接以下载股票数据

## 技术支持

如有问题，请联系技术支持。
"""
    
    with open("使用说明.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("已生成使用说明文档: 使用说明.txt")

def main():
    """主函数"""
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {platform.platform()}")
    
    # 清理旧的构建文件
    clean_build_folders()
    
    # 创建可执行文件
    if create_executable():
        # 创建说明文档
        create_readme()
        print("\n打包完成! 请在dist目录中查看生成的文件。")
    else:
        print("\n打包失败，请检查错误信息。")

if __name__ == "__main__":
    main()
