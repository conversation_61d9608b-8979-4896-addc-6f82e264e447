#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
规则策略生成器
作者: Augment AI
版本: 1.0.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime, timedelta

def rule_strategy_1(data, date):
    """
    规则策略1：技术强度>=85 + 看涨技术指标数量=5 + 涨跌幅趋势=1 + 连续技术强度5天数>=450
    """
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['技术强度'] >= 85) &
        (daily_data['看涨技术指标数量'] == 5) &
        (daily_data['涨跌幅趋势'] == 1) &
        (daily_data['连续技术强度5天数'] >= 450)
    ]
    return selected

def rule_strategy_2(data, date):
    """
    规则策略2：技术强度>=90 + 看涨技术指标数量>=4 + 技术指标_均线多头排列=1 + 技术指标_MACD金叉=1
    """
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['技术强度'] >= 90) &
        (daily_data['看涨技术指标数量'] >= 4) &
        (daily_data['技术指标_均线多头排列'] == 1) &
        (daily_data['技术指标_MACD金叉'] == 1)
    ]
    return selected

def rule_strategy_3(data, date):
    """
    规则策略3：技术强度=100 + 连续技术强度5天数>=480 + 看涨技术指标数量>=3
    """
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['技术强度'] == 100) &
        (daily_data['连续技术强度5天数'] >= 480) &
        (daily_data['看涨技术指标数量'] >= 3)
    ]
    return selected

def rule_strategy_4(data, date):
    """
    规则策略4：技术强度>=85 + 看涨技术指标数量=5 + 涨跌幅趋势=1 + 连续技术强度5天数>=450
    """
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['技术强度'] >= 85) &
        (daily_data['看涨技术指标数量'] == 5) &
        (daily_data['涨跌幅趋势'] == 1) &
        (daily_data['连续技术强度5天数'] >= 450)
    ]
    return selected

def backtest(data, strategy_fn, start_date, end_date, initial_capital=10000):
    """回测策略"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    print(f"回测日期范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
    print(f"交易日数量: {len(trading_dates)}")
    
    # 初始化回测结果
    capital = initial_capital
    trades = []
    daily_results = []
    
    # 对每个交易日进行回测
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 应用策略
        recommended_stocks = strategy_fn(data, current_date)
        
        # 记录每日推荐股票
        daily_results.append({
            '日期': current_date.strftime('%Y-%m-%d'),
            '推荐股票数量': len(recommended_stocks)
        })
        
        print(f"日期: {current_date.strftime('%Y-%m-%d')}, 推荐股票数量: {len(recommended_stocks)}")
        
        # 如果有推荐的股票，模拟买入
        if len(recommended_stocks) > 0:
            # 计算每只股票的资金分配
            capital_per_stock = capital / len(recommended_stocks)
            
            # 记录每只股票的买入和卖出情况
            for _, stock in recommended_stocks.iterrows():
                code = stock['股票代码']
                name = stock['股票名称']
                
                # 获取次日该股票数据（买入）
                next_day_data = data[(data['日期'] == next_date) & (data['股票代码'] == code)]
                
                if len(next_day_data) > 0:
                    # 获取次日涨跌幅（模拟买入后的收益）
                    next_day_change = next_day_data['涨跌幅'].values[0]
                    
                    # 计算收益
                    profit = capital_per_stock * next_day_change / 100
                    
                    # 记录交易
                    trades.append({
                        '日期': current_date.strftime('%Y-%m-%d'),
                        '次日': next_date.strftime('%Y-%m-%d'),
                        '股票代码': code,
                        '股票名称': name,
                        '技术强度': stock['技术强度'],
                        '连续技术强度5天数': stock['连续技术强度5天数'],
                        '看涨技术指标数量': stock['看涨技术指标数量'],
                        '涨跌幅趋势': stock['涨跌幅趋势'],
                        '次日涨跌幅': next_day_change,
                        '投入资金': capital_per_stock,
                        '收益': profit,
                        '是否盈利': next_day_change > 0
                    })
    
    # 计算回测结果
    if trades:
        trades_df = pd.DataFrame(trades)
        total_profit = trades_df['收益'].sum()
        win_rate = trades_df['是否盈利'].mean() * 100
        avg_return = trades_df['次日涨跌幅'].mean()
        final_capital = initial_capital + total_profit
        total_return = (final_capital / initial_capital - 1) * 100
        
        # 计算每日收益率
        daily_trades_df = trades_df.groupby('日期').agg({
            '次日涨跌幅': 'mean',
            '是否盈利': 'mean',
            '收益': 'sum',
            '股票代码': 'count'
        }).reset_index()
        
        daily_trades_df.columns = ['日期', '平均涨跌幅', '胜率', '当日收益', '交易数量']
        daily_trades_df['胜率'] = daily_trades_df['胜率'] * 100
        daily_trades_df['累计收益'] = daily_trades_df['当日收益'].cumsum()
        daily_trades_df['累计收益率'] = daily_trades_df['累计收益'] / initial_capital * 100
        
        # 打印回测结果
        print("\n回测结果:")
        print(f"初始资金: {initial_capital:,.2f}元")
        print(f"最终资金: {final_capital:,.2f}元")
        print(f"总收益: {total_profit:,.2f}元")
        print(f"总收益率: {total_return:.2f}%")
        print(f"交易次数: {len(trades)}")
        print(f"胜率: {win_rate:.2f}%")
        print(f"平均涨跌幅: {avg_return:.2f}%")
        
        # 打印每日收益
        print("\n每日收益:")
        for _, row in daily_trades_df.iterrows():
            print(f"{row['日期']}: 交易数量={row['交易数量']}, 平均涨跌幅={row['平均涨跌幅']:.2f}%, 胜率={row['胜率']:.2f}%, 当日收益={row['当日收益']:.2f}元, 累计收益率={row['累计收益率']:.2f}%")
        
        # 打印交易明细
        print("\n交易明细:")
        for _, trade in trades_df.iterrows():
            print(f"{trade['日期']} 买入 {trade['股票代码']} {trade['股票名称']}, 次日涨跌幅: {trade['次日涨跌幅']:.2f}%, 收益: {trade['收益']:.2f}元")
        
        # 保存回测结果到文件
        results_dir = 'rule_strategy_results'
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)
        
        output_file = f"{results_dir}/backtest_results.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"回测日期范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}\n")
            f.write(f"初始资金: {initial_capital:,.2f}元\n")
            f.write(f"最终资金: {final_capital:,.2f}元\n")
            f.write(f"总收益: {total_profit:,.2f}元\n")
            f.write(f"总收益率: {total_return:.2f}%\n")
            f.write(f"交易次数: {len(trades)}\n")
            f.write(f"胜率: {win_rate:.2f}%\n")
            f.write(f"平均涨跌幅: {avg_return:.2f}%\n\n")
            
            f.write("每日收益:\n")
            for _, row in daily_trades_df.iterrows():
                f.write(f"{row['日期']}: 交易数量={row['交易数量']}, 平均涨跌幅={row['平均涨跌幅']:.2f}%, 胜率={row['胜率']:.2f}%, 当日收益={row['当日收益']:.2f}元, 累计收益率={row['累计收益率']:.2f}%\n")
            
            f.write("\n交易明细:\n")
            for _, trade in trades_df.iterrows():
                f.write(f"{trade['日期']} 买入 {trade['股票代码']} {trade['股票名称']}, 次日涨跌幅: {trade['次日涨跌幅']:.2f}%, 收益: {trade['收益']:.2f}元\n")
        
        print(f"\n回测结果已保存到 {output_file}")
        
        return {
            'trades': trades,
            'trades_df': trades_df,
            'daily_trades_df': daily_trades_df,
            'daily_results': pd.DataFrame(daily_results),
            'total_profit': total_profit,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'final_capital': final_capital,
            'total_return': total_return
        }
    else:
        print("\n回测结果: 无交易记录")
        return {
            'trades': [],
            'trades_df': pd.DataFrame(),
            'daily_trades_df': pd.DataFrame(),
            'daily_results': pd.DataFrame(daily_results),
            'total_profit': 0,
            'win_rate': 0,
            'avg_return': 0,
            'final_capital': initial_capital,
            'total_return': 0
        }

def generate_recommendations(data, date, strategy_fn):
    """生成股票推荐"""
    # 确保日期是datetime类型
    date = pd.to_datetime(date)
    
    # 应用策略
    recommended_stocks = strategy_fn(data, date)
    
    print(f"日期: {date.strftime('%Y-%m-%d')}, 推荐股票数量: {len(recommended_stocks)}")
    
    # 保存推荐股票到文件
    results_dir = 'rule_strategy_results'
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
    
    output_file = f"{results_dir}/recommended_stocks_{date.strftime('%Y%m%d')}.csv"
    recommended_stocks.to_csv(output_file, index=False)
    
    print(f"推荐股票已保存到 {output_file}")
    
    # 打印推荐股票
    print("\n推荐股票列表:")
    for i, (_, stock) in enumerate(recommended_stocks.iterrows()):
        if i < 20:  # 只显示前20只
            print(f"{i+1}. {stock['股票代码']} {stock['股票名称']}, 技术强度: {stock['技术强度']}, 连续技术强度5天数: {stock['连续技术强度5天数']}, 看涨技术指标数量: {stock['看涨技术指标数量']}")
    
    if len(recommended_stocks) > 20:
        print(f"... 共 {len(recommended_stocks)} 只股票")
    
    return recommended_stocks

if __name__ == "__main__":
    # 加载数据
    try:
        data_file = '股票明细.xlsx'
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
    except Exception as e:
        print(f"加载数据失败: {e}")
        exit(1)
    
    # 确保日期列是datetime类型
    df['日期'] = pd.to_datetime(df['日期'])
    
    # 获取最新日期
    latest_date = df['日期'].max()
    print(f"最新日期: {latest_date.strftime('%Y-%m-%d')}")
    
    # 回测策略
    print("\n回测策略1:")
    backtest(df, rule_strategy_1, '2025-04-01', '2025-04-30', 10000)
    
    print("\n回测策略2:")
    backtest(df, rule_strategy_2, '2025-04-01', '2025-04-30', 10000)
    
    print("\n回测策略3:")
    backtest(df, rule_strategy_3, '2025-04-01', '2025-04-30', 10000)
    
    print("\n回测策略4:")
    backtest(df, rule_strategy_4, '2025-04-01', '2025-04-30', 10000)
    
    # 生成最新日期的股票推荐
    print("\n生成最新日期的股票推荐:")
    generate_recommendations(df, latest_date, rule_strategy_4)
