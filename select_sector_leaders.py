import pandas as pd
import numpy as np
import re
from collections import defaultdict

# 读取数据
print("读取股票数据...")
data = pd.read_excel('股票明细.xlsx')
print(f"数据形状: {data.shape}")

# 检查数据日期范围
data['日期'] = pd.to_datetime(data['日期'])
print("\n数据日期范围:")
print(f"{data['日期'].min()} 至 {data['日期'].max()}")

# 提取行业信息（从股票名称或代码中提取）
def extract_sector(stock_code, stock_name):
    """
    从股票代码和名称中提取行业信息
    这里使用简化的规则，实际应用中可能需要更复杂的行业分类
    """
    # 银行股
    if '银行' in stock_name or stock_code in ['600000', '600015', '600016', '600036', '601398', '601288']:
        return '银行'
    
    # 券商股
    if '证券' in stock_name or '券商' in stock_name or stock_code in ['600030', '600837', '601688']:
        return '券商'
    
    # 保险股
    if '保险' in stock_name or stock_code in ['601318', '601628', '601336']:
        return '保险'
    
    # 房地产
    if '地产' in stock_name or '房产' in stock_name or '置业' in stock_name:
        return '房地产'
    
    # 汽车
    if '汽车' in stock_name or '车' in stock_name:
        return '汽车'
    
    # 医药
    if '医药' in stock_name or '药' in stock_name or '生物' in stock_name:
        return '医药'
    
    # 电子
    if '电子' in stock_name or '芯片' in stock_name or '半导体' in stock_name:
        return '电子'
    
    # 能源
    if '电力' in stock_name or '能源' in stock_name or '石油' in stock_name or '煤' in stock_name:
        return '能源'
    
    # 消费
    if '消费' in stock_name or '食品' in stock_name or '饮料' in stock_name or '零售' in stock_name:
        return '消费'
    
    # 互联网
    if '网' in stock_name or '科技' in stock_name or '信息' in stock_name:
        return '科技'
    
    # 制造业
    if '机械' in stock_name or '制造' in stock_name or '工业' in stock_name:
        return '制造'
    
    # 基于股票代码的行业分类（简化版）
    if stock_code.startswith('600'):
        first_digit = int(stock_code[3])
        if first_digit == 0:
            return '能源'
        elif first_digit == 1:
            return '材料'
        elif first_digit == 2:
            return '工业'
        elif first_digit == 3:
            return '可选消费'
        elif first_digit == 6:
            return '金融'
        elif first_digit == 8:
            return '科技'
    
    # 默认行业
    return '其他'

# 添加行业信息
print("\n添加行业信息...")
data['行业'] = data.apply(lambda row: extract_sector(row['股票代码'], row['股票名称']), axis=1)

# 计算每只股票在每个日期的次日涨跌幅
def calculate_next_day_return(group):
    """计算每只股票的次日涨跌幅"""
    group = group.sort_values('日期')
    group['次日涨跌幅'] = group['当前价格'].shift(-1) / group['当前价格'] - 1
    return group

# 按股票代码分组，计算次日涨跌幅
print("\n计算次日涨跌幅...")
data_with_returns = data.groupby('股票代码', group_keys=False).apply(calculate_next_day_return)

# 删除没有次日数据的记录
data_with_returns = data_with_returns.dropna(subset=['次日涨跌幅'])

# 计算每个行业的每日平均涨跌幅
print("\n计算每个行业的每日平均涨跌幅...")
sector_daily_returns = data_with_returns.groupby(['日期', '行业'])['涨跌幅'].mean().reset_index()

# 计算每个行业的每日强度（相对于所有行业的平均涨跌幅）
print("\n计算每个行业的每日强度...")
daily_avg_returns = data_with_returns.groupby('日期')['涨跌幅'].mean().reset_index()
daily_avg_returns.columns = ['日期', '市场平均涨跌幅']

# 合并行业涨跌幅和市场平均涨跌幅
sector_strength = pd.merge(sector_daily_returns, daily_avg_returns, on='日期')
sector_strength['行业相对强度'] = sector_strength['涨跌幅'] - sector_strength['市场平均涨跌幅']

# 对每个日期的行业按相对强度排序
sector_strength['行业排名'] = sector_strength.groupby('日期')['行业相对强度'].rank(ascending=False)

# 标记强势行业（排名前3的行业）
sector_strength['是否强势行业'] = sector_strength['行业排名'] <= 3

# 将行业强度信息合并回原始数据
data_with_sector = pd.merge(
    data_with_returns,
    sector_strength[['日期', '行业', '行业相对强度', '行业排名', '是否强势行业']],
    on=['日期', '行业'],
    how='left'
)

# 计算每个行业每天的龙头股
print("\n计算每个行业每天的龙头股...")

def identify_sector_leaders(group, top_n=3):
    """识别行业龙头股（按涨跌幅排名）"""
    # 按涨跌幅排序
    group = group.sort_values('涨跌幅', ascending=False)
    # 标记前top_n只股票为龙头股
    group['是否行业龙头'] = False
    group.iloc[:top_n, group.columns.get_loc('是否行业龙头')] = True
    return group

# 按日期和行业分组，识别龙头股
data_with_leaders = data_with_sector.groupby(['日期', '行业'], group_keys=False).apply(identify_sector_leaders)

# 筛选技术强度=100的股票
print("\n筛选技术强度=100的股票...")
strength_100_stocks = data_with_leaders[data_with_leaders['技术强度'] == 100]

# 计算每个日期技术强度=100的股票中，强势行业和龙头股的比例
print("\n计算强势行业和龙头股的比例...")
daily_stats = strength_100_stocks.groupby('日期').agg({
    '股票代码': 'count',
    '是否强势行业': 'mean',
    '是否行业龙头': 'mean'
}).reset_index()
daily_stats.columns = ['日期', '技术强度100股票数量', '强势行业比例', '行业龙头比例']

print("\n每日技术强度=100股票中的强势行业和龙头股比例:")
print(daily_stats)

# 分析技术强度=100且属于强势行业的股票表现
print("\n分析技术强度=100且属于强势行业的股票表现...")
strong_sector_stocks = strength_100_stocks[strength_100_stocks['是否强势行业'] == True]
print(f"技术强度=100且属于强势行业的股票数量: {len(strong_sector_stocks)}")
print(f"平均次日涨跌幅: {strong_sector_stocks['次日涨跌幅'].mean() * 100:.2f}%")
print(f"次日上涨比例: {(strong_sector_stocks['次日涨跌幅'] > 0).mean() * 100:.2f}%")

# 分析技术强度=100且是行业龙头的股票表现
print("\n分析技术强度=100且是行业龙头的股票表现...")
leader_stocks = strength_100_stocks[strength_100_stocks['是否行业龙头'] == True]
print(f"技术强度=100且是行业龙头的股票数量: {len(leader_stocks)}")
print(f"平均次日涨跌幅: {leader_stocks['次日涨跌幅'].mean() * 100:.2f}%")
print(f"次日上涨比例: {(leader_stocks['次日涨跌幅'] > 0).mean() * 100:.2f}%")

# 分析技术强度=100且同时属于强势行业和行业龙头的股票表现
print("\n分析技术强度=100且同时属于强势行业和行业龙头的股票表现...")
elite_stocks = strength_100_stocks[(strength_100_stocks['是否强势行业'] == True) & (strength_100_stocks['是否行业龙头'] == True)]
print(f"技术强度=100且同时属于强势行业和行业龙头的股票数量: {len(elite_stocks)}")
print(f"平均次日涨跌幅: {elite_stocks['次日涨跌幅'].mean() * 100:.2f}%")
print(f"次日上涨比例: {(elite_stocks['次日涨跌幅'] > 0).mean() * 100:.2f}%")

# 保存最近一天的推荐股票
print("\n保存最近一天的推荐股票...")
recent_date = data_with_leaders['日期'].max()

# 筛选最近一天技术强度=100的股票
recent_strength_100 = strength_100_stocks[strength_100_stocks['日期'] == recent_date]
print(f"最近日期 {recent_date.date()} 技术强度=100的股票数量: {len(recent_strength_100)}")

# 筛选最近一天技术强度=100且属于强势行业的股票
recent_strong_sector = recent_strength_100[recent_strength_100['是否强势行业'] == True]
print(f"最近日期 {recent_date.date()} 技术强度=100且属于强势行业的股票数量: {len(recent_strong_sector)}")

# 筛选最近一天技术强度=100且是行业龙头的股票
recent_leaders = recent_strength_100[recent_strength_100['是否行业龙头'] == True]
print(f"最近日期 {recent_date.date()} 技术强度=100且是行业龙头的股票数量: {len(recent_leaders)}")

# 筛选最近一天技术强度=100且同时属于强势行业和行业龙头的股票
recent_elite = recent_strength_100[(recent_strength_100['是否强势行业'] == True) & (recent_strength_100['是否行业龙头'] == True)]
print(f"最近日期 {recent_date.date()} 技术强度=100且同时属于强势行业和行业龙头的股票数量: {len(recent_elite)}")

# 保存推荐股票列表
if len(recent_strength_100) > 0:
    recent_strength_100[['股票代码', '股票名称', '行业', '当前价格', '涨跌幅', '技术强度', '技术指标', '行业相对强度', '是否强势行业', '是否行业龙头']].to_excel("技术强度100_全部股票.xlsx", index=False)
    print("已将技术强度=100的全部股票保存到 '技术强度100_全部股票.xlsx'")

if len(recent_strong_sector) > 0:
    recent_strong_sector[['股票代码', '股票名称', '行业', '当前价格', '涨跌幅', '技术强度', '技术指标', '行业相对强度', '是否强势行业', '是否行业龙头']].to_excel("技术强度100_强势行业股票.xlsx", index=False)
    print("已将技术强度=100且属于强势行业的股票保存到 '技术强度100_强势行业股票.xlsx'")

if len(recent_leaders) > 0:
    recent_leaders[['股票代码', '股票名称', '行业', '当前价格', '涨跌幅', '技术强度', '技术指标', '行业相对强度', '是否强势行业', '是否行业龙头']].to_excel("技术强度100_行业龙头股票.xlsx", index=False)
    print("已将技术强度=100且是行业龙头的股票保存到 '技术强度100_行业龙头股票.xlsx'")

if len(recent_elite) > 0:
    recent_elite[['股票代码', '股票名称', '行业', '当前价格', '涨跌幅', '技术强度', '技术指标', '行业相对强度', '是否强势行业', '是否行业龙头']].to_excel("技术强度100_精选股票.xlsx", index=False)
    print("已将技术强度=100且同时属于强势行业和行业龙头的股票保存到 '技术强度100_精选股票.xlsx'")

# 创建自动选股函数
def auto_select_stocks(date, data, max_stocks=10):
    """
    自动选择指定日期的推荐股票
    
    参数:
    date: 日期
    data: 包含股票数据的DataFrame
    max_stocks: 最大推荐股票数量
    
    返回:
    推荐股票列表
    """
    # 筛选指定日期的数据
    day_data = data[data['日期'] == date]
    
    # 筛选技术强度=100的股票
    strength_100 = day_data[day_data['技术强度'] == 100]
    
    if len(strength_100) == 0:
        return []
    
    # 优先选择同时属于强势行业和行业龙头的股票
    elite = strength_100[(strength_100['是否强势行业'] == True) & (strength_100['是否行业龙头'] == True)]
    
    # 如果精选股票不足，再选择行业龙头
    if len(elite) < max_stocks:
        leaders = strength_100[strength_100['是否行业龙头'] == True]
        leaders = leaders[~leaders['股票代码'].isin(elite['股票代码'])]  # 排除已选的精选股票
        
        # 按行业相对强度排序
        leaders = leaders.sort_values('行业相对强度', ascending=False)
        
        # 添加到精选股票中，直到达到最大数量或没有更多股票
        remaining = max_stocks - len(elite)
        elite = pd.concat([elite, leaders.head(remaining)])
    
    # 如果仍然不足，再选择强势行业的股票
    if len(elite) < max_stocks:
        strong_sector = strength_100[strength_100['是否强势行业'] == True]
        strong_sector = strong_sector[~strong_sector['股票代码'].isin(elite['股票代码'])]  # 排除已选的股票
        
        # 按涨跌幅排序
        strong_sector = strong_sector.sort_values('涨跌幅', ascending=False)
        
        # 添加到精选股票中，直到达到最大数量或没有更多股票
        remaining = max_stocks - len(elite)
        elite = pd.concat([elite, strong_sector.head(remaining)])
    
    # 如果仍然不足，选择剩余的技术强度=100的股票
    if len(elite) < max_stocks:
        remaining_stocks = strength_100[~strength_100['股票代码'].isin(elite['股票代码'])]  # 排除已选的股票
        
        # 按涨跌幅排序
        remaining_stocks = remaining_stocks.sort_values('涨跌幅', ascending=False)
        
        # 添加到精选股票中，直到达到最大数量或没有更多股票
        remaining = max_stocks - len(elite)
        elite = pd.concat([elite, remaining_stocks.head(remaining)])
    
    # 按行业分组，确保行业分散
    elite_by_sector = elite.groupby('行业').apply(lambda x: x.sort_values('涨跌幅', ascending=False).head(2)).reset_index(drop=True)
    
    # 如果分散后的股票数量超过最大数量，按涨跌幅排序取前max_stocks只
    if len(elite_by_sector) > max_stocks:
        elite_by_sector = elite_by_sector.sort_values('涨跌幅', ascending=False).head(max_stocks)
    
    return elite_by_sector

# 为每个交易日生成推荐股票
print("\n为每个交易日生成推荐股票...")
daily_recommendations = []

for date in sorted(data_with_leaders['日期'].unique()):
    recommendations = auto_select_stocks(date, data_with_leaders)
    
    if len(recommendations) > 0:
        daily_recommendations.append({
            '日期': date,
            '推荐股票数量': len(recommendations),
            '推荐股票': ', '.join([f"{row['股票名称']}({row['股票代码']})" for _, row in recommendations.iterrows()])
        })

# 转换为DataFrame并显示结果
if daily_recommendations:
    recommendations_df = pd.DataFrame(daily_recommendations)
    print("\n每日推荐股票:")
    print(recommendations_df[['日期', '推荐股票数量']])
    
    # 保存每日推荐股票
    recommendations_df.to_excel("每日推荐股票.xlsx", index=False)
    print("已将每日推荐股票保存到 '每日推荐股票.xlsx'")

# 保存最近一天的自动推荐股票
recent_recommendations = auto_select_stocks(recent_date, data_with_leaders)

if len(recent_recommendations) > 0:
    recent_recommendations[['股票代码', '股票名称', '行业', '当前价格', '涨跌幅', '技术强度', '技术指标', '行业相对强度', '是否强势行业', '是否行业龙头']].to_excel("今日推荐股票.xlsx", index=False)
    print(f"\n已将今日({recent_date.date()})推荐股票保存到 '今日推荐股票.xlsx'")
    
    print("\n今日推荐股票:")
    for _, row in recent_recommendations.iterrows():
        print(f"{row['股票名称']}({row['股票代码']}): 行业={row['行业']}, 涨跌幅={row['涨跌幅']*100:.2f}%, 行业相对强度={row['行业相对强度']*100:.2f}%")
