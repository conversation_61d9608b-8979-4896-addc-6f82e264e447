"""
打包脚本 - 创建包含所有依赖的独立可执行文件（修复版）
"""

import os
import subprocess
import sys
import platform
import shutil
import site
import glob

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'pyinstaller',
        'pandas',
        'numpy',
        'akshare',
        'tqdm',
        'openpyxl',
        'pyarrow',  # 用于parquet文件支持
        'requests',
        'lxml',
        'beautifulsoup4',
        'pywin32',  # Windows特定依赖
        'py_mini_racer',  # akshare依赖
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")

    if missing_packages:
        print("\n需要安装以下依赖:")
        for package in missing_packages:
            print(f"  - {package}")

        install = input("\n是否自动安装这些依赖? (y/n): ")
        if install.lower() == 'y':
            for package in missing_packages:
                print(f"\n正在安装 {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"{package} 安装完成")
        else:
            print("\n请手动安装缺失的依赖后再运行此脚本")
            sys.exit(1)

def find_dll_files():
    """查找py_mini_racer的DLL文件"""
    dll_files = []

    # 查找所有可能的site-packages目录
    site_packages = site.getsitepackages()
    if hasattr(site, 'getusersitepackages'):
        site_packages.append(site.getusersitepackages())

    # 查找py_mini_racer目录
    for site_pkg in site_packages:
        # 查找py_mini_racer目录
        mini_racer_path = os.path.join(site_pkg, 'py_mini_racer')
        if os.path.exists(mini_racer_path):
            # 查找DLL文件
            dll_pattern = os.path.join(mini_racer_path, '*.dll')
            dll_files.extend(glob.glob(dll_pattern))

            # 特别处理mini_racer.dll
            mini_racer_dll = os.path.join(mini_racer_path, 'mini_racer.dll')
            if os.path.exists(mini_racer_dll) and mini_racer_dll not in dll_files:
                dll_files.append(mini_racer_dll)

        # 查找akshare目录
        akshare_path = os.path.join(site_pkg, 'akshare')
        if os.path.exists(akshare_path):
            # 递归查找所有DLL文件
            for root, _, files in os.walk(akshare_path):
                for file in files:
                    if file.endswith('.dll'):
                        dll_files.append(os.path.join(root, file))

    # 如果没有找到DLL文件，尝试从Python安装目录查找
    if not any(os.path.basename(dll).lower() == 'mini_racer.dll' for dll in dll_files):
        python_dlls = os.path.join(os.path.dirname(sys.executable), 'DLLs')
        if os.path.exists(python_dlls):
            mini_racer_dll = os.path.join(python_dlls, 'mini_racer.dll')
            if os.path.exists(mini_racer_dll):
                dll_files.append(mini_racer_dll)

    return dll_files

def create_spec_file(dll_files):
    """创建自定义spec文件"""
    # 准备DLL文件的数据部分
    datas_str = ""
    for dll_file in dll_files:
        datas_str += f"    (r'{dll_file}', '.'),\n"

    # 添加tkinter资源
    import tkinter
    tk_dir = os.path.dirname(tkinter.__file__)
    datas_str += f"    (r'{tk_dir}', 'tkinter'),\n"

    spec_content = f"""# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['download_stock_data_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
{datas_str}
    ],
    hiddenimports=[
        'akshare',
        'pandas',
        'numpy',
        'tqdm',
        'openpyxl',
        'pyarrow',
        'requests',
        'lxml',
        'bs4',
        'win32api',
        'win32con',
        'tkinter',
        'queue',
        'threading',
        'datetime',
        'time',
        'os',
        'glob',
        'stock_data_manager',
        'py_mini_racer',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='自动下载程序',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 改为False，不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
"""

    with open('download_stock_data_fixed.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("已创建自定义spec文件，包含必要的DLL文件")

def build_executable():
    """使用PyInstaller构建可执行文件"""
    print("\n开始构建可执行文件...")

    # 使用spec文件构建
    subprocess.check_call([
        sys.executable,
        "-m",
        "PyInstaller",
        "download_stock_data_fixed.spec",
        "--clean"
    ])

    print("\n构建完成!")

    # 检查是否成功创建了可执行文件
    if os.path.exists(os.path.join('dist', '自动下载程序.exe')):
        print(f"\n可执行文件已创建: {os.path.abspath(os.path.join('dist', '自动下载程序.exe'))}")

        # 创建一个包含必要文件的发布目录
        create_release_package()
    else:
        print("\n错误: 未能创建可执行文件")

def create_release_package():
    """创建一个包含所有必要文件的发布包"""
    release_dir = "股票数据下载工具_修复版"

    # 如果目录已存在，先删除
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)

    # 创建发布目录
    os.makedirs(release_dir)

    # 复制可执行文件
    shutil.copy(os.path.join('dist', '自动下载程序.exe'), os.path.join(release_dir, '自动下载程序.exe'))

    # 创建默认数据目录
    os.makedirs(os.path.join(release_dir, 'data', 'stock_data', 'daily'), exist_ok=True)

    # 创建说明文件
    with open(os.path.join(release_dir, '使用说明.txt'), 'w', encoding='utf-8') as f:
        f.write("""股票数据下载工具使用说明
====================

1. 运行方法:
   双击"自动下载程序.exe"即可启动程序

2. 功能说明:
   - 可以下载指定日期范围内的A股股票历史数据
   - 数据按日期分别存储在Excel文件中
   - 默认保存在程序所在目录的data文件夹中

3. 使用步骤:
   a. 设置数据输出目录(可选)
   b. 设置日期范围
   c. 点击"开始下载"按钮
   d. 等待下载完成

4. 注意事项:
   - 首次运行时可能需要等待较长时间
   - 下载过程中请保持网络连接
   - 如遇到问题，请查看程序日志窗口的提示信息
""")

    print(f"\n发布包已创建: {os.path.abspath(release_dir)}")
    print("包含以下文件:")
    print(f"  - {release_dir}/自动下载程序.exe")
    print(f"  - {release_dir}/使用说明.txt")
    print(f"  - {release_dir}/data/ (默认数据目录)")

def main():
    """主函数"""
    print("=" * 50)
    print("股票数据下载工具打包脚本 (修复版)")
    print("=" * 50)

    # 检查操作系统
    if platform.system() != 'Windows':
        print("警告: 此脚本设计用于Windows系统，在其他系统上可能无法正常工作")

    # 检查依赖
    print("\n检查依赖...")
    check_dependencies()

    # 查找DLL文件
    print("\n查找必要的DLL文件...")
    dll_files = find_dll_files()
    if dll_files:
        print(f"找到 {len(dll_files)} 个DLL文件:")
        for dll in dll_files:
            print(f"  - {dll}")
    else:
        print("警告: 未找到py_mini_racer的DLL文件，打包可能会失败")

    # 创建spec文件
    print("\n创建打包配置...")
    create_spec_file(dll_files)

    # 构建可执行文件
    build_executable()

    print("\n打包过程完成!")
    print("=" * 50)

if __name__ == "__main__":
    main()
