#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def check_actual_table():
    """检查实际生成的表格数据"""
    
    print("=== 检查实际表格数据 ===")
    
    file_path = 'E:/机器学习/complete_excel_results/tech_strength/daily/tech_strength_2025-05-15.xlsx'
    
    print(f"检查文件: {file_path}")
    print(f"文件存在: {os.path.exists(file_path)}")
    
    if os.path.exists(file_path):
        try:
            # 读取数据，不指定dtype，看看实际的数据类型
            df = pd.read_excel(file_path)
            
            print(f"数据行数: {len(df)}")
            print(f"数据列数: {len(df.columns)}")
            
            # 检查前5行的技术指标特征和趋势组合
            print(f"\n=== 前5行数据检查 ===")
            for i in range(min(5, len(df))):
                row = df.iloc[i]
                tech_feature = row.get('技术指标特征', 'N/A')
                trend_combo = row.get('趋势组合', 'N/A')
                
                print(f"行 {i+1}:")
                print(f"  股票代码: {row.get('股票代码', 'N/A')}")
                print(f"  技术指标特征: {tech_feature} (类型: {type(tech_feature)}, 值: {repr(tech_feature)})")
                print(f"  趋势组合: {trend_combo} (类型: {type(trend_combo)}, 值: {repr(trend_combo)})")
                
                # 检查长度
                if isinstance(tech_feature, str):
                    print(f"  技术指标特征长度: {len(tech_feature)}")
                if isinstance(trend_combo, str):
                    print(f"  趋势组合长度: {len(trend_combo)}")
                print()
            
            # 检查字段类型
            print(f"=== 字段类型检查 ===")
            if '技术指标特征' in df.columns:
                print(f"技术指标特征列类型: {df['技术指标特征'].dtype}")
                print(f"技术指标特征唯一值数量: {df['技术指标特征'].nunique()}")
                print(f"技术指标特征前10个值: {df['技术指标特征'].value_counts().head(10).index.tolist()}")
            
            if '趋势组合' in df.columns:
                print(f"趋势组合列类型: {df['趋势组合'].dtype}")
                print(f"趋势组合唯一值数量: {df['趋势组合'].nunique()}")
                print(f"趋势组合前10个值: {df['趋势组合'].value_counts().head(10).index.tolist()}")
            
            # 检查前导0
            print(f"\n=== 前导0检查 ===")
            if '技术指标特征' in df.columns:
                tech_features = df['技术指标特征'].astype(str)
                has_leading_zero_tech = any(x.startswith('0') for x in tech_features if x != 'nan')
                print(f"技术指标特征有前导0: {has_leading_zero_tech}")
                
                # 显示一些以0开头的值
                leading_zero_tech = [x for x in tech_features if x.startswith('0') and x != 'nan']
                if leading_zero_tech:
                    print(f"以0开头的技术指标特征示例: {leading_zero_tech[:5]}")
                else:
                    print("没有找到以0开头的技术指标特征")
            
            if '趋势组合' in df.columns:
                trend_combos = df['趋势组合'].astype(str)
                has_leading_zero_trend = any(x.startswith('0') for x in trend_combos if x != 'nan')
                print(f"趋势组合有前导0: {has_leading_zero_trend}")
                
                # 显示一些以0开头的值
                leading_zero_trend = [x for x in trend_combos if x.startswith('0') and x != 'nan']
                if leading_zero_trend:
                    print(f"以0开头的趋势组合示例: {leading_zero_trend[:5]}")
                else:
                    print("没有找到以0开头的趋势组合")
            
            # 检查连续技术强度
            print(f"\n=== 连续技术强度检查 ===")
            if all(col in df.columns for col in ['连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']):
                print("前5个股票的连续技术强度:")
                for i in range(min(5, len(df))):
                    row = df.iloc[i]
                    c3 = row['连续技术强度3天数']
                    c5 = row['连续技术强度5天数']
                    c10 = row['连续技术强度10天数']
                    order_ok = c3 <= c5 <= c10
                    print(f"  {row['股票代码']}: 3天={c3}, 5天={c5}, 10天={c10} {'✅' if order_ok else '❌'}")
            
            print(f"\n✅ 表格数据检查完成")
            
        except Exception as e:
            print(f"❌ 读取表格时出错: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ 文件不存在，无法检查表格数据")

if __name__ == "__main__":
    check_actual_table()
