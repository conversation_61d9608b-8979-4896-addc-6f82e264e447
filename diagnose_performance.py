"""
性能诊断脚本 - 分析打包后程序性能问题
"""

import time
import os
import sys
import psutil
import pandas as pd
from datetime import datetime

def log_performance(message, start_time=None):
    """记录性能日志"""
    current_time = time.time()
    if start_time:
        elapsed = current_time - start_time
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message} - 耗时: {elapsed:.2f}秒")
    else:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    return current_time

def check_system_info():
    """检查系统信息"""
    print("=" * 60)
    print("🔍 系统信息诊断")
    print("=" * 60)
    
    # 检查是否在打包环境中运行
    if getattr(sys, 'frozen', False):
        print("✅ 运行环境: PyInstaller打包程序")
        print(f"📁 程序路径: {sys.executable}")
        print(f"📁 临时目录: {sys._MEIPASS}")
    else:
        print("✅ 运行环境: Python开发环境")
        print(f"📁 脚本路径: {__file__}")
    
    # 系统资源
    memory = psutil.virtual_memory()
    print(f"💾 内存使用: {memory.percent}% ({memory.used // (1024**3)}GB / {memory.total // (1024**3)}GB)")
    print(f"💻 CPU使用: {psutil.cpu_percent()}%")
    
    # 磁盘信息
    if getattr(sys, 'frozen', False):
        temp_disk = psutil.disk_usage(sys._MEIPASS)
        print(f"💽 临时目录磁盘: {temp_disk.free // (1024**3)}GB 可用")

def test_module_imports():
    """测试模块导入性能"""
    print("\n=" * 60)
    print("📦 模块导入性能测试")
    print("=" * 60)
    
    modules_to_test = [
        'pandas',
        'numpy',
        'openpyxl',
        'pyarrow',
        'fastparquet',
        'tkinter',
        'datetime',
        'os',
        'sys'
    ]
    
    for module_name in modules_to_test:
        start_time = time.time()
        try:
            __import__(module_name)
            elapsed = time.time() - start_time
            print(f"  ✅ {module_name}: {elapsed:.3f}秒")
        except ImportError as e:
            elapsed = time.time() - start_time
            print(f"  ❌ {module_name}: {elapsed:.3f}秒 - {e}")

def test_file_access():
    """测试文件访问性能"""
    print("\n=" * 60)
    print("📁 文件访问性能测试")
    print("=" * 60)
    
    # 测试当前目录文件列表
    start_time = time.time()
    try:
        files = os.listdir('.')
        elapsed = time.time() - start_time
        print(f"  📂 当前目录列表 ({len(files)}个文件): {elapsed:.3f}秒")
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"  ❌ 当前目录列表失败: {elapsed:.3f}秒 - {e}")
    
    # 测试创建临时文件
    start_time = time.time()
    try:
        test_file = 'temp_performance_test.txt'
        with open(test_file, 'w') as f:
            f.write('test data')
        elapsed = time.time() - start_time
        print(f"  📝 创建临时文件: {elapsed:.3f}秒")
        
        # 删除临时文件
        os.remove(test_file)
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"  ❌ 创建临时文件失败: {elapsed:.3f}秒 - {e}")

def test_pandas_operations():
    """测试pandas操作性能"""
    print("\n=" * 60)
    print("🐼 Pandas操作性能测试")
    print("=" * 60)
    
    # 创建测试数据
    start_time = time.time()
    try:
        df = pd.DataFrame({
            'A': range(1000),
            'B': range(1000, 2000),
            'C': ['test'] * 1000
        })
        elapsed = time.time() - start_time
        print(f"  📊 创建DataFrame (1000行): {elapsed:.3f}秒")
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"  ❌ 创建DataFrame失败: {elapsed:.3f}秒 - {e}")
        return
    
    # 测试筛选操作
    start_time = time.time()
    try:
        filtered = df[df['A'] > 500]
        elapsed = time.time() - start_time
        print(f"  🔍 数据筛选: {elapsed:.3f}秒")
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"  ❌ 数据筛选失败: {elapsed:.3f}秒 - {e}")
    
    # 测试保存Excel
    start_time = time.time()
    try:
        test_excel = 'temp_performance_test.xlsx'
        df.to_excel(test_excel, index=False)
        elapsed = time.time() - start_time
        print(f"  💾 保存Excel文件: {elapsed:.3f}秒")
        
        # 删除临时文件
        os.remove(test_excel)
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"  ❌ 保存Excel失败: {elapsed:.3f}秒 - {e}")

def test_data_loading_simulation():
    """模拟数据加载过程"""
    print("\n=" * 60)
    print("📈 数据加载模拟测试")
    print("=" * 60)
    
    # 模拟读取多个小文件（类似技术强度数据）
    start_time = time.time()
    try:
        total_records = 0
        for i in range(10):  # 模拟读取10个文件
            # 创建模拟数据
            df = pd.DataFrame({
                '股票代码': [f'00000{j}' for j in range(100)],
                '技术强度': [50 + j % 50 for j in range(100)],
                '日期': ['2025-01-01'] * 100
            })
            total_records += len(df)
            
            # 模拟筛选操作
            filtered = df[df['技术强度'] > 70]
            
        elapsed = time.time() - start_time
        print(f"  📊 模拟数据加载和筛选 ({total_records}条记录): {elapsed:.3f}秒")
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"  ❌ 模拟数据加载失败: {elapsed:.3f}秒 - {e}")

def main():
    """主函数"""
    print("🔍 策略回测程序性能诊断")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    overall_start = time.time()
    
    # 系统信息检查
    check_system_info()
    
    # 模块导入测试
    test_module_imports()
    
    # 文件访问测试
    test_file_access()
    
    # Pandas操作测试
    test_pandas_operations()
    
    # 数据加载模拟
    test_data_loading_simulation()
    
    # 总结
    total_elapsed = time.time() - overall_start
    print("\n" + "=" * 60)
    print("📋 诊断总结")
    print("=" * 60)
    print(f"🕐 总诊断时间: {total_elapsed:.2f}秒")
    
    if total_elapsed > 30:
        print("⚠️  警告: 诊断时间过长，可能存在性能问题")
        print("建议检查:")
        print("  - 磁盘I/O性能")
        print("  - 内存使用情况")
        print("  - 防病毒软件干扰")
        print("  - PyInstaller打包配置")
    else:
        print("✅ 诊断时间正常")
    
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
