# 回测程序修复指南

## 问题描述

在回测程序中，当某个组合没有筛选出数据时，程序会跳过这个组合，不会创建相应的Excel文件。这导致程序无法继续执行下一个组合，因为程序是根据文件夹来判断要不要继续执行下一个任务的。

## 解决方案

我们需要修改回测程序，确保即使没有筛选出数据，也会创建一个空的Excel文件，并继续执行下一个组合。

## 自动修复方法

1. 将`fix_backtest_local.py`文件放在与`backtest_local.py`相同的目录下
2. 运行此文件：`python fix_backtest_local.py`
3. 程序会自动修改`backtest_local.py`文件，添加创建空Excel文件的功能

## 手动修复方法

如果自动修复失败，您可以按照以下步骤手动修改`backtest_local.py`文件：

1. 打开`backtest_local.py`文件
2. 找到`process_strategy`函数
3. 在函数中找到以下代码：

```python
else:
    print(f"策略 {strategy_id} 没有选出任何股票，不生成详细分析文件")
    return False
```

4. 将上述代码替换为以下代码：

```python
else:
    print(f"策略 {strategy_id} 没有选出任何股票，创建空的Excel文件")
    
    # 创建空的Excel文件
    detail_file = os.path.join(output_dir, f"strategy_{strategy_id}.xlsx")
    
    # 创建一个Excel写入器
    with pd.ExcelWriter(detail_file, engine='openpyxl') as writer:
        # 1. 策略汇总参数
        strategy_summary = pd.DataFrame({
            '策略编号': [strategy_id],
            '策略组合': [summary_df.loc[idx, '策略组合']],
            '特征数量': [summary_df.loc[idx, '特征数量']],
            '总收益率(%)': [0],
            '平均收益率(%)': [0],
            '平均胜率(%)': [0],
            '平均每日交易笔数': [0],
            '总交易笔数': [0],
            '交易天数': [0],
            '总天数': [len(stock_df['日期'].unique())],
            '交易频率(%)': [0],
        })
        strategy_summary.to_excel(writer, sheet_name='策略汇总参数', index=False)

        # 2. 策略参数
        strategy_params = pd.DataFrame({
            '策略编号': [strategy_id],
            '策略条件描述': [summary_df.loc[idx, '策略条件描述']],
        })
        strategy_params.to_excel(writer, sheet_name='策略参数', index=False)
        
        # 3. 空的股票选择结果
        empty_df = pd.DataFrame(columns=[
            '股票代码', '股票名称', '日期', '收盘价', '涨跌幅', 
            '技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数',
            '技术指标特征', '趋势组合', '成交量是前一日几倍', 
            '买入日开盘涨跌幅', '卖出日开盘涨跌幅', '日内股票标记'
        ])
        empty_df.to_excel(writer, sheet_name='股票选择', index=False)
        
        # 4. 说明
        info_df = pd.DataFrame({
            '说明': [
                f'策略编号: {strategy_id}',
                f'创建时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                '该策略没有筛选出符合条件的股票',
                '这是一个空的Excel文件，用于确保程序能够继续执行下一个组合'
            ]
        })
        info_df.to_excel(writer, sheet_name='说明', index=False)
    
    print(f"已创建空的Excel文件: {detail_file}")
    return True
```

5. 保存文件

## 验证修复

修复后，您可以运行回测程序，验证是否能够正常处理没有筛选出数据的组合。如果修复成功，程序应该能够创建空的Excel文件，并继续执行下一个组合。

## 注意事项

1. 修改前请备份原始文件
2. 如果您的回测程序版本与本指南不同，可能需要根据实际情况调整代码
3. 如果您不确定如何修改，请联系技术支持
