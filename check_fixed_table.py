import pandas as pd

print("=== 检查修复后的强势股表格 ===")

# 读取修复后的文件
df = pd.read_excel('E:/机器学习/complete_excel_results/tech_strength/daily/strong/tech_strength_strong_2025-05-15.xlsx', 
                   dtype={'技术指标特征': str, '趋势组合': str})

print(f"数据行数: {len(df)}")

# 检查前10行
print("\n前10行数据检查:")
for i in range(min(10, len(df))):
    row = df.iloc[i]
    tech_feature = row['技术指标特征']
    trend_combo = row['趋势组合']
    
    print(f"行{i+1}:")
    print(f"  股票代码: {row['股票代码']}")
    print(f"  技术指标特征: '{tech_feature}' (类型: {type(tech_feature)}, 长度: {len(str(tech_feature))})")
    print(f"  趋势组合: '{trend_combo}' (类型: {type(trend_combo)}, 长度: {len(str(trend_combo))})")
    
    # 检查前导0
    tech_str = str(tech_feature)
    trend_str = str(trend_combo)
    
    if tech_str.startswith('0'):
        print(f"  ✅ 技术指标特征保留前导0")
    else:
        print(f"  ❌ 技术指标特征无前导0: {tech_str}")
    
    if trend_str.startswith('0'):
        print(f"  ✅ 趋势组合保留前导0")
    else:
        print(f"  ❌ 趋势组合无前导0: {trend_str}")
    print()

# 统计前导0情况
print("=== 前导0统计 ===")

tech_features = df['技术指标特征'].astype(str)
trend_combos = df['趋势组合'].astype(str)

tech_with_leading_zero = sum(1 for x in tech_features if x.startswith('0'))
trend_with_leading_zero = sum(1 for x in trend_combos if x.startswith('0'))

print(f"技术指标特征:")
print(f"  总数: {len(tech_features)}")
print(f"  以0开头的数量: {tech_with_leading_zero}")
print(f"  前导0保留率: {tech_with_leading_zero/len(tech_features)*100:.2f}%")

print(f"趋势组合:")
print(f"  总数: {len(trend_combos)}")
print(f"  以0开头的数量: {trend_with_leading_zero}")
print(f"  前导0保留率: {trend_with_leading_zero/len(trend_combos)*100:.2f}%")

# 检查长度
tech_lengths = [len(str(x)) for x in tech_features]
trend_lengths = [len(str(x)) for x in trend_combos]

print(f"\n=== 长度检查 ===")
print(f"技术指标特征长度都是6位: {all(l == 6 for l in tech_lengths)}")
print(f"趋势组合长度都是6位: {all(l == 6 for l in trend_lengths)}")

if tech_with_leading_zero > 0 and trend_with_leading_zero > 0:
    print("\n🎉 前导0修复成功！")
else:
    print("\n❌ 前导0修复失败")

print("\n检查完成")
