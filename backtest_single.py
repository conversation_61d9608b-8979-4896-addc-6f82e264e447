import argparse
import subprocess
import time
import os

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='回测单个股票策略')
    parser.add_argument('--id', type=int, required=True, help='策略ID')
    parser.add_argument('--batch', type=int, default=1, help='批处理大小，同时处理多少个策略')
    parser.add_argument('--preload', action='store_true', help='是否预加载数据')
    parser.add_argument('--data_dir', type=str, help='数据目录路径')
    args = parser.parse_args()

    strategy_id = args.id
    batch_size = args.batch
    data_dir = args.data_dir

    print(f"开始回测策略 {strategy_id}...")

    # 获取当前脚本的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # 构建backtest_local.py的绝对路径
    backtest_local_path = os.path.join(script_dir, "backtest_local.py")

    # 构建命令，添加预加载数据标志和数据目录参数
    cmd = f"python \"{backtest_local_path}\" --id {strategy_id} --batch {batch_size} --preload"

    # 如果指定了数据目录，添加到命令中
    if data_dir:
        print(f"使用指定的数据目录: {data_dir}")
        cmd += f" --data_dir \"{data_dir}\""

    # 执行命令
    try:
        start_time = time.time()
        subprocess.run(cmd, shell=True, check=True)
        end_time = time.time()

        elapsed_time = end_time - start_time
        minutes, seconds = divmod(elapsed_time, 60)

        print(f"策略 {strategy_id} 回测完成！耗时: {int(minutes)}分钟 {int(seconds)}秒")
    except subprocess.CalledProcessError as e:
        print(f"策略 {strategy_id} 回测失败: {e}")
    except KeyboardInterrupt:
        print(f"用户中断策略 {strategy_id} 的回测")

if __name__ == "__main__":
    main()
