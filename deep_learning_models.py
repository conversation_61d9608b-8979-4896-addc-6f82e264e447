import pandas as pd
import numpy as np
import os
import joblib
import matplotlib.pyplot as plt
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor, GradientBoostingClassifier, GradientBoostingRegressor
from sklearn.neural_network import MLPClassifier, MLPRegressor
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score, accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.model_selection import train_test_split, GridSearchCV

class DeepLearningModels:
    """
    机器学习模型模块，使用随机森林、梯度提升和神经网络等模型捕捉时间序列特征
    """

    def __init__(self):
        """初始化机器学习模型模块"""
        # 创建模型目录
        if not os.path.exists('dl_models'):
            os.makedirs('dl_models')

        # 设置随机种子，确保结果可复现
        np.random.seed(42)

        # 模型参数
        self.sequence_length = 5  # 时间序列长度
        self.cv = 3  # 交叉验证折数

    def prepare_time_series_data(self, stock_data, target_type='next_day_return'):
        """
        准备时间序列数据
        参数:
            stock_data: 股票历史数据DataFrame
            target_type: 目标变量类型，可选值:
                - 'next_day_return': 次日收益率
                - 'next_day_direction': 次日涨跌方向
                - 'price': 股价预测
        返回X, y和相关的数据处理器
        """
        print(f"准备{target_type}的时间序列数据...")

        # 确保日期格式正确
        stock_data['日期'] = pd.to_datetime(stock_data['日期'])

        # 按股票代码和日期排序
        stock_data = stock_data.sort_values(['股票代码', '日期'])

        # 计算每只股票的次日收益率
        def calculate_returns(group):
            group['次日收益率'] = group['当前价格'].shift(-1) / group['当前价格'] - 1
            group['次日涨跌方向'] = (group['次日收益率'] > 0).astype(int)
            return group

        data = stock_data.groupby('股票代码', group_keys=False).apply(calculate_returns)

        # 删除没有目标变量的记录
        data = data.dropna(subset=['次日收益率', '次日涨跌方向'])

        # 选择特征
        features = [
            '技术强度', '涨跌幅', '当前价格',
            '均线多头排列', '成交量放大', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
            '趋势_强势上涨', '趋势_上涨', '趋势_盘整', '趋势_下跌', '趋势_强势下跌',
            '目标价差比', '止损价差比'
        ]

        # 如果有情感数据，添加情感特征
        if '新闻情感得分' in data.columns:
            features.extend(['新闻情感得分', '论坛情感得分', '分析师情感得分', '综合情感得分'])

        # 如果有宏观经济数据，添加宏观特征
        if 'GDP增长率' in data.columns:
            features.extend(['GDP增长率', 'CPI同比', 'PMI', '贷款基准利率', '美元兑人民币汇率'])

        # 如果有行业基本面数据，添加行业特征
        if '行业市盈率' in data.columns:
            features.extend(['行业市盈率', '行业市净率', '行业营收增长率', '行业净利润增长率', '行业资金流向'])

        # 如果有股票基本面数据，添加基本面特征
        if '市值(亿元)' in data.columns:
            features.extend(['市值(亿元)', '市盈率', '市净率', '市销率', '股息率', '营收增长率', '净利润增长率', 'ROE', '毛利率', '净利率'])

        # 检查特征是否存在于数据中
        features = [f for f in features if f in data.columns]

        print(f"使用 {len(features)} 个特征: {features}")

        # 创建特征缩放器
        scaler = MinMaxScaler()

        # 准备时间序列数据
        X_sequences = []
        y_values = []
        stock_codes = []
        dates = []

        # 按股票代码分组处理
        for code, group in data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 缩放特征
            scaled_features = scaler.fit_transform(group[features].values)

            # 创建时间序列序列
            for i in range(len(group) - self.sequence_length):
                # 特征序列
                X_sequences.append(scaled_features[i:i+self.sequence_length])

                # 目标变量
                if target_type == 'next_day_return':
                    y_values.append(group.iloc[i+self.sequence_length]['次日收益率'])
                elif target_type == 'next_day_direction':
                    y_values.append(group.iloc[i+self.sequence_length]['次日涨跌方向'])
                elif target_type == 'price':
                    y_values.append(group.iloc[i+self.sequence_length]['当前价格'])

                # 记录股票代码和日期
                stock_codes.append(code)
                dates.append(group.iloc[i+self.sequence_length]['日期'])

        # 转换为numpy数组
        X = np.array(X_sequences)
        y = np.array(y_values)

        print(f"时间序列数据形状: X={X.shape}, y={y.shape}")

        # 如果目标是价格，需要缩放目标变量
        if target_type == 'price':
            y_scaler = MinMaxScaler()
            y = y_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        else:
            y_scaler = None

        # 创建元数据DataFrame
        metadata = pd.DataFrame({
            '股票代码': stock_codes,
            '日期': dates
        })

        return X, y, metadata, scaler, y_scaler, features

    def build_model(self, model_type='random_forest', target_type='next_day_direction'):
        """
        构建模型
        参数:
            model_type: 模型类型，可选值:
                - 'random_forest': 随机森林
                - 'gradient_boosting': 梯度提升
                - 'neural_network': 神经网络
            target_type: 目标变量类型
        返回模型
        """
        if target_type == 'next_day_direction':
            # 分类模型
            if model_type == 'random_forest':
                model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42
                )
            elif model_type == 'gradient_boosting':
                model = GradientBoostingClassifier(
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=5,
                    random_state=42
                )
            elif model_type == 'neural_network':
                model = MLPClassifier(
                    hidden_layer_sizes=(100, 50),
                    activation='relu',
                    solver='adam',
                    alpha=0.0001,
                    batch_size='auto',
                    learning_rate='adaptive',
                    max_iter=200,
                    random_state=42
                )
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
        else:
            # 回归模型
            if model_type == 'random_forest':
                model = RandomForestRegressor(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42
                )
            elif model_type == 'gradient_boosting':
                model = GradientBoostingRegressor(
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=5,
                    random_state=42
                )
            elif model_type == 'neural_network':
                model = MLPRegressor(
                    hidden_layer_sizes=(100, 50),
                    activation='relu',
                    solver='adam',
                    alpha=0.0001,
                    batch_size='auto',
                    learning_rate='adaptive',
                    max_iter=200,
                    random_state=42
                )
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")

        return model

    def train_model(self, X, y, model_type='random_forest', target_type='next_day_direction'):
        """
        训练模型
        参数:
            X: 特征数据
            y: 目标变量
            model_type: 模型类型，可选值:
                - 'random_forest': 随机森林
                - 'gradient_boosting': 梯度提升
                - 'neural_network': 神经网络
            target_type: 目标变量类型
        返回训练好的模型
        """
        print(f"训练{model_type}模型，目标: {target_type}...")

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        print(f"训练集大小: {X_train.shape}")
        print(f"测试集大小: {X_test.shape}")

        # 构建模型
        model = self.build_model(model_type, target_type)

        # 训练模型
        model.fit(X_train, y_train)

        # 预测
        y_pred = model.predict(X_test)

        # 评估模型
        print("\n模型评估:")

        if target_type == 'next_day_direction':
            # 分类评估
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)

            print(f"准确率: {accuracy:.4f}")
            print(f"精确率: {precision:.4f}")
            print(f"召回率: {recall:.4f}")
            print(f"F1分数: {f1:.4f}")

            # 如果模型支持predict_proba，计算AUC
            if hasattr(model, 'predict_proba'):
                y_prob = model.predict_proba(X_test)[:, 1]
                auc = roc_auc_score(y_test, y_prob)
                print(f"AUC: {auc:.4f}")
        else:
            # 回归评估
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)

            print(f"均方误差: {mse:.4f}")
            print(f"平均绝对误差: {mae:.4f}")
            print(f"R²分数: {r2:.4f}")

        # 保存模型
        model_path = f'dl_models/{model_type}_{target_type}_model.pkl'
        joblib.dump(model, model_path)
        print(f"模型已保存至 {model_path}")

        return model

    def predict(self, X, model_path, y_scaler=None):
        """
        使用模型进行预测
        参数:
            X: 特征数据
            model_path: 模型路径
            y_scaler: 目标变量缩放器（如果有）
        返回预测结果
        """
        print(f"使用模型 {model_path} 进行预测...")

        # 加载模型
        model = joblib.load(model_path)

        # 进行预测
        if hasattr(model, 'predict_proba'):
            predictions = model.predict_proba(X)[:, 1]
        else:
            predictions = model.predict(X)

        # 如果有目标变量缩放器，反向转换预测结果
        if y_scaler is not None:
            predictions = y_scaler.inverse_transform(predictions.reshape(-1, 1)).flatten()

        return predictions

    def train_all_models(self, stock_data):
        """
        训练所有模型
        参数:
            stock_data: 股票历史数据DataFrame
        """
        print("训练所有模型...")

        # 准备次日涨跌方向预测的数据
        X_dir, y_dir, meta_dir, scaler_dir, _, features_dir = self.prepare_time_series_data(
            stock_data, target_type='next_day_direction'
        )

        # 保存数据处理器
        joblib.dump(scaler_dir, 'dl_models/direction_scaler.pkl')
        joblib.dump(features_dir, 'dl_models/direction_features.pkl')

        # 训练随机森林模型
        rf_dir_model = self.train_model(X_dir, y_dir, model_type='random_forest', target_type='next_day_direction')

        # 训练梯度提升模型
        gb_dir_model = self.train_model(X_dir, y_dir, model_type='gradient_boosting', target_type='next_day_direction')

        # 训练神经网络模型
        nn_dir_model = self.train_model(X_dir, y_dir, model_type='neural_network', target_type='next_day_direction')

        # 准备次日收益率预测的数据
        X_ret, y_ret, meta_ret, scaler_ret, _, features_ret = self.prepare_time_series_data(
            stock_data, target_type='next_day_return'
        )

        # 保存数据处理器
        joblib.dump(scaler_ret, 'dl_models/return_scaler.pkl')
        joblib.dump(features_ret, 'dl_models/return_features.pkl')

        # 训练随机森林模型
        rf_ret_model = self.train_model(X_ret, y_ret, model_type='random_forest', target_type='next_day_return')

        # 训练梯度提升模型
        gb_ret_model = self.train_model(X_ret, y_ret, model_type='gradient_boosting', target_type='next_day_return')

        # 训练神经网络模型
        nn_ret_model = self.train_model(X_ret, y_ret, model_type='neural_network', target_type='next_day_return')

        print("所有模型训练完成!")

    def predict_with_ensemble(self, stock_data):
        """
        使用集成模型进行预测
        参数:
            stock_data: 股票数据
        返回预测结果DataFrame
        """
        print("使用集成模型进行预测...")

        # 检查模型文件是否存在
        model_files = [
            'dl_models/random_forest_next_day_direction_model.pkl',
            'dl_models/gradient_boosting_next_day_direction_model.pkl',
            'dl_models/neural_network_next_day_direction_model.pkl',
            'dl_models/random_forest_next_day_return_model.pkl',
            'dl_models/gradient_boosting_next_day_return_model.pkl',
            'dl_models/neural_network_next_day_return_model.pkl'
        ]

        for model_file in model_files:
            if not os.path.exists(model_file):
                print(f"模型文件 {model_file} 不存在，请先训练模型")
                return pd.DataFrame()

        # 加载特征列表
        direction_features = joblib.load('dl_models/direction_features.pkl')
        return_features = joblib.load('dl_models/return_features.pkl')

        # 加载缩放器
        direction_scaler = joblib.load('dl_models/direction_scaler.pkl')
        return_scaler = joblib.load('dl_models/return_scaler.pkl')

        # 准备预测数据
        predictions = []

        # 获取最新日期的股票数据
        latest_date = stock_data['日期'].max()
        latest_stock_data = stock_data[stock_data['日期'] == latest_date]

        for _, row in latest_stock_data.iterrows():
            # 提取特征
            direction_X = np.array([row[direction_features].values])
            return_X = np.array([row[return_features].values])

            # 缩放特征
            scaled_direction_X = direction_scaler.transform(direction_X)
            scaled_return_X = return_scaler.transform(return_X)

            # 加载模型
            rf_dir_model = joblib.load('dl_models/random_forest_next_day_direction_model.pkl')
            gb_dir_model = joblib.load('dl_models/gradient_boosting_next_day_direction_model.pkl')
            nn_dir_model = joblib.load('dl_models/neural_network_next_day_direction_model.pkl')

            rf_ret_model = joblib.load('dl_models/random_forest_next_day_return_model.pkl')
            gb_ret_model = joblib.load('dl_models/gradient_boosting_next_day_return_model.pkl')
            nn_ret_model = joblib.load('dl_models/neural_network_next_day_return_model.pkl')

            # 预测
            rf_dir_pred = rf_dir_model.predict_proba(scaled_direction_X)[0, 1]
            gb_dir_pred = gb_dir_model.predict_proba(scaled_direction_X)[0, 1]
            nn_dir_pred = nn_dir_model.predict_proba(scaled_direction_X)[0, 1]

            rf_ret_pred = rf_ret_model.predict(scaled_return_X)[0]
            gb_ret_pred = gb_ret_model.predict(scaled_return_X)[0]
            nn_ret_pred = nn_ret_model.predict(scaled_return_X)[0]

            # 集成预测结果
            ensemble_dir_pred = (rf_dir_pred + gb_dir_pred + nn_dir_pred) / 3
            ensemble_ret_pred = (rf_ret_pred + gb_ret_pred + nn_ret_pred) / 3

            # 添加到预测结果
            predictions.append({
                '股票代码': row['股票代码'],
                '股票名称': row['股票名称'],
                '当前价格': row['当前价格'],
                '技术强度': row['技术强度'],
                'RF涨跌概率': rf_dir_pred,
                'GB涨跌概率': gb_dir_pred,
                'NN涨跌概率': nn_dir_pred,
                '集成涨跌概率': ensemble_dir_pred,
                'RF收益率预测': rf_ret_pred,
                'GB收益率预测': gb_ret_pred,
                'NN收益率预测': nn_ret_pred,
                '集成收益率预测': ensemble_ret_pred,
                '预测涨跌': '上涨' if ensemble_dir_pred > 0.5 else '下跌',
                '预测信号强度': abs(ensemble_dir_pred - 0.5) * 2  # 0到1之间，越接近1信号越强
            })

        # 转换为DataFrame
        predictions_df = pd.DataFrame(predictions)

        # 按预测信号强度排序
        if len(predictions_df) > 0:
            predictions_df = predictions_df.sort_values('预测信号强度', ascending=False)

        return predictions_df

if __name__ == "__main__":
    # 测试深度学习模型模块
    dl_models = DeepLearningModels()

    # 加载股票数据
    try:
        stock_data = pd.read_excel('股票明细.xlsx')
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")

        # 训练所有模型
        dl_models.train_all_models(stock_data)

        # 使用集成模型进行预测
        predictions = dl_models.predict_with_ensemble(stock_data)
        print("\n预测结果:")
        print(predictions.head())
    except Exception as e:
        print(f"测试失败: {e}")
