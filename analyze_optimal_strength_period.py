import pandas as pd
import numpy as np
import os
import joblib
from datetime import datetime, timedelta
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

def analyze_optimal_strength_period():
    """
    分析技术强度累积的最优天数，结合买入时上涨的条件
    """
    print("开始分析技术强度累积的最优天数（结合买入时上涨条件）...")
    
    # 创建结果目录
    if not os.path.exists('optimal_strength_results'):
        os.makedirs('optimal_strength_results')
    
    # 加载股票数据
    print("\n加载股票数据...")
    try:
        stock_data = pd.read_excel('股票明细.xlsx')
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
        
        # 转换日期格式
        if isinstance(stock_data['日期'].iloc[0], str):
            stock_data['日期'] = pd.to_datetime(stock_data['日期'])
        
        # 获取所有日期并排序
        all_dates = sorted(stock_data['日期'].unique())
        print(f"数据集中的日期范围: {all_dates[0]} 到 {all_dates[-1]}")
        
        # 按股票代码和日期排序
        stock_data = stock_data.sort_values(['股票代码', '日期'])
        
        # 数据预处理
        print("\n数据预处理...")
        
        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')
            
            # 计算连续技术强度天数（连续多少天为100）
            consecutive_days = []
            current_count = 0
            
            for strength in group['技术强度'].values:
                if strength == 100:
                    current_count += 1
                else:
                    current_count = 0
                consecutive_days.append(current_count)
            
            # 更新原始数据
            stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days
            
            # 计算技术强度累积值（1-10天）
            for period in range(1, 11):
                # 计算连续N天技术强度求和
                cumulative_strength = group['技术强度'].copy()
                for i in range(1, period):
                    cumulative_strength += group['技术强度'].shift(i).fillna(0)
                
                # 更新原始数据
                stock_data.loc[group.index, f'连续技术强度{period}天数'] = cumulative_strength
            
            # 计算趋势特征
            stock_data.loc[group.index, '技术强度趋势'] = (
                (group['技术强度'] > group['技术强度'].shift(1)) & 
                (group['技术强度'].shift(1) > group['技术强度'].shift(2))
            ).astype(int)
            
            stock_data.loc[group.index, '价格趋势'] = (
                (group['当前价格'] > group['当前价格'].shift(1)) & 
                (group['当前价格'].shift(1) > group['当前价格'].shift(2))
            ).astype(int)
            
            if '涨跌幅' in group.columns:
                stock_data.loc[group.index, '涨跌幅趋势'] = (
                    (group['涨跌幅'] > group['涨跌幅'].shift(1)) & 
                    (group['涨跌幅'].shift(1) > group['涨跌幅'].shift(2))
                ).astype(int)
            
            # 计算次日涨跌方向和收益率
            group['次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)
            group['次日收益率'] = (group['当前价格'].shift(-1) / group['当前价格'] - 1) * 100
            
            # 计算后日涨跌方向和收益率（买入后的第二天，即卖出日）
            group['后日涨跌方向'] = (group['当前价格'].shift(-2) > group['当前价格'].shift(-1)).astype(int)
            group['后日收益率'] = (group['当前价格'].shift(-2) / group['当前价格'].shift(-1) - 1) * 100
            
            # 计算两日收益率（买入后持有两天的总收益）
            group['两日收益率'] = (group['当前价格'].shift(-2) / group['当前价格'] - 1) * 100
            
            # 计算是否盈利（两日收益率为正）
            group['是否盈利'] = (group['两日收益率'] > 0).astype(int)
            
            # 更新原始数据
            stock_data.loc[group.index, '次日涨跌方向'] = group['次日涨跌方向']
            stock_data.loc[group.index, '次日收益率'] = group['次日收益率']
            stock_data.loc[group.index, '后日涨跌方向'] = group['后日涨跌方向']
            stock_data.loc[group.index, '后日收益率'] = group['后日收益率']
            stock_data.loc[group.index, '两日收益率'] = group['两日收益率']
            stock_data.loc[group.index, '是否盈利'] = group['是否盈利']
        
        # 处理技术指标特征
        if '技术指标' in stock_data.columns:
            # 提取常见的技术指标关键词
            tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破', 
                              '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']
            
            # 为每个技术指标创建一个新列
            for indicator in tech_indicators:
                col_name = f'技术指标_{indicator}'
                # 检查技术指标文本中是否包含该关键词
                stock_data[col_name] = stock_data['技术指标'].str.contains(indicator, na=False).astype(int)
        
        # 删除没有完整数据的记录
        stock_data = stock_data.dropna(subset=['次日涨跌方向', '次日收益率', '后日涨跌方向', '后日收益率', '两日收益率', '是否盈利'])
        
        print(f"处理后的数据集大小: {len(stock_data)} 条记录")
        
        # 创建结果DataFrame
        results = pd.DataFrame(columns=[
            '累积天数', '所有股票胜率', '买入时上涨股票胜率', '买入时下跌股票胜率',
            '所有股票平均收益率', '买入时上涨股票平均收益率', '买入时下跌股票平均收益率'
        ])
        
        # 基础特征（不包括技术强度累积值）
        base_features = ['技术强度', '技术强度趋势', '价格趋势', '涨跌幅趋势', '涨跌幅']
        
        # 添加技术指标特征
        if '技术指标' in stock_data.columns:
            for indicator in tech_indicators:
                base_features.append(f'技术指标_{indicator}')
        
        # 对每个累积天数进行评估
        for period in range(1, 11):
            print(f"\n评估累积天数 {period}...")
            
            # 特征集合：基础特征 + 当前周期的技术强度累积值
            features = base_features + [f'连续技术强度{period}天数']
            
            # 创建回测结果DataFrame
            backtest_results = pd.DataFrame(columns=[
                '日期', '推荐股票数', '买入时上涨股票数', '买入时下跌股票数',
                '所有推荐股票胜率', '买入时上涨股票胜率', '买入时下跌股票胜率',
                '所有推荐股票平均收益率', '买入时上涨股票平均收益率', '买入时下跌股票平均收益率'
            ])
            
            # 回测每个日期
            for i in range(len(all_dates) - 2):  # 需要有后两天的数据
                current_date = all_dates[i]
                
                # 获取当前日期的数据
                current_data = stock_data[stock_data['日期'] == current_date]
                
                # 准备训练数据（使用当前日期之前的所有数据）
                train_data = stock_data[stock_data['日期'] < current_date]
                
                if len(train_data) < 1000:  # 确保有足够的训练数据
                    continue
                
                # 特征和目标变量
                X_train = train_data[features]
                y_train = train_data['是否盈利']
                
                # 处理缺失值
                valid_indices = ~X_train.isnull().any(axis=1)
                X_train = X_train[valid_indices]
                y_train = y_train[valid_indices]
                
                # 标准化特征
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                
                # 训练梯度提升模型
                gb_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
                gb_model.fit(X_train_scaled, y_train)
                
                # 准备预测数据
                X_pred = current_data[features]
                
                # 处理预测数据中的缺失值
                valid_pred_indices = ~X_pred.isnull().any(axis=1)
                X_pred = X_pred[valid_pred_indices]
                current_data_filtered = current_data.loc[valid_pred_indices.index[valid_pred_indices]]
                
                if len(X_pred) == 0:
                    continue
                
                # 标准化预测数据
                X_pred_scaled = scaler.transform(X_pred)
                
                # 预测盈利概率
                pred_proba = gb_model.predict_proba(X_pred_scaled)[:, 1]
                
                # 创建预测结果DataFrame
                predictions = pd.DataFrame({
                    '股票代码': current_data_filtered['股票代码'],
                    '股票名称': current_data_filtered['股票名称'],
                    '涨跌幅': current_data_filtered['涨跌幅'],
                    '技术强度': current_data_filtered['技术强度'],
                    f'连续技术强度{period}天数': current_data_filtered[f'连续技术强度{period}天数'],
                    '预测盈利概率': pred_proba,
                    '次日涨跌方向': current_data_filtered['次日涨跌方向'],
                    '次日收益率': current_data_filtered['次日收益率'],
                    '实际是否盈利': current_data_filtered['是否盈利'],
                    '两日收益率': current_data_filtered['两日收益率']
                })
                
                # 按预测盈利概率降序排序
                predictions = predictions.sort_values('预测盈利概率', ascending=False)
                
                # 选择预测盈利概率最高的前10%股票作为推荐
                top_percent = 0.1
                top_n = max(int(len(predictions) * top_percent), 10)  # 至少10只股票
                recommended_stocks = predictions.head(top_n)
                
                # 分析推荐股票中买入时上涨和下跌的股票
                up_stocks = recommended_stocks[recommended_stocks['次日涨跌方向'] == 1]
                down_stocks = recommended_stocks[recommended_stocks['次日涨跌方向'] == 0]
                
                # 计算各类股票的胜率和平均收益率
                total_count = len(recommended_stocks)
                up_count = len(up_stocks)
                down_count = len(down_stocks)
                
                if total_count == 0 or (up_count == 0 and down_count == 0):
                    continue
                
                total_win_count = sum(recommended_stocks['实际是否盈利'])
                up_win_count = sum(up_stocks['实际是否盈利']) if up_count > 0 else 0
                down_win_count = sum(down_stocks['实际是否盈利']) if down_count > 0 else 0
                
                total_win_rate = total_win_count / total_count if total_count > 0 else 0
                up_win_rate = up_win_count / up_count if up_count > 0 else 0
                down_win_rate = down_win_count / down_count if down_count > 0 else 0
                
                total_avg_return = recommended_stocks['两日收益率'].mean()
                up_avg_return = up_stocks['两日收益率'].mean() if up_count > 0 else 0
                down_avg_return = down_stocks['两日收益率'].mean() if down_count > 0 else 0
                
                # 添加到回测结果
                new_row = pd.DataFrame({
                    '日期': [current_date],
                    '推荐股票数': [total_count],
                    '买入时上涨股票数': [up_count],
                    '买入时下跌股票数': [down_count],
                    '所有推荐股票胜率': [total_win_rate],
                    '买入时上涨股票胜率': [up_win_rate],
                    '买入时下跌股票胜率': [down_win_rate],
                    '所有推荐股票平均收益率': [total_avg_return],
                    '买入时上涨股票平均收益率': [up_avg_return],
                    '买入时下跌股票平均收益率': [down_avg_return]
                })
                backtest_results = pd.concat([backtest_results, new_row], ignore_index=True)
            
            # 计算整体统计
            if len(backtest_results) > 0:
                total_recommended = backtest_results['推荐股票数'].sum()
                total_up = backtest_results['买入时上涨股票数'].sum()
                total_down = backtest_results['买入时下跌股票数'].sum()
                
                # 计算加权平均胜率
                overall_win_rate = (backtest_results['推荐股票数'] * backtest_results['所有推荐股票胜率']).sum() / total_recommended
                up_win_rate = (backtest_results['买入时上涨股票数'] * backtest_results['买入时上涨股票胜率']).sum() / total_up if total_up > 0 else 0
                down_win_rate = (backtest_results['买入时下跌股票数'] * backtest_results['买入时下跌股票胜率']).sum() / total_down if total_down > 0 else 0
                
                # 计算平均收益率
                overall_avg_return = backtest_results['所有推荐股票平均收益率'].mean()
                up_avg_return = backtest_results.loc[backtest_results['买入时上涨股票数'] > 0, '买入时上涨股票平均收益率'].mean()
                down_avg_return = backtest_results.loc[backtest_results['买入时下跌股票数'] > 0, '买入时下跌股票平均收益率'].mean()
                
                print(f"累积天数 {period} 的整体统计:")
                print(f"总推荐股票数: {total_recommended}")
                print(f"买入时上涨股票数: {total_up} ({total_up/total_recommended*100:.2f}%)")
                print(f"买入时下跌股票数: {total_down} ({total_down/total_recommended*100:.2f}%)")
                print(f"所有推荐股票整体胜率: {overall_win_rate:.4f} ({overall_win_rate*100:.2f}%)")
                print(f"买入时上涨股票整体胜率: {up_win_rate:.4f} ({up_win_rate*100:.2f}%)")
                print(f"买入时下跌股票整体胜率: {down_win_rate:.4f} ({down_win_rate*100:.2f}%)")
                print(f"所有推荐股票平均收益率: {overall_avg_return:.4f}%")
                print(f"买入时上涨股票平均收益率: {up_avg_return:.4f}%")
                print(f"买入时下跌股票平均收益率: {down_avg_return:.4f}%")
                
                # 添加到结果
                new_row = pd.DataFrame({
                    '累积天数': [period],
                    '所有股票胜率': [overall_win_rate],
                    '买入时上涨股票胜率': [up_win_rate],
                    '买入时下跌股票胜率': [down_win_rate],
                    '所有股票平均收益率': [overall_avg_return],
                    '买入时上涨股票平均收益率': [up_avg_return],
                    '买入时下跌股票平均收益率': [down_avg_return]
                })
                results = pd.concat([results, new_row], ignore_index=True)
        
        # 保存结果
        results.to_excel('optimal_strength_results/累积天数评估.xlsx', index=False)
        
        # 绘制胜率比较图表
        plt.figure(figsize=(12, 6))
        plt.plot(results['累积天数'], results['所有股票胜率'], marker='o', linestyle='-', label='所有推荐股票')
        plt.plot(results['累积天数'], results['买入时上涨股票胜率'], marker='x', linestyle='--', color='green', label='买入时上涨股票')
        plt.plot(results['累积天数'], results['买入时下跌股票胜率'], marker='s', linestyle='-.', color='red', label='买入时下跌股票')
        plt.title('不同累积天数的胜率比较')
        plt.xlabel('技术强度累积天数')
        plt.ylabel('胜率')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.xticks(results['累积天数'])
        plt.tight_layout()
        plt.savefig('optimal_strength_results/胜率比较.png')
        
        # 绘制收益率比较图表
        plt.figure(figsize=(12, 6))
        plt.plot(results['累积天数'], results['所有股票平均收益率'], marker='o', linestyle='-', label='所有推荐股票')
        plt.plot(results['累积天数'], results['买入时上涨股票平均收益率'], marker='x', linestyle='--', color='green', label='买入时上涨股票')
        plt.plot(results['累积天数'], results['买入时下跌股票平均收益率'], marker='s', linestyle='-.', color='red', label='买入时下跌股票')
        plt.title('不同累积天数的平均收益率比较')
        plt.xlabel('技术强度累积天数')
        plt.ylabel('平均收益率 (%)')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.xticks(results['累积天数'])
        plt.tight_layout()
        plt.savefig('optimal_strength_results/收益率比较.png')
        
        # 找出最优累积天数
        best_overall_win_rate = results.loc[results['所有股票胜率'].idxmax(), '累积天数']
        best_up_win_rate = results.loc[results['买入时上涨股票胜率'].idxmax(), '累积天数']
        best_overall_return = results.loc[results['所有股票平均收益率'].idxmax(), '累积天数']
        best_up_return = results.loc[results['买入时上涨股票平均收益率'].idxmax(), '累积天数']
        
        print("\n最优累积天数分析结果:")
        print(f"所有股票胜率最高的累积天数: {best_overall_win_rate}天")
        print(f"买入时上涨股票胜率最高的累积天数: {best_up_win_rate}天")
        print(f"所有股票平均收益率最高的累积天数: {best_overall_return}天")
        print(f"买入时上涨股票平均收益率最高的累积天数: {best_up_return}天")
        
        # 综合评分（胜率 * 0.6 + 收益率 * 0.4）
        results['所有股票综合评分'] = results['所有股票胜率'] * 0.6 + results['所有股票平均收益率'] / 10 * 0.4
        results['买入时上涨股票综合评分'] = results['买入时上涨股票胜率'] * 0.6 + results['买入时上涨股票平均收益率'] / 10 * 0.4
        
        best_overall_score = results.loc[results['所有股票综合评分'].idxmax(), '累积天数']
        best_up_score = results.loc[results['买入时上涨股票综合评分'].idxmax(), '累积天数']
        
        print(f"所有股票综合评分最高的累积天数: {best_overall_score}天")
        print(f"买入时上涨股票综合评分最高的累积天数: {best_up_score}天")
        
        # 保存更新后的结果
        results.to_excel('optimal_strength_results/累积天数评估_带评分.xlsx', index=False)
        
        print("\n分析完成！结果已保存至 optimal_strength_results 目录")
        
        return results
    
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    analyze_optimal_strength_period()
