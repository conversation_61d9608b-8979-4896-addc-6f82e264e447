import pandas as pd

print("开始验证...")

# 读取原始数据
df = pd.read_excel('E:/机器学习/complete_excel_results/股票明细_完整.xlsx')
sample_data = df[df['日期'] == '2025-05-15'].head(10)

print(f"原始数据行数: {len(sample_data)}")

# 检查原始数据
sample = sample_data.iloc[0]
print(f"原始数据示例:")
print(f"  股票代码: {sample['股票代码']}")
print(f"  技术指标特征: {sample['技术指标特征']} (类型: {type(sample['技术指标特征'])})")
print(f"  趋势组合: {sample['趋势组合']} (类型: {type(sample['趋势组合'])})")
print(f"  连续技术强度3天数: {sample['连续技术强度3天数']}")

print("验证完成")
