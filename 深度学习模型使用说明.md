# 深度学习股票高胜率策略分析工具

## 简介

这是一个使用深度学习技术分析股票高胜率策略的工具，可以帮助您找到具有高胜率的股票交易策略。该工具基于深度神经网络算法，通过分析历史数据，找出能够预测股票涨跌的规律，并生成股票推荐列表。

## 功能

1. **训练深度学习模型**：使用历史股票数据训练深度神经网络模型
2. **回测深度学习高胜率策略**：在历史数据上回测深度学习高胜率策略的表现
3. **生成股票推荐**：根据深度学习高胜率策略生成股票推荐列表

## 深度学习高胜率策略

本工具使用的深度学习高胜率策略基于以下条件：

1. 预测盈利概率>95%
2. 技术强度=85
3. 连续技术强度5天数≥440且≤445
4. 看涨技术指标数量≥4
5. 技术指标_均线多头排列=1
6. 技术指标_MACD金叉=1
7. 历史胜率>0.6
8. 排除特定股票（指南针、西昌电力、尖峰集团）

## 深度学习模型特点

1. **更多特征**：除了基本特征外，还添加了以下特征：
   - 技术强度变化率
   - 连续技术强度变化率
   - 看涨技术指标占比
   - 技术指标组合特征（均线多头_MACD金叉、RSI反弹_KDJ金叉）
   - 历史胜率
   - 星期几
   - 月份

2. **深度神经网络**：使用多层神经网络，包括：
   - 输入层：128个神经元
   - 隐藏层1：64个神经元
   - 隐藏层2：32个神经元
   - 输出层：1个神经元（预测盈利概率）

3. **正则化技术**：使用以下技术防止过拟合：
   - Batch Normalization
   - Dropout
   - Early Stopping

4. **优化器**：使用Adam优化器，自适应学习率

## 使用方法

### 方法一：使用批处理文件

1. 双击`run_deep_learning.bat`文件
2. 选择要执行的操作：
   - 1: 训练深度学习模型
   - 2: 回测深度学习高胜率策略
   - 3: 生成股票推荐
   - 4: 全部执行
3. 根据提示输入相关参数

### 方法二：使用命令行

1. 训练深度学习模型：
   ```
   python run_deep_learning.py --train --data_file 股票明细.xlsx
   ```

2. 回测深度学习高胜率策略：
   ```
   python run_deep_learning.py --backtest --data_file 股票明细.xlsx --start_date 2025-04-25 --end_date 2025-05-08 --output 深度学习高胜率策略回测结果.txt
   ```

3. 生成股票推荐：
   ```
   python run_deep_learning.py --recommend --data_file 股票明细.xlsx --date 2025-05-09 --output 深度学习高胜率策略推荐股票.xlsx
   ```

## 文件说明

- `run_deep_learning.py`：主程序文件
- `deep_learning_model.py`：深度学习模型实现
- `deep_learning_strategy.py`：深度学习高胜率策略实现
- `run_deep_learning.bat`：批处理文件，方便用户运行分析
- `股票明细.xlsx`：股票数据
- `models/`：保存训练好的模型
- `backtest_results/`：保存回测结果

## 深度学习模型与传统机器学习模型的比较

| 特点 | 深度学习模型 | 传统机器学习模型 |
| --- | --- | --- |
| 特征数量 | 更多（19个） | 较少（11个） |
| 模型复杂度 | 高 | 中等 |
| 训练时间 | 较长 | 较短 |
| 预测准确率 | 更高 | 较高 |
| 过拟合风险 | 中等（有正则化） | 中等 |
| 解释性 | 较低 | 较高 |
| 适用场景 | 大数据集 | 中小数据集 |

## 注意事项

1. 本工具仅供学习和研究使用，不构成投资建议
2. 过去的表现不代表未来的收益，投资有风险，请谨慎操作
3. 建议在实际交易中结合其他分析方法进行决策
4. 如果您使用自己的数据文件，请确保其格式与示例数据文件相同
5. 深度学习模型训练需要较长时间，请耐心等待
6. 如果程序运行出错，请检查数据文件格式是否正确

## 交易建议

1. 在开盘时买入推荐的股票
2. 在下一个交易日开盘时卖出
3. 分散投资，不要将资金集中在少数几只股票上
4. 设置止损，控制风险

## 改进方向

1. 添加更多特征，如成交量、市值、行业等
2. 尝试不同的深度学习架构，如LSTM、GRU等
3. 使用集成学习，结合多个模型的预测结果
4. 添加更多的正则化技术，如L1/L2正则化
5. 使用交叉验证，提高模型的泛化能力
