import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from enhanced_stock_selector import EnhancedStockSelector

class TradingStrategy:
    """
    完整的交易策略类，结合技术强度变化监控和爬虫数据
    """
    
    def __init__(self):
        """初始化交易策略"""
        # 创建结果目录
        if not os.path.exists('trading'):
            os.makedirs('trading')
        
        # 持仓文件路径
        self.holdings_file = 'trading/holdings.xlsx'
        
        # 交易记录文件路径
        self.transactions_file = 'trading/transactions.xlsx'
        
        # 初始化持仓和交易记录
        self.initialize_files()
    
    def initialize_files(self):
        """初始化持仓和交易记录文件"""
        # 初始化持仓文件
        if not os.path.exists(self.holdings_file):
            holdings = pd.DataFrame(columns=[
                '股票代码', '股票名称', '买入日期', '买入价格', '持仓数量', 
                '当前价格', '当前市值', '盈亏金额', '盈亏比例', '技术强度'
            ])
            holdings.to_excel(self.holdings_file, index=False)
            print(f"已创建持仓文件: {self.holdings_file}")
        
        # 初始化交易记录文件
        if not os.path.exists(self.transactions_file):
            transactions = pd.DataFrame(columns=[
                '交易日期', '股票代码', '股票名称', '交易类型', '交易价格', 
                '交易数量', '交易金额', '技术强度', '持有天数', '盈亏金额', '盈亏比例'
            ])
            transactions.to_excel(self.transactions_file, index=False)
            print(f"已创建交易记录文件: {self.transactions_file}")
    
    def load_holdings(self):
        """加载当前持仓"""
        try:
            holdings = pd.read_excel(self.holdings_file)
            print(f"当前持仓 {len(holdings)} 只股票")
            return holdings
        except Exception as e:
            print(f"加载持仓失败: {e}")
            return pd.DataFrame(columns=[
                '股票代码', '股票名称', '买入日期', '买入价格', '持仓数量', 
                '当前价格', '当前市值', '盈亏金额', '盈亏比例', '技术强度'
            ])
    
    def load_transactions(self):
        """加载交易记录"""
        try:
            transactions = pd.read_excel(self.transactions_file)
            print(f"历史交易记录 {len(transactions)} 条")
            return transactions
        except Exception as e:
            print(f"加载交易记录失败: {e}")
            return pd.DataFrame(columns=[
                '交易日期', '股票代码', '股票名称', '交易类型', '交易价格', 
                '交易数量', '交易金额', '技术强度', '持有天数', '盈亏金额', '盈亏比例'
            ])
    
    def load_stock_data(self):
        """加载最新股票数据"""
        print("加载最新股票数据...")
        try:
            data = pd.read_excel('股票明细.xlsx')
            data['日期'] = pd.to_datetime(data['日期'])
            latest_date = data['日期'].max()
            latest_data = data[data['日期'] == latest_date]
            print(f"最新日期: {latest_date.date()}, 股票数量: {len(latest_data)}")
            return latest_data
        except Exception as e:
            print(f"加载股票数据失败: {e}")
            return pd.DataFrame()
    
    def update_holdings(self, latest_data):
        """
        更新持仓信息
        参数:
            latest_data: 最新股票数据
        返回更新后的持仓DataFrame
        """
        print("更新持仓信息...")
        holdings = self.load_holdings()
        
        if len(holdings) == 0:
            print("当前无持仓")
            return holdings
        
        # 标准化股票代码格式
        latest_data['标准代码'] = latest_data['股票代码']
        
        # 更新持仓信息
        for i, row in holdings.iterrows():
            stock_code = row['股票代码']
            stock_data = latest_data[latest_data['标准代码'] == stock_code]
            
            if len(stock_data) > 0:
                # 更新当前价格和技术强度
                current_price = stock_data.iloc[0]['当前价格']
                tech_strength = stock_data.iloc[0]['技术强度']
                
                # 计算市值和盈亏
                current_value = current_price * row['持仓数量']
                profit_amount = current_value - row['买入价格'] * row['持仓数量']
                profit_ratio = profit_amount / (row['买入价格'] * row['持仓数量'])
                
                # 更新持仓信息
                holdings.at[i, '当前价格'] = current_price
                holdings.at[i, '当前市值'] = current_value
                holdings.at[i, '盈亏金额'] = profit_amount
                holdings.at[i, '盈亏比例'] = profit_ratio
                holdings.at[i, '技术强度'] = tech_strength
            else:
                print(f"警告: 未找到股票 {stock_code} 的最新数据")
        
        # 保存更新后的持仓
        holdings.to_excel(self.holdings_file, index=False)
        print("持仓信息已更新")
        
        return holdings
    
    def identify_sell_signals(self, holdings):
        """
        识别卖出信号
        参数:
            holdings: 持仓DataFrame
        返回应当卖出的股票DataFrame
        """
        print("识别卖出信号...")
        
        # 卖出条件1: 技术强度降至71或以下
        condition1 = holdings['技术强度'] <= 71
        
        # 卖出条件2: 持有超过5个交易日
        holdings['买入日期'] = pd.to_datetime(holdings['买入日期'])
        today = datetime.now().date()
        holdings['持有天数'] = holdings['买入日期'].apply(lambda x: (today - x.date()).days)
        condition2 = holdings['持有天数'] >= 5
        
        # 卖出条件3: 盈利超过10%
        condition3 = holdings['盈亏比例'] >= 0.1
        
        # 卖出条件4: 亏损超过5%
        condition4 = holdings['盈亏比例'] <= -0.05
        
        # 综合卖出条件
        sell_condition = condition1 | condition2 | condition3 | condition4
        
        # 筛选应当卖出的股票
        stocks_to_sell = holdings[sell_condition].copy()
        
        if len(stocks_to_sell) > 0:
            print(f"发现 {len(stocks_to_sell)} 只股票满足卖出条件:")
            for _, row in stocks_to_sell.iterrows():
                sell_reasons = []
                if row['技术强度'] <= 71:
                    sell_reasons.append("技术强度降低")
                if row['持有天数'] >= 5:
                    sell_reasons.append("持有超过5天")
                if row['盈亏比例'] >= 0.1:
                    sell_reasons.append("盈利超过10%")
                if row['盈亏比例'] <= -0.05:
                    sell_reasons.append("亏损超过5%")
                
                print(f"- {row['股票名称']}({row['股票代码']}): 卖出原因={', '.join(sell_reasons)}, 持有天数={row['持有天数']}, 盈亏比例={row['盈亏比例']*100:.2f}%, 技术强度={row['技术强度']}")
        else:
            print("没有股票满足卖出条件")
        
        return stocks_to_sell
    
    def execute_sell_orders(self, stocks_to_sell):
        """
        执行卖出订单
        参数:
            stocks_to_sell: 应当卖出的股票DataFrame
        """
        if len(stocks_to_sell) == 0:
            return
        
        print("执行卖出订单...")
        
        # 加载当前持仓和交易记录
        holdings = self.load_holdings()
        transactions = self.load_transactions()
        
        # 记录卖出交易
        for _, row in stocks_to_sell.iterrows():
            transaction = {
                '交易日期': datetime.now().strftime('%Y-%m-%d'),
                '股票代码': row['股票代码'],
                '股票名称': row['股票名称'],
                '交易类型': '卖出',
                '交易价格': row['当前价格'],
                '交易数量': row['持仓数量'],
                '交易金额': row['当前市值'],
                '技术强度': row['技术强度'],
                '持有天数': row['持有天数'],
                '盈亏金额': row['盈亏金额'],
                '盈亏比例': row['盈亏比例']
            }
            transactions = pd.concat([transactions, pd.DataFrame([transaction])], ignore_index=True)
            
            print(f"卖出 {row['股票名称']}({row['股票代码']}): 价格={row['当前价格']}, 数量={row['持仓数量']}, 金额={row['当前市值']:.2f}, 盈亏={row['盈亏金额']:.2f}({row['盈亏比例']*100:.2f}%)")
        
        # 更新持仓
        holdings = holdings[~holdings['股票代码'].isin(stocks_to_sell['股票代码'])]
        
        # 保存更新后的持仓和交易记录
        holdings.to_excel(self.holdings_file, index=False)
        transactions.to_excel(self.transactions_file, index=False)
        
        print(f"已完成 {len(stocks_to_sell)} 只股票的卖出交易")
    
    def identify_buy_signals(self, latest_data, holdings):
        """
        识别买入信号
        参数:
            latest_data: 最新股票数据
            holdings: 当前持仓
        返回应当买入的股票DataFrame
        """
        print("识别买入信号...")
        
        # 运行增强版股票选择器
        selector = EnhancedStockSelector()
        recommended_stocks = selector.run()
        
        if len(recommended_stocks) == 0:
            print("没有推荐的买入股票")
            return pd.DataFrame()
        
        # 排除已持仓的股票
        if len(holdings) > 0:
            recommended_stocks = recommended_stocks[~recommended_stocks['股票代码'].isin(holdings['股票代码'])]
        
        # 限制买入数量
        max_positions = 10  # 最大持仓数量
        available_slots = max_positions - len(holdings)
        
        if available_slots <= 0:
            print("当前持仓已满，无法买入新股票")
            return pd.DataFrame()
        
        stocks_to_buy = recommended_stocks.head(min(available_slots, len(recommended_stocks)))
        
        if len(stocks_to_buy) > 0:
            print(f"推荐买入 {len(stocks_to_buy)} 只股票:")
            for _, row in stocks_to_buy.iterrows():
                print(f"- {row['股票名称']}({row['股票代码']}): 价格={row['当前价格']}, 技术强度={row['技术强度']}, 综合得分={row.get('综合得分', 'N/A')}")
        else:
            print("没有推荐的买入股票")
        
        return stocks_to_buy
    
    def execute_buy_orders(self, stocks_to_buy, investment_per_stock=100000):
        """
        执行买入订单
        参数:
            stocks_to_buy: 应当买入的股票DataFrame
            investment_per_stock: 每只股票的投资金额
        """
        if len(stocks_to_buy) == 0:
            return
        
        print("执行买入订单...")
        
        # 加载当前持仓和交易记录
        holdings = self.load_holdings()
        transactions = self.load_transactions()
        
        # 记录买入交易
        for _, row in stocks_to_buy.iterrows():
            # 计算买入数量（取整百）
            buy_quantity = int(investment_per_stock / row['当前价格'] / 100) * 100
            if buy_quantity == 0:
                buy_quantity = 100  # 最少买入100股
            
            buy_amount = buy_quantity * row['当前价格']
            
            # 添加到持仓
            new_holding = {
                '股票代码': row['股票代码'],
                '股票名称': row['股票名称'],
                '买入日期': datetime.now().strftime('%Y-%m-%d'),
                '买入价格': row['当前价格'],
                '持仓数量': buy_quantity,
                '当前价格': row['当前价格'],
                '当前市值': buy_amount,
                '盈亏金额': 0,
                '盈亏比例': 0,
                '技术强度': row['技术强度']
            }
            holdings = pd.concat([holdings, pd.DataFrame([new_holding])], ignore_index=True)
            
            # 记录交易
            transaction = {
                '交易日期': datetime.now().strftime('%Y-%m-%d'),
                '股票代码': row['股票代码'],
                '股票名称': row['股票名称'],
                '交易类型': '买入',
                '交易价格': row['当前价格'],
                '交易数量': buy_quantity,
                '交易金额': buy_amount,
                '技术强度': row['技术强度'],
                '持有天数': 0,
                '盈亏金额': 0,
                '盈亏比例': 0
            }
            transactions = pd.concat([transactions, pd.DataFrame([transaction])], ignore_index=True)
            
            print(f"买入 {row['股票名称']}({row['股票代码']}): 价格={row['当前价格']}, 数量={buy_quantity}, 金额={buy_amount:.2f}")
        
        # 保存更新后的持仓和交易记录
        holdings.to_excel(self.holdings_file, index=False)
        transactions.to_excel(self.transactions_file, index=False)
        
        print(f"已完成 {len(stocks_to_buy)} 只股票的买入交易")
    
    def run(self):
        """运行交易策略"""
        print("开始运行交易策略...")
        
        # 加载最新股票数据
        latest_data = self.load_stock_data()
        if len(latest_data) == 0:
            print("无法获取最新股票数据，程序终止")
            return
        
        # 更新持仓信息
        holdings = self.update_holdings(latest_data)
        
        # 识别卖出信号
        stocks_to_sell = self.identify_sell_signals(holdings)
        
        # 执行卖出订单
        self.execute_sell_orders(stocks_to_sell)
        
        # 更新持仓（卖出后）
        holdings = self.load_holdings()
        
        # 识别买入信号
        stocks_to_buy = self.identify_buy_signals(latest_data, holdings)
        
        # 执行买入订单
        self.execute_buy_orders(stocks_to_buy)
        
        print("交易策略运行完成!")
        
        # 输出当前持仓概况
        holdings = self.load_holdings()
        if len(holdings) > 0:
            total_value = holdings['当前市值'].sum()
            total_profit = holdings['盈亏金额'].sum()
            total_cost = total_value - total_profit
            total_profit_ratio = total_profit / total_cost if total_cost > 0 else 0
            
            print("\n当前持仓概况:")
            print(f"持仓股票数: {len(holdings)}")
            print(f"持仓总市值: {total_value:.2f}")
            print(f"总盈亏金额: {total_profit:.2f}")
            print(f"总盈亏比例: {total_profit_ratio*100:.2f}%")
            
            # 输出每只股票的持仓情况
            print("\n持仓明细:")
            for _, row in holdings.iterrows():
                print(f"{row['股票名称']}({row['股票代码']}): 持仓={row['持仓数量']}, 市值={row['当前市值']:.2f}, 盈亏={row['盈亏金额']:.2f}({row['盈亏比例']*100:.2f}%), 技术强度={row['技术强度']}")
        else:
            print("\n当前无持仓")

if __name__ == "__main__":
    strategy = TradingStrategy()
    strategy.run()
