import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys
from trading_strategy import TradingStrategy
from ml_stock_predictor import MLStockPredictor
from stock_crawler import StockCrawler

class MLEnhancedTrading:
    """
    机器学习增强的交易系统，整合爬虫、机器学习预测和交易策略
    """
    
    def __init__(self):
        """初始化交易系统"""
        # 创建结果目录
        if not os.path.exists('ml_trading'):
            os.makedirs('ml_trading')
        
        # 初始化组件
        self.crawler = StockCrawler()
        self.predictor = MLStockPredictor()
        self.strategy = TradingStrategy()
        
        # 配置参数
        self.ml_threshold = 0.7  # 机器学习预测概率阈值
        self.tech_strength_threshold = 100  # 技术强度阈值
        self.max_positions = 10  # 最大持仓数量
        self.investment_per_stock = 100000  # 每只股票投资金额
    
    def load_stock_data(self):
        """加载股票明细数据"""
        print("加载股票明细数据...")
        try:
            data = pd.read_excel('股票明细.xlsx')
            data['日期'] = pd.to_datetime(data['日期'])
            print(f"成功加载 {len(data)} 条股票数据")
            return data
        except Exception as e:
            print(f"加载股票数据失败: {e}")
            return pd.DataFrame()
    
    def get_latest_data(self):
        """获取最新数据，包括爬虫数据和股票明细数据"""
        print("获取最新数据...")
        
        # 运行爬虫获取行业和龙头股数据
        crawler_data = self.crawler.run()
        
        # 加载股票明细数据
        stock_data = self.load_stock_data()
        if len(stock_data) == 0:
            print("无法获取股票明细数据，程序终止")
            return None, None
        
        # 获取最新日期的股票数据
        latest_date = stock_data['日期'].max()
        latest_stock_data = stock_data[stock_data['日期'] == latest_date]
        
        print(f"最新日期: {latest_date.date()}, 股票数量: {len(latest_stock_data)}")
        
        return latest_stock_data, crawler_data
    
    def merge_data(self, stock_data, crawler_data):
        """合并股票明细数据和爬虫数据"""
        print("合并股票明细数据和爬虫数据...")
        
        if len(crawler_data) == 0:
            print("爬虫数据为空，将只使用股票明细数据")
            return stock_data
        
        # 标准化股票代码格式
        stock_data['标准代码'] = stock_data['股票代码'].apply(
            lambda x: x.split('.')[1].lower() + '.' + x.split('.')[0].lower() if '.' in x else x
        )
        
        # 确保爬虫数据中的股票代码格式一致
        if '标准代码' not in crawler_data.columns:
            crawler_data['标准代码'] = crawler_data['股票代码']
        
        # 合并数据
        merged_data = pd.merge(
            stock_data,
            crawler_data[['标准代码', '行业', '是否行业龙头', '是否强势行业']],
            on='标准代码',
            how='left'
        )
        
        # 填充缺失值
        merged_data['是否行业龙头'] = merged_data['是否行业龙头'].fillna(False)
        merged_data['是否强势行业'] = merged_data['是否强势行业'].fillna(False)
        
        print(f"合并后的数据共 {len(merged_data)} 条记录")
        return merged_data
    
    def select_stocks_with_ml(self, merged_data):
        """使用机器学习模型选择股票"""
        print("使用机器学习模型选择股票...")
        
        # 首先筛选技术强度符合条件的股票
        strength_stocks = merged_data[merged_data['技术强度'] >= self.tech_strength_threshold]
        print(f"技术强度>={self.tech_strength_threshold}的股票共 {len(strength_stocks)} 只")
        
        if len(strength_stocks) == 0:
            print("没有符合技术强度条件的股票")
            return pd.DataFrame()
        
        try:
            # 使用机器学习模型增强选股
            ml_enhanced = self.predictor.enhance_stock_selection(strength_stocks, threshold=self.ml_threshold)
            
            if len(ml_enhanced) == 0:
                print(f"没有预测概率>={self.ml_threshold}的股票，降低阈值重试")
                # 降低阈值重试
                ml_enhanced = self.predictor.enhance_stock_selection(strength_stocks, threshold=0.6)
            
            print(f"机器学习模型选出 {len(ml_enhanced)} 只股票")
            return ml_enhanced
        except Exception as e:
            print(f"机器学习选股失败: {e}")
            print("将使用传统方法选股")
            
            # 如果机器学习选股失败，使用传统方法
            # 按技术强度和是否行业龙头排序
            strength_stocks['综合得分'] = strength_stocks['技术强度'] + \
                                     strength_stocks['是否行业龙头'].astype(int) * 20 + \
                                     strength_stocks['是否强势行业'].astype(int) * 15
            
            traditional_selection = strength_stocks.sort_values('综合得分', ascending=False)
            
            print(f"传统方法选出 {len(traditional_selection)} 只股票")
            return traditional_selection
    
    def update_holdings(self):
        """更新持仓信息"""
        print("更新持仓信息...")
        
        # 获取最新数据
        latest_stock_data, crawler_data = self.get_latest_data()
        if latest_stock_data is None:
            return None
        
        # 合并数据
        merged_data = self.merge_data(latest_stock_data, crawler_data)
        
        # 更新持仓
        holdings = self.strategy.update_holdings(merged_data)
        
        return holdings, merged_data
    
    def identify_sell_signals_with_ml(self, holdings, merged_data):
        """使用机器学习增强的卖出信号识别"""
        print("使用机器学习增强的卖出信号识别...")
        
        if len(holdings) == 0:
            print("当前无持仓")
            return pd.DataFrame()
        
        # 基本卖出条件
        # 卖出条件1: 技术强度降至71或以下
        condition1 = holdings['技术强度'] <= 71
        
        # 卖出条件2: 持有超过5个交易日
        holdings['买入日期'] = pd.to_datetime(holdings['买入日期'])
        today = datetime.now().date()
        holdings['持有天数'] = holdings['买入日期'].apply(lambda x: (today - x.date()).days)
        condition2 = holdings['持有天数'] >= 5
        
        # 卖出条件3: 盈利超过10%
        condition3 = holdings['盈亏比例'] >= 0.1
        
        # 卖出条件4: 亏损超过5%
        condition4 = holdings['盈亏比例'] <= -0.05
        
        # 尝试使用机器学习模型预测技术强度是否会下降
        try:
            # 提取持仓股票的数据
            holdings_data = pd.DataFrame()
            for _, row in holdings.iterrows():
                stock_code = row['股票代码']
                stock_data = merged_data[merged_data['股票代码'] == stock_code]
                if len(stock_data) > 0:
                    holdings_data = pd.concat([holdings_data, stock_data])
            
            if len(holdings_data) > 0:
                # 使用技术强度减少预测模型
                # 注意：这里假设已经训练了技术强度减少预测模型
                # 实际使用时需要确保模型已经训练好
                self.predictor.model_file = 'models/strength_decrease_model.pkl'
                features = self.predictor.extract_features(holdings_data)
                
                try:
                    # 预测技术强度是否会下降
                    strength_decrease_probs = self.predictor.predict(features)
                    
                    # 添加预测概率到持仓数据
                    holdings['技术强度下降概率'] = 0.5  # 默认值
                    for i, (_, row) in enumerate(holdings_data.iterrows()):
                        stock_code = row['股票代码']
                        idx = holdings[holdings['股票代码'] == stock_code].index
                        if len(idx) > 0 and i < len(strength_decrease_probs):
                            holdings.loc[idx, '技术强度下降概率'] = strength_decrease_probs[i]
                    
                    # 卖出条件5: 技术强度下降概率高
                    condition5 = holdings['技术强度下降概率'] >= 0.7
                    
                    # 综合卖出条件
                    sell_condition = condition1 | condition2 | condition3 | condition4 | condition5
                except Exception as e:
                    print(f"预测技术强度下降失败: {e}")
                    # 如果预测失败，使用基本卖出条件
                    sell_condition = condition1 | condition2 | condition3 | condition4
            else:
                # 如果没有持仓数据，使用基本卖出条件
                sell_condition = condition1 | condition2 | condition3 | condition4
        except Exception as e:
            print(f"机器学习增强卖出信号识别失败: {e}")
            # 如果机器学习增强失败，使用基本卖出条件
            sell_condition = condition1 | condition2 | condition3 | condition4
        
        # 筛选应当卖出的股票
        stocks_to_sell = holdings[sell_condition].copy()
        
        if len(stocks_to_sell) > 0:
            print(f"发现 {len(stocks_to_sell)} 只股票满足卖出条件:")
            for _, row in stocks_to_sell.iterrows():
                sell_reasons = []
                if row['技术强度'] <= 71:
                    sell_reasons.append("技术强度降低")
                if row['持有天数'] >= 5:
                    sell_reasons.append("持有超过5天")
                if row['盈亏比例'] >= 0.1:
                    sell_reasons.append("盈利超过10%")
                if row['盈亏比例'] <= -0.05:
                    sell_reasons.append("亏损超过5%")
                if '技术强度下降概率' in row and row['技术强度下降概率'] >= 0.7:
                    sell_reasons.append(f"技术强度下降概率高({row['技术强度下降概率']:.2f})")
                
                print(f"- {row['股票名称']}({row['股票代码']}): 卖出原因={', '.join(sell_reasons)}, 持有天数={row['持有天数']}, 盈亏比例={row['盈亏比例']*100:.2f}%, 技术强度={row['技术强度']}")
        else:
            print("没有股票满足卖出条件")
        
        return stocks_to_sell
    
    def identify_buy_signals_with_ml(self, merged_data, holdings):
        """使用机器学习增强的买入信号识别"""
        print("使用机器学习增强的买入信号识别...")
        
        # 使用机器学习模型选择股票
        recommended_stocks = self.select_stocks_with_ml(merged_data)
        
        if len(recommended_stocks) == 0:
            print("没有推荐的买入股票")
            return pd.DataFrame()
        
        # 排除已持仓的股票
        if len(holdings) > 0:
            recommended_stocks = recommended_stocks[~recommended_stocks['股票代码'].isin(holdings['股票代码'])]
        
        # 限制买入数量
        available_slots = self.max_positions - len(holdings)
        
        if available_slots <= 0:
            print("当前持仓已满，无法买入新股票")
            return pd.DataFrame()
        
        stocks_to_buy = recommended_stocks.head(min(available_slots, len(recommended_stocks)))
        
        if len(stocks_to_buy) > 0:
            print(f"推荐买入 {len(stocks_to_buy)} 只股票:")
            for _, row in stocks_to_buy.iterrows():
                buy_reasons = []
                if row['技术强度'] >= self.tech_strength_threshold:
                    buy_reasons.append(f"技术强度高({row['技术强度']})")
                if '预测概率' in row:
                    buy_reasons.append(f"上涨概率高({row['预测概率']:.2f})")
                if row.get('是否行业龙头', False):
                    buy_reasons.append("行业龙头")
                if row.get('是否强势行业', False):
                    buy_reasons.append("强势行业")
                
                print(f"- {row['股票名称']}({row['股票代码']}): 买入原因={', '.join(buy_reasons)}, 价格={row['当前价格']}, 技术强度={row['技术强度']}")
        else:
            print("没有推荐的买入股票")
        
        return stocks_to_buy
    
    def run(self, train_model=False):
        """运行机器学习增强的交易系统"""
        print("开始运行机器学习增强的交易系统...")
        
        # 如果需要，训练模型
        if train_model:
            print("训练机器学习模型...")
            stock_data = self.load_stock_data()
            if len(stock_data) > 0:
                self.predictor.run(stock_data, mode='train')
            else:
                print("无法获取股票数据，无法训练模型")
                return
        
        # 更新持仓信息
        holdings, merged_data = self.update_holdings()
        if holdings is None:
            print("更新持仓信息失败，程序终止")
            return
        
        # 识别卖出信号
        stocks_to_sell = self.identify_sell_signals_with_ml(holdings, merged_data)
        
        # 执行卖出订单
        self.strategy.execute_sell_orders(stocks_to_sell)
        
        # 更新持仓（卖出后）
        holdings = self.strategy.load_holdings()
        
        # 识别买入信号
        stocks_to_buy = self.identify_buy_signals_with_ml(merged_data, holdings)
        
        # 执行买入订单
        self.strategy.execute_buy_orders(stocks_to_buy, self.investment_per_stock)
        
        print("机器学习增强的交易系统运行完成!")
        
        # 输出当前持仓概况
        holdings = self.strategy.load_holdings()
        if len(holdings) > 0:
            total_value = holdings['当前市值'].sum()
            total_profit = holdings['盈亏金额'].sum()
            total_cost = total_value - total_profit
            total_profit_ratio = total_profit / total_cost if total_cost > 0 else 0
            
            print("\n当前持仓概况:")
            print(f"持仓股票数: {len(holdings)}")
            print(f"持仓总市值: {total_value:.2f}")
            print(f"总盈亏金额: {total_profit:.2f}")
            print(f"总盈亏比例: {total_profit_ratio*100:.2f}%")
            
            # 输出每只股票的持仓情况
            print("\n持仓明细:")
            for _, row in holdings.iterrows():
                print(f"{row['股票名称']}({row['股票代码']}): 持仓={row['持仓数量']}, 市值={row['当前市值']:.2f}, 盈亏={row['盈亏金额']:.2f}({row['盈亏比例']*100:.2f}%), 技术强度={row['技术强度']}")
        else:
            print("\n当前无持仓")

if __name__ == "__main__":
    # 解析命令行参数
    train_model = False
    if len(sys.argv) > 1 and sys.argv[1] == '--train':
        train_model = True
    
    # 运行交易系统
    trading = MLEnhancedTrading()
    trading.run(train_model=train_model)
