#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用中文字段进行策略回测
作者: Augment AI
版本: 1.0.0

该脚本用于使用新的中文字段格式进行策略回测。
"""

import os
import pandas as pd
import numpy as np
import glob
from datetime import datetime, timedelta
import time

def read_stock_data_chinese(file_path):
    """
    读取中文字段格式的股票数据
    
    参数:
        file_path (str): 股票数据文件路径
        
    返回:
        DataFrame: 股票数据
    """
    print(f"正在读取股票数据: {file_path}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 检查必要的列是否存在
        required_columns = ['证券代码', '日期', '开盘价', '收盘价', '前收盘价', '涨跌幅', '股票名称']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"错误: 缺少必要的列: {missing_columns}")
            return None
        
        # 确保日期列是日期类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 计算买入日开盘涨跌幅（当日开盘价相对于前一日收盘价的涨跌幅）
        df['买入日开盘涨跌幅'] = (df['开盘价'] - df['前收盘价']) / df['前收盘价'] * 100
        
        # 按股票代码分组，计算卖出日开盘涨跌幅
        df = df.sort_values(['证券代码', '日期'])
        df['下一日开盘价'] = df.groupby('证券代码')['开盘价'].shift(-1)
        df['卖出日开盘涨跌幅'] = (df['下一日开盘价'] - df['收盘价']) / df['收盘价'] * 100
        
        print(f"成功读取股票数据，共{len(df)}条记录")
        return df
    except Exception as e:
        print(f"读取股票数据时出错: {str(e)}")
        return None

def read_all_stock_data_chinese(directory):
    """
    读取目录中的所有中文字段格式股票数据文件
    
    参数:
        directory (str): 股票数据目录
        
    返回:
        DataFrame: 合并后的股票数据
    """
    print(f"正在读取目录中的所有股票数据: {directory}")
    
    try:
        # 查找所有Excel文件
        file_pattern = os.path.join(directory, "*.xlsx")
        files = glob.glob(file_pattern)
        
        if not files:
            print(f"错误: 目录中没有Excel文件: {directory}")
            return None
        
        print(f"找到 {len(files)} 个Excel文件")
        
        # 读取所有文件并合并
        all_data = []
        for file_path in files:
            df = read_stock_data_chinese(file_path)
            if df is not None:
                all_data.append(df)
        
        if not all_data:
            print("错误: 没有成功读取任何数据")
            return None
        
        # 合并数据
        merged_df = pd.concat(all_data, ignore_index=True)
        
        # 去除重复记录
        merged_df = merged_df.drop_duplicates(subset=['证券代码', '日期'])
        
        print(f"成功合并股票数据，共{len(merged_df)}条记录")
        return merged_df
    except Exception as e:
        print(f"读取目录中的所有股票数据时出错: {str(e)}")
        return None

def backtest_strategy_chinese(strategy_info, stock_data, start_date=None, end_date=None):
    """
    使用中文字段格式进行策略回测
    
    参数:
        strategy_info (Series): 策略信息
        stock_data (DataFrame): 股票历史数据
        start_date (str): 回测开始日期，格式：YYYY-MM-DD
        end_date (str): 回测结束日期，格式：YYYY-MM-DD
        
    返回:
        dict: 回测结果
    """
    print(f"正在回测策略 {strategy_info['策略编号']}: {strategy_info['策略条件描述']}")
    
    # 转换日期格式
    if start_date:
        start_date = pd.to_datetime(start_date)
    else:
        start_date = stock_data['日期'].min()
        
    if end_date:
        end_date = pd.to_datetime(end_date)
    else:
        end_date = stock_data['日期'].max()
    
    # 筛选日期范围内的数据
    mask = (stock_data['日期'] >= start_date) & (stock_data['日期'] <= end_date)
    data = stock_data[mask].copy()
    
    # 如果数据为空，返回空结果
    if len(data) == 0:
        print("选定日期范围内没有数据")
        return None
    
    # 获取日期范围内的交易日
    trading_dates = sorted(data['日期'].unique())
    
    # 初始化结果
    results = {
        'daily_performance': [],
        'trades': [],
        'summary': {}
    }
    
    # 初始化资金
    initial_capital = 1000000  # 初始资金100万
    current_capital = initial_capital
    
    # 初始化持仓
    positions = {}
    
    # 解析策略条件
    conditions_str = strategy_info['策略条件描述']
    conditions_list = conditions_str.split(' AND ')
    
    # 遍历每个交易日
    for i, date in enumerate(trading_dates[:-1]):  # 最后一天不买入
        # 当前日期的数据
        current_day_data = data[data['日期'] == date]
        
        # 下一个交易日
        next_date = trading_dates[i + 1]
        next_day_data = data[data['日期'] == next_date]
        
        # 卖出昨日持仓（次日早盘卖出）
        if positions:
            for stock_code, position in list(positions.items()):
                # 获取今日该股票的数据
                next_stock_data = next_day_data[next_day_data['证券代码'] == stock_code]
                
                if len(next_stock_data) > 0:
                    # 使用开盘价作为卖出价格
                    sell_price = next_stock_data['开盘价'].values[0]
                    
                    # 计算卖出收益
                    sell_value = position['quantity'] * sell_price
                    profit = sell_value - position['cost']
                    profit_rate = profit / position['cost'] * 100
                    
                    # 更新资金
                    current_capital += sell_value
                    
                    # 记录交易
                    results['trades'].append({
                        '日期': next_date,
                        '交易时间': '09:30',  # 早盘开盘时间
                        '股票代码': stock_code,
                        '股票名称': next_stock_data['股票名称'].values[0],
                        '操作': '卖出',
                        '价格': sell_price,
                        '数量': position['quantity'],
                        '金额': sell_value,
                        '涨跌幅(%)': next_stock_data['买入日开盘涨跌幅'].values[0],
                        '收益': profit,
                        '收益率(%)': profit_rate
                    })
                    
                    # 从持仓中移除
                    del positions[stock_code]
        
        # 应用策略条件筛选股票
        filtered_data = current_day_data.copy()
        
        for condition in conditions_list:
            if '大于等于' in condition:
                feature, threshold = condition.split('大于等于')
                feature = feature.strip()
                threshold = float(threshold.strip())
                if feature in filtered_data.columns:
                    filtered_data = filtered_data[filtered_data[feature] >= threshold]
            elif '为 1（是）' in condition:
                feature = condition.split('为')[0].strip()
                if feature in filtered_data.columns:
                    filtered_data = filtered_data[filtered_data[feature] == 1]
        
        # 获取符合条件的股票
        selected_stocks = filtered_data.copy()
        
        # 买入股票（次日早盘买入）
        if len(selected_stocks) > 0:
            # 计算每只股票的买入金额
            max_stocks = 10  # 最多买入10只股票
            num_stocks = min(len(selected_stocks), max_stocks)
            per_stock_value = current_capital / num_stocks
            
            # 买入股票
            for _, stock in selected_stocks.head(max_stocks).iterrows():
                stock_code = stock['证券代码']
                
                # 获取次日该股票的数据
                next_stock_data = next_day_data[next_day_data['证券代码'] == stock_code]
                
                if len(next_stock_data) > 0:
                    # 使用次日开盘价作为买入价格
                    buy_price = next_stock_data['开盘价'].values[0]
                    
                    # 计算买入数量（整百股）
                    quantity = int(per_stock_value / buy_price / 100) * 100
                    
                    if quantity > 0:
                        # 计算买入金额
                        cost = quantity * buy_price
                        
                        # 更新资金
                        current_capital -= cost
                        
                        # 添加到持仓
                        positions[stock_code] = {
                            'quantity': quantity,
                            'price': buy_price,
                            'cost': cost,
                            'buy_date': next_date
                        }
                        
                        # 记录交易
                        results['trades'].append({
                            '日期': next_date,
                            '交易时间': '09:30',  # 早盘开盘时间
                            '股票代码': stock_code,
                            '股票名称': next_stock_data['股票名称'].values[0],
                            '操作': '买入',
                            '价格': buy_price,
                            '数量': quantity,
                            '金额': cost,
                            '涨跌幅(%)': next_stock_data['买入日开盘涨跌幅'].values[0],
                            '收益': 0,
                            '收益率(%)': 0
                        })
        
        # 计算当日总资产
        total_position_value = sum([position['quantity'] * position['price'] for position in positions.values()])
        total_assets = current_capital + total_position_value
        
        # 计算当日收益率
        if i == 0:
            daily_return = 0
        else:
            prev_assets = results['daily_performance'][-1]['总资产']
            daily_return = (total_assets - prev_assets) / prev_assets * 100
        
        # 记录每日表现
        results['daily_performance'].append({
            '日期': date,
            '现金': current_capital,
            '持仓市值': total_position_value,
            '总资产': total_assets,
            '日收益率(%)': daily_return,
            '持仓数量': len(positions)
        })
    
    # 计算汇总统计
    if results['daily_performance']:
        # 计算总收益率
        initial_assets = initial_capital
        final_assets = results['daily_performance'][-1]['总资产']
        total_return = (final_assets - initial_assets) / initial_assets * 100
        
        # 计算年化收益率
        days = (trading_dates[-1] - trading_dates[0]).days
        annual_return = total_return * 365 / days if days > 0 else 0
        
        # 计算胜率
        if results['trades']:
            win_trades = [trade for trade in results['trades'] if trade['操作'] == '卖出' and trade['收益'] > 0]
            sell_trades = [trade for trade in results['trades'] if trade['操作'] == '卖出']
            win_rate = len(win_trades) / len(sell_trades) * 100 if sell_trades else 0
        else:
            win_rate = 0
        
        # 计算平均每日交易笔数
        daily_trades = {}
        for trade in results['trades']:
            date = trade['日期']
            if date not in daily_trades:
                daily_trades[date] = 0
            daily_trades[date] += 1
        
        avg_daily_trades = sum(daily_trades.values()) / len(trading_dates) if trading_dates else 0
        
        # 汇总统计
        results['summary'] = {
            '初始资金': initial_capital,
            '最终资金': final_assets,
            '总收益率(%)': total_return,
            '年化收益率(%)': annual_return,
            '胜率(%)': win_rate,
            '总交易笔数': len(results['trades']),
            '平均每日交易笔数': avg_daily_trades,
            '交易天数': len(daily_trades),
            '总天数': len(trading_dates),
            '交易频率(%)': len(daily_trades) / len(trading_dates) * 100 if trading_dates else 0
        }
    
    print(f"回测完成，总收益率: {results['summary']['总收益率(%)']}%，胜率: {results['summary']['胜率(%)']}%")
    return results

def main():
    """主函数"""
    # 设置股票数据目录
    data_dir = "E:\\机器学习\\complete_excel_results\\stock_data\\daily"
    
    # 读取所有股票数据
    stock_data = read_all_stock_data_chinese(data_dir)
    if stock_data is None:
        return
    
    print("中文字段格式的股票数据读取完成")
    print(f"数据列名: {list(stock_data.columns)}")
    print(f"数据行数: {len(stock_data)}")

if __name__ == "__main__":
    main()
