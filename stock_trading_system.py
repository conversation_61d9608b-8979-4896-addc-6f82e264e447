#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票交易系统 - 高收益高胜率策略
作者: Augment AI
版本: 1.0.0
"""

import pandas as pd
import numpy as np
import os
import datetime
import argparse
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
import joblib
import warnings
warnings.filterwarnings('ignore')

class StockTradingSystem:
    """股票交易系统主类"""

    def __init__(self, data_path=None):
        """初始化交易系统"""
        self.data_path = data_path
        self.data = None
        self.model = None
        self.model_path = "stock_model.pkl"
        self.scaler_path = "stock_scaler.pkl"

    def load_data(self, file_path=None):
        """加载股票数据"""
        if file_path is None:
            file_path = self.data_path

        if file_path is None:
            raise ValueError("请提供数据文件路径")

        try:
            self.data = pd.read_excel(file_path)
            print(f"成功加载数据，共 {len(self.data)} 条记录")

            # 确保日期列是datetime类型
            self.data['日期'] = pd.to_datetime(self.data['日期'])

            # 按日期和股票代码排序
            self.data = self.data.sort_values(['日期', '股票代码'])

            # 检查是否有缺失值
            missing_values = self.data.isnull().sum().sum()
            if missing_values > 0:
                print(f"警告: 数据中存在 {missing_values} 个缺失值")

            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False

    def train_model(self, save_model=True):
        """训练机器学习模型"""
        if self.data is None:
            print("错误: 请先加载数据")
            return False

        print("开始训练机器学习模型...")

        # 选择特征和目标变量
        features = ['技术强度', '连续技术强度5天数', '看涨技术指标数量', '价格趋势', '涨跌幅趋势']
        target = '涨跌幅'  # 次日涨跌幅作为目标变量

        # 准备训练数据
        X = self.data[features].values
        y = self.data[target].values

        # 将目标变量二值化（上涨=1，下跌=0）
        y_binary = (y > 0).astype(int)

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 划分训练集和测试集（使用最近一个月的数据作为测试集）
        latest_month = self.data['日期'].dt.month.max()
        train_mask = self.data['日期'].dt.month != latest_month
        test_mask = self.data['日期'].dt.month == latest_month

        X_train, X_test = X_scaled[train_mask], X_scaled[test_mask]
        y_train, y_test = y_binary[train_mask], y_binary[test_mask]

        print(f"训练集大小: {len(X_train)}")
        print(f"测试集大小: {len(X_test)}")

        # 构建随机森林模型
        model = RandomForestClassifier(
            n_estimators=200,
            max_depth=7,
            min_samples_split=10,
            min_samples_leaf=1,
            random_state=42
        )

        # 训练模型
        model.fit(X_train, y_train)

        # 评估模型
        train_accuracy = model.score(X_train, y_train)
        test_accuracy = model.score(X_test, y_test)

        print(f"训练集准确率: {train_accuracy:.4f}")
        print(f"测试集准确率: {test_accuracy:.4f}")

        # 保存模型和标准化器
        if save_model:
            joblib.dump(model, self.model_path)
            joblib.dump(scaler, self.scaler_path)
            print(f"模型已保存到 {self.model_path}")
            print(f"标准化器已保存到 {self.scaler_path}")

        self.model = model
        self.scaler = scaler

        return True

    def load_model(self):
        """加载已训练的模型"""
        try:
            self.model = joblib.load(self.model_path)
            self.scaler = joblib.load(self.scaler_path)
            print("模型加载成功")
            return True
        except Exception as e:
            print(f"加载模型失败: {e}")
            return False

    def rule_strategy_1(self, data, date):
        """规则策略1：技术强度=85 + 看涨技术指标数量=5 + 涨跌幅趋势=1"""
        daily_data = data[data['日期'] == date]
        selected = daily_data[
            (daily_data['技术强度'] == 85) &
            (daily_data['看涨技术指标数量'] == 5) &
            (daily_data['涨跌幅趋势'] == 1)
        ]
        return selected

    def rule_strategy_4(self, data, date):
        """规则策略4：技术强度>=85 + 看涨技术指标数量=5 + 涨跌幅趋势=1 + 连续技术强度5天数>=450"""
        daily_data = data[data['日期'] == date]
        selected = daily_data[
            (daily_data['技术强度'] >= 85) &
            (daily_data['看涨技术指标数量'] == 5) &
            (daily_data['涨跌幅趋势'] == 1) &
            (daily_data['连续技术强度5天数'] >= 450)
        ]
        return selected

    def rule_strategy_4_plus(self, data, date):
        """
        规则策略4+：在策略4的基础上进一步优化
        - 优先选择技术强度=100的股票
        - 按连续技术强度5天数降序排序
        """
        daily_data = data[data['日期'] == date]
        selected = daily_data[
            (daily_data['技术强度'] >= 85) &
            (daily_data['看涨技术指标数量'] == 5) &
            (daily_data['涨跌幅趋势'] == 1) &
            (daily_data['连续技术强度5天数'] >= 450)
        ]

        # 按技术强度和连续技术强度5天数排序
        selected = selected.sort_values(['技术强度', '连续技术强度5天数'], ascending=[False, False])

        return selected

    def ml_strategy(self, data, date, threshold=0.8, top_n=20):
        """机器学习策略：预测概率>0.8，选择前20只股票"""
        if self.model is None:
            print("错误: 请先训练或加载模型")
            return pd.DataFrame()

        daily_data = data[data['日期'] == date]

        if len(daily_data) == 0:
            print(f"警告: {date} 没有数据")
            return pd.DataFrame()

        # 准备特征
        features = ['技术强度', '连续技术强度5天数', '看涨技术指标数量', '价格趋势', '涨跌幅趋势']
        X = daily_data[features].values

        # 标准化特征
        X_scaled = self.scaler.transform(X)

        # 预测概率
        y_pred_proba = self.model.predict_proba(X_scaled)[:, 1]

        # 添加预测概率到数据中
        daily_data['预测概率'] = y_pred_proba

        # 筛选预测概率高的股票
        high_prob_stocks = daily_data[daily_data['预测概率'] > threshold]

        # 按预测概率降序排序
        sorted_stocks = high_prob_stocks.sort_values('预测概率', ascending=False)

        # 选择预测概率最高的前N只股票
        selected = sorted_stocks.head(top_n)

        return selected

    def hybrid_strategy(self, data, date, rule_weight=0.7, ml_weight=0.3, top_n=10):
        """
        混合策略：结合规则策略和机器学习策略
        - 使用规则策略4+筛选股票
        - 使用机器学习模型计算预测概率
        - 计算综合得分 = 规则得分*rule_weight + 预测概率*ml_weight
        - 选择综合得分最高的前N只股票
        """
        if self.model is None:
            print("错误: 请先训练或加载模型")
            return pd.DataFrame()

        daily_data = data[data['日期'] == date]

        if len(daily_data) == 0:
            print(f"警告: {date} 没有数据")
            return pd.DataFrame()

        # 准备特征
        features = ['技术强度', '连续技术强度5天数', '看涨技术指标数量', '价格趋势', '涨跌幅趋势']
        X = daily_data[features].values

        # 标准化特征
        X_scaled = self.scaler.transform(X)

        # 预测概率
        y_pred_proba = self.model.predict_proba(X_scaled)[:, 1]

        # 添加预测概率到数据中
        daily_data['预测概率'] = y_pred_proba

        # 计算规则得分
        daily_data['规则得分'] = 0.0

        # 技术强度>=85
        daily_data.loc[daily_data['技术强度'] >= 85, '规则得分'] += 0.25

        # 看涨技术指标数量=5
        daily_data.loc[daily_data['看涨技术指标数量'] == 5, '规则得分'] += 0.25

        # 涨跌幅趋势=1
        daily_data.loc[daily_data['涨跌幅趋势'] == 1, '规则得分'] += 0.25

        # 连续技术强度5天数>=450
        daily_data.loc[daily_data['连续技术强度5天数'] >= 450, '规则得分'] += 0.25

        # 计算综合得分
        daily_data['综合得分'] = daily_data['规则得分'] * rule_weight + daily_data['预测概率'] * ml_weight

        # 按综合得分降序排序
        sorted_stocks = daily_data.sort_values('综合得分', ascending=False)

        # 选择综合得分最高的前N只股票
        selected = sorted_stocks.head(top_n)

        return selected

    def backtest(self, strategy_fn, start_date=None, end_date=None, initial_capital=10000):
        """回测策略"""
        if self.data is None:
            print("错误: 请先加载数据")
            return None

        # 设置日期范围
        if start_date is None:
            start_date = self.data['日期'].min()
        else:
            start_date = pd.to_datetime(start_date)

        if end_date is None:
            end_date = self.data['日期'].max()
        else:
            end_date = pd.to_datetime(end_date)

        # 筛选日期范围内的数据
        date_range_data = self.data[(self.data['日期'] >= start_date) & (self.data['日期'] <= end_date)]

        # 获取日期范围内的所有交易日
        trading_dates = sorted(date_range_data['日期'].unique())

        print(f"回测日期范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        print(f"交易日数量: {len(trading_dates)}")

        # 初始化回测结果
        capital = initial_capital
        trades = []
        daily_results = []

        # 对每个交易日进行回测
        for i, current_date in enumerate(trading_dates):
            # 跳过最后一个交易日，因为没有后续数据来验证
            if i >= len(trading_dates) - 1:
                continue

            # 获取次日日期
            next_date = trading_dates[i + 1]

            # 应用策略
            recommended_stocks = strategy_fn(self.data, current_date)

            # 记录每日推荐股票
            daily_results.append({
                '日期': current_date.strftime('%Y-%m-%d'),
                '推荐股票数量': len(recommended_stocks)
            })

            # 如果有推荐的股票，模拟买入
            if len(recommended_stocks) > 0:
                # 计算每只股票的资金分配
                capital_per_stock = capital / len(recommended_stocks)

                # 记录每只股票的买入和卖出情况
                for _, stock in recommended_stocks.iterrows():
                    code = stock['股票代码']
                    name = stock['股票名称']

                    # 获取次日该股票数据（买入）
                    next_day_data = self.data[(self.data['日期'] == next_date) & (self.data['股票代码'] == code)]

                    if len(next_day_data) > 0:
                        # 获取次日涨跌幅（模拟买入后的收益）
                        next_day_change = next_day_data['涨跌幅'].values[0]

                        # 计算收益
                        profit = capital_per_stock * next_day_change / 100

                        # 记录交易
                        trades.append({
                            '日期': current_date.strftime('%Y-%m-%d'),
                            '次日': next_date.strftime('%Y-%m-%d'),
                            '股票代码': code,
                            '股票名称': name,
                            '技术强度': stock['技术强度'],
                            '连续技术强度5天数': stock['连续技术强度5天数'],
                            '看涨技术指标数量': stock['看涨技术指标数量'],
                            '涨跌幅趋势': stock['涨跌幅趋势'],
                            '次日涨跌幅': next_day_change,
                            '投入资金': capital_per_stock,
                            '收益': profit,
                            '是否盈利': next_day_change > 0
                        })

        # 计算回测结果
        if trades:
            trades_df = pd.DataFrame(trades)
            total_profit = trades_df['收益'].sum()
            win_rate = trades_df['是否盈利'].mean() * 100
            avg_return = trades_df['次日涨跌幅'].mean()
            final_capital = initial_capital + total_profit
            total_return = (final_capital / initial_capital - 1) * 100

            # 计算每日收益率
            daily_trades_df = trades_df.groupby('日期').agg({
                '次日涨跌幅': 'mean',
                '是否盈利': 'mean',
                '收益': 'sum',
                '股票代码': 'count'
            }).reset_index()

            daily_trades_df.columns = ['日期', '平均涨跌幅', '胜率', '当日收益', '交易数量']
            daily_trades_df['胜率'] = daily_trades_df['胜率'] * 100
            daily_trades_df['累计收益'] = daily_trades_df['当日收益'].cumsum()
            daily_trades_df['累计收益率'] = daily_trades_df['累计收益'] / initial_capital * 100

            # 打印回测结果
            print("\n回测结果:")
            print(f"初始资金: {initial_capital:,.2f}元")
            print(f"最终资金: {final_capital:,.2f}元")
            print(f"总收益: {total_profit:,.2f}元")
            print(f"总收益率: {total_return:.2f}%")
            print(f"交易次数: {len(trades)}")
            print(f"胜率: {win_rate:.2f}%")
            print(f"平均涨跌幅: {avg_return:.2f}%")

            # 打印每日收益
            print("\n每日收益:")
            for _, row in daily_trades_df.iterrows():
                print(f"{row['日期']}: 交易数量={row['交易数量']}, 平均涨跌幅={row['平均涨跌幅']:.2f}%, 胜率={row['胜率']:.2f}%, 当日收益={row['当日收益']:.2f}元, 累计收益率={row['累计收益率']:.2f}%")

            return {
                'trades': trades,
                'trades_df': trades_df,
                'daily_trades_df': daily_trades_df,
                'daily_results': pd.DataFrame(daily_results),
                'total_profit': total_profit,
                'win_rate': win_rate,
                'avg_return': avg_return,
                'final_capital': final_capital,
                'total_return': total_return
            }
        else:
            print("\n回测结果: 无交易记录")
            return {
                'trades': [],
                'trades_df': pd.DataFrame(),
                'daily_trades_df': pd.DataFrame(),
                'daily_results': pd.DataFrame(daily_results),
                'total_profit': 0,
                'win_rate': 0,
                'avg_return': 0,
                'final_capital': initial_capital,
                'total_return': 0
            }
