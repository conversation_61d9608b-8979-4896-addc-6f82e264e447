import pandas as pd
import os
import glob
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置文件路径
source_dir = r'E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0'
target_file = r'E:\机器学习\complete_excel_results\股票明细.xlsx'
output_file = r'E:\机器学习\complete_excel_results\股票明细_完整.xlsx'

def find_excel_files(directory):
    """查找目录中的所有Excel文件"""
    excel_files = []

    # 遍历目录及其子目录
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.xlsx') or file.endswith('.xls'):
                excel_files.append(os.path.join(root, file))

    return excel_files

def extract_date_from_path(file_path):
    """从文件路径中提取日期"""
    try:
        # 尝试从文件名中提取日期
        file_name = os.path.basename(file_path)

        # 检查文件名中是否包含日期格式 YYYY-MM-DD
        if '_' in file_name and '-' in file_name:
            # 提取文件名中的日期部分
            date_part = file_name.split('_')[-1].split('.')[0]  # 获取最后一个_后面的部分，去掉扩展名

            # 检查是否是日期格式 YYYY-MM-DD
            if date_part.count('-') == 2:
                return date_part

        # 尝试从目录名中提取日期
        dir_name = os.path.basename(os.path.dirname(file_path))

        # 检查目录名中是否包含"选股结果_YYYY-MM-DD"格式
        if dir_name.startswith('选股结果_') and '-' in dir_name:
            date_part = dir_name.split('_')[-1]
            if date_part.count('-') == 2:
                return date_part

        # 打印更多调试信息
        print(f"文件名: {file_name}, 目录名: {dir_name}")

        # 如果是"强势股选股结果_YYYY-MM-DD.xlsx"格式
        if file_name.startswith('强势股选股结果_') and '-' in file_name:
            date_part = file_name.split('_')[-1].split('.')[0]
            if date_part.count('-') == 2:
                return date_part
    except Exception as e:
        print(f"提取日期时出错: {e}, 文件路径: {file_path}")

    # 如果无法提取日期，返回None
    return None

def calculate_features(df):
    """计算技术指标特征"""
    # 确保数据类型正确
    for col in ['技术强度', '连续技术强度5天数', '连续技术强度3天数', '连续技术强度10天数']:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

    # 如果缺少必要的列，添加默认值
    required_columns = [
        '技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
        '技术指标_KDJ金叉', '技术指标_布林带突破', '看涨技术指标数量',
        '价格趋势', '涨跌幅趋势', '技术强度趋势',
        '连续技术强度3天数趋势', '连续技术强度3天数价格趋势', '连续技术强度3天数涨跌幅趋势',
        '连续技术强度5天数趋势', '连续技术强度5天数价格趋势', '连续技术强度5天数涨跌幅趋势',
        '连续技术强度10天数趋势', '连续技术强度10天数价格趋势', '连续技术强度10天数涨跌幅趋势',
        '是否盈利', '开盘涨跌'
    ]

    for col in required_columns:
        if col not in df.columns:
            df[col] = 0

    # 计算看涨技术指标数量
    if '看涨技术指标数量' in df.columns:
        indicator_cols = [
            '技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
            '技术指标_KDJ金叉', '技术指标_布林带突破'
        ]
        df['看涨技术指标数量'] = df[indicator_cols].sum(axis=1)

    # 计算趋势特征
    if '技术强度' in df.columns:
        # 技术强度趋势：如果技术强度 >= 70，则为1，否则为0
        df['技术强度趋势'] = (df['技术强度'] >= 70).astype(int)

    if '涨跌幅' in df.columns:
        # 涨跌幅趋势：如果涨跌幅 > 0，则为1，否则为0
        df['涨跌幅趋势'] = (df['涨跌幅'] > 0).astype(int)

    # 计算价格趋势（假设当前价格高于5日均价则为上涨趋势）
    if '当前价格' in df.columns and '5日均价' in df.columns:
        df['价格趋势'] = (df['当前价格'] > df['5日均价']).astype(int)
    else:
        # 如果没有均价数据，使用涨跌幅作为替代
        df['价格趋势'] = df['涨跌幅趋势']

    # 计算连续技术强度趋势特征
    for period in [3, 5, 10]:
        col = f'连续技术强度{period}天数'
        if col in df.columns:
            # 趋势：如果连续技术强度 >= 70，则为1，否则为0
            df[f'{col}趋势'] = (df[col] >= 70).astype(int)
            # 价格趋势：与整体价格趋势相同
            df[f'{col}价格趋势'] = df['价格趋势']
            # 涨跌幅趋势：与整体涨跌幅趋势相同
            df[f'{col}涨跌幅趋势'] = df['涨跌幅趋势']

    # 开盘涨跌：假设与涨跌幅趋势相同
    df['开盘涨跌'] = df['涨跌幅趋势']

    return df

def process_excel_file(file_path):
    """处理单个Excel文件，提取股票数据并添加日期"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)

        # 检查是否是股票数据文件
        if '股票代码' not in df.columns:
            print(f"跳过非股票数据文件: {file_path}")
            return None

        # 提取日期
        date_str = extract_date_from_path(file_path)
        if date_str:
            try:
                date = pd.to_datetime(date_str)
                df['日期'] = date
                print(f"处理文件: {file_path}, 日期: {date_str}")
                return df
            except:
                print(f"无法解析日期: {date_str}, 文件: {file_path}")
                return None
        else:
            print(f"无法从路径提取日期: {file_path}")
            return None
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return None

def main():
    # 读取现有的股票明细数据
    print("读取现有的股票明细数据...")
    try:
        existing_data = pd.read_excel(target_file)
        print(f"成功读取现有数据，共 {len(existing_data)} 条记录")
    except Exception as e:
        print(f"读取现有数据时出错: {e}")
        existing_data = pd.DataFrame()

    # 查找所有Excel文件
    print(f"在 {source_dir} 中查找Excel文件...")
    excel_files = find_excel_files(source_dir)
    print(f"找到 {len(excel_files)} 个Excel文件")

    # 处理每个Excel文件
    all_data = []
    for file in excel_files:
        df = process_excel_file(file)
        if df is not None and not df.empty:
            # 计算特征
            df = calculate_features(df)
            all_data.append(df)

    # 合并所有数据
    if all_data:
        new_data = pd.concat(all_data, ignore_index=True)
        print(f"从Excel文件中提取了 {len(new_data)} 条记录")

        # 合并新数据和现有数据
        if not existing_data.empty:
            # 确保日期列是日期类型
            existing_data['日期'] = pd.to_datetime(existing_data['日期'])
            new_data['日期'] = pd.to_datetime(new_data['日期'])

            # 创建唯一标识符（股票代码+日期）
            existing_data['标识符'] = existing_data['股票代码'] + '_' + existing_data['日期'].dt.strftime('%Y-%m-%d')
            new_data['标识符'] = new_data['股票代码'] + '_' + new_data['日期'].dt.strftime('%Y-%m-%d')

            # 找出新数据中不在现有数据中的记录
            existing_identifiers = set(existing_data['标识符'])
            new_data_unique = new_data[~new_data['标识符'].isin(existing_identifiers)]

            # 合并数据
            combined_data = pd.concat([existing_data, new_data_unique], ignore_index=True)

            # 删除临时列
            combined_data = combined_data.drop(columns=['标识符'])

            print(f"合并后共有 {len(combined_data)} 条记录（新增 {len(new_data_unique)} 条）")
        else:
            combined_data = new_data
            print(f"没有现有数据，使用新提取的 {len(combined_data)} 条记录")

        # 按股票代码和日期排序
        combined_data = combined_data.sort_values(['股票代码', '日期'])

        # 保存合并后的数据
        combined_data.to_excel(output_file, index=False)
        print(f"已将合并后的数据保存到 {output_file}")
    else:
        print("没有找到有效的股票数据")

if __name__ == "__main__":
    main()
