#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试从按日期存储的技术强度文件中加载数据
"""

import pandas as pd
import os
import glob

def test_loading():
    """测试数据加载"""
    print("开始测试...")
    
    # 设置路径
    base_dir = r'E:\机器学习\complete_excel_results'
    tech_strength_daily_dir = os.path.join(base_dir, 'tech_strength', 'daily')
    
    print(f"检查目录: {tech_strength_daily_dir}")
    
    if not os.path.exists(tech_strength_daily_dir):
        print(f"目录不存在: {tech_strength_daily_dir}")
        return False
    
    # 查找文件
    pattern = os.path.join(tech_strength_daily_dir, "tech_strength_strong_*_smart.xlsx")
    files = glob.glob(pattern)
    
    print(f"找到 {len(files)} 个文件")
    
    if len(files) == 0:
        print("没有找到文件")
        return False
    
    # 测试读取第一个文件
    first_file = files[0]
    print(f"测试读取: {os.path.basename(first_file)}")
    
    try:
        df = pd.read_excel(first_file)
        print(f"成功读取，共 {len(df)} 行")
        print(f"列名: {list(df.columns)}")
        
        if len(df) > 0:
            print("前3行数据:")
            print(df.head(3))
        
        return True
    except Exception as e:
        print(f"读取失败: {e}")
        return False

if __name__ == "__main__":
    test_loading()
