#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试新增的函数
"""

import pandas as pd
import os
import glob

def test_setup_daily_data_index():
    """测试设置按日期存储的数据索引"""
    print("测试设置按日期存储的数据索引...")
    
    # 设置路径
    base_dir = r'E:\机器学习\complete_excel_results'
    tech_strength_daily_dir = os.path.join(base_dir, 'tech_strength', 'daily')
    
    print(f"检查目录: {tech_strength_daily_dir}")
    
    if not os.path.exists(tech_strength_daily_dir):
        print(f"目录不存在: {tech_strength_daily_dir}")
        return None, None
    
    # 查找所有技术强度文件
    pattern = os.path.join(tech_strength_daily_dir, "tech_strength_strong_*_smart.xlsx")
    tech_files = glob.glob(pattern)
    
    if not tech_files:
        print(f"在 {tech_strength_daily_dir} 中没有找到技术强度文件")
        return None, None
    
    print(f"找到 {len(tech_files)} 个技术强度文件")
    
    # 创建日期到文件路径的映射
    date_to_file_map = {}
    available_dates = []
    
    for file_path in tech_files:
        try:
            # 从文件名中提取日期
            filename = os.path.basename(file_path)
            # 文件名格式: tech_strength_strong_2025-05-15_smart.xlsx
            date_part = filename.replace('tech_strength_strong_', '').replace('_smart.xlsx', '')
            date_obj = pd.to_datetime(date_part)
            
            date_to_file_map[date_obj] = file_path
            available_dates.append(date_obj)
            
        except Exception as e:
            print(f"解析文件名 {file_path} 时出错: {e}")
            continue
    
    available_dates.sort()
    print(f"可用日期范围: {available_dates[0].strftime('%Y-%m-%d')} 到 {available_dates[-1].strftime('%Y-%m-%d')}")
    
    return date_to_file_map, available_dates

def test_load_daily_stock_data(date, date_to_file_map):
    """测试按需加载指定日期的股票数据"""
    if isinstance(date, str):
        date = pd.to_datetime(date)
    
    if date not in date_to_file_map:
        print(f"没有找到日期 {date.strftime('%Y-%m-%d')} 的数据文件")
        return pd.DataFrame()
    
    file_path = date_to_file_map[date]
    
    try:
        df = pd.read_excel(file_path)
        df['日期'] = date
        print(f"成功加载 {date.strftime('%Y-%m-%d')} 的数据，共 {len(df)} 条记录")
        return df
    except Exception as e:
        print(f"读取日期 {date.strftime('%Y-%m-%d')} 的数据文件时出错: {e}")
        return pd.DataFrame()

def main():
    print("=== 测试新增的按日期加载函数 ===")
    
    # 测试设置数据索引
    date_to_file_map, available_dates = test_setup_daily_data_index()
    
    if date_to_file_map is None:
        print("没有找到按日期存储的数据文件")
        return
    
    # 测试加载一个日期的数据
    if available_dates:
        test_date = available_dates[0]
        daily_data = test_load_daily_stock_data(test_date, date_to_file_map)
        
        if not daily_data.empty:
            print(f"数据列名: {list(daily_data.columns)}")
            
            # 显示前几行数据
            if len(daily_data) > 0:
                print("前3行数据:")
                display_columns = ['股票代码', '股票名称', '技术强度']
                available_columns = [col for col in display_columns if col in daily_data.columns]
                if available_columns:
                    print(daily_data[available_columns].head(3).to_string(index=False))
    
    print("\n=== 测试完成 ===")
    print("修改总结:")
    print("1. 新增了 setup_daily_data_index() 函数，用于创建日期到文件的映射")
    print("2. 新增了 load_daily_stock_data() 函数，用于按需加载指定日期的数据")
    print("3. 修改了 backtest_strategy_manually() 函数，支持按日期筛选和加载数据")
    print("4. 这样可以避免一次性加载所有数据，突破Excel 100万行限制")

if __name__ == "__main__":
    main()
