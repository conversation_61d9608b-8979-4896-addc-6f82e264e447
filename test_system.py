import pandas as pd
import numpy as np
import os
import sys
import joblib
from datetime import datetime

# 导入自定义模块
from enhanced_data_sources import EnhancedDataSources
from sentiment_analyzer import SentimentAnalyzer
from deep_learning_models import DeepLearningModels
from ensemble_learning import EnsembleLearning
from trading_strategy import TradingStrategy

def test_system():
    """测试高级交易系统的各个组件"""
    print("开始测试高级交易系统...")
    
    # 创建结果目录
    if not os.path.exists('test_results'):
        os.makedirs('test_results')
    
    # 初始化组件
    data_sources = EnhancedDataSources()
    sentiment_analyzer = SentimentAnalyzer()
    dl_models = DeepLearningModels()
    ensemble = EnsembleLearning()
    strategy = TradingStrategy()
    
    # 测试宏观经济数据获取
    print("\n测试宏观经济数据获取...")
    macro_data = data_sources.get_macro_economic_data()
    print(f"宏观经济数据: {len(macro_data)} 行, {len(macro_data.columns)} 列")
    print(macro_data.head())
    
    # 测试行业基本面数据获取
    print("\n测试行业基本面数据获取...")
    industry_data = data_sources.get_industry_fundamentals()
    print(f"行业基本面数据: {len(industry_data)} 行, {len(industry_data.columns)} 列")
    print(industry_data.head())
    
    # 测试情感分析
    print("\n测试情感分析...")
    market_sentiment = sentiment_analyzer.get_market_sentiment()
    print(f"市场情感数据: {len(market_sentiment)} 行, {len(market_sentiment.columns)} 列")
    print(market_sentiment)
    
    # 加载股票数据
    print("\n加载股票数据...")
    try:
        stock_data = pd.read_excel('股票明细.xlsx')
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
        
        # 只取最新日期的前10只股票进行测试
        latest_date = stock_data['日期'].max()
        test_data = stock_data[stock_data['日期'] == latest_date].head(10)
        print(f"测试数据: {len(test_data)} 行, {len(test_data.columns)} 列")
        
        # 测试股票情感分析
        print("\n测试股票情感分析...")
        stock_codes = test_data['股票代码'].tolist()
        stock_sentiment = sentiment_analyzer.get_stock_sentiment(stock_codes)
        print(f"股票情感数据: {len(stock_sentiment)} 行, {len(stock_sentiment.columns)} 列")
        print(stock_sentiment.head())
        
        # 测试机器学习模型
        print("\n测试机器学习模型...")
        # 准备训练数据（使用小样本）
        train_data = stock_data.sample(min(1000, len(stock_data)), random_state=42)
        
        # 计算次日涨跌方向和收益率
        train_data['次日涨跌方向'] = 0
        train_data['次日收益率'] = 0
        
        # 按股票代码分组处理
        for code, group in train_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')
            
            # 计算次日涨跌方向和收益率
            group['次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)
            group['次日收益率'] = group['当前价格'].shift(-1) / group['当前价格'] - 1
            
            # 更新原始数据
            train_data.loc[group.index, '次日涨跌方向'] = group['次日涨跌方向']
            train_data.loc[group.index, '次日收益率'] = group['次日收益率']
        
        # 删除没有次日数据的记录
        train_data = train_data.dropna(subset=['次日涨跌方向', '次日收益率'])
        
        # 训练随机森林模型
        print("\n训练随机森林模型...")
        features = ['技术强度', '涨跌幅', '当前价格']
        X = train_data[features].values
        y_dir = train_data['次日涨跌方向'].values
        
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score
        
        X_train, X_test, y_train, y_test = train_test_split(X, y_dir, test_size=0.2, random_state=42)
        
        rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
        rf_model.fit(X_train, y_train)
        
        y_pred = rf_model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        print(f"随机森林模型准确率: {accuracy:.4f}")
        
        # 保存模型
        joblib.dump(rf_model, 'test_results/rf_model.pkl')
        
        # 使用模型进行预测
        print("\n使用模型进行预测...")
        test_X = test_data[features].values
        test_pred = rf_model.predict_proba(test_X)[:, 1]
        
        # 创建预测结果
        predictions = pd.DataFrame({
            '股票代码': test_data['股票代码'],
            '股票名称': test_data['股票名称'],
            '当前价格': test_data['当前价格'],
            '技术强度': test_data['技术强度'],
            '预测涨跌概率': test_pred,
            '预测涨跌': ['上涨' if p > 0.5 else '下跌' for p in test_pred],
            '预测信号强度': [abs(p - 0.5) * 2 for p in test_pred]
        })
        
        print("\n预测结果:")
        print(predictions)
        
        # 保存预测结果
        predictions.to_excel('test_results/predictions.xlsx', index=False)
        
        print("\n测试完成！结果已保存至 test_results 目录")
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_system()
