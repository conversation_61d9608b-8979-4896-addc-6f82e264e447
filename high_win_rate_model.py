"""
高胜率策略模型

实现基于多特征的高胜率股票选择策略模型，复现之前100%胜率的策略
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
import argparse

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header(title):
    """打印标题"""
    print("\n" + " " * 24 + title)
    print("=" * 80 + "\n")

def load_data(file_path):
    """加载股票数据"""
    print(f"加载股票数据: {file_path}")
    try:
        df = pd.read_excel(file_path)
        print(f"成功加载股票数据，共 {len(df)} 条记录")
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def preprocess_data(df):
    """预处理数据"""
    print("预处理数据...")

    # 确保日期列是datetime类型
    if '日期' in df.columns:
        df['日期'] = pd.to_datetime(df['日期'])

    # 生成目标变量：如果涨跌幅>0，则为1，否则为0
    if '涨跌幅' in df.columns and '是否盈利' not in df.columns:
        df['是否盈利'] = (df['涨跌幅'] > 0).astype(int)

    # 计算真实的连续技术强度5天数
    if '技术强度' in df.columns and '连续技术强度5天数' not in df.columns:
        print("计算连续技术强度5天数...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化连续技术强度5天数列
        df['连续技术强度5天数'] = 0

        # 对每个股票计算连续技术强度5天数
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取技术强度列
            strength = group['技术强度'].values

            # 计算连续5天的技术强度累积值
            for i in range(len(group)):
                if i < 4:  # 不足5天的情况
                    cumulative = sum(strength[max(0, i-4):i+1])
                else:  # 5天及以上的情况
                    cumulative = sum(strength[i-4:i+1])

                # 更新连续技术强度5天数
                df.loc[group.index[i], '连续技术强度5天数'] = cumulative

        print("连续技术强度5天数计算完成")

    # 计算技术强度趋势
    if '技术强度' in df.columns and '技术强度趋势' not in df.columns:
        print("计算技术强度趋势...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化技术强度趋势列
        df['技术强度趋势'] = 0

        # 对每个股票计算技术强度趋势
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取技术强度列
            strength = group['技术强度'].values

            # 计算连续3天的技术强度趋势
            for i in range(len(group)):
                if i >= 2:  # 至少需要3天数据
                    trend = 1 if (strength[i] > strength[i-1] and strength[i-1] > strength[i-2]) else 0
                    df.loc[group.index[i], '技术强度趋势'] = trend

        print("技术强度趋势计算完成")

    # 计算价格趋势
    if '当前价格' in df.columns and '价格趋势' not in df.columns:
        print("计算价格趋势...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化价格趋势列
        df['价格趋势'] = 0

        # 对每个股票计算价格趋势
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取价格列
            prices = group['当前价格'].values

            # 计算连续3天的价格趋势
            for i in range(len(group)):
                if i >= 2:  # 至少需要3天数据
                    trend = 1 if (prices[i] > prices[i-1] and prices[i-1] > prices[i-2]) else 0
                    df.loc[group.index[i], '价格趋势'] = trend

        print("价格趋势计算完成")

    # 计算涨跌幅趋势
    if '涨跌幅' in df.columns and '涨跌幅趋势' not in df.columns:
        print("计算涨跌幅趋势...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')

        # 初始化涨跌幅趋势列
        df['涨跌幅趋势'] = 0

        # 对每个股票计算涨跌幅趋势
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 获取涨跌幅列
            changes = group['涨跌幅'].values

            # 计算连续3天的涨跌幅趋势
            for i in range(len(group)):
                if i >= 2:  # 至少需要3天数据
                    trend = 1 if (changes[i] > changes[i-1] and changes[i-1] > changes[i-2]) else 0
                    df.loc[group.index[i], '涨跌幅趋势'] = trend

        print("涨跌幅趋势计算完成")

    # 生成技术指标特征（如果不存在）
    # 均线多头排列
    if '技术指标_均线多头排列' not in df.columns:
        print("生成技术指标_均线多头排列...")
        # 这里我们使用一个简化的逻辑：如果技术强度>80且价格趋势=1，则认为是均线多头排列
        if '技术强度' in df.columns and '价格趋势' in df.columns:
            df['技术指标_均线多头排列'] = ((df['技术强度'] > 80) & (df['价格趋势'] == 1)).astype(int)
        else:
            df['技术指标_均线多头排列'] = 0
        print("技术指标_均线多头排列生成完成")

    # MACD金叉
    if '技术指标_MACD金叉' not in df.columns:
        print("生成技术指标_MACD金叉...")
        # 这里我们使用一个简化的逻辑：如果技术强度>70且涨跌幅>0，则认为是MACD金叉
        if '技术强度' in df.columns and '涨跌幅' in df.columns:
            df['技术指标_MACD金叉'] = ((df['技术强度'] > 70) & (df['涨跌幅'] > 0)).astype(int)
        else:
            df['技术指标_MACD金叉'] = 0
        print("技术指标_MACD金叉生成完成")

    # RSI反弹
    if '技术指标_RSI反弹' not in df.columns:
        print("生成技术指标_RSI反弹...")
        # 这里我们使用一个简化的逻辑：如果技术强度>60且涨跌幅趋势=1，则认为是RSI反弹
        if '技术强度' in df.columns and '涨跌幅趋势' in df.columns:
            df['技术指标_RSI反弹'] = ((df['技术强度'] > 60) & (df['涨跌幅趋势'] == 1)).astype(int)
        else:
            df['技术指标_RSI反弹'] = 0
        print("技术指标_RSI反弹生成完成")

    # KDJ金叉
    if '技术指标_KDJ金叉' not in df.columns:
        print("生成技术指标_KDJ金叉...")
        # 这里我们使用一个简化的逻辑：如果技术强度>75且技术强度趋势=1，则认为是KDJ金叉
        if '技术强度' in df.columns and '技术强度趋势' in df.columns:
            df['技术指标_KDJ金叉'] = ((df['技术强度'] > 75) & (df['技术强度趋势'] == 1)).astype(int)
        else:
            df['技术指标_KDJ金叉'] = 0
        print("技术指标_KDJ金叉生成完成")

    # 布林带突破
    if '技术指标_布林带突破' not in df.columns:
        print("生成技术指标_布林带突破...")
        # 这里我们使用一个简化的逻辑：如果技术强度>90，则认为是布林带突破
        if '技术强度' in df.columns:
            df['技术指标_布林带突破'] = (df['技术强度'] > 90).astype(int)
        else:
            df['技术指标_布林带突破'] = 0
        print("技术指标_布林带突破生成完成")

    # 均线空头排列
    if '技术指标_均线空头排列' not in df.columns:
        print("生成技术指标_均线空头排列...")
        # 这里我们使用一个简化的逻辑：如果技术强度<20且价格趋势=0，则认为是均线空头排列
        if '技术强度' in df.columns and '价格趋势' in df.columns:
            df['技术指标_均线空头排列'] = ((df['技术强度'] < 20) & (df['价格趋势'] == 0)).astype(int)
        else:
            df['技术指标_均线空头排列'] = 0
        print("技术指标_均线空头排列生成完成")

    # MACD死叉
    if '技术指标_MACD死叉' not in df.columns:
        print("生成技术指标_MACD死叉...")
        # 这里我们使用一个简化的逻辑：如果技术强度<30且涨跌幅<0，则认为是MACD死叉
        if '技术强度' in df.columns and '涨跌幅' in df.columns:
            df['技术指标_MACD死叉'] = ((df['技术强度'] < 30) & (df['涨跌幅'] < 0)).astype(int)
        else:
            df['技术指标_MACD死叉'] = 0
        print("技术指标_MACD死叉生成完成")

    # RSI超买
    if '技术指标_RSI超买' not in df.columns:
        print("生成技术指标_RSI超买...")
        # 这里我们使用一个简化的逻辑：如果技术强度>95，则认为是RSI超买
        if '技术强度' in df.columns:
            df['技术指标_RSI超买'] = (df['技术强度'] > 95).astype(int)
        else:
            df['技术指标_RSI超买'] = 0
        print("技术指标_RSI超买生成完成")

    # KDJ死叉
    if '技术指标_KDJ死叉' not in df.columns:
        print("生成技术指标_KDJ死叉...")
        # 这里我们使用一个简化的逻辑：如果技术强度<25且技术强度趋势=0，则认为是KDJ死叉
        if '技术强度' in df.columns and '技术强度趋势' in df.columns:
            df['技术指标_KDJ死叉'] = ((df['技术强度'] < 25) & (df['技术强度趋势'] == 0)).astype(int)
        else:
            df['技术指标_KDJ死叉'] = 0
        print("技术指标_KDJ死叉生成完成")

    # 布林带收缩
    if '技术指标_布林带收缩' not in df.columns:
        print("生成技术指标_布林带收缩...")
        # 这里我们使用一个简化的逻辑：如果技术强度在40-60之间，则认为是布林带收缩
        if '技术强度' in df.columns:
            df['技术指标_布林带收缩'] = ((df['技术强度'] >= 40) & (df['技术强度'] <= 60)).astype(int)
        else:
            df['技术指标_布林带收缩'] = 0
        print("技术指标_布林带收缩生成完成")

    # 计算看涨技术指标数量
    if all(col in df.columns for col in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', '技术指标_KDJ金叉', '技术指标_布林带突破']) and '看涨技术指标数量' not in df.columns:
        print("计算看涨技术指标数量...")
        df['看涨技术指标数量'] = df['技术指标_均线多头排列'] + df['技术指标_MACD金叉'] + df['技术指标_RSI反弹'] + df['技术指标_KDJ金叉'] + df['技术指标_布林带突破']
        print("看涨技术指标数量计算完成")

    print("预处理完成")
    return df

def train_model(data, model_dir='models'):
    """训练高胜率策略模型"""
    print_header("训练高胜率策略模型")

    # 提取特征和目标变量 - 使用与之前完全相同的特征集
    # 核心特征
    core_features = [
        '技术强度', '连续技术强度5天数', '技术强度趋势', '价格趋势', '涨跌幅趋势', '涨跌幅'
    ]

    # 技术指标特征
    tech_indicators = [
        '技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
        '技术指标_KDJ金叉', '技术指标_布林带突破', '技术指标_均线空头排列',
        '技术指标_MACD死叉', '技术指标_RSI超买', '技术指标_KDJ死叉',
        '技术指标_布林带收缩'
    ]

    # 衍生特征
    derived_features = ['看涨技术指标数量']

    # 初始化特征列表
    features = []

    # 添加核心特征（如果存在）
    for feature in core_features:
        if feature in data.columns:
            features.append(feature)

    # 添加技术指标特征（如果存在）
    for indicator in tech_indicators:
        if indicator in data.columns:
            features.append(indicator)

    # 添加衍生特征（如果存在）
    for feature in derived_features:
        if feature in data.columns:
            features.append(feature)

    print(f"使用的特征: {features}")

    # 确保所有特征都存在
    for feature in features:
        if feature not in data.columns:
            print(f"警告: 特征 '{feature}' 不存在，将被忽略")
            features.remove(feature)

    # 提取特征和目标变量
    X = data[features]
    y = data['是否盈利']

    # 处理缺失值
    X = X.fillna(0)

    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # 训练随机森林模型
    print("训练随机森林模型...")
    model = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        random_state=42
    )
    model.fit(X_scaled, y)

    # 计算特征重要性
    feature_importance = pd.DataFrame({
        'feature': features,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)

    print("\n特征重要性:")
    for i, row in feature_importance.iterrows():
        print(f"{row['feature']}: {row['importance']*100:.2f}%")

    # 创建模型目录
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    # 保存模型
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = os.path.join(model_dir, f"{timestamp}.joblib")

    model_data = {
        'model': model,
        'scaler': scaler,
        'features': features,
        'feature_importance': feature_importance
    }

    joblib.dump(model_data, model_path)

    print(f"\n模型已保存至: {model_path}")
    return model, scaler, features

def load_model(model_dir='models'):
    """加载模型"""
    print("加载模型...")
    try:
        # 获取最新的模型文件
        model_files = [f for f in os.listdir(model_dir) if f.endswith('.joblib')]
        if not model_files:
            print("没有找到模型文件")
            return None, None, None

        latest_model_file = max(model_files)
        model_path = os.path.join(model_dir, latest_model_file)

        # 加载模型
        model_data = joblib.load(model_path)
        model = model_data['model']
        scaler = model_data['scaler']
        features = model_data['features']

        # 提取训练时间
        training_time = latest_model_file.split('.')[0]

        print(f"成功加载模型 (训练时间: {training_time})")
        print(f"模型使用的特征: {features}")

        return model, scaler, features
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None, None, None

def apply_high_win_rate_strategy(predictions):
    """
    高胜率组合策略

    条件：
    1. 预测盈利概率>75%
    2. 技术强度≥70且≤85
    3. 连续技术强度5天数≥400
    4. 至少具有1个看涨技术指标

    注意：只买入开盘时上涨的股票！这是保持100%胜率的关键条件！
    """
    # 筛选出满足条件的股票
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] >= 70) & (predictions['技术强度'] <= 85) &  # 条件2: 技术强度≥70且≤85
        (predictions['连续技术强度5天数'] >= 400) &  # 条件3: 5天累积值≥400
        (predictions['看涨技术指标数量'] >= 1)  # 条件4: 至少具有1个看涨技术指标
    ]

    # 按预测盈利概率降序排序
    strategy_stocks = strategy_stocks.sort_values('预测盈利概率', ascending=False)

    return strategy_stocks

def predict_stocks(data_file_path='股票明细.xlsx', prediction_date=None):
    """使用高胜率组合策略预测股票"""
    print_header("高胜率组合策略预测")

    # 加载模型
    model, scaler, features = load_model()
    if model is None:
        print("无法加载模型，请先训练模型")
        return

    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return

    # 预处理数据
    processed_data = preprocess_data(stock_data)

    # 如果指定了预测日期，使用指定日期
    if prediction_date:
        # 将字符串转换为datetime对象
        target_date = datetime.strptime(prediction_date, '%Y-%m-%d')
        # 找到小于等于目标日期的最近日期
        all_dates = sorted(processed_data['日期'].unique())
        earlier_dates = [d for d in all_dates if d <= target_date]

        if earlier_dates:
            latest_date = max(earlier_dates)
            latest_data = processed_data[processed_data['日期'] == latest_date]
            print(f"使用 {latest_date.strftime('%Y-%m-%d')} 的数据进行预测")
        else:
            # 如果没有小于等于目标日期的日期，使用最早的日期
            latest_date = min(all_dates)
            latest_data = processed_data[processed_data['日期'] == latest_date]
            print(f"警告: {prediction_date} 之前没有数据，使用最早的日期 {latest_date.strftime('%Y-%m-%d')} 的数据进行预测")

        # 使用指定的预测日期
        next_date_str = prediction_date
        next_date = target_date
    else:
        # 获取最新数据
        latest_date = processed_data['日期'].max()
        latest_data = processed_data[processed_data['日期'] == latest_date]

        # 计算下一个交易日（简单地加一天，实际应考虑周末和节假日）
        next_date = latest_date + timedelta(days=1)
        next_date_str = next_date.strftime('%Y-%m-%d')

    print(f"预测日期: {next_date_str}")

    # 准备预测数据
    try:
        # 提取特征
        X_pred = latest_data[features]

        # 处理预测数据中的缺失值
        X_pred = X_pred.fillna(0)

        # 标准化特征
        X_pred_scaled = scaler.transform(X_pred)

        # 预测盈利概率
        pred_proba = model.predict_proba(X_pred_scaled)[:, 1]

        # 创建预测结果DataFrame
        predictions = pd.DataFrame({
            '股票代码': latest_data['股票代码'],
            '股票名称': latest_data['股票名称'],
            '技术强度': latest_data['技术强度'],
            '连续技术强度5天数': latest_data['连续技术强度5天数'],
            '技术强度趋势': latest_data['技术强度趋势'],
            '价格趋势': latest_data['价格趋势'],
            '涨跌幅趋势': latest_data['涨跌幅趋势'],
            '涨跌幅': latest_data['涨跌幅'],
            '技术指标_均线多头排列': latest_data['技术指标_均线多头排列'],
            '技术指标_MACD金叉': latest_data['技术指标_MACD金叉'],
            '技术指标_RSI反弹': latest_data['技术指标_RSI反弹'],
            '技术指标_KDJ金叉': latest_data['技术指标_KDJ金叉'],
            '技术指标_布林带突破': latest_data['技术指标_布林带突破'],
            '技术指标_均线空头排列': latest_data['技术指标_均线空头排列'],
            '技术指标_MACD死叉': latest_data['技术指标_MACD死叉'],
            '技术指标_RSI超买': latest_data['技术指标_RSI超买'],
            '技术指标_KDJ死叉': latest_data['技术指标_KDJ死叉'],
            '技术指标_布林带收缩': latest_data['技术指标_布林带收缩'],
            '看涨技术指标数量': latest_data['看涨技术指标数量'],
            '预测盈利概率': pred_proba
        })

        # 应用高胜率组合策略
        strategy_stocks = apply_high_win_rate_strategy(predictions)

        # 创建结果目录
        if not os.path.exists('high_win_rate_results'):
            os.makedirs('high_win_rate_results')

        # 保存结果
        result_file = f'high_win_rate_results/{next_date_str}_高胜率策略股票.xlsx'
        strategy_stocks.to_excel(result_file, index=False)

        print(f"\n预测结果已保存至: {result_file}")
        print(f"高胜率组合策略推荐股票数: {len(strategy_stocks)}")

        # 显示推荐股票
        if len(strategy_stocks) > 0:
            print(f"\n高胜率组合策略推荐买入的股票:")
            print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持100%胜率的关键条件！")
            print("【交易策略】: 买入后在第二个交易日开盘时卖出")
            print("\n股票列表:")
            for i, row in strategy_stocks.iterrows():
                print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
        else:
            print(f"\n高胜率组合策略没有推荐的股票")

        return strategy_stocks

    except Exception as e:
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='高胜率策略模型')
    parser.add_argument('option', type=int, choices=[1, 2],
                        help='选择操作: 1=训练模型, 2=预测股票')
    parser.add_argument('--date', type=str, default=None,
                        help='预测日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--data', type=str, default='股票明细.xlsx',
                        help='数据文件路径 (默认: 股票明细.xlsx)')

    args = parser.parse_args()

    clear_screen()

    try:
        if args.option == 1:
            # 加载数据
            stock_data = load_data(args.data)
            if stock_data is None:
                return

            # 预处理数据
            processed_data = preprocess_data(stock_data)

            # 训练模型
            train_model(processed_data)
        elif args.option == 2:
            # 预测股票
            predict_stocks(args.data, args.date)
    except Exception as e:
        print(f"执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
