"""
将合并的股票数据文件拆分为按日期存储的数据文件
"""

import os
import pandas as pd
import time
import argparse
import stock_data_manager as sdm

def main():
    parser = argparse.ArgumentParser(description='将合并的股票数据文件拆分为按日期存储的数据文件')
    parser.add_argument('--file', type=str, help='要拆分的合并数据文件路径')
    parser.add_argument('--dir', type=str, help='合并数据文件所在目录')
    
    args = parser.parse_args()
    
    if args.file:
        # 拆分指定的文件
        if os.path.exists(args.file):
            print(f"开始拆分文件: {args.file}")
            sdm.split_combined_data_by_date(args.file)
        else:
            print(f"错误: 文件 {args.file} 不存在")
    elif args.dir:
        # 拆分指定目录下的所有合并数据文件
        if os.path.exists(args.dir):
            print(f"开始拆分目录 {args.dir} 下的所有合并数据文件")
            
            # 查找所有可能的合并数据文件
            combined_files = []
            for root, dirs, files in os.walk(args.dir):
                for file in files:
                    if file.startswith('stock_history_data') and (file.endswith('.xlsx') or file.endswith('.parquet')):
                        combined_files.append(os.path.join(root, file))
            
            print(f"找到 {len(combined_files)} 个可能的合并数据文件")
            
            # 拆分所有文件
            for file in combined_files:
                print(f"开始拆分文件: {file}")
                sdm.split_combined_data_by_date(file)
        else:
            print(f"错误: 目录 {args.dir} 不存在")
    else:
        # 使用默认路径
        default_file = os.path.join(sdm.base_dir, 'stock_data', 'stock_history_data.xlsx')
        if os.path.exists(default_file):
            print(f"使用默认文件: {default_file}")
            sdm.split_combined_data_by_date(default_file)
        else:
            print(f"错误: 默认文件 {default_file} 不存在")
            print("请使用 --file 或 --dir 参数指定要拆分的文件或目录")

if __name__ == "__main__":
    main()
