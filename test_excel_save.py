#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
sys.path.append('.')

import tech_strength_manager as tsm

def test_excel_save():
    """测试Excel保存前导0效果"""
    
    print("=== 测试Excel保存前导0效果 ===")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '股票代码': ['sh.600000', 'sz.000001', 'sh.600036'],
        '股票名称': ['浦发银行', '平安银行', '招商银行'],
        '行业': ['银行', '银行', '银行'],
        '当前价格': [10.5, 12.3, 45.6],
        '涨跌幅': [1.2, -0.5, 2.1],
        '技术强度': [70, 85, 60],
        '趋势': ['强势', '强势', '中性'],
        '目标价': [12.0, 14.0, 50.0],
        '止损价': [9.0, 11.0, 40.0],
        '跟踪止损': [8.5, 10.5, 38.0],
        '技术指标': ['均线多头', 'MACD金叉', 'RSI反弹'],
        '日期': ['2025-05-15', '2025-05-15', '2025-05-15'],
        '成交量是前一日几倍': [1.5, 2.0, 1.2],
        '连续技术强度5天数': [350, 425, 300],
        '连续技术强度3天数': [210, 255, 180],
        '连续技术强度10天数': [700, 850, 600],
        '技术指标特征': ['000001', '010111', '001010'],  # 测试前导0
        '买入日开盘涨跌幅': [0, 1, 0],
        '日内股票标记': [0, 1, 0],
        '卖出日开盘涨跌幅': [0, 1, 0],
        '趋势组合': ['000001', '010111', '001010']  # 测试前导0
    })
    
    print(f"测试数据:")
    for i, row in test_data.iterrows():
        print(f"  行{i+1}: 技术指标特征='{row['技术指标特征']}', 趋势组合='{row['趋势组合']}'")
    
    # 保存数据
    print(f"\n保存测试数据...")
    success = tsm.save_daily_tech_strength(test_data, '2025-05-15')
    
    if success:
        print(f"✅ 数据保存成功")
        
        # 读取数据验证
        print(f"\n读取数据验证...")
        loaded_data = tsm.load_daily_tech_strength('2025-05-15')
        
        if not loaded_data.empty:
            print(f"✅ 数据读取成功，行数: {len(loaded_data)}")
            
            print(f"\n验证前导0保存效果:")
            for i, row in loaded_data.iterrows():
                tech_feature = row['技术指标特征']
                trend_combo = row['趋势组合']
                
                print(f"  行{i+1}:")
                print(f"    技术指标特征: '{tech_feature}' (类型: {type(tech_feature)}, 长度: {len(str(tech_feature))})")
                print(f"    趋势组合: '{trend_combo}' (类型: {type(trend_combo)}, 长度: {len(str(trend_combo))})")
                
                # 检查前导0
                tech_str = str(tech_feature)
                trend_str = str(trend_combo)
                
                if tech_str.startswith('0') and len(tech_str) == 6:
                    print(f"    ✅ 技术指标特征前导0保留正确")
                else:
                    print(f"    ❌ 技术指标特征前导0丢失: {tech_str}")
                
                if trend_str.startswith('0') and len(trend_str) == 6:
                    print(f"    ✅ 趋势组合前导0保留正确")
                else:
                    print(f"    ❌ 趋势组合前导0丢失: {trend_str}")
            
            # 检查文件路径
            file_path = tsm.get_daily_tech_strength_path('2025-05-15')
            print(f"\n文件保存位置: {file_path}")
            
        else:
            print(f"❌ 数据读取失败")
    else:
        print(f"❌ 数据保存失败")
    
    print(f"\n✅ Excel保存测试完成")

if __name__ == "__main__":
    test_excel_save()
