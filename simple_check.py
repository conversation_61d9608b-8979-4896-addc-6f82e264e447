import pandas as pd

# 读取数据
df = pd.read_excel('E:/机器学习/complete_excel_results/tech_strength/daily/tech_strength_2025-05-15.xlsx', 
                  dtype={'股票代码': str, '技术指标特征': str, '趋势组合': str})

print("=== 最终数据检查 ===")
print(f"数据行数: {len(df)}")

# 检查样本
sample = df.iloc[0]
print(f"\n样本股票: {sample['股票代码']}")
print(f"技术指标特征: {sample['技术指标特征']} (类型: {type(sample['技术指标特征'])})")
print(f"趋势组合: {sample['趋势组合']} (类型: {type(sample['趋势组合'])})")
print(f"连续技术强度: 3天={sample['连续技术强度3天数']}, 5天={sample['连续技术强度5天数']}, 10天={sample['连续技术强度10天数']}")

# 检查字段类型
print(f"\n字段类型:")
print(f"技术指标特征: {df['技术指标特征'].dtype}")
print(f"趋势组合: {df['趋势组合'].dtype}")

# 检查多样性
print(f"\n数据多样性:")
print(f"技术指标特征唯一值: {df['技术指标特征'].nunique()}")
print(f"趋势组合唯一值: {df['趋势组合'].nunique()}")

# 检查连续技术强度
print(f"\n连续技术强度检查:")
for i in range(5):
    row = df.iloc[i]
    c3, c5, c10 = row['连续技术强度3天数'], row['连续技术强度5天数'], row['连续技术强度10天数']
    print(f"  {row['股票代码']}: 3天={c3}, 5天={c5}, 10天={c10}")

print("\n✅ 检查完成")
