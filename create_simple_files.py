#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建简单的Excel文件
作者: Augment AI
版本: 1.0.0

该脚本用于创建简单的Excel文件，测试文件创建功能。
"""

import os
import pandas as pd

def main():
    """主函数"""
    # 设置输出目录
    output_dir = "E:\\机器学习\\test_excel_files"
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")
    
    # 创建一个简单的Excel文件
    output_file = os.path.join(output_dir, "test.xlsx")
    
    # 创建一个简单的DataFrame
    df = pd.DataFrame({
        'A': [1, 2, 3],
        'B': [4, 5, 6]
    })
    
    # 保存到Excel文件
    df.to_excel(output_file, index=False)
    
    print(f"创建文件: {output_file}")
    
    # 检查文件是否存在
    if os.path.exists(output_file):
        print(f"文件创建成功: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file)} 字节")
    else:
        print(f"文件创建失败: {output_file}")

if __name__ == "__main__":
    main()
