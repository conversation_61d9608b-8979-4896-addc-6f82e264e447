#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前导0修复效果
"""

import pandas as pd

def test_leading_zeros():
    """测试前导0的格式化"""
    
    print("=== 测试前导0格式化 ===")
    
    # 测试技术指标特征格式化
    print("1. 测试技术指标特征格式化:")
    
    # 模拟不同的数值
    test_values = [0, 1, 5, 10, 32, 63]
    
    for value in test_values:
        formatted = format(int(value), '06d')
        print(f"  数值 {value} -> 格式化后: '{formatted}' (长度: {len(formatted)})")
    
    # 测试趋势组合格式化
    print("\n2. 测试趋势组合格式化:")
    
    # 模拟二进制组合
    test_combinations = [
        (0, 0, 0, 0, 0, 0),  # 000000
        (1, 0, 0, 0, 0, 1),  # 100001
        (0, 1, 0, 1, 1, 1),  # 010111
        (1, 1, 1, 1, 1, 1),  # 111111
    ]
    
    for combo in test_combinations:
        value = (combo[0] * 32 + combo[1] * 16 + combo[2] * 8 + 
                combo[3] * 4 + combo[4] * 2 + combo[5] * 1)
        formatted = format(int(value), '06d')
        binary_str = ''.join(map(str, combo))
        print(f"  二进制 {binary_str} (十进制 {value}) -> 格式化后: '{formatted}'")
    
    # 测试DataFrame应用
    print("\n3. 测试DataFrame应用:")
    
    # 创建测试数据
    test_df = pd.DataFrame({
        '技术指标_均线多头排列': [0, 1, 0, 1],
        '技术指标_MACD金叉': [0, 0, 1, 1],
        '技术指标_RSI反弹': [0, 0, 0, 1],
        '技术指标_KDJ金叉': [0, 0, 1, 0],
        '技术指标_布林带突破': [0, 1, 0, 1]
    })
    
    # 计算技术指标特征
    tech_feature_values = (test_df['技术指标_均线多头排列'] * 32 +
                          test_df['技术指标_MACD金叉'] * 16 +
                          test_df['技术指标_RSI反弹'] * 8 +
                          test_df['技术指标_KDJ金叉'] * 4 +
                          test_df['技术指标_布林带突破'] * 2 +
                          0 * 1)
    
    # 格式化为6位字符串
    test_df['技术指标特征'] = tech_feature_values.apply(lambda x: format(int(x), '06d'))
    
    print("技术指标特征计算结果:")
    for i, row in test_df.iterrows():
        binary = f"{row['技术指标_均线多头排列']}{row['技术指标_MACD金叉']}{row['技术指标_RSI反弹']}{row['技术指标_KDJ金叉']}{row['技术指标_布林带突破']}0"
        print(f"  行 {i}: 二进制 {binary} -> 技术指标特征: '{row['技术指标特征']}'")
    
    # 测试趋势组合
    print("\n4. 测试趋势组合计算:")
    
    test_trend_df = pd.DataFrame({
        '3天技术强度上升': [0, 1, 0, 1],
        '3天价格上升': [0, 0, 1, 1],
        '5天技术强度上升': [0, 0, 0, 1],
        '5天价格上升': [0, 0, 1, 0],
        '10天技术强度上升': [0, 1, 0, 1],
        '10天价格上升': [1, 0, 1, 1]
    })
    
    # 计算趋势组合
    trend_combo_values = (test_trend_df['3天技术强度上升'] * 32 +
                         test_trend_df['3天价格上升'] * 16 +
                         test_trend_df['5天技术强度上升'] * 8 +
                         test_trend_df['5天价格上升'] * 4 +
                         test_trend_df['10天技术强度上升'] * 2 +
                         test_trend_df['10天价格上升'] * 1)
    
    # 格式化为6位字符串
    test_trend_df['趋势组合'] = trend_combo_values.apply(lambda x: format(int(x), '06d'))
    
    print("趋势组合计算结果:")
    for i, row in test_trend_df.iterrows():
        binary = f"{row['3天技术强度上升']}{row['3天价格上升']}{row['5天技术强度上升']}{row['5天价格上升']}{row['10天技术强度上升']}{row['10天价格上升']}"
        print(f"  行 {i}: 二进制 {binary} -> 趋势组合: '{row['趋势组合']}'")
    
    print("\n✅ 前导0格式化测试完成")
    
    # 验证修复效果
    print("\n=== 验证修复效果 ===")
    
    # 检查是否有前导0
    tech_features = test_df['技术指标特征'].tolist()
    trend_combos = test_trend_df['趋势组合'].tolist()
    
    print(f"技术指标特征示例: {tech_features}")
    print(f"趋势组合示例: {trend_combos}")
    
    # 检查长度
    tech_lengths = [len(x) for x in tech_features]
    trend_lengths = [len(x) for x in trend_combos]
    
    print(f"技术指标特征长度都是6位: {all(l == 6 for l in tech_lengths)}")
    print(f"趋势组合长度都是6位: {all(l == 6 for l in trend_lengths)}")
    
    # 检查前导0
    has_leading_zero_tech = any(x.startswith('0') for x in tech_features)
    has_leading_zero_trend = any(x.startswith('0') for x in trend_combos)
    
    print(f"技术指标特征有前导0: {has_leading_zero_tech}")
    print(f"趋势组合有前导0: {has_leading_zero_trend}")
    
    if has_leading_zero_tech and has_leading_zero_trend:
        print("✅ 前导0修复成功！")
    else:
        print("❌ 前导0修复可能有问题")

if __name__ == "__main__":
    test_leading_zeros()
