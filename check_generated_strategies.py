import pandas as pd

# 读取生成的特征组合文件
file_path = r"E:\机器学习\complete_excel_results\特征组合_含成交量比_new.xlsx"
df = pd.read_excel(file_path)

# 查看前几个策略
print("生成的策略示例:")
for i in range(5):
    print(f"策略编号: {df.iloc[i]['策略编号']}")
    print(f"策略组合: {df.iloc[i]['策略组合']}")
    print(f"策略代码: {df.iloc[i]['策略代码']}")
    print(f"策略条件描述: {df.iloc[i]['策略条件描述']}")
    print("-" * 50)

# 统计特征数量
feature_counts = df['特征数量'].value_counts().sort_index()
print("\n特征数量统计:")
for count, num in feature_counts.items():
    print(f"{count}个特征的组合数: {num}")

# 检查是否所有组合都包含"成交量是前一日几倍"特征
contains_volume_ratio = df['策略组合'].str.contains('成交量是前一日几倍').all()
print(f"\n所有组合是否都包含'成交量是前一日几倍'特征: {contains_volume_ratio}")

# 随机抽取一些不同特征数量的组合进行检查
print("\n随机抽取的不同特征数量的组合:")
for count in range(3, 8):
    sample = df[df['特征数量'] == count].sample(1).iloc[0]
    print(f"\n{count}个特征的组合示例:")
    print(f"策略编号: {sample['策略编号']}")
    print(f"策略组合: {sample['策略组合']}")
    print(f"策略代码: {sample['策略代码']}")
    print(f"策略条件描述: {sample['策略条件描述']}")
