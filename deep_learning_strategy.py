"""
深度学习策略 - 股票高胜率策略

使用深度学习模型预测股票涨跌，实现高胜率策略。
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import joblib
try:
    import tensorflow as tf
    from tensorflow.keras.models import load_model
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
from sklearn.preprocessing import StandardScaler

def print_header(title):
    """打印标题"""
    print("\n" + " " * 24 + title)
    print("=" * 80 + "\n")

def load_model_data(model_data_path):
    """加载模型数据"""
    print(f"加载模型数据: {model_data_path}")
    try:
        model_data = joblib.load(model_data_path)
        print(f"成功加载模型数据")
        return model_data
    except Exception as e:
        print(f"加载模型数据失败: {e}")
        return None

def load_deep_learning_model(model_data):
    """加载深度学习模型"""
    # 如果模型数据中直接包含模型对象，直接使用
    if 'model' in model_data:
        print("使用模型数据中的模型对象")
        return model_data['model']

    # 否则，尝试从文件加载模型
    model_path = model_data['model_path']
    model_type = model_data['model_type']

    print(f"从文件加载模型: {model_path}")
    try:
        if model_type == 'keras':
            if not TENSORFLOW_AVAILABLE:
                print("TensorFlow不可用，无法加载Keras模型")
                return None
            model = load_model(model_path)
            print(f"成功加载Keras模型")
        else:
            model = joblib.load(model_path)
            print(f"成功加载scikit-learn模型")
        return model
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None

def preprocess_features(data, features, scaler):
    """预处理特征"""
    # 确保所有特征都存在
    for feature in features:
        if feature not in data.columns:
            if feature == '技术强度变化率':
                # 计算技术强度变化率
                if '技术强度' in data.columns:
                    # 按股票代码分组
                    grouped = data.sort_values(['股票代码', '日期']).groupby('股票代码')

                    # 初始化技术强度变化率列
                    data['技术强度变化率'] = 0.0

                    # 对每个股票计算技术强度变化率
                    for name, group in grouped:
                        # 确保数据按日期排序
                        group = group.sort_values('日期')

                        # 获取技术强度列
                        strength = group['技术强度'].values

                        # 计算技术强度变化率
                        for i in range(1, len(group)):
                            if strength[i-1] != 0:
                                change_rate = (strength[i] - strength[i-1]) / strength[i-1]
                            else:
                                change_rate = 0
                            data.loc[group.index[i], '技术强度变化率'] = change_rate

            elif feature == '连续技术强度变化率':
                # 计算连续技术强度变化率
                if '连续技术强度5天数' in data.columns:
                    # 按股票代码分组
                    grouped = data.sort_values(['股票代码', '日期']).groupby('股票代码')

                    # 初始化连续技术强度变化率列
                    data['连续技术强度变化率'] = 0.0

                    # 对每个股票计算连续技术强度变化率
                    for name, group in grouped:
                        # 确保数据按日期排序
                        group = group.sort_values('日期')

                        # 获取连续技术强度列
                        strength = group['连续技术强度5天数'].values

                        # 计算连续技术强度变化率
                        for i in range(1, len(group)):
                            if strength[i-1] != 0:
                                change_rate = (strength[i] - strength[i-1]) / strength[i-1]
                            else:
                                change_rate = 0
                            data.loc[group.index[i], '连续技术强度变化率'] = change_rate

            elif feature == '看涨技术指标占比':
                # 计算看涨技术指标占比
                if '看涨技术指标数量' in data.columns:
                    data['看涨技术指标占比'] = data['看涨技术指标数量'] / 5  # 总共5个技术指标

            elif feature == '均线多头_MACD金叉':
                # 计算均线多头_MACD金叉
                if all(col in data.columns for col in ['技术指标_均线多头排列', '技术指标_MACD金叉']):
                    data['均线多头_MACD金叉'] = data['技术指标_均线多头排列'] & data['技术指标_MACD金叉']

            elif feature == 'RSI反弹_KDJ金叉':
                # 计算RSI反弹_KDJ金叉
                if all(col in data.columns for col in ['技术指标_RSI反弹', '技术指标_KDJ金叉']):
                    data['RSI反弹_KDJ金叉'] = data['技术指标_RSI反弹'] & data['技术指标_KDJ金叉']

            elif feature == '历史胜率':
                # 计算历史胜率
                if all(col in data.columns for col in ['股票代码', '日期', '次日买后日卖收益率']):
                    # 按股票代码分组
                    grouped = data.sort_values(['股票代码', '日期']).groupby('股票代码')

                    # 初始化历史胜率列
                    data['历史胜率'] = 0.5  # 默认为50%

                    # 对每个股票计算历史胜率
                    for name, group in grouped:
                        # 确保数据按日期排序
                        group = group.sort_values('日期')

                        # 获取收益率列
                        returns = group['次日买后日卖收益率'].values

                        # 计算历史胜率
                        for i in range(1, len(group)):
                            if i < 5:  # 不足5天的情况
                                win_rate = np.mean(returns[:i] > 0) if i > 0 else 0.5
                            else:  # 5天及以上的情况
                                win_rate = np.mean(returns[i-5:i] > 0)
                            data.loc[group.index[i], '历史胜率'] = win_rate

            elif feature == '星期几':
                # 计算星期几
                if '日期' in data.columns:
                    data['星期几'] = pd.to_datetime(data['日期']).dt.dayofweek

            elif feature == '月份':
                # 计算月份
                if '日期' in data.columns:
                    data['月份'] = pd.to_datetime(data['日期']).dt.month

            else:
                # 对于其他缺失的特征，填充为0
                data[feature] = 0

    # 提取特征
    X = data[features]

    # 处理缺失值
    X = X.fillna(0)

    # 标准化特征
    X_scaled = scaler.transform(X)

    return X_scaled

def calibrate_probabilities(predictions):
    """
    校准预测概率

    使用等分箱法校准预测概率，使其更接近实际概率
    """
    if '预测盈利概率' not in predictions.columns or '实际是否盈利' not in predictions.columns:
        print("警告: 缺少校准所需的列，无法校准概率")
        return predictions

    # 复制预测结果
    calibrated_predictions = predictions.copy()

    # 将预测概率分成10个等分箱
    bins = np.linspace(0, 1, 11)
    calibrated_predictions['概率分箱'] = pd.cut(calibrated_predictions['预测盈利概率'], bins)

    # 计算每个分箱中的实际盈利率
    bin_stats = calibrated_predictions.groupby('概率分箱')['实际是否盈利'].agg(['mean', 'count']).reset_index()
    bin_stats.columns = ['概率分箱', '实际盈利率', '样本数']

    # 创建映射字典
    prob_map = dict(zip(bin_stats['概率分箱'], bin_stats['实际盈利率']))

    # 应用映射，校准预测概率
    calibrated_predictions['校准后概率'] = calibrated_predictions['概率分箱'].map(prob_map)

    # 处理缺失值
    calibrated_predictions['校准后概率'] = calibrated_predictions['校准后概率'].fillna(
        calibrated_predictions['预测盈利概率']
    )

    # 打印校准结果
    print("\n概率校准结果:")
    for i, row in bin_stats.iterrows():
        print(f"预测概率区间: {row['概率分箱']}, 实际盈利率: {row['实际盈利率']:.4f}, 样本数: {row['样本数']}")

    return calibrated_predictions

def apply_deep_learning_strategy(predictions):
    """
    深度学习高胜率策略（放宽条件版）

    条件：
    1. 校准后概率>0.7 (或预测盈利概率>0.9)
    2. 技术强度>=80
    3. 连续技术强度5天数≥400
    4. 看涨技术指标数量≥4
    5. 技术指标_均线多头排列=1
    6. 技术指标_MACD金叉=1
    7. 技术指标_RSI反弹=1
    8. 技术指标_KDJ金叉=1
    9. 排除特定股票（指南针、西昌电力、尖峰集团）
    """
    # 校准预测概率
    if '实际是否盈利' in predictions.columns and len(predictions) > 100:
        # 只有在有足够样本时才进行校准
        calibrated_predictions = calibrate_probabilities(predictions)
    else:
        calibrated_predictions = predictions.copy()
        calibrated_predictions['校准后概率'] = predictions['预测盈利概率']

    # 基本条件
    base_condition = (
        (
            (calibrated_predictions['校准后概率'] > 0.6) if '校准后概率' in calibrated_predictions.columns
            else (calibrated_predictions['预测盈利概率'] > 0.8)
        ) &  # 条件1: 校准后概率>0.6或预测盈利概率>0.8
        (calibrated_predictions['技术强度'] >= 70) &  # 条件2: 技术强度>=70
        (calibrated_predictions['连续技术强度5天数'] >= 350) &  # 条件3: 连续技术强度5天数≥350
        (calibrated_predictions['看涨技术指标数量'] >= 3) &  # 条件4: 看涨技术指标数量≥3
        (calibrated_predictions['股票代码'] != 'sz.300803') &  # 条件9: 排除指南针股票
        (calibrated_predictions['股票代码'] != 'sh.600505') &  # 条件9: 排除西昌电力股票
        (calibrated_predictions['股票代码'] != 'sh.600668')  # 条件9: 排除尖峰集团股票
    )

    # 技术指标条件
    tech_condition = pd.Series(True, index=predictions.index)

    # 条件5: 技术指标_均线多头排列=1
    if '技术指标_均线多头排列' in predictions.columns:
        tech_condition = tech_condition & (predictions['技术指标_均线多头排列'] == 1)

    # 条件6: 技术指标_MACD金叉=1
    if '技术指标_MACD金叉' in predictions.columns:
        tech_condition = tech_condition & (predictions['技术指标_MACD金叉'] == 1)

    # 条件7: 技术指标_RSI反弹=1
    if '技术指标_RSI反弹' in predictions.columns:
        tech_condition = tech_condition & (predictions['技术指标_RSI反弹'] == 1)

    # 条件8: 技术指标_KDJ金叉=1
    if '技术指标_KDJ金叉' in predictions.columns:
        tech_condition = tech_condition & (predictions['技术指标_KDJ金叉'] == 1)

    # 额外的条件
    extra_condition = pd.Series(True, index=predictions.index)

    # 价格趋势为上升
    if '价格趋势' in predictions.columns:
        extra_condition = extra_condition & (predictions['价格趋势'] == 1)

    # 涨跌幅趋势为上升
    if '涨跌幅趋势' in predictions.columns:
        extra_condition = extra_condition & (predictions['涨跌幅趋势'] == 1)

    # 技术强度趋势为上升
    if '技术强度趋势' in predictions.columns:
        extra_condition = extra_condition & (predictions['技术强度趋势'] == 1)

    # 当前涨跌幅为正
    if '涨跌幅' in predictions.columns:
        extra_condition = extra_condition & (predictions['涨跌幅'] > 0)

    # 组合所有条件
    # 注意：我们使用基本条件和技术指标条件，但不使用额外的条件
    # 因为额外的条件可能会过于严格，导致没有股票被推荐
    strategy_stocks = predictions[base_condition & tech_condition]

    # 如果没有股票满足条件，尝试放宽条件
    if len(strategy_stocks) == 0:
        print("没有股票满足所有条件，尝试放宽条件...")
        strategy_stocks = predictions[base_condition]

    # 按预测盈利概率降序排序
    strategy_stocks = strategy_stocks.sort_values('预测盈利概率', ascending=False)

    return strategy_stocks

def backtest_deep_learning_strategy(data, model_data, start_date_str, end_date_str, output_file=None):
    """回测深度学习高胜率策略在指定日期范围内的表现"""
    print_header("回测深度学习高胜率策略")

    # 创建结果字符串，用于输出到TXT文档
    result_str = "股票深度学习高胜率策略分析结果\n"
    result_str += "=" * 50 + "\n\n"
    result_str += "分析日期: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\n\n"
    result_str += "一、策略说明\n"
    result_str += "-" * 50 + "\n"
    result_str += "深度学习高胜率策略条件：\n"
    result_str += "1. 预测盈利概率>95%\n"
    result_str += "2. 技术强度=85\n"
    result_str += "3. 连续技术强度5天数≥440且≤445\n"
    result_str += "4. 看涨技术指标数量≥4\n"
    result_str += "5. 技术指标_均线多头排列=1\n"
    result_str += "6. 技术指标_MACD金叉=1\n"
    result_str += "7. 历史胜率>0.6\n"
    result_str += "8. 排除特定股票（指南针、西昌电力、尖峰集团）\n\n"

    # 获取模型信息
    scaler = model_data['scaler']
    features = model_data['features']

    # 加载深度学习模型
    model = load_deep_learning_model(model_data)
    if model is None:
        error_msg = f"加载模型失败"
        print(error_msg)
        result_str += "错误: " + error_msg + "\n"
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result_str)
        return result_str

    model_type = model_data['model_type']

    result_str += f"模型准确率: {model_data['accuracy']:.4f}\n"
    result_str += f"模型精确率: {model_data['precision']:.4f}\n"
    result_str += f"模型召回率: {model_data['recall']:.4f}\n"
    result_str += f"模型F1分数: {model_data['f1']:.4f}\n"
    result_str += f"模型特征: {', '.join(features)}\n\n"

    # 转换日期
    start_date = pd.to_datetime(start_date_str)
    end_date = pd.to_datetime(end_date_str)

    # 获取日期范围内的所有日期
    date_range = data['日期'].unique()
    date_range = sorted([d for d in date_range if start_date <= d <= end_date])

    if not date_range:
        error_msg = f"在{start_date_str}至{end_date_str}之间没有找到数据"
        print(error_msg)
        result_str += "错误: " + error_msg + "\n"
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result_str)
        return result_str

    print(f"回测日期范围: {date_range[0].strftime('%Y-%m-%d')} 至 {date_range[-1].strftime('%Y-%m-%d')}")
    print(f"共 {len(date_range)} 个交易日")

    result_str += "二、回测信息\n"
    result_str += "-" * 50 + "\n"
    result_str += f"回测日期范围: {date_range[0].strftime('%Y-%m-%d')} 至 {date_range[-1].strftime('%Y-%m-%d')}\n"
    result_str += f"交易日数量: {len(date_range)}\n\n"

    # 创建结果DataFrame
    results = []
    all_recommended_stocks = []

    # 对每个日期进行回测
    result_str += "三、回测结果\n"
    result_str += "-" * 50 + "\n"

    for test_date in date_range[:-2]:  # 排除最后两天，因为需要计算后日收益率
        test_date_str = test_date.strftime('%Y-%m-%d')
        print(f"\n回测日期: {test_date_str}")
        result_str += f"\n日期: {test_date_str}\n"

        # 获取当天的数据
        current_data = data[data['日期'] == test_date]

        # 准备预测数据
        try:
            # 预处理特征
            X_pred_scaled = preprocess_features(current_data, features, scaler)

            # 预测盈利概率
            if model_type == 'keras':
                pred_proba = model.predict(X_pred_scaled)
            else:
                pred_proba = model.predict_proba(X_pred_scaled)[:, 1].reshape(-1, 1)

            # 创建预测结果DataFrame
            # 使用current_data作为基础，添加预测结果
            predictions = current_data.copy()
            predictions['日期'] = test_date_str
            predictions['预测盈利概率'] = pred_proba.flatten()

            # 添加历史胜率
            if '历史胜率' not in predictions.columns:
                predictions['历史胜率'] = 0.5

            # 添加次日买后日卖收益率
            if '次日买后日卖收益率' not in predictions.columns:
                # 如果没有次日买后日卖收益率，使用涨跌幅作为替代
                if '涨跌幅' in predictions.columns:
                    # 获取下一个交易日的数据
                    next_date = date_range[date_range.index(test_date) + 1] if date_range.index(test_date) + 1 < len(date_range) else None
                    if next_date:
                        next_data = data[data['日期'] == next_date]
                        # 合并当前数据和下一个交易日的数据
                        merged_data = pd.merge(predictions, next_data, on='股票代码', suffixes=('', '_next'))
                        if len(merged_data) > 0 and '涨跌幅_next' in merged_data.columns:
                            # 创建映射
                            next_day_returns = dict(zip(merged_data['股票代码'], merged_data['涨跌幅_next']))
                            # 应用映射
                            predictions['次日买后日卖收益率'] = predictions['股票代码'].map(next_day_returns).fillna(0)
                        else:
                            predictions['次日买后日卖收益率'] = 0
                    else:
                        predictions['次日买后日卖收益率'] = 0
                else:
                    predictions['次日买后日卖收益率'] = 0

            # 添加实际是否盈利
            predictions['实际是否盈利'] = (predictions['次日买后日卖收益率'] > 0).astype(int)

            # 应用深度学习高胜率策略
            strategy_stocks = apply_deep_learning_strategy(predictions)

            # 计算胜率和平均收益率
            if len(strategy_stocks) > 0:
                # 计算实际是否盈利
                strategy_stocks['实际是否盈利'] = (strategy_stocks['次日买后日卖收益率'] > 0).astype(int)

                # 只考虑开盘上涨的股票
                open_up_stocks = strategy_stocks[strategy_stocks['开盘涨跌'] > 0]

                if len(open_up_stocks) > 0:
                    win_rate_open_up = open_up_stocks['实际是否盈利'].mean() * 100
                    avg_return_open_up = open_up_stocks['次日买后日卖收益率'].mean() * 100

                    print(f"推荐股票数: {len(strategy_stocks)}")
                    print(f"开盘上涨的股票数: {len(open_up_stocks)}")
                    print(f"开盘上涨股票的胜率: {win_rate_open_up:.2f}%")
                    print(f"开盘上涨股票的平均收益率: {avg_return_open_up:.2f}%")

                    result_str += f"推荐股票数: {len(strategy_stocks)}\n"
                    result_str += f"开盘上涨的股票数: {len(open_up_stocks)}\n"
                    result_str += f"开盘上涨股票的胜率: {win_rate_open_up:.2f}%\n"
                    result_str += f"开盘上涨股票的平均收益率: {avg_return_open_up:.2f}%\n\n"

                    # 添加到结果
                    results.append({
                        '日期': test_date_str,
                        '推荐股票数': len(strategy_stocks),
                        '开盘上涨的股票数': len(open_up_stocks),
                        '开盘上涨股票的胜率': win_rate_open_up,
                        '开盘上涨股票的平均收益率': avg_return_open_up
                    })

                    # 保存推荐股票及其结果
                    all_recommended_stocks.append(open_up_stocks)

                    # 显示所有盈利的股票的详细信息
                    profitable_stocks = open_up_stocks[open_up_stocks['实际是否盈利'] == 1]
                    if len(profitable_stocks) > 0:
                        print(f"\n{test_date_str} 盈利的股票:")
                        result_str += f"盈利的股票:\n"
                        for i, row in profitable_stocks.iterrows():
                            stock_info = f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 看涨技术指标数量={row['看涨技术指标数量']}, 历史胜率={row['历史胜率']:.2f}, 预测盈利概率={row['预测盈利概率']*100:.2f}%, 实际收益率={row['次日买后日卖收益率']*100:.2f}%"
                            print(stock_info)
                            result_str += stock_info + "\n"
                        result_str += "\n"

                    # 显示所有亏损的股票的详细信息
                    losing_stocks = open_up_stocks[open_up_stocks['实际是否盈利'] == 0]
                    if len(losing_stocks) > 0:
                        print(f"\n{test_date_str} 亏损的股票:")
                        result_str += f"亏损的股票:\n"
                        for i, row in losing_stocks.iterrows():
                            stock_info = f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 看涨技术指标数量={row['看涨技术指标数量']}, 历史胜率={row['历史胜率']:.2f}, 预测盈利概率={row['预测盈利概率']*100:.2f}%, 实际收益率={row['次日买后日卖收益率']*100:.2f}%"
                            print(stock_info)
                            result_str += stock_info + "\n"
                        result_str += "\n"
                else:
                    print(f"推荐股票数: {len(strategy_stocks)}")
                    print("没有开盘上涨的股票")
                    result_str += f"推荐股票数: {len(strategy_stocks)}\n"
                    result_str += "没有开盘上涨的股票\n\n"
            else:
                print("没有推荐的股票")
                result_str += "没有推荐的股票\n\n"

        except Exception as e:
            error_msg = f"回测失败: {e}"
            print(error_msg)
            result_str += "错误: " + error_msg + "\n\n"
            import traceback
            traceback.print_exc()

    # 创建结果DataFrame
    if results:
        results_df = pd.DataFrame(results)

        # 计算整体表现
        overall_win_rate = results_df['开盘上涨股票的胜率'].mean()
        overall_return = results_df['开盘上涨股票的平均收益率'].mean()
        total_stocks = results_df['推荐股票数'].sum()
        total_open_up_stocks = results_df['开盘上涨的股票数'].sum()

        print("\n整体表现:")
        print(f"总推荐股票数: {total_stocks}")
        print(f"总开盘上涨的股票数: {total_open_up_stocks}")
        print(f"开盘上涨股票的平均胜率: {overall_win_rate:.2f}%")
        print(f"开盘上涨股票的平均收益率: {overall_return:.2f}%")

        result_str += "四、整体表现\n"
        result_str += "-" * 50 + "\n"
        result_str += f"总推荐股票数: {total_stocks}\n"
        result_str += f"总开盘上涨的股票数: {total_open_up_stocks}\n"
        result_str += f"开盘上涨股票的平均胜率: {overall_win_rate:.2f}%\n"
        result_str += f"开盘上涨股票的平均收益率: {overall_return:.2f}%\n\n"

        # 保存结果
        if not os.path.exists('backtest_results'):
            os.makedirs('backtest_results')

        # 保存回测结果
        result_file = f'backtest_results/深度学习高胜率策略回测结果_{start_date_str}至{end_date_str}.xlsx'
        results_df.to_excel(result_file, index=False)

        # 保存所有推荐股票及其结果
        if all_recommended_stocks:
            all_stocks_df = pd.concat(all_recommended_stocks)
            all_stocks_file = f'backtest_results/深度学习高胜率策略推荐股票_{start_date_str}至{end_date_str}.xlsx'
            all_stocks_df.to_excel(all_stocks_file, index=False)

        print(f"\n回测结果已保存至: {result_file}")
        result_str += f"回测结果已保存至: {result_file}\n\n"
    else:
        print("\n没有回测结果")
        result_str += "没有回测结果\n"

    # 输出结果到TXT文档
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result_str)
        print(f"\n分析结果已保存至: {output_file}")

    return result_str
