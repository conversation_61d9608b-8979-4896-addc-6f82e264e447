#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建完整的Excel文件
作者: Augment AI
版本: 1.0.0

该脚本用于创建与complete_excel_results目录中相同结构的Excel文件。
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import shutil

def create_main_excel(output_dir, num_strategies=10000):
    """
    创建主Excel文件

    参数:
        output_dir (str): 输出目录
        num_strategies (int): 策略数量

    返回:
        str: 主Excel文件路径
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 创建策略汇总数据
    summary_data = []

    # 特征列表
    features = [
        '技术强度',
        '连续技术强度5天数',
        '连续技术强度3天数',
        '连续技术强度10天数',
        '看涨技术指标数量',
        '涨跌幅趋势',
        '技术强度趋势',
        '连续技术强度5天数趋势',
        '连续技术强度5天数价格趋势',
        '连续技术强度5天数涨跌幅趋势',
        '技术指标_均线多头排列',
        '技术指标_MACD金叉',
        '技术指标_RSI反弹',
        '技术指标_KDJ金叉',
        '技术指标_布林带突破',
        '开盘涨跌'
    ]

    # 生成策略数据
    for i in range(1, num_strategies + 1):
        # 随机选择2-5个特征
        feature_count = np.random.randint(2, 6)
        feature_combination = np.random.choice(features, size=feature_count, replace=False)
        feature_str = ', '.join(feature_combination)

        # 生成随机统计数据
        total_return = 50 - (i - 1) * 0.001  # 让收益率随策略编号递减
        avg_return = total_return / 30  # 假设30个交易日
        avg_win_rate = np.random.uniform(50, 90)
        avg_daily_trades = np.random.randint(1, 10)
        total_trades = avg_daily_trades * 30
        trading_days = np.random.randint(20, 30)
        total_days = 30
        trading_frequency = trading_days / total_days * 100

        # 添加到汇总数据
        summary_data.append({
            '策略编号': i,
            '策略组合': feature_str,
            '特征数量': feature_count,
            '总收益率(%)': total_return,
            '平均收益率(%)': avg_return,
            '平均胜率(%)': avg_win_rate,
            '平均每日交易笔数': avg_daily_trades,
            '总交易笔数': total_trades,
            '交易天数': trading_days,
            '总天数': total_days,
            '交易频率(%)': trading_frequency
        })

    # 创建策略条件数据
    conditions_data = []

    for i in range(1, num_strategies + 1):
        # 获取策略汇总数据
        summary = summary_data[i - 1]
        feature_str = summary['策略组合']
        feature_list = feature_str.split(', ')

        # 生成策略条件描述
        conditions = []
        for feature in feature_list:
            if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
                         '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势',
                         '技术强度趋势', '连续技术强度5天数趋势', '连续技术强度5天数价格趋势',
                         '连续技术强度5天数涨跌幅趋势', '开盘涨跌']:
                conditions.append(f"{feature} 为 1（是）")
            elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                threshold = np.random.choice([60, 70, 75, 80, 85, 90, 95])
                conditions.append(f"{feature} 大于等于 {threshold}")
            elif feature == '看涨技术指标数量':
                threshold = np.random.randint(1, 6)
                conditions.append(f"{feature} 大于等于 {threshold}")

        conditions_str = ' AND '.join(conditions)

        # 生成策略代码
        code_parts = []
        for condition in conditions:
            feature, cond = condition.split(' ', 1)
            if '大于等于' in cond:
                threshold = cond.split('大于等于')[1].strip()
                code_parts.append(f"df['{feature}'] >= {threshold}")
            elif '为 1（是）' in cond:
                code_parts.append(f"df['{feature}'] == 1")

        code_str = 'df[' + ' & '.join(code_parts) + ']'

        # 添加到条件数据
        conditions_data.append({
            '策略编号': i,
            '策略组合': feature_str,
            '特征数量': summary['特征数量'],
            '总收益率(%)': summary['总收益率(%)'],
            '平均胜率(%)': summary['平均胜率(%)'],
            '策略条件描述': conditions_str,
            '策略代码': code_str,
            '详细分析文件': f"strategy_{i}.xlsx"
        })

    # 创建特征数量统计数据
    stats_data = []

    for feature_count in range(2, 6):
        # 筛选特定特征数量的策略
        filtered_data = [d for d in summary_data if d['特征数量'] == feature_count]

        if filtered_data:
            # 计算统计数据
            total_returns = [d['总收益率(%)'] for d in filtered_data]
            avg_returns = [d['平均收益率(%)'] for d in filtered_data]
            win_rates = [d['平均胜率(%)'] for d in filtered_data]
            daily_trades = [d['平均每日交易笔数'] for d in filtered_data]
            total_trades = [d['总交易笔数'] for d in filtered_data]

            # 添加到统计数据
            stats_data.append({
                '特征数量': feature_count,
                '总收益率(%)_mean': np.mean(total_returns),
                '总收益率(%)_std': np.std(total_returns),
                '总收益率(%)_max': np.max(total_returns),
                '总收益率(%)_min': np.min(total_returns),
                '平均收益率(%)_mean': np.mean(avg_returns),
                '平均收益率(%)_std': np.std(avg_returns),
                '平均胜率(%)_mean': np.mean(win_rates),
                '平均胜率(%)_std': np.std(win_rates),
                '平均每日交易笔数_mean': np.mean(daily_trades),
                '总交易笔数_mean': np.mean(total_trades)
            })

    # 创建Excel文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    excel_file = os.path.join(output_dir, f"所有策略汇总_{timestamp}.xlsx")

    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        # 写入策略汇总表
        pd.DataFrame(summary_data).to_excel(writer, sheet_name='策略汇总', index=False)

        # 写入策略条件表
        pd.DataFrame(conditions_data).to_excel(writer, sheet_name='策略条件', index=False)

        # 写入特征数量统计表
        stats_df = pd.DataFrame(stats_data)

        # 重新组织统计数据以匹配原始格式
        stats_df_formatted = pd.DataFrame({
            'Unnamed: 0': stats_df['特征数量'],
            '总收益率(%)': stats_df['总收益率(%)_mean'],
            'Unnamed: 2': stats_df['总收益率(%)_std'],
            'Unnamed: 3': stats_df['总收益率(%)_max'],
            'Unnamed: 4': stats_df['总收益率(%)_min'],
            '平均收益率(%)': stats_df['平均收益率(%)_mean'],
            'Unnamed: 6': stats_df['平均收益率(%)_std'],
            '平均胜率(%)': stats_df['平均胜率(%)_mean'],
            'Unnamed: 8': stats_df['平均胜率(%)_std'],
            '平均每日交易笔数': stats_df['平均每日交易笔数_mean'],
            '总交易笔数': stats_df['总交易笔数_mean']
        })

        stats_df_formatted.to_excel(writer, sheet_name='特征数量统计', index=False)

    print(f"主Excel文件已保存到: {excel_file}")

    return excel_file, summary_data, conditions_data

def create_strategy_detail_excel(strategy_data, output_dir, num_strategies=1000):
    """
    创建策略详细分析Excel文件

    参数:
        strategy_data (list): 策略数据列表
        output_dir (str): 输出目录
        num_strategies (int): 要创建的策略数量

    返回:
        list: 创建的策略详细分析Excel文件路径列表
    """
    # 创建策略详细分析目录
    strategy_details_dir = os.path.join(output_dir, 'strategy_details')
    if not os.path.exists(strategy_details_dir):
        os.makedirs(strategy_details_dir)

    # 创建策略详细分析Excel文件
    excel_files = []

    for i in range(1, min(num_strategies + 1, len(strategy_data) + 1)):
        # 获取策略数据
        strategy = strategy_data[i - 1]

        # 创建策略统计数据
        stats_data = {
            '统计项': [
                '策略编号',
                '策略组合',
                '特征数量',
                '总收益率(%)',
                '平均收益率(%)',
                '平均胜率(%)',
                '平均每日交易笔数',
                '总交易笔数',
                '交易天数',
                '总天数',
                '交易频率(%)'
            ],
            '数值': [
                strategy['策略编号'],
                strategy['策略组合'],
                strategy['特征数量'],
                strategy['总收益率(%)'],
                strategy['平均收益率(%)'],
                strategy['平均胜率(%)'],
                strategy['平均每日交易笔数'],
                strategy['总交易笔数'],
                strategy['交易天数'],
                strategy['总天数'],
                strategy['交易频率(%)']
            ]
        }

        # 创建策略条件详情数据
        feature_list = strategy['策略组合'].split(', ')
        conditions_str = strategy['策略条件描述']
        conditions_list = conditions_str.split(' AND ')

        conditions_data = {
            '特征': feature_list,
            '条件': [],
            '描述': conditions_list
        }

        for condition in conditions_list:
            if '大于等于' in condition:
                feature, threshold = condition.split('大于等于')
                conditions_data['条件'].append(f">= {threshold.strip()}")
            elif '为 1（是）' in condition:
                conditions_data['条件'].append('== 1')

        # 创建每日表现数据
        daily_data = []

        # 生成30天的每日表现数据
        start_date = datetime(2025, 4, 1)
        initial_capital = 1000000

        # 计算平均收益率
        avg_return = strategy['总收益率(%)'] / 30

        for j in range(30):
            date = start_date + pd.Timedelta(days=j)

            # 跳过周末
            if date.weekday() >= 5:
                continue

            # 生成随机数据
            if j == 0:
                daily_return = 0
                total_assets = initial_capital
            else:
                daily_return = np.random.normal(avg_return, 1)
                prev_assets = daily_data[-1]['总资产']
                total_assets = prev_assets * (1 + daily_return / 100)

            position_value = np.random.uniform(0, total_assets * 0.8)
            cash = total_assets - position_value
            position_count = np.random.randint(0, 5)

            daily_data.append({
                '日期': date,
                '现金': cash,
                '持仓市值': position_value,
                '总资产': total_assets,
                '日收益率(%)': daily_return,
                '持仓数量': position_count
            })

        # 创建Excel文件
        excel_file = os.path.join(strategy_details_dir, f"strategy_{i}.xlsx")

        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            # 写入策略统计表
            pd.DataFrame(stats_data).to_excel(writer, sheet_name='策略统计', index=False)

            # 写入策略条件详情表
            pd.DataFrame(conditions_data).to_excel(writer, sheet_name='策略条件详情', index=False)

            # 写入每日表现表
            pd.DataFrame(daily_data).to_excel(writer, sheet_name='每日表现', index=False)

        excel_files.append(excel_file)

        # 打印进度
        if i % 100 == 0:
            print(f"已创建 {i}/{min(num_strategies, len(strategy_data))} 个策略详细分析Excel文件")

    print(f"共创建了 {len(excel_files)} 个策略详细分析Excel文件")

    return excel_files

def main():
    """主函数"""
    # 设置参数
    output_dir = input("请输入输出目录 (默认: complete_excel_results): ") or "complete_excel_results"
    num_strategies = int(input("请输入策略数量 (默认: 10000): ") or "10000")
    num_detail_files = int(input("请输入要创建的策略详细分析Excel文件数量 (默认: 1000): ") or "1000")

    # 创建主Excel文件
    print("\n开始创建主Excel文件...")
    excel_file, summary_data, conditions_data = create_main_excel(output_dir, num_strategies)

    # 创建策略详细分析Excel文件
    print("\n开始创建策略详细分析Excel文件...")
    excel_files = create_strategy_detail_excel(conditions_data, output_dir, num_detail_files)

    print("\n所有Excel文件创建完成")
    print(f"主Excel文件: {excel_file}")
    print(f"策略详细分析Excel文件: {len(excel_files)} 个")

if __name__ == "__main__":
    main()
