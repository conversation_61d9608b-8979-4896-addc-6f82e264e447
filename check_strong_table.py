#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def check_strong_table():
    """检查强势股表格的实际内容"""
    
    print("=== 检查强势股表格内容 ===")
    
    file_path = 'E:/机器学习/complete_excel_results/tech_strength/daily/strong/tech_strength_strong_2025-05-15.xlsx'
    
    try:
        # 读取数据，不指定dtype，看看实际的数据类型
        df = pd.read_excel(file_path)
        
        print(f"强势股数据行数: {len(df)}")
        print(f"强势股数据列数: {len(df.columns)}")
        print(f"列名: {df.columns.tolist()}")
        
        # 检查前10行的技术指标特征和趋势组合
        print(f"\n=== 前10行数据详细检查 ===")
        for i in range(min(10, len(df))):
            row = df.iloc[i]
            tech_feature = row.get('技术指标特征', 'N/A')
            trend_combo = row.get('趋势组合', 'N/A')
            
            print(f"\n行 {i+1}:")
            print(f"  股票代码: {row.get('股票代码', 'N/A')}")
            print(f"  技术强度: {row.get('技术强度', 'N/A')}")
            print(f"  技术指标特征: {tech_feature}")
            print(f"    类型: {type(tech_feature)}")
            print(f"    原始值: {repr(tech_feature)}")
            if isinstance(tech_feature, str):
                print(f"    长度: {len(tech_feature)}")
                print(f"    是否以0开头: {tech_feature.startswith('0')}")
            
            print(f"  趋势组合: {trend_combo}")
            print(f"    类型: {type(trend_combo)}")
            print(f"    原始值: {repr(trend_combo)}")
            if isinstance(trend_combo, str):
                print(f"    长度: {len(trend_combo)}")
                print(f"    是否以0开头: {trend_combo.startswith('0')}")
        
        # 检查字段类型
        print(f"\n=== 字段类型检查 ===")
        if '技术指标特征' in df.columns:
            print(f"技术指标特征列类型: {df['技术指标特征'].dtype}")
            print(f"技术指标特征唯一值数量: {df['技术指标特征'].nunique()}")
            unique_values = df['技术指标特征'].unique()[:10]
            print(f"技术指标特征前10个唯一值: {unique_values}")
            print(f"技术指标特征前10个唯一值类型: {[type(x) for x in unique_values]}")
        
        if '趋势组合' in df.columns:
            print(f"趋势组合列类型: {df['趋势组合'].dtype}")
            print(f"趋势组合唯一值数量: {df['趋势组合'].nunique()}")
            unique_values = df['趋势组合'].unique()[:10]
            print(f"趋势组合前10个唯一值: {unique_values}")
            print(f"趋势组合前10个唯一值类型: {[type(x) for x in unique_values]}")
        
        # 专门检查前导0问题
        print(f"\n=== 前导0问题检查 ===")
        
        if '技术指标特征' in df.columns:
            # 转换为字符串进行检查
            tech_features_str = df['技术指标特征'].astype(str)
            
            # 找出应该有前导0但没有的情况
            print(f"技术指标特征检查:")
            for i in range(min(20, len(df))):
                value = df.iloc[i]['技术指标特征']
                str_value = str(value)
                
                # 如果是数字且小于6位，说明丢失了前导0
                if str_value.isdigit() and len(str_value) < 6:
                    print(f"  ❌ 行{i+1}: {value} -> 应该是 {str_value.zfill(6)}")
                elif len(str_value) == 6 and str_value.startswith('0'):
                    print(f"  ✅ 行{i+1}: {value} -> 正确保留前导0")
                elif len(str_value) == 6:
                    print(f"  ✅ 行{i+1}: {value} -> 6位数字")
                else:
                    print(f"  ⚠️ 行{i+1}: {value} -> 异常格式")
        
        if '趋势组合' in df.columns:
            print(f"\n趋势组合检查:")
            for i in range(min(20, len(df))):
                value = df.iloc[i]['趋势组合']
                str_value = str(value)
                
                # 如果是数字且小于6位，说明丢失了前导0
                if str_value.isdigit() and len(str_value) < 6:
                    print(f"  ❌ 行{i+1}: {value} -> 应该是 {str_value.zfill(6)}")
                elif len(str_value) == 6 and str_value.startswith('0'):
                    print(f"  ✅ 行{i+1}: {value} -> 正确保留前导0")
                elif len(str_value) == 6:
                    print(f"  ✅ 行{i+1}: {value} -> 6位数字")
                else:
                    print(f"  ⚠️ 行{i+1}: {value} -> 异常格式")
        
        # 统计前导0丢失的情况
        print(f"\n=== 前导0丢失统计 ===")
        
        if '技术指标特征' in df.columns:
            tech_missing_zeros = 0
            tech_correct_zeros = 0
            tech_total = len(df)
            
            for value in df['技术指标特征']:
                str_value = str(value)
                if str_value.isdigit() and len(str_value) < 6:
                    tech_missing_zeros += 1
                elif len(str_value) == 6 and str_value.startswith('0'):
                    tech_correct_zeros += 1
            
            print(f"技术指标特征:")
            print(f"  总数: {tech_total}")
            print(f"  丢失前导0的数量: {tech_missing_zeros}")
            print(f"  正确保留前导0的数量: {tech_correct_zeros}")
            print(f"  前导0丢失率: {tech_missing_zeros/tech_total*100:.2f}%")
        
        if '趋势组合' in df.columns:
            trend_missing_zeros = 0
            trend_correct_zeros = 0
            trend_total = len(df)
            
            for value in df['趋势组合']:
                str_value = str(value)
                if str_value.isdigit() and len(str_value) < 6:
                    trend_missing_zeros += 1
                elif len(str_value) == 6 and str_value.startswith('0'):
                    trend_correct_zeros += 1
            
            print(f"趋势组合:")
            print(f"  总数: {trend_total}")
            print(f"  丢失前导0的数量: {trend_missing_zeros}")
            print(f"  正确保留前导0的数量: {trend_correct_zeros}")
            print(f"  前导0丢失率: {trend_missing_zeros/trend_total*100:.2f}%")
        
        print(f"\n✅ 强势股表格检查完成")
        
    except Exception as e:
        print(f"❌ 检查强势股表格时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_strong_table()
