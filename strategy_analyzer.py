#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
策略分析模块
作者: Augment AI
版本: 1.0.0

该模块提供策略分析和生成功能。
"""

import os
import pandas as pd
import numpy as np
import itertools
from datetime import datetime
from tqdm import tqdm
import matplotlib.pyplot as plt
from real_stock_backtest import RealStockBacktester

class StrategyAnalyzer:
    """策略分析器"""
    
    def __init__(self, stock_data_file, output_dir):
        """
        初始化策略分析器
        
        参数:
            stock_data_file (str): 股票历史数据文件路径
            output_dir (str): 输出目录
        """
        self.stock_data_file = stock_data_file
        self.output_dir = output_dir
        self.results_dir = os.path.join(output_dir, 'strategy_results')
        self.strategy_details_dir = os.path.join(self.results_dir, 'strategy_details')
        
        # 创建结果目录
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
            
        # 创建策略详细分析目录
        if not os.path.exists(self.strategy_details_dir):
            os.makedirs(self.strategy_details_dir)
            
        # 初始化回测器
        self.backtester = RealStockBacktester(stock_data_file, output_dir)
        
    def generate_feature_conditions(self, feature):
        """
        生成特征的所有可能筛选条件
        
        参数:
            feature (str): 特征名称
            
        返回:
            list: 条件列表
        """
        if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', 
                     '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势', 
                     '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势', 
                     '连续技术强度10天数趋势', '连续技术强度3天数价格趋势', 
                     '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势', 
                     '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势', 
                     '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
            # 二元特征，只有一种条件：== 1
            return [{
                'feature': feature,
                'condition': '== 1',
                'description': f"{feature} 为 1（是）"
            }]
        elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
            # 连续值特征，有多种条件
            conditions = []
            
            # 使用不同的阈值
            thresholds = [60, 70, 75, 80, 85, 90, 95]  # 更多阈值
            
            for threshold in thresholds:
                conditions.append({
                    'feature': feature,
                    'condition': f">= {threshold}",
                    'description': f"{feature} 大于等于 {threshold}"
                })
            
            return conditions
        elif feature == '看涨技术指标数量':
            # 看涨技术指标数量，有多种条件
            conditions = []
            
            # 使用不同的阈值
            for threshold in range(1, 6):  # 1到5
                conditions.append({
                    'feature': feature,
                    'condition': f'>= {threshold}',
                    'description': f"{feature} 大于等于 {threshold}"
                })
            
            return conditions
        
        return []
        
    def generate_all_condition_combinations(self, feature_combination):
        """
        生成特征组合的所有可能筛选条件组合
        
        参数:
            feature_combination (tuple): 特征组合
            
        返回:
            list: 条件组合列表
        """
        # 获取每个特征的所有可能筛选条件
        feature_conditions = []
        for feature in feature_combination:
            conditions = self.generate_feature_conditions(feature)
            if conditions:
                feature_conditions.append(conditions)
        
        # 生成所有可能的条件组合
        if feature_conditions:
            all_condition_combinations = list(itertools.product(*feature_conditions))
            return all_condition_combinations
        else:
            return []
            
    def analyze_strategies(self, features, min_features=2, max_features=5, max_strategies=10000, start_date=None, end_date=None, callback=None):
        """
        分析策略
        
        参数:
            features (list): 特征列表
            min_features (int): 最小特征数
            max_features (int): 最大特征数
            max_strategies (int): 最大策略数
            start_date (str): 回测开始日期，格式：YYYY-MM-DD
            end_date (str): 回测结束日期，格式：YYYY-MM-DD
            callback (function): 回调函数，用于更新进度和日志
            
        返回:
            list: 策略结果列表
        """
        # 记录日志
        if callback:
            callback("info", "开始分析策略...")
            callback("info", f"特征: {features}")
            callback("info", f"特征组合范围: {min_features} - {max_features}")
            callback("info", f"最大策略数: {max_strategies}")
            callback("info", f"回测开始日期: {start_date}")
            callback("info", f"回测结束日期: {end_date}")
            
        # 生成所有特征组合
        if callback:
            callback("info", f"生成从{min_features}到{max_features}个特征的所有组合...")
            
        all_combinations = []
        for r in range(min_features, max_features + 1):
            combinations = list(itertools.combinations(features, r))
            if callback:
                callback("info", f"{r}特征组合数量: {len(combinations)}")
            all_combinations.extend(combinations)
            
        if callback:
            callback("info", f"总组合数量: {len(all_combinations)}")
            
        # 生成所有策略
        all_strategies = []
        strategy_index = 1
        
        # 创建进度条
        total_combinations = len(all_combinations)
        
        for i, feature_combination in enumerate(all_combinations):
            # 更新进度
            if callback:
                progress = (i + 1) / total_combinations * 100
                callback("progress", progress)
                callback("info", f"正在处理特征组合: {feature_combination} ({i+1}/{total_combinations})")
                
            # 生成所有可能的条件组合
            all_condition_combinations = self.generate_all_condition_combinations(feature_combination)
            
            if all_condition_combinations:
                # 生成每个条件组合的策略
                for condition_combination in all_condition_combinations:
                    # 创建策略
                    strategy = {
                        'name': f"strategy_{strategy_index}",
                        'feature_combination': feature_combination,
                        'feature_count': len(feature_combination),
                        'conditions': condition_combination
                    }
                    
                    # 添加到策略列表
                    all_strategies.append(strategy)
                    
                    # 更新策略索引
                    strategy_index += 1
                    
                    # 限制策略数量，避免生成过多
                    if strategy_index > max_strategies:
                        if callback:
                            callback("info", f"已达到最大策略数量限制 ({max_strategies})，停止生成")
                        break
            
            # 限制策略数量，避免生成过多
            if strategy_index > max_strategies:
                break
                
            # 每处理100个特征组合，保存一次中间结果
            if (i + 1) % 100 == 0 or i == total_combinations - 1:
                # 创建中间Excel文件
                interim_excel_file = os.path.join(self.results_dir, f"中间结果_{i+1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
                self.save_strategies_to_excel(all_strategies[:1000], interim_excel_file)  # 只保存前1000个结果，避免文件过大
                
                if callback:
                    callback("info", f"已处理 {i+1}/{total_combinations} 个特征组合，中间结果已保存到 {interim_excel_file}")
        
        if callback:
            callback("info", f"共生成 {len(all_strategies)} 个策略")
            
        # 回测策略
        if callback:
            callback("info", "开始回测策略...")
            
        # 回测前1000个策略
        backtest_strategies = all_strategies[:1000]
        
        # 回测策略
        backtest_results = self.backtester.backtest_multiple_strategies(backtest_strategies, start_date, end_date, callback)
        
        # 更新策略结果
        for i, result in enumerate(backtest_results):
            backtest_strategies[i]['backtest_result'] = result
            
        # 按总收益率排序
        backtest_strategies.sort(key=lambda x: x['backtest_result']['summary']['总收益率(%)'] if 'backtest_result' in x else 0, reverse=True)
        
        # 创建主Excel文件
        main_excel_file = os.path.join(self.results_dir, f"所有策略汇总_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        self.save_strategies_to_excel(backtest_strategies, main_excel_file)
        
        if callback:
            callback("info", f"主Excel文件已保存到: {main_excel_file}")
            
        return backtest_strategies
        
    def save_strategies_to_excel(self, strategies, output_file):
        """
        保存策略到Excel文件
        
        参数:
            strategies (list): 策略列表
            output_file (str): 输出文件路径
        """
        # 创建Excel写入器
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 创建策略汇总表格
            self.create_strategy_summary_sheet(strategies, writer)
            
            # 创建策略条件表格
            self.create_strategy_conditions_sheet(strategies, writer)
            
            # 创建按特征数量分组的统计表格
            self.create_feature_count_stats_sheet(strategies, writer)
            
    def create_strategy_summary_sheet(self, strategies, writer):
        """
        创建策略汇总表格
        
        参数:
            strategies (list): 策略列表
            writer: Excel写入器
        """
        # 创建汇总数据
        summary_data = []
        
        for strategy in strategies:
            feature_str = ', '.join(strategy['feature_combination'])
            
            # 获取回测结果
            if 'backtest_result' in strategy:
                result = strategy['backtest_result']
                summary = result['summary']
                
                summary_data.append({
                    '策略编号': strategy['name'],
                    '策略组合': feature_str,
                    '特征数量': strategy['feature_count'],
                    '总收益率(%)': summary['总收益率(%)'],
                    '年化收益率(%)': summary['年化收益率(%)'],
                    '最大回撤(%)': summary['最大回撤(%)'],
                    '胜率(%)': summary['胜率(%)'],
                    '总交易笔数': summary['总交易笔数'],
                    '平均每日交易笔数': summary['平均每日交易笔数'],
                    '交易天数': summary['交易天数'],
                    '总天数': summary['总天数'],
                    '交易频率(%)': summary['交易频率(%)']
                })
            else:
                summary_data.append({
                    '策略编号': strategy['name'],
                    '策略组合': feature_str,
                    '特征数量': strategy['feature_count'],
                    '总收益率(%)': np.nan,
                    '年化收益率(%)': np.nan,
                    '最大回撤(%)': np.nan,
                    '胜率(%)': np.nan,
                    '总交易笔数': np.nan,
                    '平均每日交易笔数': np.nan,
                    '交易天数': np.nan,
                    '总天数': np.nan,
                    '交易频率(%)': np.nan
                })
        
        # 转换为DataFrame
        summary_df = pd.DataFrame(summary_data)
        
        # 写入Excel
        summary_df.to_excel(writer, sheet_name='策略汇总', index=False)
        
    def create_strategy_conditions_sheet(self, strategies, writer):
        """
        创建策略条件表格
        
        参数:
            strategies (list): 策略列表
            writer: Excel写入器
        """
        # 创建策略条件数据
        conditions_data = []
        
        for strategy in strategies:
            feature_str = ', '.join(strategy['feature_combination'])
            
            # 生成策略条件描述
            conditions_str = ' AND '.join([cond['description'] for cond in strategy['conditions']])
            
            # 生成策略代码
            code_parts = []
            for cond in strategy['conditions']:
                code_parts.append(f"df['{cond['feature']}'] {cond['condition']}")
            code_str = 'df[' + ' & '.join(code_parts) + ']'
            
            # 获取回测结果
            if 'backtest_result' in strategy:
                result = strategy['backtest_result']
                summary = result['summary']
                
                conditions_data.append({
                    '策略编号': strategy['name'],
                    '策略组合': feature_str,
                    '特征数量': strategy['feature_count'],
                    '总收益率(%)': summary['总收益率(%)'],
                    '胜率(%)': summary['胜率(%)'],
                    '策略条件描述': conditions_str,
                    '策略代码': code_str,
                    '详细分析文件': f"{strategy['name']}.xlsx"
                })
            else:
                conditions_data.append({
                    '策略编号': strategy['name'],
                    '策略组合': feature_str,
                    '特征数量': strategy['feature_count'],
                    '总收益率(%)': np.nan,
                    '胜率(%)': np.nan,
                    '策略条件描述': conditions_str,
                    '策略代码': code_str,
                    '详细分析文件': f"{strategy['name']}.xlsx"
                })
        
        # 转换为DataFrame
        conditions_df = pd.DataFrame(conditions_data)
        
        # 写入Excel
        conditions_df.to_excel(writer, sheet_name='策略条件', index=False)
        
    def create_feature_count_stats_sheet(self, strategies, writer):
        """
        创建按特征数量分组的统计表格
        
        参数:
            strategies (list): 策略列表
            writer: Excel写入器
        """
        # 创建汇总数据
        summary_data = []
        
        for strategy in strategies:
            if 'backtest_result' in strategy:
                result = strategy['backtest_result']
                summary = result['summary']
                
                summary_data.append({
                    '特征数量': strategy['feature_count'],
                    '总收益率(%)': summary['总收益率(%)'],
                    '年化收益率(%)': summary['年化收益率(%)'],
                    '最大回撤(%)': summary['最大回撤(%)'],
                    '胜率(%)': summary['胜率(%)'],
                    '平均每日交易笔数': summary['平均每日交易笔数'],
                    '总交易笔数': summary['总交易笔数']
                })
        
        # 转换为DataFrame
        summary_df = pd.DataFrame(summary_data)
        
        # 按特征数量分组
        if not summary_df.empty:
            grouped = summary_df.groupby('特征数量').agg({
                '总收益率(%)': ['mean', 'std', 'max', 'min'],
                '年化收益率(%)': ['mean', 'std'],
                '最大回撤(%)': ['mean', 'std'],
                '胜率(%)': ['mean', 'std'],
                '平均每日交易笔数': 'mean',
                '总交易笔数': 'mean'
            })
            
            # 写入Excel
            grouped.to_excel(writer, sheet_name='特征数量统计')
