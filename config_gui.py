"""
配置GUI

用于设置数据目录等配置信息
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json

# 添加当前目录到Python路径，确保能够导入config模块
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

try:
    import config
except ImportError:
    messagebox.showerror("错误", "无法导入config模块，请确保config.py文件存在")
    sys.exit(1)

# 配置文件路径
CONFIG_FILE = os.path.join(script_dir, "config.json")

class ConfigGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("股票回测系统配置")
        self.root.geometry("600x400")
        self.root.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建数据目录设置部分
        data_dir_frame = ttk.LabelFrame(main_frame, text="数据目录设置", padding=10)
        data_dir_frame.pack(fill=tk.X, pady=5)
        
        # 数据目录输入框
        ttk.Label(data_dir_frame, text="数据目录:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.data_dir_var = tk.StringVar(value=config.DATA_DIR)
        data_dir_entry = ttk.Entry(data_dir_frame, textvariable=self.data_dir_var, width=50)
        data_dir_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        
        # 浏览按钮
        browse_button = ttk.Button(data_dir_frame, text="浏览...", command=self.browse_data_dir)
        browse_button.grid(row=0, column=2, padx=5, pady=5)
        
        # 当前配置信息
        info_frame = ttk.LabelFrame(main_frame, text="当前配置信息", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建文本框显示当前配置
        self.info_text = tk.Text(info_frame, wrap=tk.WORD, width=70, height=15)
        self.info_text.pack(fill=tk.BOTH, expand=True)
        
        # 更新配置信息显示
        self.update_config_info()
        
        # 创建按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 保存按钮
        save_button = ttk.Button(button_frame, text="保存配置", command=self.save_config)
        save_button.pack(side=tk.RIGHT, padx=5)
        
        # 重置按钮
        reset_button = ttk.Button(button_frame, text="重置为默认", command=self.reset_config)
        reset_button.pack(side=tk.RIGHT, padx=5)
        
        # 加载保存的配置
        self.load_saved_config()
    
    def browse_data_dir(self):
        """浏览并选择数据目录"""
        directory = filedialog.askdirectory(initialdir=self.data_dir_var.get())
        if directory:
            self.data_dir_var.set(directory)
            self.update_config_info()
    
    def update_config_info(self):
        """更新配置信息显示"""
        # 清空文本框
        self.info_text.delete(1.0, tk.END)
        
        # 获取当前配置
        data_dir = self.data_dir_var.get()
        
        # 更新配置模块中的路径
        paths = config.set_data_dir(data_dir)
        
        # 显示配置信息
        self.info_text.insert(tk.END, "数据目录:\n")
        self.info_text.insert(tk.END, f"  {paths['data_dir']}\n\n")
        
        self.info_text.insert(tk.END, "股票数据目录:\n")
        self.info_text.insert(tk.END, f"  {paths['stock_data_dir']}\n\n")
        
        self.info_text.insert(tk.END, "每日数据目录:\n")
        self.info_text.insert(tk.END, f"  {paths['daily_data_dir']}\n\n")
        
        self.info_text.insert(tk.END, "股票明细文件:\n")
        self.info_text.insert(tk.END, f"  {paths['stock_details_file']}\n\n")
        
        self.info_text.insert(tk.END, "历史数据文件:\n")
        self.info_text.insert(tk.END, f"  {paths['history_data_file']}\n\n")
        
        # 检查目录是否存在
        if not os.path.exists(paths['data_dir']):
            self.info_text.insert(tk.END, "警告: 数据目录不存在，将在保存配置时创建\n", "warning")
        
        # 设置标签
        self.info_text.tag_configure("warning", foreground="red")
    
    def save_config(self):
        """保存配置"""
        data_dir = self.data_dir_var.get()
        
        # 检查目录是否存在，如果不存在则创建
        if not os.path.exists(data_dir):
            try:
                os.makedirs(data_dir)
                messagebox.showinfo("提示", f"已创建数据目录: {data_dir}")
            except Exception as e:
                messagebox.showerror("错误", f"创建数据目录时出错: {e}")
                return
        
        # 更新配置模块中的路径
        paths = config.set_data_dir(data_dir)
        
        # 保存配置到文件
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump({'data_dir': data_dir}, f, ensure_ascii=False, indent=4)
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置时出错: {e}")
    
    def reset_config(self):
        """重置为默认配置"""
        self.data_dir_var.set(config.DEFAULT_DATA_DIR)
        self.update_config_info()
    
    def load_saved_config(self):
        """加载保存的配置"""
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                
                if 'data_dir' in saved_config:
                    self.data_dir_var.set(saved_config['data_dir'])
                    self.update_config_info()
            except Exception as e:
                messagebox.showerror("错误", f"加载配置时出错: {e}")

def main():
    root = tk.Tk()
    app = ConfigGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
