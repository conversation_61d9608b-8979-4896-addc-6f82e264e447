@echo off
chcp 65001 > nul
echo ===================================================
echo        股票高胜率策略分析工具打包程序
echo ===================================================
echo.

echo 步骤1: 正在安装依赖项...
pip install pandas numpy joblib scikit-learn cx_Freeze

echo.
echo 步骤2: 创建必要的目录...
if not exist "models" mkdir models
if not exist "backtest_results" mkdir backtest_results

echo.
echo 步骤3: 正在构建可执行文件...
python setup.py build

echo.
echo 步骤4: 正在创建启动脚本...
(
echo @echo off
echo chcp 65001 ^> nul
echo echo 正在启动股票高胜率策略分析工具(图形界面版)...
echo start build\exe.win-amd64-3.8\股票分析器_GUI.exe --gui
) > run_gui.bat

(
echo @echo off
echo chcp 65001 ^> nul
echo echo 正在启动股票高胜率策略分析工具(命令行版)...
echo build\exe.win-amd64-3.8\股票分析器.exe --data 股票明细.xlsx --start_date 2025-04-25 --end_date 2025-05-08 --output 股票高胜率策略分析结果.txt
echo echo 分析完成，结果已保存到 股票高胜率策略分析结果.txt
echo pause
) > run_console.bat

echo.
echo ===================================================
echo                  构建完成！
echo ===================================================
echo.
echo 请运行以下文件启动程序:
echo 1. run_gui.bat - 启动图形界面版
echo 2. run_console.bat - 启动命令行版
echo.
echo 按任意键退出...
pause > nul
