import pandas as pd

print("=== 最终数据验证 ===")

try:
    # 读取生成的数据
    df = pd.read_excel('E:/机器学习/complete_excel_results/tech_strength/daily/tech_strength_2025-05-15.xlsx')
    
    print(f"数据行数: {len(df)}")
    print(f"数据列数: {len(df.columns)}")
    
    # 检查样本
    sample = df.iloc[0]
    print(f"\n样本股票: {sample['股票代码']}")
    
    # 检查关键字段
    print(f"\n=== 关键字段检查 ===")
    print(f"技术指标特征: {sample.get('技术指标特征', 'N/A')}")
    print(f"趋势组合: {sample.get('趋势组合', 'N/A')}")
    print(f"连续技术强度3天数: {sample.get('连续技术强度3天数', 'N/A')}")
    print(f"连续技术强度5天数: {sample.get('连续技术强度5天数', 'N/A')}")
    print(f"连续技术强度10天数: {sample.get('连续技术强度10天数', 'N/A')}")
    
    # 检查字段类型
    print(f"\n=== 字段类型检查 ===")
    if '技术指标特征' in df.columns:
        print(f"技术指标特征类型: {df['技术指标特征'].dtype}")
    if '趋势组合' in df.columns:
        print(f"趋势组合类型: {df['趋势组合'].dtype}")
    
    # 检查多样性
    print(f"\n=== 数据多样性检查 ===")
    if '技术指标特征' in df.columns:
        print(f"技术指标特征唯一值数量: {df['技术指标特征'].nunique()}")
    if '趋势组合' in df.columns:
        print(f"趋势组合唯一值数量: {df['趋势组合'].nunique()}")
    
    # 检查连续技术强度
    print(f"\n=== 连续技术强度检查 ===")
    for i in range(min(5, len(df))):
        row = df.iloc[i]
        c3 = row.get('连续技术强度3天数', 0)
        c5 = row.get('连续技术强度5天数', 0)
        c10 = row.get('连续技术强度10天数', 0)
        order_ok = c3 <= c5 <= c10
        print(f"  {row['股票代码']}: 3天={c3}, 5天={c5}, 10天={c10} {'✅' if order_ok else '❌'}")
    
    print("\n✅ 验证完成")
    
except Exception as e:
    print(f"❌ 验证失败: {e}")
    import traceback
    traceback.print_exc()
