import pandas as pd
import os
import glob

print("=== 检查天富能源（sh.600509）的数据匹配问题 ===")

# 目标股票和日期
target_code = 'sh.600509'
buy_date = '2025-05-22'
sell_date = '2025-05-23'

print(f"目标股票: {target_code}")
print(f"买入日期: {buy_date}")
print(f"卖出日期: {sell_date}")

# 1. 检查历史数据中是否有这个股票的数据
print(f"\n1. 检查历史数据...")
stock_data_dir = r'E:\机器学习\complete_excel_results\stock_data\daily'

if os.path.exists(stock_data_dir):
    # 检查买入日期的数据
    buy_date_file = os.path.join(stock_data_dir, f"stock_data_{buy_date}.xlsx")
    sell_date_file = os.path.join(stock_data_dir, f"stock_data_{sell_date}.xlsx")
    
    print(f"买入日期文件: {buy_date_file}")
    print(f"文件存在: {os.path.exists(buy_date_file)}")
    
    if os.path.exists(buy_date_file):
        try:
            buy_df = pd.read_excel(buy_date_file)
            print(f"买入日期数据形状: {buy_df.shape}")
            print(f"买入日期数据列名: {buy_df.columns.tolist()}")
            
            # 查找目标股票
            if '证券代码' in buy_df.columns:
                target_rows = buy_df[buy_df['证券代码'] == target_code]
                print(f"找到目标股票记录数: {len(target_rows)}")
                
                if not target_rows.empty:
                    print(f"买入日期({buy_date})的天富能源数据:")
                    print(target_rows.to_string())
                else:
                    print(f"在买入日期({buy_date})的数据中没有找到{target_code}")
                    
                    # 检查是否有类似的股票代码
                    similar_codes = buy_df[buy_df['证券代码'].str.contains('600509', na=False)]['证券代码'].unique()
                    print(f"包含600509的股票代码: {similar_codes}")
            else:
                print("买入日期数据中没有'证券代码'列")
                
        except Exception as e:
            print(f"读取买入日期文件出错: {e}")
    
    print(f"\n卖出日期文件: {sell_date_file}")
    print(f"文件存在: {os.path.exists(sell_date_file)}")
    
    if os.path.exists(sell_date_file):
        try:
            sell_df = pd.read_excel(sell_date_file)
            print(f"卖出日期数据形状: {sell_df.shape}")
            
            # 查找目标股票
            if '证券代码' in sell_df.columns:
                target_rows = sell_df[sell_df['证券代码'] == target_code]
                print(f"找到目标股票记录数: {len(target_rows)}")
                
                if not target_rows.empty:
                    print(f"卖出日期({sell_date})的天富能源数据:")
                    print(target_rows.to_string())
                else:
                    print(f"在卖出日期({sell_date})的数据中没有找到{target_code}")
            else:
                print("卖出日期数据中没有'证券代码'列")
                
        except Exception as e:
            print(f"读取卖出日期文件出错: {e}")
else:
    print("历史数据目录不存在")

# 2. 模拟关联键创建过程
print(f"\n2. 模拟关联键创建过程...")

buy_key = f"{target_code}_{buy_date}"
sell_key = f"{target_code}_{sell_date}"

print(f"买入关联键: {buy_key}")
print(f"卖出关联键: {sell_key}")

# 3. 检查程序中实际使用的历史数据
print(f"\n3. 检查程序加载的历史数据...")

# 尝试加载stock_data_manager模块
try:
    import sys
    sys.path.append(r'E:\机器学习')
    import stock_data_manager as sdm
    
    # 获取可用日期
    available_dates = sdm.get_available_dates()
    print(f"可用日期数量: {len(available_dates)}")
    
    # 检查目标日期是否在可用日期中
    buy_date_obj = pd.to_datetime(buy_date)
    sell_date_obj = pd.to_datetime(sell_date)
    
    print(f"买入日期在可用日期中: {buy_date_obj in available_dates}")
    print(f"卖出日期在可用日期中: {sell_date_obj in available_dates}")
    
    if buy_date_obj in available_dates:
        buy_data = sdm.load_daily_data(buy_date_obj)
        print(f"买入日期数据加载成功，形状: {buy_data.shape}")
        
        if '证券代码' in buy_data.columns:
            target_buy_data = buy_data[buy_data['证券代码'] == target_code]
            if not target_buy_data.empty:
                print(f"通过stock_data_manager找到买入日期数据:")
                print(target_buy_data.to_string())
            else:
                print(f"通过stock_data_manager未找到买入日期的{target_code}数据")
    
    if sell_date_obj in available_dates:
        sell_data = sdm.load_daily_data(sell_date_obj)
        print(f"卖出日期数据加载成功，形状: {sell_data.shape}")
        
        if '证券代码' in sell_data.columns:
            target_sell_data = sell_data[sell_data['证券代码'] == target_code]
            if not target_sell_data.empty:
                print(f"通过stock_data_manager找到卖出日期数据:")
                print(target_sell_data.to_string())
            else:
                print(f"通过stock_data_manager未找到卖出日期的{target_code}数据")
                
except Exception as e:
    print(f"加载stock_data_manager模块出错: {e}")

print(f"\n=== 检查完成 ===")
