import pandas as pd
import numpy as np

# 读取数据
print("读取股票数据...")
data = pd.read_excel('股票明细.xlsx')
print(f"数据形状: {data.shape}")

# 检查数据日期范围
data['日期'] = pd.to_datetime(data['日期'])
print("\n数据日期范围:")
print(f"{data['日期'].min()} 至 {data['日期'].max()}")

# 获取5月8日涨幅大于3.15%的股票
may_8 = data[data['日期'] == '2025-05-08']
high_gain_stocks = may_8[may_8['涨跌幅'] >= 3.15][['股票代码', '股票名称', '当前价格', '涨跌幅']]
print(f"\n5月8日涨幅大于3.15%的股票数量: {len(high_gain_stocks)}")
print(high_gain_stocks.head(10))

# 获取5月9日的数据
may_9 = data[data['日期'] == '2025-05-09']

# 检查这些股票在5月9日的表现
print("\n检查这些股票在5月9日的表现:")
results = []

for _, row in high_gain_stocks.iterrows():
    stock_code = row['股票代码']
    next_day = may_9[may_9['股票代码'] == stock_code]
    
    if len(next_day) > 0:
        next_day_price = next_day['当前价格'].values[0]
        next_day_change = next_day['涨跌幅'].values[0]
        
        results.append({
            '股票代码': stock_code,
            '股票名称': row['股票名称'],
            '5月8日价格': row['当前价格'],
            '5月8日涨跌幅': row['涨跌幅'],
            '5月9日价格': next_day_price,
            '5月9日涨跌幅': next_day_change,
            '两日累计涨跌幅': (next_day_price / row['当前价格'] - 1) * 100
        })

# 转换为DataFrame并显示结果
results_df = pd.DataFrame(results)
print(results_df)

# 统计5月9日上涨和下跌的股票数量
up_count = len(results_df[results_df['5月9日涨跌幅'] > 0])
down_count = len(results_df[results_df['5月9日涨跌幅'] < 0])
flat_count = len(results_df[results_df['5月9日涨跌幅'] == 0])

print(f"\n5月9日上涨的股票数量: {up_count} ({up_count/len(results_df)*100:.2f}%)")
print(f"5月9日下跌的股票数量: {down_count} ({down_count/len(results_df)*100:.2f}%)")
print(f"5月9日持平的股票数量: {flat_count} ({flat_count/len(results_df)*100:.2f}%)")

# 计算平均涨跌幅
avg_change = results_df['5月9日涨跌幅'].mean()
print(f"5月9日平均涨跌幅: {avg_change:.4f}%")

# 计算两日累计平均涨跌幅
avg_cumulative = results_df['两日累计涨跌幅'].mean()
print(f"两日累计平均涨跌幅: {avg_cumulative:.4f}%")

# 保存结果
results_df.to_excel('5月8日高涨幅股票次日表现.xlsx', index=False)
print("\n已将结果保存到 '5月8日高涨幅股票次日表现.xlsx'")

# 检查特定的10只股票
print("\n检查推荐的10只股票在5月9日的表现:")
recommended_stocks = [
    'sh.600183',  # 生益科技
    'sh.600255',  # 鑫科材料
    'sh.600336',  # 澳柯玛
    'sh.600396',  # 华电辽能
    'sh.600416',  # 湘电股份
    'sh.600448',  # 华纺股份
    'sh.600499',  # 科达制造
    'sh.600523',  # 贵航股份
    'sh.600562',  # 国睿科技
    'sh.600668'   # 尖峰集团
]

recommended_results = results_df[results_df['股票代码'].isin(recommended_stocks)]
print(recommended_results)

# 统计推荐股票的5月9日表现
rec_up_count = len(recommended_results[recommended_results['5月9日涨跌幅'] > 0])
rec_down_count = len(recommended_results[recommended_results['5月9日涨跌幅'] < 0])
rec_flat_count = len(recommended_results[recommended_results['5月9日涨跌幅'] == 0])

print(f"\n推荐股票中5月9日上涨的数量: {rec_up_count} ({rec_up_count/len(recommended_results)*100:.2f}%)")
print(f"推荐股票中5月9日下跌的数量: {rec_down_count} ({rec_down_count/len(recommended_results)*100:.2f}%)")
print(f"推荐股票中5月9日持平的数量: {rec_flat_count} ({rec_flat_count/len(recommended_results)*100:.2f}%)")

# 计算推荐股票的平均涨跌幅
rec_avg_change = recommended_results['5月9日涨跌幅'].mean()
print(f"推荐股票5月9日平均涨跌幅: {rec_avg_change:.4f}%")

# 计算推荐股票的两日累计平均涨跌幅
rec_avg_cumulative = recommended_results['两日累计涨跌幅'].mean()
print(f"推荐股票两日累计平均涨跌幅: {rec_avg_cumulative:.4f}%")
