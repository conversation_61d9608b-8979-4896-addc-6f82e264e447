"""
完整修复版打包脚本 - 专门解决py_mini_racer的所有依赖问题
"""

import os
import subprocess
import sys
import platform
import shutil
import site
import glob
import importlib.util
import tempfile
import zipfile
import io

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'pyinstaller',
        'pandas',
        'numpy',
        'akshare',
        'tqdm',
        'openpyxl',
        'pyarrow',
        'requests',
        'lxml',
        'beautifulsoup4',
        'pywin32',
        'py_mini_racer',
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print("\n需要安装以下依赖:")
        for package in missing_packages:
            print(f"  - {package}")
        
        install = input("\n是否自动安装这些依赖? (y/n): ")
        if install.lower() == 'y':
            for package in missing_packages:
                print(f"\n正在安装 {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"{package} 安装完成")
        else:
            print("\n请手动安装缺失的依赖后再运行此脚本")
            sys.exit(1)

def extract_py_mini_racer_files():
    """提取py_mini_racer库的所有文件到临时目录"""
    try:
        import py_mini_racer
        
        # 获取py_mini_racer模块的路径
        mini_racer_dir = os.path.dirname(py_mini_racer.__file__)
        print(f"py_mini_racer模块路径: {mini_racer_dir}")
        
        # 创建临时目录
        temp_dir = os.path.join(tempfile.gettempdir(), 'py_mini_racer_files')
        os.makedirs(temp_dir, exist_ok=True)
        
        # 复制所有文件到临时目录
        for root, dirs, files in os.walk(mini_racer_dir):
            for file in files:
                src_file = os.path.join(root, file)
                rel_path = os.path.relpath(src_file, mini_racer_dir)
                dst_file = os.path.join(temp_dir, rel_path)
                
                # 确保目标目录存在
                os.makedirs(os.path.dirname(dst_file), exist_ok=True)
                
                # 复制文件
                shutil.copy2(src_file, dst_file)
                print(f"已复制: {rel_path}")
        
        return temp_dir
    except Exception as e:
        print(f"提取py_mini_racer文件时出错: {e}")
        return None

def create_hook_file():
    """创建自定义钩子文件来处理py_mini_racer"""
    hook_content = """
# 自定义钩子文件，用于处理py_mini_racer
from PyInstaller.utils.hooks import collect_all, collect_data_files

# 收集py_mini_racer的所有依赖
datas, binaries, hiddenimports = collect_all('py_mini_racer')

# 添加额外的数据文件
extra_datas = collect_data_files('py_mini_racer')
for src, dst in extra_datas:
    if src not in [d[0] for d in datas]:
        datas.append((src, dst))

# 确保添加了所有必要的隐藏导入
hiddenimports += ['py_mini_racer']
"""
    
    hook_dir = os.path.join(tempfile.gettempdir(), 'custom_hooks')
    os.makedirs(hook_dir, exist_ok=True)
    
    hook_file = os.path.join(hook_dir, 'hook-py_mini_racer.py')
    with open(hook_file, 'w') as f:
        f.write(hook_content)
    
    return hook_dir

def create_bundle_script():
    """创建一个捆绑脚本，用于确保所有必要的文件都被包含"""
    bundle_content = """
# 捆绑脚本，用于确保所有必要的文件都被包含
import os
import sys
import py_mini_racer
import shutil

def extract_bundled_files():
    # 获取临时目录
    temp_dir = os.path.join(os.environ.get('TEMP', os.path.dirname(sys.executable)), '_MEI')
    
    # 获取py_mini_racer模块的路径
    mini_racer_dir = os.path.dirname(py_mini_racer.__file__)
    
    # 复制所有必要的文件
    for file in ['mini_racer.dll', 'snapshot_blob.bin', 'icudtl.dat']:
        src_file = os.path.join(mini_racer_dir, file)
        if os.path.exists(src_file):
            # 查找临时目录中的目标位置
            for root, dirs, files in os.walk(temp_dir):
                if any(f.endswith('.dll') for f in files):
                    dst_file = os.path.join(root, file)
                    shutil.copy2(src_file, dst_file)
                    print(f"已复制: {file} 到 {dst_file}")
                    break

# 在导入时执行
extract_bundled_files()
"""
    
    bundle_file = 'bundle_mini_racer.py'
    with open(bundle_file, 'w') as f:
        f.write(bundle_content)
    
    return bundle_file

def build_executable(mini_racer_dir, hook_dir, bundle_file):
    """使用PyInstaller构建可执行文件"""
    print("\n开始构建可执行文件...")
    
    # 准备命令行参数
    cmd = [
        sys.executable, 
        "-m", 
        "PyInstaller",
        "--clean",
        "--windowed",  # 不显示控制台窗口
        "--onefile",   # 生成单个可执行文件
        "--name", "自动下载程序",
        "--additional-hooks-dir", hook_dir,
        "--hidden-import", "akshare",
        "--hidden-import", "py_mini_racer",
        "--hidden-import", "stock_data_manager",
        "--add-data", f"{mini_racer_dir}{os.pathsep}py_mini_racer",
    ]
    
    # 添加捆绑脚本
    cmd.append(bundle_file)
    
    # 添加主脚本
    cmd.append("download_stock_data_gui.py")
    
    # 执行命令
    subprocess.check_call(cmd)
    
    print("\n构建完成!")
    
    # 检查是否成功创建了可执行文件
    if os.path.exists(os.path.join('dist', '自动下载程序.exe')):
        print(f"\n可执行文件已创建: {os.path.abspath(os.path.join('dist', '自动下载程序.exe'))}")
        
        # 创建一个包含必要文件的发布目录
        create_release_package()
    else:
        print("\n错误: 未能创建可执行文件")

def create_release_package():
    """创建一个包含所有必要文件的发布包"""
    release_dir = "股票数据下载工具_完整版"
    
    # 如果目录已存在，先删除
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    
    # 创建发布目录
    os.makedirs(release_dir)
    
    # 复制可执行文件
    shutil.copy(os.path.join('dist', '自动下载程序.exe'), os.path.join(release_dir, '自动下载程序.exe'))
    
    # 创建默认数据目录
    os.makedirs(os.path.join(release_dir, 'data', 'stock_data', 'daily'), exist_ok=True)
    
    # 创建说明文件
    with open(os.path.join(release_dir, '使用说明.txt'), 'w', encoding='utf-8') as f:
        f.write("""股票数据下载工具使用说明
====================

1. 运行方法:
   双击"自动下载程序.exe"即可启动程序

2. 功能说明:
   - 可以下载指定日期范围内的A股股票历史数据
   - 数据按日期分别存储在Excel文件中
   - 默认保存在程序所在目录的data文件夹中

3. 使用步骤:
   a. 设置数据输出目录(可选)
   b. 设置日期范围
   c. 点击"开始下载"按钮
   d. 等待下载完成

4. 注意事项:
   - 首次运行时可能需要等待较长时间
   - 下载过程中请保持网络连接
   - 如遇到问题，请查看程序日志窗口的提示信息
""")
    
    print(f"\n发布包已创建: {os.path.abspath(release_dir)}")
    print("包含以下文件:")
    print(f"  - {release_dir}/自动下载程序.exe")
    print(f"  - {release_dir}/使用说明.txt")
    print(f"  - {release_dir}/data/ (默认数据目录)")

def main():
    """主函数"""
    print("=" * 50)
    print("股票数据下载工具打包脚本 (完整修复版)")
    print("=" * 50)
    
    # 检查操作系统
    if platform.system() != 'Windows':
        print("警告: 此脚本设计用于Windows系统，在其他系统上可能无法正常工作")
    
    # 检查依赖
    print("\n检查依赖...")
    check_dependencies()
    
    # 提取py_mini_racer文件
    print("\n提取py_mini_racer文件...")
    mini_racer_dir = extract_py_mini_racer_files()
    if not mini_racer_dir:
        print("错误: 无法提取py_mini_racer文件")
        sys.exit(1)
    
    # 创建钩子文件
    print("\n创建自定义钩子文件...")
    hook_dir = create_hook_file()
    print(f"钩子文件已创建: {hook_dir}")
    
    # 创建捆绑脚本
    print("\n创建捆绑脚本...")
    bundle_file = create_bundle_script()
    print(f"捆绑脚本已创建: {bundle_file}")
    
    # 构建可执行文件
    build_executable(mini_racer_dir, hook_dir, bundle_file)
    
    # 清理临时文件
    try:
        os.remove(bundle_file)
        print(f"已删除临时文件: {bundle_file}")
    except:
        pass
    
    print("\n打包过程完成!")
    print("=" * 50)

if __name__ == "__main__":
    main()
