"""
专门测试策略回测性能的脚本
"""

import time
import sys
import os
from datetime import datetime

def log_time(message, start_time=None):
    """记录时间"""
    current_time = time.time()
    if start_time:
        elapsed = current_time - start_time
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message} - 耗时: {elapsed:.3f}秒")
    else:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    return current_time

def test_strategy_backtest():
    """测试策略回测性能"""
    print("🔍 策略回测性能测试")
    print("=" * 50)
    
    # 检查运行环境
    if getattr(sys, 'frozen', False):
        print("✅ 运行环境: PyInstaller打包程序")
    else:
        print("✅ 运行环境: Python开发环境")
    
    overall_start = time.time()
    
    # 测试1: 导入回测模块
    print("\n📦 测试导入回测模块...")
    start = time.time()
    try:
        import backtest_local
        log_time("导入backtest_local", start)
    except Exception as e:
        log_time(f"导入backtest_local失败: {e}", start)
        return
    
    # 测试2: 检查数据文件
    print("\n📁 检查数据文件...")
    start = time.time()
    
    # 检查汇总表
    summary_files = [
        'E:/机器学习/complete_excel_results/所有策略汇总_已回测.xlsx',
        'E:/机器学习/complete_excel_results/所有策略汇总.xlsx'
    ]
    
    summary_file_found = None
    for file_path in summary_files:
        if os.path.exists(file_path):
            summary_file_found = file_path
            break
    
    if summary_file_found:
        log_time(f"找到汇总表: {os.path.basename(summary_file_found)}", start)
    else:
        log_time("未找到汇总表文件", start)
        return
    
    # 测试3: 读取汇总表
    print("\n📊 测试读取汇总表...")
    start = time.time()
    try:
        import pandas as pd
        if summary_file_found.endswith('.parquet'):
            df = pd.read_parquet(summary_file_found)
        else:
            df = pd.read_excel(summary_file_found)
        log_time(f"读取汇总表 ({len(df)}条记录)", start)
    except Exception as e:
        log_time(f"读取汇总表失败: {e}", start)
        return
    
    # 测试4: 查找测试策略
    print("\n🔍 查找测试策略...")
    start = time.time()
    
    # 找一个存在的策略进行测试
    test_strategy_id = None
    for strategy_id in [1, 10, 100, 1000]:
        if strategy_id in df['策略编号'].values:
            test_strategy_id = strategy_id
            break
    
    if test_strategy_id:
        strategy_info = df[df['策略编号'] == test_strategy_id].iloc[0]
        log_time(f"找到测试策略{test_strategy_id}: {strategy_info['策略条件描述'][:50]}...", start)
    else:
        log_time("未找到合适的测试策略", start)
        return
    
    # 测试5: 模拟策略执行的各个步骤
    print("\n⚡ 模拟策略执行步骤...")
    
    # 步骤1: 初始化
    start = time.time()
    try:
        # 模拟初始化过程
        import glob
        tech_files = glob.glob('E:/机器学习/complete_excel_results/tech_strength/daily/*.xlsx')
        log_time(f"检查技术强度文件 (找到{len(tech_files)}个)", start)
    except Exception as e:
        log_time(f"检查技术强度文件失败: {e}", start)
    
    # 步骤2: 数据加载模拟
    start = time.time()
    try:
        # 模拟读取几个技术强度文件
        sample_files = tech_files[:3] if tech_files else []
        total_records = 0
        for file_path in sample_files:
            sample_df = pd.read_excel(file_path)
            total_records += len(sample_df)
        log_time(f"模拟数据加载 ({total_records}条记录)", start)
    except Exception as e:
        log_time(f"模拟数据加载失败: {e}", start)
    
    # 步骤3: 策略筛选模拟
    start = time.time()
    try:
        # 模拟策略筛选逻辑
        condition = strategy_info['策略条件描述']
        
        # 简单解析条件
        if "技术强度" in condition and "等于" in condition:
            # 模拟筛选
            filtered_count = 0
            for file_path in sample_files:
                sample_df = pd.read_excel(file_path)
                if '技术强度' in sample_df.columns:
                    # 模拟筛选条件
                    filtered = sample_df[sample_df['技术强度'] > 70]
                    filtered_count += len(filtered)
        
        log_time(f"模拟策略筛选 (筛选出{filtered_count}条)", start)
    except Exception as e:
        log_time(f"模拟策略筛选失败: {e}", start)
    
    # 步骤4: Excel文件创建模拟
    start = time.time()
    try:
        # 模拟创建Excel文件
        test_df = pd.DataFrame({
            '股票代码': ['000001', '000002'],
            '股票名称': ['测试股票1', '测试股票2'],
            '技术强度': [85, 90]
        })
        
        test_file = 'temp_strategy_test.xlsx'
        test_df.to_excel(test_file, index=False)
        
        # 检查文件
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            os.remove(test_file)  # 清理
            log_time(f"模拟Excel创建 ({file_size}字节)", start)
        else:
            log_time("模拟Excel创建失败", start)
    except Exception as e:
        log_time(f"模拟Excel创建失败: {e}", start)
    
    # 总结
    total_elapsed = time.time() - overall_start
    print("\n" + "=" * 50)
    print("📋 性能测试总结")
    print("=" * 50)
    print(f"🕐 总测试时间: {total_elapsed:.3f}秒")
    
    if total_elapsed > 30:
        print("⚠️  警告: 测试时间过长，可能存在性能问题")
        print("可能的问题:")
        print("  - 文件I/O操作慢")
        print("  - 数据加载效率低")
        print("  - Excel操作耗时")
    elif total_elapsed > 10:
        print("⚠️  注意: 测试时间较长，需要优化")
    else:
        print("✅ 测试时间正常")
    
    return total_elapsed

def test_actual_strategy():
    """测试实际的策略执行"""
    print("\n" + "=" * 50)
    print("🎯 实际策略执行测试")
    print("=" * 50)
    
    try:
        # 导入并执行实际的策略
        import backtest_local
        
        # 测试一个简单的策略
        start = time.time()
        
        # 这里可以调用实际的策略执行函数
        # result = backtest_local.process_strategy(1)
        
        # 由于实际执行可能很慢，我们先模拟
        print("模拟实际策略执行...")
        time.sleep(0.1)  # 模拟一些处理时间
        
        elapsed = time.time() - start
        print(f"实际策略执行耗时: {elapsed:.3f}秒")
        
        return elapsed
        
    except Exception as e:
        print(f"实际策略执行失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 策略回测程序性能深度分析")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 基础性能测试
    basic_time = test_strategy_backtest()
    
    # 实际策略测试
    actual_time = test_actual_strategy()
    
    # 最终总结
    print("\n" + "=" * 60)
    print("🏁 最终分析结果")
    print("=" * 60)
    
    if basic_time and basic_time < 5:
        print("✅ 基础操作性能正常")
    else:
        print("⚠️  基础操作性能有问题")
    
    if actual_time and actual_time > 60:
        print("❌ 实际策略执行过慢，需要优化")
        print("建议检查:")
        print("  1. 数据预加载是否正常工作")
        print("  2. 是否有重复的文件读取")
        print("  3. 策略筛选逻辑是否高效")
        print("  4. Excel文件操作是否优化")
    elif actual_time:
        print("✅ 实际策略执行性能可接受")
    
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
