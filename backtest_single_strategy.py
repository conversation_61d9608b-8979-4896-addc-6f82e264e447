import pandas as pd
import os
import warnings
warnings.filterwarnings('ignore')

# 设置文件路径
base_dir = r'E:\机器学习\complete_excel_results'
summary_file = os.path.join(base_dir, '所有策略汇总.xlsx')
details_file = os.path.join(base_dir, '股票明细_完整.xlsx')  # 使用更完整的数据集
output_dir = os.path.join(base_dir, 'new_strategy_details')

# 确保输出目录存在
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 读取数据
print("读取策略汇总文件...")
summary_df = pd.read_excel(summary_file)
print("读取股票明细文件...")
stock_df = pd.read_excel(details_file)

# 将日期列转换为日期类型
stock_df['日期'] = pd.to_datetime(stock_df['日期'])

# 按股票代码和日期排序
stock_df = stock_df.sort_values(['股票代码', '日期'])

# 根据交易策略：选出当日目标股票，次日开盘买入，第二个交易日开盘卖出
# 盈利是基于买入当天的涨跌幅
print("准备交易数据...")

# 我们直接使用当天的涨跌幅作为收益率
# 因为策略是：选出当日目标股票，次日开盘买入，盈利是买入当天的涨跌幅
stock_df['交易收益率'] = stock_df['涨跌幅'] / 100  # 转换为小数形式

def backtest_strategy_manually(strategy_id):
    """
    手动回测特定策略

    参数:
    strategy_id: 策略ID

    返回:
    dict: 包含策略回测结果的字典
    """
    try:
        # 获取策略信息
        strategy_row = summary_df[summary_df['策略编号'] == strategy_id].iloc[0]
        strategy_desc = strategy_row['策略条件描述']

        print(f"回测策略 {strategy_id}: {strategy_desc}")

        # 解析策略条件
        conditions = []
        if "AND" in strategy_desc:
            condition_parts = strategy_desc.split("AND")
            for part in condition_parts:
                part = part.strip()
                conditions.append(part)
        else:
            conditions.append(strategy_desc.strip())

        # 应用条件筛选股票
        filtered_df = stock_df.copy()

        for condition in conditions:
            condition = condition.strip()
            print(f"应用条件: {condition}")

            # 解析条件
            if "大于等于" in condition:
                parts = condition.split("大于等于")
                col_name = parts[0].strip()
                value_part = parts[1].strip()

                # 提取数值
                if "（" in value_part:
                    value = float(value_part.split("（")[0].strip())
                else:
                    value = float(value_part)

                # 应用条件
                filtered_df = filtered_df[filtered_df[col_name] >= value]
                print(f"  应用 {col_name} >= {value} 后剩余记录数: {len(filtered_df)}")

            elif "小于等于" in condition:
                parts = condition.split("小于等于")
                col_name = parts[0].strip()
                value_part = parts[1].strip()

                # 提取数值
                if "（" in value_part:
                    value = float(value_part.split("（")[0].strip())
                else:
                    value = float(value_part)

                # 应用条件
                filtered_df = filtered_df[filtered_df[col_name] <= value]
                print(f"  应用 {col_name} <= {value} 后剩余记录数: {len(filtered_df)}")

            elif "为" in condition:
                parts = condition.split("为")
                col_name = parts[0].strip()
                value_part = parts[1].strip()

                # 提取数值
                if "（是）" in value_part:
                    value = 1
                elif "（否）" in value_part:
                    value = 0
                else:
                    value = value_part

                # 应用条件
                filtered_df = filtered_df[filtered_df[col_name] == value]
                print(f"  应用 {col_name} == {value} 后剩余记录数: {len(filtered_df)}")

        selected_stocks = filtered_df

        if len(selected_stocks) == 0:
            print(f"策略 {strategy_id} 没有选出任何股票")
            return {
                '总收益率(%)': 0,
                '平均收益率(%)': 0,
                '平均胜率(%)': 0,
                '平均每日交易笔数': 0,
                '总交易笔数': 0,
                '交易天数': 0,
                '总天数': len(stock_df['日期'].unique()),
                '交易频率(%)': 0,
                '选出的股票': pd.DataFrame()
            }

        # 计算策略统计数据
        total_return = selected_stocks['交易收益率'].sum()
        avg_return = selected_stocks['交易收益率'].mean()
        win_rate = (selected_stocks['交易收益率'] > 0).mean() * 100

        # 计算交易统计
        trade_days = len(selected_stocks['日期'].unique())
        total_days = len(stock_df['日期'].unique())
        trade_frequency = (trade_days / total_days) * 100 if total_days > 0 else 0
        total_trades = len(selected_stocks)
        avg_daily_trades = total_trades / trade_days if trade_days > 0 else 0

        print(f"策略 {strategy_id} 回测结果:")
        print(f"  总收益率(%): {round(total_return * 100, 2)}")
        print(f"  平均收益率(%): {round(avg_return * 100, 2)}")
        print(f"  平均胜率(%): {round(win_rate, 2)}")
        print(f"  总交易笔数: {total_trades}")
        print(f"  交易天数: {trade_days}")

        # 返回结果
        return {
            '总收益率(%)': round(total_return * 100, 2),
            '平均收益率(%)': round(avg_return * 100, 2),
            '平均胜率(%)': round(win_rate, 2),
            '平均每日交易笔数': round(avg_daily_trades, 2),
            '总交易笔数': total_trades,
            '交易天数': trade_days,
            '总天数': total_days,
            '交易频率(%)': round(trade_frequency, 2),
            '选出的股票': selected_stocks
        }
    except Exception as e:
        print(f"策略 {strategy_id} 回测过程中发生错误: {e}")
        return None

def process_strategy(strategy_id):
    """处理单个策略，更新汇总表并生成详细分析文件"""
    # 回测策略
    result = backtest_strategy_manually(strategy_id)

    if result:
        # 更新汇总表中的统计数据
        idx = summary_df[summary_df['策略编号'] == strategy_id].index[0]
        summary_df.loc[idx, '总收益率(%)'] = result['总收益率(%)']
        summary_df.loc[idx, '平均收益率(%)'] = result['平均收益率(%)']
        summary_df.loc[idx, '平均胜率(%)'] = result['平均胜率(%)']
        summary_df.loc[idx, '平均每日交易笔数'] = result['平均每日交易笔数']
        summary_df.loc[idx, '总交易笔数'] = result['总交易笔数']
        summary_df.loc[idx, '交易天数'] = result['交易天数']
        summary_df.loc[idx, '总天数'] = result['总天数']
        summary_df.loc[idx, '交易频率(%)'] = result['交易频率(%)']

        # 保存更新后的汇总表
        summary_df.to_excel(os.path.join(base_dir, '所有策略汇总_已回测.xlsx'), index=False)
        print(f"已更新策略 {strategy_id} 的汇总数据")

        # 保存详细分析文件
        detail_file = os.path.join(output_dir, f"strategy_{strategy_id}.xlsx")

        # 如果文件已存在，尝试删除
        if os.path.exists(detail_file):
            try:
                os.remove(detail_file)
                print(f"已删除现有文件: {detail_file}")
            except Exception as e:
                print(f"无法删除文件 {detail_file}: {e}")
                print("尝试使用不同的文件名...")
                detail_file = os.path.join(output_dir, f"strategy_{strategy_id}_{pd.Timestamp.now().strftime('%Y%m%d%H%M%S')}.xlsx")

        # 如果有选出的股票，保存详细分析
        if '选出的股票' in result and len(result['选出的股票']) > 0:
            try:
                # 创建一个Excel写入器
                with pd.ExcelWriter(detail_file, engine='openpyxl') as writer:
                    # 1. 策略汇总参数
                    strategy_summary = pd.DataFrame({
                        '策略编号': [strategy_id],
                        '策略组合': [summary_df.loc[idx, '策略组合']],
                        '特征数量': [summary_df.loc[idx, '特征数量']],
                        '总收益率(%)': [result['总收益率(%)']],
                        '平均收益率(%)': [result['平均收益率(%)']],
                        '平均胜率(%)': [result['平均胜率(%)']],
                        '平均每日交易笔数': [result['平均每日交易笔数']],
                        '总交易笔数': [result['总交易笔数']],
                        '交易天数': [result['交易天数']],
                        '总天数': [result['总天数']],
                        '交易频率(%)': [result['交易频率(%)']],
                    })
                    strategy_summary.to_excel(writer, sheet_name='策略汇总参数', index=False)

                    # 2. 策略参数
                    strategy_params = pd.DataFrame({
                        '策略编号': [strategy_id],
                        '策略条件描述': [summary_df.loc[idx, '策略条件描述']],
                    })
                    strategy_params.to_excel(writer, sheet_name='策略参数', index=False)

                    # 3. 选股明细
                    selected_stocks = result['选出的股票'].copy()

                    # 添加选股日期
                    selected_stocks['选股日期'] = selected_stocks['日期']

                    # 获取所有交易日期列表（按日期排序）
                    all_trading_dates = sorted(stock_df['日期'].unique())

                    # 创建日期映射字典，用于查找下一个交易日
                    next_trading_day = {}
                    for i in range(len(all_trading_dates) - 1):
                        next_trading_day[all_trading_dates[i]] = all_trading_dates[i + 1]

                    # 创建日期映射字典，用于查找下下一个交易日
                    next_next_trading_day = {}
                    for i in range(len(all_trading_dates) - 2):
                        next_next_trading_day[all_trading_dates[i]] = all_trading_dates[i + 2]

                    # 添加买入日期（下一个交易日）和卖出日期（下下一个交易日）
                    selected_stocks['买入日期'] = selected_stocks['选股日期'].apply(
                        lambda x: next_trading_day.get(x, x + pd.Timedelta(days=1))
                    )
                    selected_stocks['卖出日期'] = selected_stocks['选股日期'].apply(
                        lambda x: next_next_trading_day.get(x, x + pd.Timedelta(days=2))
                    )

                    # 添加买入日涨跌幅（就是当前的涨跌幅）
                    selected_stocks['买入日涨跌幅'] = selected_stocks['涨跌幅']

                    # 创建股票代码和日期的组合键，用于查找卖出日涨跌幅
                    stock_df['股票日期键'] = stock_df['股票代码'] + '_' + stock_df['日期'].dt.strftime('%Y-%m-%d')

                    # 创建涨跌幅查找字典
                    price_change_dict = dict(zip(stock_df['股票日期键'], stock_df['涨跌幅']))

                    # 添加卖出日涨跌幅
                    selected_stocks['卖出日股票涨跌幅'] = selected_stocks.apply(
                        lambda row: price_change_dict.get(
                            row['股票代码'] + '_' + row['卖出日期'].strftime('%Y-%m-%d'),
                            0  # 如果找不到对应的卖出日数据，默认为0
                        ),
                        axis=1
                    )

                    # 计算每只股票的收益情况
                    selected_stocks['是否盈利'] = selected_stocks['交易收益率'] > 0

                    # 重新组织列顺序
                    stock_details = selected_stocks[[
                        '股票代码', '股票名称', '选股日期', '技术强度', '涨跌幅',
                        '买入日期', '买入日涨跌幅', '卖出日期', '卖出日股票涨跌幅', '是否盈利'
                    ]].copy()

                    # 添加策略信息
                    stock_details['策略编号'] = strategy_id

                    # 保存到Excel
                    stock_details.to_excel(writer, sheet_name='选股明细', index=False)
            except Exception as e:
                print(f"保存Excel文件时出错: {e}")
                return False

            print(f"已保存策略 {strategy_id} 的详细分析到 {detail_file}")
            return True
        else:
            print(f"策略 {strategy_id} 没有选出任何股票，不生成详细分析文件")
            return False

    return False

# 主函数
def main():
    # 获取所有策略ID
    strategy_ids = summary_df['策略编号'].tolist()

    # 统计成功和失败的策略
    success_count = 0
    fail_count = 0

    # 逐个处理策略
    for strategy_id in strategy_ids:
        print(f"\n{'='*50}")
        print(f"开始回测策略 {strategy_id}...")

        # 处理策略
        success = process_strategy(strategy_id)

        if success:
            print(f"策略 {strategy_id} 回测完成并已更新汇总表")
            success_count += 1
        else:
            print(f"策略 {strategy_id} 回测失败或未选出股票")
            fail_count += 1

        print(f"{'='*50}\n")

    # 打印总结
    print(f"\n回测完成！成功: {success_count}, 失败: {fail_count}, 总计: {len(strategy_ids)}")

if __name__ == "__main__":
    main()
