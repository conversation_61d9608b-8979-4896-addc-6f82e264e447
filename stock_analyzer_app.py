"""
股票分析器主程序

整合数据获取和技术指标组合策略
"""

import os
import sys
import datetime
import pandas as pd
import numpy as np
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkcalendar import DateEntry
import threading
import data_fetcher
import technical_indicator_strategy as tis

class StockAnalyzerApp:
    """股票分析器应用程序"""
    
    def __init__(self, root):
        """初始化应用程序"""
        self.root = root
        self.root.title("股票分析器")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 创建标签页
        self.notebook = ttk.Notebook(root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建数据获取标签页
        self.data_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_frame, text="数据获取")
        self.setup_data_frame()
        
        # 创建策略回测标签页
        self.backtest_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.backtest_frame, text="策略回测")
        self.setup_backtest_frame()
        
        # 创建股票推荐标签页
        self.recommend_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.recommend_frame, text="股票推荐")
        self.setup_recommend_frame()
        
        # 创建状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_data_frame(self):
        """设置数据获取标签页"""
        # 创建数据获取表单
        form_frame = ttk.LabelFrame(self.data_frame, text="数据获取设置")
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 开始日期
        ttk.Label(form_frame, text="开始日期:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.start_date_entry = DateEntry(form_frame, width=12, background='darkblue', foreground='white', borderwidth=2)
        self.start_date_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 结束日期
        ttk.Label(form_frame, text="结束日期:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.end_date_entry = DateEntry(form_frame, width=12, background='darkblue', foreground='white', borderwidth=2)
        self.end_date_entry.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 最大股票数量
        ttk.Label(form_frame, text="最大股票数量:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.max_stocks_var = tk.StringVar(value="")
        self.max_stocks_entry = ttk.Entry(form_frame, textvariable=self.max_stocks_var, width=15)
        self.max_stocks_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 输出文件
        ttk.Label(form_frame, text="输出文件:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.output_file_var = tk.StringVar(value="股票明细.xlsx")
        self.output_file_entry = ttk.Entry(form_frame, textvariable=self.output_file_var, width=30)
        self.output_file_entry.grid(row=3, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 浏览按钮
        self.browse_button = ttk.Button(form_frame, text="浏览...", command=self.browse_output_file)
        self.browse_button.grid(row=3, column=2, padx=5, pady=5, sticky=tk.W)
        
        # Tushare Token
        ttk.Label(form_frame, text="Tushare Token:").grid(row=4, column=0, padx=5, pady=5, sticky=tk.W)
        self.token_var = tk.StringVar(value="")
        self.token_entry = ttk.Entry(form_frame, textvariable=self.token_var, width=30)
        self.token_entry.grid(row=4, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 获取数据按钮
        self.fetch_button = ttk.Button(form_frame, text="获取数据", command=self.fetch_data)
        self.fetch_button.grid(row=5, column=0, columnspan=3, padx=5, pady=10)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(form_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=6, column=0, columnspan=3, padx=5, pady=5, sticky=tk.EW)
        
        # 日志框
        log_frame = ttk.LabelFrame(self.data_frame, text="日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, width=80, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(self.log_text, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)
    
    def setup_backtest_frame(self):
        """设置策略回测标签页"""
        # 创建回测表单
        form_frame = ttk.LabelFrame(self.backtest_frame, text="回测设置")
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 开始日期
        ttk.Label(form_frame, text="开始日期:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.backtest_start_date_entry = DateEntry(form_frame, width=12, background='darkblue', foreground='white', borderwidth=2)
        self.backtest_start_date_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 结束日期
        ttk.Label(form_frame, text="结束日期:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.backtest_end_date_entry = DateEntry(form_frame, width=12, background='darkblue', foreground='white', borderwidth=2)
        self.backtest_end_date_entry.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 数据文件
        ttk.Label(form_frame, text="数据文件:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.backtest_data_file_var = tk.StringVar(value="股票明细.xlsx")
        self.backtest_data_file_entry = ttk.Entry(form_frame, textvariable=self.backtest_data_file_var, width=30)
        self.backtest_data_file_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 浏览按钮
        self.backtest_browse_button = ttk.Button(form_frame, text="浏览...", command=self.browse_backtest_data_file)
        self.backtest_browse_button.grid(row=2, column=2, padx=5, pady=5, sticky=tk.W)
        
        # 输出文件
        ttk.Label(form_frame, text="输出文件:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        self.backtest_output_file_var = tk.StringVar(value="技术指标组合策略回测结果.txt")
        self.backtest_output_file_entry = ttk.Entry(form_frame, textvariable=self.backtest_output_file_var, width=30)
        self.backtest_output_file_entry.grid(row=3, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 浏览按钮
        self.backtest_output_browse_button = ttk.Button(form_frame, text="浏览...", command=self.browse_backtest_output_file)
        self.backtest_output_browse_button.grid(row=3, column=2, padx=5, pady=5, sticky=tk.W)
        
        # 回测按钮
        self.backtest_button = ttk.Button(form_frame, text="开始回测", command=self.run_backtest)
        self.backtest_button.grid(row=4, column=0, columnspan=3, padx=5, pady=10)
        
        # 回测结果框
        result_frame = ttk.LabelFrame(self.backtest_frame, text="回测结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.backtest_result_text = tk.Text(result_frame, wrap=tk.WORD, width=80, height=15)
        self.backtest_result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(self.backtest_result_text, command=self.backtest_result_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.backtest_result_text.config(yscrollcommand=scrollbar.set)
    
    def setup_recommend_frame(self):
        """设置股票推荐标签页"""
        # 创建推荐表单
        form_frame = ttk.LabelFrame(self.recommend_frame, text="推荐设置")
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 日期
        ttk.Label(form_frame, text="日期:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.recommend_date_entry = DateEntry(form_frame, width=12, background='darkblue', foreground='white', borderwidth=2)
        self.recommend_date_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 数据文件
        ttk.Label(form_frame, text="数据文件:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.recommend_data_file_var = tk.StringVar(value="股票明细.xlsx")
        self.recommend_data_file_entry = ttk.Entry(form_frame, textvariable=self.recommend_data_file_var, width=30)
        self.recommend_data_file_entry.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 浏览按钮
        self.recommend_browse_button = ttk.Button(form_frame, text="浏览...", command=self.browse_recommend_data_file)
        self.recommend_browse_button.grid(row=1, column=2, padx=5, pady=5, sticky=tk.W)
        
        # 输出文件
        ttk.Label(form_frame, text="输出文件:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.recommend_output_file_var = tk.StringVar(value="技术指标组合策略推荐股票.xlsx")
        self.recommend_output_file_entry = ttk.Entry(form_frame, textvariable=self.recommend_output_file_var, width=30)
        self.recommend_output_file_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 浏览按钮
        self.recommend_output_browse_button = ttk.Button(form_frame, text="浏览...", command=self.browse_recommend_output_file)
        self.recommend_output_browse_button.grid(row=2, column=2, padx=5, pady=5, sticky=tk.W)
        
        # 推荐按钮
        self.recommend_button = ttk.Button(form_frame, text="生成推荐", command=self.run_recommend)
        self.recommend_button.grid(row=3, column=0, columnspan=3, padx=5, pady=10)
        
        # 推荐结果框
        result_frame = ttk.LabelFrame(self.recommend_frame, text="推荐结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建表格
        columns = ('股票代码', '股票名称', '技术强度', '连续技术强度5天数', '看涨技术指标数量')
        self.recommend_tree = ttk.Treeview(result_frame, columns=columns, show='headings')
        
        # 设置列标题
        for col in columns:
            self.recommend_tree.heading(col, text=col)
            self.recommend_tree.column(col, width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.recommend_tree.yview)
        self.recommend_tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.recommend_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def browse_output_file(self):
        """浏览输出文件"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if filename:
            self.output_file_var.set(filename)
    
    def browse_backtest_data_file(self):
        """浏览回测数据文件"""
        filename = filedialog.askopenfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if filename:
            self.backtest_data_file_var.set(filename)
    
    def browse_backtest_output_file(self):
        """浏览回测输出文件"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.backtest_output_file_var.set(filename)
    
    def browse_recommend_data_file(self):
        """浏览推荐数据文件"""
        filename = filedialog.askopenfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if filename:
            self.recommend_data_file_var.set(filename)
    
    def browse_recommend_output_file(self):
        """浏览推荐输出文件"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if filename:
            self.recommend_output_file_var.set(filename)
    
    def fetch_data(self):
        """获取数据"""
        # 获取参数
        start_date = self.start_date_entry.get_date().strftime('%Y%m%d')
        end_date = self.end_date_entry.get_date().strftime('%Y%m%d')
        max_stocks = self.max_stocks_var.get()
        output_file = self.output_file_var.get()
        token = self.token_var.get()
        
        # 验证参数
        if not token:
            messagebox.showerror("错误", "请输入Tushare Token")
            return
        
        # 设置Tushare Token
        data_fetcher.TUSHARE_TOKEN = token
        
        # 转换max_stocks为整数
        if max_stocks:
            try:
                max_stocks = int(max_stocks)
            except ValueError:
                messagebox.showerror("错误", "最大股票数量必须是整数")
                return
        else:
            max_stocks = None
        
        # 禁用获取数据按钮
        self.fetch_button.config(state=tk.DISABLED)
        
        # 清空日志
        self.log_text.delete(1.0, tk.END)
        
        # 更新状态
        self.status_var.set("正在获取数据...")
        
        # 在新线程中获取数据
        thread = threading.Thread(target=self._fetch_data_thread, args=(start_date, end_date, max_stocks, output_file))
        thread.daemon = True
        thread.start()
    
    def _fetch_data_thread(self, start_date, end_date, max_stocks, output_file):
        """在新线程中获取数据"""
        try:
            # 重定向标准输出到日志框
            original_stdout = sys.stdout
            sys.stdout = self
            
            # 获取数据
            success = data_fetcher.fetch_all_stock_data(start_date, end_date, max_stocks, output_file)
            
            # 恢复标准输出
            sys.stdout = original_stdout
            
            # 更新UI
            self.root.after(0, self._update_ui_after_fetch, success)
        except Exception as e:
            # 恢复标准输出
            sys.stdout = original_stdout
            
            # 更新UI
            self.root.after(0, self._update_ui_after_fetch, False, str(e))
    
    def _update_ui_after_fetch(self, success, error_msg=None):
        """更新UI"""
        # 启用获取数据按钮
        self.fetch_button.config(state=tk.NORMAL)
        
        # 更新状态
        if success:
            self.status_var.set("数据获取成功")
            messagebox.showinfo("成功", "数据获取成功")
        else:
            self.status_var.set("数据获取失败")
            if error_msg:
                messagebox.showerror("错误", f"数据获取失败: {error_msg}")
            else:
                messagebox.showerror("错误", "数据获取失败")
    
    def run_backtest(self):
        """运行回测"""
        # 获取参数
        start_date = self.backtest_start_date_entry.get_date().strftime('%Y-%m-%d')
        end_date = self.backtest_end_date_entry.get_date().strftime('%Y-%m-%d')
        data_file = self.backtest_data_file_var.get()
        output_file = self.backtest_output_file_var.get()
        
        # 验证参数
        if not os.path.exists(data_file):
            messagebox.showerror("错误", f"数据文件 {data_file} 不存在")
            return
        
        # 禁用回测按钮
        self.backtest_button.config(state=tk.DISABLED)
        
        # 清空回测结果
        self.backtest_result_text.delete(1.0, tk.END)
        
        # 更新状态
        self.status_var.set("正在回测...")
        
        # 在新线程中运行回测
        thread = threading.Thread(target=self._run_backtest_thread, args=(data_file, start_date, end_date, output_file))
        thread.daemon = True
        thread.start()
    
    def _run_backtest_thread(self, data_file, start_date, end_date, output_file):
        """在新线程中运行回测"""
        try:
            # 加载数据
            data = tis.load_data(data_file)
            
            if data is None:
                self.root.after(0, self._update_ui_after_backtest, False, "数据加载失败")
                return
            
            # 预处理数据
            processed_data = tis.preprocess_data(data)
            
            # 回测策略
            result = tis.backtest_technical_indicator_strategy(processed_data, start_date, end_date, output_file)
            
            # 更新UI
            self.root.after(0, self._update_ui_after_backtest, True, result)
        except Exception as e:
            # 更新UI
            self.root.after(0, self._update_ui_after_backtest, False, str(e))
    
    def _update_ui_after_backtest(self, success, result):
        """更新UI"""
        # 启用回测按钮
        self.backtest_button.config(state=tk.NORMAL)
        
        # 更新状态
        if success:
            self.status_var.set("回测完成")
            self.backtest_result_text.insert(tk.END, result)
        else:
            self.status_var.set("回测失败")
            messagebox.showerror("错误", f"回测失败: {result}")
    
    def run_recommend(self):
        """运行推荐"""
        # 获取参数
        date = self.recommend_date_entry.get_date().strftime('%Y-%m-%d')
        data_file = self.recommend_data_file_var.get()
        output_file = self.recommend_output_file_var.get()
        
        # 验证参数
        if not os.path.exists(data_file):
            messagebox.showerror("错误", f"数据文件 {data_file} 不存在")
            return
        
        # 禁用推荐按钮
        self.recommend_button.config(state=tk.DISABLED)
        
        # 清空推荐结果
        for item in self.recommend_tree.get_children():
            self.recommend_tree.delete(item)
        
        # 更新状态
        self.status_var.set("正在生成推荐...")
        
        # 在新线程中运行推荐
        thread = threading.Thread(target=self._run_recommend_thread, args=(data_file, date, output_file))
        thread.daemon = True
        thread.start()
    
    def _run_recommend_thread(self, data_file, date, output_file):
        """在新线程中运行推荐"""
        try:
            # 加载数据
            data = tis.load_data(data_file)
            
            if data is None:
                self.root.after(0, self._update_ui_after_recommend, False, "数据加载失败")
                return
            
            # 预处理数据
            processed_data = tis.preprocess_data(data)
            
            # 生成推荐
            recommended_stocks = tis.recommend_stocks(processed_data, date, output_file)
            
            # 更新UI
            self.root.after(0, self._update_ui_after_recommend, True, recommended_stocks)
        except Exception as e:
            # 更新UI
            self.root.after(0, self._update_ui_after_recommend, False, str(e))
    
    def _update_ui_after_recommend(self, success, result):
        """更新UI"""
        # 启用推荐按钮
        self.recommend_button.config(state=tk.NORMAL)
        
        # 更新状态
        if success:
            self.status_var.set("推荐生成成功")
            
            # 更新推荐结果表格
            for item in self.recommend_tree.get_children():
                self.recommend_tree.delete(item)
            
            for _, row in result.iterrows():
                self.recommend_tree.insert('', tk.END, values=(
                    row['股票代码'],
                    row['股票名称'],
                    row['技术强度'],
                    row['连续技术强度5天数'],
                    row['看涨技术指标数量']
                ))
            
            messagebox.showinfo("成功", f"推荐生成成功，共 {len(result)} 只股票")
        else:
            self.status_var.set("推荐生成失败")
            messagebox.showerror("错误", f"推荐生成失败: {result}")
    
    def write(self, text):
        """将文本写入日志框"""
        self.log_text.insert(tk.END, text)
        self.log_text.see(tk.END)
    
    def flush(self):
        """刷新日志框"""
        self.log_text.update_idletasks()

def main():
    """主函数"""
    root = tk.Tk()
    app = StockAnalyzerApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
