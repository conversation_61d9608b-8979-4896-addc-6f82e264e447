import pandas as pd
import os

# 查找所有可能的汇总表文件
base_dir = "E:/机器学习"
possible_files = [
    "所有策略汇总.xlsx",
    "所有策略汇总_已回测.xlsx",
    "策略汇总.xlsx",
    "策略汇总表.xlsx"
]

for file_name in possible_files:
    file_path = os.path.join(base_dir, file_name)
    if os.path.exists(file_path):
        print(f"找到汇总表文件: {file_path}")
        try:
            df = pd.read_excel(file_path)
            print(f"列名: {df.columns.tolist()}")
            print(f"行数: {len(df)}")
            print(f"前5行数据:")
            print(df.head())
        except Exception as e:
            print(f"读取文件时出错: {e}")
    else:
        print(f"文件不存在: {file_path}")

# 查找其他可能的Excel文件
excel_files = [f for f in os.listdir(base_dir) if f.endswith('.xlsx') or f.endswith('.xls')]
print(f"\n在 {base_dir} 目录下找到的所有Excel文件:")
for file in excel_files:
    print(f"- {file}")
