"""
使用akshare库下载股票数据
按日期遍历下载
"""

import pandas as pd
import akshare as ak
import os
import datetime
import time
import stock_data_manager as sdm

# 默认文件路径
DEFAULT_BASE_DIR = r'E:\机器学习\complete_excel_results'

# 这些变量将在main函数中设置
base_dir = None
stock_data_dir = None
daily_data_dir = None
stock_details_file = None
history_data_file = None

def setup_directories(output_dir=None):
    """
    设置目录结构

    参数:
        output_dir: 输出目录，如果为None则使用默认目录
    """
    global base_dir, stock_data_dir, daily_data_dir, stock_details_file, history_data_file

    # 设置基础目录
    base_dir = output_dir if output_dir else DEFAULT_BASE_DIR

    # 设置其他目录和文件路径
    stock_data_dir = os.path.join(base_dir, 'stock_data')
    daily_data_dir = os.path.join(stock_data_dir, 'daily')
    stock_details_file = os.path.join(base_dir, '股票明细_完整.xlsx')
    history_data_file = os.path.join(stock_data_dir, 'stock_history_data.xlsx')

    # 确保目录存在
    if not os.path.exists(stock_data_dir):
        os.makedirs(stock_data_dir)
    if not os.path.exists(daily_data_dir):
        os.makedirs(daily_data_dir)

    # 更新stock_data_manager模块中的路径
    sdm.base_dir = base_dir
    sdm.stock_data_dir = stock_data_dir
    sdm.daily_data_dir = daily_data_dir
    sdm.stock_details_file = stock_details_file
    sdm.history_data_file = history_data_file

def get_all_stock_codes():
    """获取所有A股股票代码"""
    # 直接使用akshare获取所有A股股票代码，不从文件读取
    try:
        # 获取A股所有股票的基本信息
        stock_info_df = ak.stock_info_a_code_name()

        # 确保股票代码格式正确（去掉可能的前缀）
        stock_codes = []
        for code in stock_info_df['code'].tolist():
            # 如果代码带有前缀（如sh或sz），去掉前缀
            if code.startswith('sh') or code.startswith('sz'):
                code = code[2:]
            stock_codes.append(code)

        print(f"使用akshare获取到 {len(stock_codes)} 只A股股票")
        return stock_codes
    except Exception as e:
        print(f"使用akshare获取股票代码时出错: {e}")
        return []

def get_trading_dates(start_date, end_date):
    """获取交易日历"""
    try:
        # 使用akshare获取交易日历
        tool_trade_date_hist_sina_df = ak.tool_trade_date_hist_sina()
        # 转换日期列为日期类型
        tool_trade_date_hist_sina_df['trade_date'] = pd.to_datetime(tool_trade_date_hist_sina_df['trade_date'])

        # 筛选指定日期范围内的交易日
        start_date_dt = pd.to_datetime(start_date)
        end_date_dt = pd.to_datetime(end_date)
        trading_dates = tool_trade_date_hist_sina_df[
            (tool_trade_date_hist_sina_df['trade_date'] >= start_date_dt) &
            (tool_trade_date_hist_sina_df['trade_date'] <= end_date_dt)
        ]['trade_date'].tolist()

        return trading_dates
    except Exception as e:
        print(f"获取交易日历时出错: {e}")
        # 如果获取失败，则使用日期范围内的所有日期
        date_range = pd.date_range(start=start_date, end=end_date)
        print(f"使用日期范围内的所有日期作为交易日: {len(date_range)} 天")
        return date_range

def download_stock_data_for_date(date_str):
    """
    下载指定日期的所有股票数据

    参数:
        date_str: 日期字符串，格式为YYYY-MM-DD

    返回:
        DataFrame: 包含所有股票在该日期的数据
    """
    print(f"开始下载日期 {date_str} 的股票数据")
    start_time = time.time()

    try:
        # 不检查日期是否为交易日，直接尝试下载数据
        print(f"开始下载日期 {date_str} 的所有股票数据")

        # 获取所有股票代码
        stock_codes = get_all_stock_codes()
        if not stock_codes:
            print("没有找到任何股票代码")
            return pd.DataFrame()

        print(f"共找到 {len(stock_codes)} 只股票，开始批量下载日期 {date_str} 的数据")

        # 使用akshare的stock_zh_a_spot_em接口获取当日所有A股行情数据
        try:
            # 这个接口获取当日行情，如果是历史日期可能无法获取
            stock_data_df = ak.stock_zh_a_spot_em()

            # 检查是否获取到数据
            if not stock_data_df.empty:
                print(f"成功获取到 {len(stock_data_df)} 只股票的数据")

                # 添加日期列
                stock_data_df['date'] = date_str

                # 打印原始数据结构
                print(f"原始数据列名: {stock_data_df.columns.tolist()}")
                print(f"原始数据前5行:\n{stock_data_df.head()}")

                # 重命名列，使其与我们的数据结构一致
                column_mapping = {
                    '代码': 'code',
                    '名称': 'name',
                    '今开': 'open',
                    '最高': 'high',
                    '最低': 'low',
                    '最新价': 'close',
                    '成交量': 'volume',
                    '成交额': 'amount',
                    '涨跌幅': 'pctChg'
                }

                # 应用列映射
                renamed_columns = {}
                for old_col, new_col in column_mapping.items():
                    if old_col in stock_data_df.columns:
                        renamed_columns[old_col] = new_col

                stock_data_df = stock_data_df.rename(columns=renamed_columns)

                # 确保必要的列存在
                required_columns = ['date', 'code', 'open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']
                for col in required_columns:
                    if col not in stock_data_df.columns:
                        print(f"警告: 列 '{col}' 不存在，将创建空列")
                        stock_data_df[col] = None

                # 选择需要的列
                available_columns = [col for col in required_columns if col in stock_data_df.columns]
                result_df = stock_data_df[available_columns]

                end_time = time.time()
                elapsed_time = end_time - start_time

                print(f"日期 {date_str} 的股票数据下载完成，共 {len(result_df)} 条记录，耗时: {elapsed_time:.2f}秒")

                return result_df
        except Exception as e:
            print(f"使用stock_zh_a_spot_em接口下载数据时出错: {e}")

        # 如果上面的方法失败，尝试使用另一种方法
        print("尝试使用另一种方法下载历史数据...")

        # 使用akshare的stock_zh_a_hist接口逐只股票下载历史数据
        # 但是我们会使用多批次并行下载来提高效率
        batch_size = 50  # 每批次处理的股票数量
        all_stock_data = []

        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i+batch_size]
            print(f"正在下载第 {i//batch_size + 1} 批股票数据，共 {len(batch_codes)} 只股票")

            for stock_code in batch_codes:
                try:
                    # 根据股票代码判断是上证还是深证
                    if stock_code.startswith('6'):
                        stock_code_full = f"sh{stock_code}"
                    else:
                        stock_code_full = f"sz{stock_code}"

                    # 获取单只股票的历史数据（仅指定日期）
                    stock_hist_df = ak.stock_zh_a_hist(
                        symbol=stock_code_full,
                        period="daily",
                        start_date=date_str,
                        end_date=date_str,
                        adjust="qfq"
                    )

                    # 如果有数据，添加到结果中
                    if not stock_hist_df.empty:
                        # 添加股票代码列
                        stock_hist_df['code'] = stock_code

                        # 重命名列，使其与我们的数据结构一致
                        stock_hist_df = stock_hist_df.rename(columns={
                            '日期': 'date',
                            '开盘': 'open',
                            '最高': 'high',
                            '最低': 'low',
                            '收盘': 'close',
                            '成交量': 'volume',
                            '成交额': 'amount',
                            '涨跌幅': 'pctChg'
                        })

                        # 选择需要的列
                        columns = ['date', 'code', 'open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']
                        available_columns = [col for col in columns if col in stock_hist_df.columns]
                        stock_data = stock_hist_df[available_columns]

                        all_stock_data.append(stock_data)
                except Exception as e:
                    # 不打印每只股票的错误，避免输出过多
                    continue

            print(f"第 {i//batch_size + 1} 批数据下载完成，当前已获取 {len(all_stock_data)} 只股票的数据")

        # 合并所有股票的数据
        if all_stock_data:
            result_df = pd.concat(all_stock_data, ignore_index=True)

            # 确保必要的列存在
            required_columns = ['date', 'code', 'open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']
            for col in required_columns:
                if col not in result_df.columns:
                    print(f"警告: 列 '{col}' 不存在，将创建空列")
                    result_df[col] = None

            # 选择需要的列
            result_df = result_df[required_columns]

            end_time = time.time()
            elapsed_time = end_time - start_time

            print(f"日期 {date_str} 的股票数据下载完成，共 {len(result_df)} 条记录，耗时: {elapsed_time:.2f}秒")

            return result_df
        else:
            print(f"日期 {date_str} 没有找到任何股票数据")
            return pd.DataFrame()
    except Exception as e:
        print(f"下载日期 {date_str} 的股票数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def download_stock_history_by_code(stock_code, start_date, end_date):
    """
    下载单只股票的历史数据

    参数:
        stock_code: 股票代码，如"600000"
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD

    返回:
        DataFrame: 包含该股票在日期范围内的历史数据
    """
    try:
        # 使用akshare获取单只股票的历史数据
        # 根据股票代码判断是上证还是深证
        if stock_code.startswith('6'):
            stock_code_full = f"sh{stock_code}"
        else:
            stock_code_full = f"sz{stock_code}"

        # 获取日K线数据
        stock_zh_a_hist_df = ak.stock_zh_a_hist(symbol=stock_code_full, period="daily", start_date=start_date, end_date=end_date, adjust="qfq")

        # 添加股票代码列
        stock_zh_a_hist_df['code'] = stock_code

        # 重命名列，使其与我们的数据结构一致
        stock_zh_a_hist_df = stock_zh_a_hist_df.rename(columns={
            '日期': 'date',
            '开盘': 'open',
            '最高': 'high',
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume',
            '成交额': 'amount',
            '涨跌幅': 'pctChg'
        })

        # 选择需要的列
        columns = ['date', 'code', 'open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']
        available_columns = [col for col in columns if col in stock_zh_a_hist_df.columns]
        result_df = stock_zh_a_hist_df[available_columns]

        return result_df
    except Exception as e:
        print(f"下载股票 {stock_code} 的历史数据时出错: {e}")
        return pd.DataFrame()

def remove_trading_calendar_file(start_date, end_date):
    """
    删除交易日历文件

    参数:
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD
    """
    # 构造可能的交易日历文件名
    calendar_file = os.path.join(stock_data_dir, f'trading_calendar_{start_date.replace("-", "")}_{end_date.replace("-", "")}.xlsx')

    # 检查文件是否存在，如果存在则删除
    if os.path.exists(calendar_file):
        try:
            os.remove(calendar_file)
            print(f"已删除交易日历文件: {calendar_file}")
        except Exception as e:
            print(f"删除交易日历文件时出错: {e}")

def remove_history_data_file(start_date=None, end_date=None):
    """
    删除合并的历史数据文件

    参数:
        start_date: 开始日期，格式为YYYY-MM-DD，如果为None则删除所有合并文件
        end_date: 结束日期，格式为YYYY-MM-DD，如果为None则删除所有合并文件
    """
    if start_date is not None and end_date is not None:
        # 删除指定日期范围的合并文件
        history_file = os.path.join(stock_data_dir, f'stock_history_data_{start_date.replace("-", "")}_{end_date.replace("-", "")}.xlsx')
        if os.path.exists(history_file):
            try:
                os.remove(history_file)
                print(f"已删除合并数据文件: {history_file}")
            except Exception as e:
                print(f"删除合并数据文件时出错: {e}")
    else:
        # 删除所有合并文件
        import glob
        history_files = glob.glob(os.path.join(stock_data_dir, 'stock_history_data_*.xlsx'))
        for file in history_files:
            try:
                os.remove(file)
                print(f"已删除合并数据文件: {file}")
            except Exception as e:
                print(f"删除合并数据文件时出错: {e}")

def download_all_stocks_for_date_range(start_date, end_date):
    """
    下载指定日期范围内的所有股票数据

    参数:
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD

    返回:
        DataFrame: 包含所有股票在日期范围内的数据
    """
    print(f"开始下载从 {start_date} 到 {end_date} 的所有股票数据")
    start_time = time.time()

    # 获取所有股票代码
    stock_codes = get_all_stock_codes()
    if not stock_codes:
        print("没有找到任何股票代码")
        return pd.DataFrame()

    # 获取交易日列表
    trading_dates = get_trading_dates(start_date, end_date)
    if not trading_dates:
        print("没有找到任何交易日")
        return pd.DataFrame()

    print(f"共找到 {len(stock_codes)} 只股票和 {len(trading_dates)} 个交易日")

    # 按日期下载数据
    all_data = []
    total_records = 0

    for day in trading_dates:
        if isinstance(day, datetime.datetime):
            day_str = day.strftime('%Y-%m-%d')
        else:
            day_str = day

        # 检查该日期的数据文件是否已存在
        daily_file_path = sdm.get_daily_data_path(day_str)
        if os.path.exists(daily_file_path):
            print(f"日期 {day_str} 的数据文件已存在，跳过")

            # 读取已存在的数据，用于合并
            try:
                day_df = sdm.load_daily_data(day_str)
                if not day_df.empty:
                    all_data.append(day_df)
                    total_records += len(day_df)
                    print(f"已加载日期 {day_str} 的现有数据，共 {len(day_df)} 条记录")
            except Exception as e:
                print(f"加载日期 {day_str} 的现有数据时出错: {e}")

            continue

        # 下载该日期的数据
        day_df = download_stock_data_for_date(day_str)

        # 保存该日期的数据
        if not day_df.empty:
            sdm.save_daily_data(day_df, day_str)
            print(f"日期 {day_str} 的数据已保存")

            all_data.append(day_df)
            total_records += len(day_df)

    # 合并所有数据
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)

        end_time = time.time()
        elapsed_time = end_time - start_time

        print(f"日期范围 {start_date} 到 {end_date} 的数据下载完成，共 {len(combined_df)} 条记录，耗时: {elapsed_time:.2f}秒")

        # 删除可能生成的交易日历文件
        remove_trading_calendar_file(start_date, end_date)

        # 不再保存合并文件，直接返回
        return combined_df
    else:
        print(f"日期范围 {start_date} 到 {end_date} 没有下载到任何数据")
        return pd.DataFrame()

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='使用akshare下载股票数据')
    parser.add_argument('--start_date', type=str, default='2025-02-05', help='开始日期，格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, default='2025-02-05', help='结束日期，格式为YYYY-MM-DD')
    parser.add_argument('--clean', action='store_true', help='删除所有合并数据文件')
    parser.add_argument('--output_dir', type=str, help='数据输出目录，默认为E:\\机器学习\\complete_excel_results')

    args = parser.parse_args()

    # 设置目录结构
    setup_directories(args.output_dir)

    print(f"数据将保存到: {base_dir}")
    print(f"每日数据目录: {daily_data_dir}")

    # 如果指定了--clean参数，则删除所有合并文件
    if args.clean:
        remove_history_data_file()
        print("已清理所有合并数据文件")

    try:
        # 执行下载
        download_all_stocks_for_date_range(args.start_date, args.end_date)
    finally:
        # 确保删除交易日历文件和当前日期范围的合并文件，即使下载过程中出错
        remove_trading_calendar_file(args.start_date, args.end_date)
        remove_history_data_file(args.start_date, args.end_date)
