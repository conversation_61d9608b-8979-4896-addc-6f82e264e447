#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成策略Excel文件
作者: Augment AI
版本: 1.0.0

该脚本用于从股票历史数据生成策略统计结果并输出到Excel文件。
使用方法:
    python generate_strategy_excel.py

该脚本会读取股票历史数据，生成所有可能的特征组合和筛选条件组合，
对每个策略进行回测，并将结果保存到Excel文件中。
"""

import os
import pandas as pd
import numpy as np
import itertools
from datetime import datetime

def read_stock_data(data_file):
    """
    读取股票历史数据

    参数:
        data_file (str): 股票历史数据文件路径

    返回:
        DataFrame: 股票历史数据
    """
    print(f"正在读取股票历史数据: {data_file}")
    df = pd.read_excel(data_file)

    # 确保日期列是日期类型
    if '日期' in df.columns:
        df['日期'] = pd.to_datetime(df['日期'])

    print(f"成功读取股票历史数据，共{len(df)}条记录")
    return df

def generate_feature_conditions(feature):
    """
    生成特征的所有可能筛选条件

    参数:
        feature (str): 特征名称

    返回:
        list: 条件列表
    """
    if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
                 '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势',
                 '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势',
                 '连续技术强度10天数趋势', '连续技术强度3天数价格趋势',
                 '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势',
                 '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势',
                 '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
        # 二元特征，只有一种条件：== 1
        return [{
            'feature': feature,
            'condition': '== 1',
            'description': f"{feature} 为 1（是）"
        }]
    elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
        # 连续值特征，有多种条件
        conditions = []

        # 使用不同的阈值
        thresholds = [60, 70, 75, 80, 85, 90, 95]  # 更多阈值

        for threshold in thresholds:
            conditions.append({
                'feature': feature,
                'condition': f">= {threshold}",
                'description': f"{feature} 大于等于 {threshold}"
            })

        return conditions
    elif feature == '看涨技术指标数量':
        # 看涨技术指标数量，有多种条件
        conditions = []

        # 使用不同的阈值
        for threshold in range(1, 6):  # 1到5
            conditions.append({
                'feature': feature,
                'condition': f'>= {threshold}',
                'description': f"{feature} 大于等于 {threshold}"
            })

        return conditions

    return []

def generate_all_condition_combinations(feature_combination):
    """
    生成特征组合的所有可能筛选条件组合

    参数:
        feature_combination (tuple): 特征组合

    返回:
        list: 条件组合列表
    """
    # 获取每个特征的所有可能筛选条件
    feature_conditions = []
    for feature in feature_combination:
        conditions = generate_feature_conditions(feature)
        if conditions:
            feature_conditions.append(conditions)

    # 生成所有可能的条件组合
    if feature_conditions:
        all_condition_combinations = list(itertools.product(*feature_conditions))
        return all_condition_combinations
    else:
        return []

def generate_excel_file(data_file, output_dir, features, min_features=2, max_features=5, max_strategies=10000, start_date=None, end_date=None):
    """
    生成策略Excel文件

    参数:
        data_file (str): 股票历史数据文件路径
        output_dir (str): 输出目录
        features (list): 特征列表
        min_features (int): 最小特征数
        max_features (int): 最大特征数
        max_strategies (int): 最大策略数
        start_date (str): 回测开始日期，格式：YYYY-MM-DD
        end_date (str): 回测结束日期，格式：YYYY-MM-DD

    返回:
        str: 生成的Excel文件路径
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 读取股票历史数据
    df = read_stock_data(data_file)

    # 生成所有特征组合
    print(f"生成从{min_features}到{max_features}个特征的所有组合...")

    all_combinations = []
    for r in range(min_features, max_features + 1):
        combinations = list(itertools.combinations(features, r))
        print(f"{r}特征组合数量: {len(combinations)}")
        all_combinations.extend(combinations)

    print(f"总组合数量: {len(all_combinations)}")

    # 生成所有策略
    all_results = []
    strategy_index = 1

    # 创建进度条
    total_combinations = len(all_combinations)

    for i, feature_combination in enumerate(all_combinations):
        print(f"正在处理特征组合: {feature_combination} ({i+1}/{total_combinations})")

        # 生成所有可能的条件组合
        all_condition_combinations = generate_all_condition_combinations(feature_combination)

        if all_condition_combinations:
            # 生成每个条件组合的策略结果
            for condition_combination in all_condition_combinations:
                # 回测策略
                result = backtest_strategy(df, condition_combination, start_date, end_date)

                if result:
                    # 创建策略结果
                    strategy_result = {
                        'strategy_index': strategy_index,
                        'feature_combination': feature_combination,
                        'feature_count': len(feature_combination),
                        'feature_conditions': condition_combination,
                        'backtest_result': result
                    }

                    # 添加到结果列表
                    all_results.append(strategy_result)

                    # 更新策略索引
                    strategy_index += 1

                    # 限制策略数量，避免生成过多
                    if strategy_index > max_strategies:
                        print(f"已达到最大策略数量限制 ({max_strategies})，停止生成")
                        break

        # 限制策略数量，避免生成过多
        if strategy_index > max_strategies:
            break

        # 每处理100个特征组合，保存一次中间结果
        if (i + 1) % 100 == 0 or i == total_combinations - 1:
            # 按总收益率排序
            sorted_results = sorted(all_results, key=lambda x: x['backtest_result']['summary']['总收益率(%)'] if 'backtest_result' in x else 0, reverse=True)

            # 重新分配策略编号，确保按总收益率排序
            for j, result in enumerate(sorted_results, 1):
                result['strategy_index'] = j

            # 创建中间Excel文件
            interim_excel_file = os.path.join(output_dir, f"中间结果_{i+1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
            create_excel_file(sorted_results[:1000], interim_excel_file)  # 只保存前1000个结果，避免文件过大

            print(f"已处理 {i+1}/{total_combinations} 个特征组合，中间结果已保存到 {interim_excel_file}")

    # 按总收益率排序
    all_results.sort(key=lambda x: x['backtest_result']['summary']['总收益率(%)'] if 'backtest_result' in x else 0, reverse=True)

    # 重新分配策略编号，确保按总收益率排序
    for i, result in enumerate(all_results, 1):
        result['strategy_index'] = i

    print(f"共生成 {len(all_results)} 个策略")

    # 创建主Excel文件
    main_excel_file = os.path.join(output_dir, f"所有策略汇总_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
    create_excel_file(all_results, main_excel_file)

    print(f"主Excel文件已保存到: {main_excel_file}")

    # 创建前1000个策略的详细分析Excel文件
    print(f"创建前1000个策略的详细分析Excel文件...")

    # 创建策略详细分析目录
    strategy_details_dir = os.path.join(output_dir, 'strategy_details')
    if not os.path.exists(strategy_details_dir):
        os.makedirs(strategy_details_dir)

    # 只处理前1000个策略
    top_results = all_results[:1000]

    for i, result in enumerate(top_results):
        strategy_excel_file = os.path.join(strategy_details_dir, f"strategy_{result['strategy_index']}.xlsx")
        create_strategy_detail_excel(result, strategy_excel_file)

        if (i + 1) % 100 == 0:
            print(f"已创建 {i+1}/{len(top_results)} 个策略的详细分析Excel文件")

    print(f"已创建前1000个策略的详细分析Excel文件")

    return main_excel_file

def create_excel_file(results, output_file):
    """
    创建Excel文件

    参数:
        results (list): 策略结果列表
        output_file (str): 输出文件路径
    """
    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 创建策略汇总表格
        create_strategy_summary_sheet(results, writer)

        # 创建策略条件表格
        create_strategy_conditions_sheet(results, writer)

        # 创建按特征数量分组的统计表格
        create_feature_count_stats_sheet(results, writer)

def create_strategy_summary_sheet(results, writer):
    """
    创建策略汇总表格

    参数:
        results (list): 策略结果列表
        writer: Excel写入器
    """
    # 创建汇总数据
    summary_data = []

    for result in results:
        feature_str = ', '.join(result['feature_combination'])

        # 获取回测结果
        if 'backtest_result' in result:
            backtest_result = result['backtest_result']
            summary = backtest_result['summary']

            summary_data.append({
                '策略编号': result['strategy_index'],
                '策略组合': feature_str,
                '特征数量': result['feature_count'],
                '总收益率(%)': summary['总收益率(%)'],
                '年化收益率(%)': summary['年化收益率(%)'],
                '最大回撤(%)': summary['最大回撤(%)'],
                '胜率(%)': summary['胜率(%)'],
                '总交易笔数': summary['总交易笔数'],
                '平均每日交易笔数': summary['平均每日交易笔数'],
                '交易天数': summary['交易天数'],
                '总天数': summary['总天数'],
                '交易频率(%)': summary['交易频率(%)']
            })

    # 转换为DataFrame
    summary_df = pd.DataFrame(summary_data)

    # 写入Excel
    summary_df.to_excel(writer, sheet_name='策略汇总', index=False)

def create_strategy_conditions_sheet(results, writer):
    """
    创建策略条件表格

    参数:
        results (list): 策略结果列表
        writer: Excel写入器
    """
    # 创建策略条件数据
    conditions_data = []

    for result in results:
        feature_str = ', '.join(result['feature_combination'])

        # 生成策略条件描述
        conditions_str = ' AND '.join([cond['description'] for cond in result['feature_conditions']])

        # 生成策略代码
        code_parts = []
        for cond in result['feature_conditions']:
            code_parts.append(f"df['{cond['feature']}'] {cond['condition']}")
        code_str = 'df[' + ' & '.join(code_parts) + ']'

        # 获取回测结果
        if 'backtest_result' in result:
            backtest_result = result['backtest_result']
            summary = backtest_result['summary']

            conditions_data.append({
                '策略编号': result['strategy_index'],
                '策略组合': feature_str,
                '特征数量': result['feature_count'],
                '总收益率(%)': summary['总收益率(%)'],
                '胜率(%)': summary['胜率(%)'],
                '策略条件描述': conditions_str,
                '策略代码': code_str,
                '详细分析文件': f"strategy_{result['strategy_index']}.xlsx"
            })

    # 转换为DataFrame
    conditions_df = pd.DataFrame(conditions_data)

    # 写入Excel
    conditions_df.to_excel(writer, sheet_name='策略条件', index=False)

def create_feature_count_stats_sheet(results, writer):
    """
    创建按特征数量分组的统计表格

    参数:
        results (list): 策略结果列表
        writer: Excel写入器
    """
    # 创建汇总数据
    summary_data = []

    for result in results:
        if 'backtest_result' in result:
            backtest_result = result['backtest_result']
            summary = backtest_result['summary']

            summary_data.append({
                '特征数量': result['feature_count'],
                '总收益率(%)': summary['总收益率(%)'],
                '年化收益率(%)': summary['年化收益率(%)'],
                '最大回撤(%)': summary['最大回撤(%)'],
                '胜率(%)': summary['胜率(%)'],
                '平均每日交易笔数': summary['平均每日交易笔数'],
                '总交易笔数': summary['总交易笔数']
            })

    # 转换为DataFrame
    summary_df = pd.DataFrame(summary_data)

    # 按特征数量分组
    if not summary_df.empty:
        grouped = summary_df.groupby('特征数量').agg({
            '总收益率(%)': ['mean', 'std', 'max', 'min'],
            '年化收益率(%)': ['mean', 'std'],
            '最大回撤(%)': ['mean', 'std'],
            '胜率(%)': ['mean', 'std'],
            '平均每日交易笔数': 'mean',
            '总交易笔数': 'mean'
        })

        # 写入Excel
        grouped.to_excel(writer, sheet_name='特征数量统计')

def create_strategy_detail_excel(result, output_file):
    """
    创建策略详细分析Excel文件

    参数:
        result (dict): 策略结果
        output_file (str): 输出文件路径
    """
    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 创建策略统计信息表格
        create_strategy_stats_sheet(result, writer)

        # 创建策略条件详情表格
        create_strategy_conditions_detail_sheet(result, writer)

        # 创建每日表现数据表格
        create_daily_performance_sheet(result, writer)

def create_strategy_stats_sheet(result, writer):
    """
    创建策略统计信息表格

    参数:
        result (dict): 策略结果
        writer: Excel写入器
    """
    # 获取回测结果
    backtest_result = result['backtest_result']
    summary = backtest_result['summary']

    # 创建策略统计信息
    stats_data = {
        '统计项': [
            '策略编号',
            '特征组合',
            '特征数量',
            '总收益率(%)',
            '年化收益率(%)',
            '最大回撤(%)',
            '胜率(%)',
            '总交易笔数',
            '平均每日交易笔数',
            '交易天数',
            '总天数',
            '交易频率(%)'
        ],
        '数值': [
            result['strategy_index'],
            ', '.join(result['feature_combination']),
            result['feature_count'],
            summary['总收益率(%)'],
            summary['年化收益率(%)'],
            summary['最大回撤(%)'],
            summary['胜率(%)'],
            summary['总交易笔数'],
            summary['平均每日交易笔数'],
            summary['交易天数'],
            summary['总天数'],
            summary['交易频率(%)']
        ]
    }

    # 转换为DataFrame
    stats_df = pd.DataFrame(stats_data)

    # 写入Excel
    stats_df.to_excel(writer, sheet_name='策略统计', index=False)

def create_strategy_conditions_detail_sheet(result, writer):
    """
    创建策略条件详情表格

    参数:
        result (dict): 策略结果
        writer: Excel写入器
    """
    # 创建策略条件数据
    conditions_data = {
        '特征': [cond['feature'] for cond in result['feature_conditions']],
        '条件': [cond['condition'] for cond in result['feature_conditions']],
        '描述': [cond['description'] for cond in result['feature_conditions']]
    }

    # 转换为DataFrame
    conditions_df = pd.DataFrame(conditions_data)

    # 写入Excel
    conditions_df.to_excel(writer, sheet_name='策略条件详情', index=False)

def create_daily_performance_sheet(result, writer):
    """
    创建每日表现数据表格

    参数:
        result (dict): 策略结果
        writer: Excel写入器
    """
    # 获取回测结果
    backtest_result = result['backtest_result']
    daily_performance = backtest_result['daily_performance']

    # 创建DataFrame
    daily_df = pd.DataFrame(daily_performance)

    # 写入Excel
    daily_df.to_excel(writer, sheet_name='每日表现', index=False)

def backtest_strategy(df, strategy_conditions, start_date=None, end_date=None):
    """
    回测策略

    参数:
        df (DataFrame): 股票历史数据
        strategy_conditions (list): 策略条件列表
        start_date (str): 回测开始日期，格式：YYYY-MM-DD
        end_date (str): 回测结束日期，格式：YYYY-MM-DD

    返回:
        dict: 回测结果
    """
    # 转换日期格式
    if start_date:
        start_date = pd.to_datetime(start_date)
    else:
        start_date = df['日期'].min()

    if end_date:
        end_date = pd.to_datetime(end_date)
    else:
        end_date = df['日期'].max()

    # 筛选日期范围内的数据
    mask = (df['日期'] >= start_date) & (df['日期'] <= end_date)
    data = df[mask].copy()

    # 如果数据为空，返回空结果
    if len(data) == 0:
        print("选定日期范围内没有数据")
        return None

    # 获取日期范围内的交易日
    trading_dates = sorted(data['日期'].unique())

    # 初始化结果
    results = {
        'daily_performance': [],
        'trades': [],
        'summary': {}
    }

    # 初始化资金
    initial_capital = 1000000  # 初始资金100万
    current_capital = initial_capital

    # 初始化持仓
    positions = {}

    # 遍历每个交易日
    for i, date in enumerate(trading_dates[:-1]):  # 最后一天不买入
        # 当前日期的数据
        current_day_data = data[data['日期'] == date]

        # 下一个交易日
        next_date = trading_dates[i + 1]
        next_day_data = data[data['日期'] == next_date]

        # 卖出昨日持仓
        if positions:
            for stock_code, position in list(positions.items()):
                # 获取今日该股票的数据
                next_stock_data = next_day_data[next_day_data['股票代码'] == stock_code]

                if len(next_stock_data) > 0:
                    # 获取今日开盘价
                    open_price = next_stock_data['开盘价'].values[0]

                    # 计算卖出收益
                    sell_value = position['quantity'] * open_price
                    profit = sell_value - position['cost']
                    profit_rate = profit / position['cost'] * 100

                    # 更新资金
                    current_capital += sell_value

                    # 记录交易
                    results['trades'].append({
                        '日期': next_date,
                        '股票代码': stock_code,
                        '操作': '卖出',
                        '价格': open_price,
                        '数量': position['quantity'],
                        '金额': sell_value,
                        '收益': profit,
                        '收益率(%)': profit_rate
                    })

                    # 从持仓中移除
                    del positions[stock_code]
                else:
                    # 如果今日没有该股票的数据，假设以昨日收盘价卖出
                    sell_price = position['price']
                    sell_value = position['quantity'] * sell_price
                    profit = sell_value - position['cost']
                    profit_rate = profit / position['cost'] * 100

                    # 更新资金
                    current_capital += sell_value

                    # 记录交易
                    results['trades'].append({
                        '日期': next_date,
                        '股票代码': stock_code,
                        '操作': '卖出(无数据)',
                        '价格': sell_price,
                        '数量': position['quantity'],
                        '金额': sell_value,
                        '收益': profit,
                        '收益率(%)': profit_rate
                    })

                    # 从持仓中移除
                    del positions[stock_code]

        # 应用策略条件筛选股票
        filtered_data = current_day_data.copy()

        for condition in strategy_conditions:
            feature = condition['feature']
            cond = condition['condition']

            # 确保特征存在
            if feature not in filtered_data.columns:
                print(f"特征 {feature} 不存在于数据中")
                continue

            # 应用条件
            if '>=' in cond:
                threshold = float(cond.split('>=')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] >= threshold]
            elif '<=' in cond:
                threshold = float(cond.split('<=')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] <= threshold]
            elif '==' in cond:
                threshold = float(cond.split('==')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] == threshold]
            elif '>' in cond:
                threshold = float(cond.split('>')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] > threshold]
            elif '<' in cond:
                threshold = float(cond.split('<')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] < threshold]

        # 获取符合条件的股票
        selected_stocks = filtered_data.copy()

        # 买入股票
        if len(selected_stocks) > 0:
            # 计算每只股票的买入金额
            max_stocks = 10  # 最多买入10只股票
            num_stocks = min(len(selected_stocks), max_stocks)
            per_stock_value = current_capital / num_stocks

            # 买入股票
            for _, stock in selected_stocks.head(max_stocks).iterrows():
                stock_code = stock['股票代码']
                close_price = stock['收盘价']

                # 计算买入数量（整百股）
                quantity = int(per_stock_value / close_price / 100) * 100

                if quantity > 0:
                    # 计算买入金额
                    cost = quantity * close_price

                    # 更新资金
                    current_capital -= cost

                    # 添加到持仓
                    positions[stock_code] = {
                        'quantity': quantity,
                        'price': close_price,
                        'cost': cost,
                        'buy_date': date
                    }

                    # 记录交易
                    results['trades'].append({
                        '日期': date,
                        '股票代码': stock_code,
                        '操作': '买入',
                        '价格': close_price,
                        '数量': quantity,
                        '金额': cost,
                        '收益': 0,
                        '收益率(%)': 0
                    })

        # 计算当日总资产
        total_position_value = sum([position['quantity'] * position['price'] for position in positions.values()])
        total_assets = current_capital + total_position_value

        # 计算当日收益率
        if i == 0:
            daily_return = 0
        else:
            prev_assets = results['daily_performance'][-1]['总资产']
            daily_return = (total_assets - prev_assets) / prev_assets * 100

        # 记录每日表现
        results['daily_performance'].append({
            '日期': date,
            '现金': current_capital,
            '持仓市值': total_position_value,
            '总资产': total_assets,
            '日收益率(%)': daily_return,
            '持仓数量': len(positions)
        })

    # 计算汇总统计
    if results['daily_performance']:
        # 计算总收益率
        initial_assets = initial_capital
        final_assets = results['daily_performance'][-1]['总资产']
        total_return = (final_assets - initial_assets) / initial_assets * 100

        # 计算年化收益率
        days = (trading_dates[-1] - trading_dates[0]).days
        annual_return = total_return * 365 / days if days > 0 else 0

        # 计算最大回撤
        max_drawdown = 0
        peak = initial_assets

        for day in results['daily_performance']:
            if day['总资产'] > peak:
                peak = day['总资产']
            drawdown = (peak - day['总资产']) / peak * 100
            max_drawdown = max(max_drawdown, drawdown)

        # 计算胜率
        if results['trades']:
            win_trades = [trade for trade in results['trades'] if trade['操作'].startswith('卖出') and trade['收益'] > 0]
            win_rate = len(win_trades) / len([trade for trade in results['trades'] if trade['操作'].startswith('卖出')]) * 100 if len([trade for trade in results['trades'] if trade['操作'].startswith('卖出')]) > 0 else 0
        else:
            win_rate = 0

        # 计算平均每日交易笔数
        daily_trades = {}
        for trade in results['trades']:
            date = trade['日期']
            if date not in daily_trades:
                daily_trades[date] = 0
            daily_trades[date] += 1

        avg_daily_trades = sum(daily_trades.values()) / len(trading_dates) if trading_dates else 0

        # 汇总统计
        results['summary'] = {
            '初始资金': initial_capital,
            '最终资金': final_assets,
            '总收益率(%)': total_return,
            '年化收益率(%)': annual_return,
            '最大回撤(%)': max_drawdown,
            '胜率(%)': win_rate,
            '总交易笔数': len(results['trades']),
            '平均每日交易笔数': avg_daily_trades,
            '交易天数': len(daily_trades),
            '总天数': len(trading_dates),
            '交易频率(%)': len(daily_trades) / len(trading_dates) * 100 if trading_dates else 0
        }

    return results

if __name__ == "__main__":
    # 示例用法
    data_file = input("请输入股票数据文件路径 (默认: 股票数据汇总.xlsx): ") or "股票数据汇总.xlsx"
    output_dir = input("请输入输出目录 (默认: 策略结果): ") or "策略结果"

    # 默认特征列表
    default_features = [
        '技术强度',
        '连续技术强度5天数',
        '连续技术强度3天数',
        '连续技术强度10天数',
        '看涨技术指标数量',
        '涨跌幅趋势',
        '技术强度趋势',
        '连续技术强度5天数趋势',
        '技术指标_均线多头排列',
        '技术指标_MACD金叉',
        '技术指标_RSI反弹',
        '技术指标_KDJ金叉',
        '技术指标_布林带突破',
        '开盘涨跌'
    ]

    print("\n可用特征列表:")
    for i, feature in enumerate(default_features, 1):
        print(f"{i}. {feature}")

    # 让用户选择特征
    feature_input = input("\n请输入要使用的特征编号，用逗号分隔 (默认: 全部): ")
    if feature_input:
        feature_indices = [int(idx.strip()) - 1 for idx in feature_input.split(",")]
        features = [default_features[idx] for idx in feature_indices if 0 <= idx < len(default_features)]
    else:
        features = default_features

    print(f"\n已选择 {len(features)} 个特征: {', '.join(features)}")

    # 获取特征组合范围
    min_features = int(input("\n请输入最小特征数 (默认: 2): ") or "2")
    max_features = int(input("请输入最大特征数 (默认: 3): ") or "3")

    # 获取最大策略数
    max_strategies = int(input("\n请输入最大策略数 (默认: 1000): ") or "1000")

    # 获取回测日期范围
    start_date = input("\n请输入回测开始日期，格式: YYYY-MM-DD (默认: 2025-04-01): ") or "2025-04-01"
    end_date = input("请输入回测结束日期，格式: YYYY-MM-DD (默认: 2025-04-30): ") or "2025-04-30"

    print("\n开始生成策略Excel文件...")
    print(f"数据文件: {data_file}")
    print(f"输出目录: {output_dir}")
    print(f"特征数量范围: {min_features} - {max_features}")
    print(f"最大策略数: {max_strategies}")
    print(f"回测日期范围: {start_date} - {end_date}")

    # 生成Excel文件
    excel_file = generate_excel_file(data_file, output_dir, features, min_features, max_features, max_strategies, start_date, end_date)

    print(f"\n策略Excel文件生成完成: {excel_file}")
    print("程序执行完毕。")

def backtest_strategy(df, strategy_conditions, start_date=None, end_date=None):
    """
    回测策略

    参数:
        df (DataFrame): 股票历史数据
        strategy_conditions (list): 策略条件列表
        start_date (str): 回测开始日期，格式：YYYY-MM-DD
        end_date (str): 回测结束日期，格式：YYYY-MM-DD

    返回:
        dict: 回测结果
    """
    # 转换日期格式
    if start_date:
        start_date = pd.to_datetime(start_date)
    else:
        start_date = df['日期'].min()

    if end_date:
        end_date = pd.to_datetime(end_date)
    else:
        end_date = df['日期'].max()

    # 筛选日期范围内的数据
    mask = (df['日期'] >= start_date) & (df['日期'] <= end_date)
    data = df[mask].copy()

    # 如果数据为空，返回空结果
    if len(data) == 0:
        print("选定日期范围内没有数据")
        return None

    # 获取日期范围内的交易日
    trading_dates = sorted(data['日期'].unique())

    # 初始化结果
    results = {
        'daily_performance': [],
        'trades': [],
        'summary': {}
    }

    # 初始化资金
    initial_capital = 1000000  # 初始资金100万
    current_capital = initial_capital

    # 初始化持仓
    positions = {}

    # 遍历每个交易日
    for i, date in enumerate(trading_dates[:-1]):  # 最后一天不买入
        # 当前日期的数据
        current_day_data = data[data['日期'] == date]

        # 下一个交易日
        next_date = trading_dates[i + 1]
        next_day_data = data[data['日期'] == next_date]

        # 卖出昨日持仓
        if positions:
            for stock_code, position in list(positions.items()):
                # 获取今日该股票的数据
                next_stock_data = next_day_data[next_day_data['股票代码'] == stock_code]

                if len(next_stock_data) > 0:
                    # 获取今日开盘价
                    open_price = next_stock_data['开盘价'].values[0]

                    # 计算卖出收益
                    sell_value = position['quantity'] * open_price
                    profit = sell_value - position['cost']
                    profit_rate = profit / position['cost'] * 100

                    # 更新资金
                    current_capital += sell_value

                    # 记录交易
                    results['trades'].append({
                        '日期': next_date,
                        '股票代码': stock_code,
                        '操作': '卖出',
                        '价格': open_price,
                        '数量': position['quantity'],
                        '金额': sell_value,
                        '收益': profit,
                        '收益率(%)': profit_rate
                    })

                    # 从持仓中移除
                    del positions[stock_code]
                else:
                    # 如果今日没有该股票的数据，假设以昨日收盘价卖出
                    sell_price = position['price']
                    sell_value = position['quantity'] * sell_price
                    profit = sell_value - position['cost']
                    profit_rate = profit / position['cost'] * 100

                    # 更新资金
                    current_capital += sell_value

                    # 记录交易
                    results['trades'].append({
                        '日期': next_date,
                        '股票代码': stock_code,
                        '操作': '卖出(无数据)',
                        '价格': sell_price,
                        '数量': position['quantity'],
                        '金额': sell_value,
                        '收益': profit,
                        '收益率(%)': profit_rate
                    })

                    # 从持仓中移除
                    del positions[stock_code]

        # 应用策略条件筛选股票
        filtered_data = current_day_data.copy()

        for condition in strategy_conditions:
            feature = condition['feature']
            cond = condition['condition']

            # 确保特征存在
            if feature not in filtered_data.columns:
                print(f"特征 {feature} 不存在于数据中")
                continue

            # 应用条件
            if '>=' in cond:
                threshold = float(cond.split('>=')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] >= threshold]
            elif '<=' in cond:
                threshold = float(cond.split('<=')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] <= threshold]
            elif '==' in cond:
                threshold = float(cond.split('==')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] == threshold]
            elif '>' in cond:
                threshold = float(cond.split('>')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] > threshold]
            elif '<' in cond:
                threshold = float(cond.split('<')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] < threshold]

        # 获取符合条件的股票
        selected_stocks = filtered_data.copy()

        # 买入股票
        if len(selected_stocks) > 0:
            # 计算每只股票的买入金额
            max_stocks = 10  # 最多买入10只股票
            num_stocks = min(len(selected_stocks), max_stocks)
            per_stock_value = current_capital / num_stocks

            # 买入股票
            for _, stock in selected_stocks.head(max_stocks).iterrows():
                stock_code = stock['股票代码']
                close_price = stock['收盘价']

                # 计算买入数量（整百股）
                quantity = int(per_stock_value / close_price / 100) * 100

                if quantity > 0:
                    # 计算买入金额
                    cost = quantity * close_price

                    # 更新资金
                    current_capital -= cost

                    # 添加到持仓
                    positions[stock_code] = {
                        'quantity': quantity,
                        'price': close_price,
                        'cost': cost,
                        'buy_date': date
                    }

                    # 记录交易
                    results['trades'].append({
                        '日期': date,
                        '股票代码': stock_code,
                        '操作': '买入',
                        '价格': close_price,
                        '数量': quantity,
                        '金额': cost,
                        '收益': 0,
                        '收益率(%)': 0
                    })

        # 计算当日总资产
        total_position_value = sum([position['quantity'] * position['price'] for position in positions.values()])
        total_assets = current_capital + total_position_value

        # 计算当日收益率
        if i == 0:
            daily_return = 0
        else:
            prev_assets = results['daily_performance'][-1]['总资产']
            daily_return = (total_assets - prev_assets) / prev_assets * 100

        # 记录每日表现
        results['daily_performance'].append({
            '日期': date,
            '现金': current_capital,
            '持仓市值': total_position_value,
            '总资产': total_assets,
            '日收益率(%)': daily_return,
            '持仓数量': len(positions)
        })

    # 计算汇总统计
    if results['daily_performance']:
        # 计算总收益率
        initial_assets = initial_capital
        final_assets = results['daily_performance'][-1]['总资产']
        total_return = (final_assets - initial_assets) / initial_assets * 100

        # 计算年化收益率
        days = (trading_dates[-1] - trading_dates[0]).days
        annual_return = total_return * 365 / days if days > 0 else 0

        # 计算最大回撤
        max_drawdown = 0
        peak = initial_assets

        for day in results['daily_performance']:
            if day['总资产'] > peak:
                peak = day['总资产']
            drawdown = (peak - day['总资产']) / peak * 100
            max_drawdown = max(max_drawdown, drawdown)

        # 计算胜率
        if results['trades']:
            win_trades = [trade for trade in results['trades'] if trade['操作'].startswith('卖出') and trade['收益'] > 0]
            win_rate = len(win_trades) / len([trade for trade in results['trades'] if trade['操作'].startswith('卖出')]) * 100 if len([trade for trade in results['trades'] if trade['操作'].startswith('卖出')]) > 0 else 0
        else:
            win_rate = 0

        # 计算平均每日交易笔数
        daily_trades = {}
        for trade in results['trades']:
            date = trade['日期']
            if date not in daily_trades:
                daily_trades[date] = 0
            daily_trades[date] += 1

        avg_daily_trades = sum(daily_trades.values()) / len(trading_dates) if trading_dates else 0

        # 汇总统计
        results['summary'] = {
            '初始资金': initial_capital,
            '最终资金': final_assets,
            '总收益率(%)': total_return,
            '年化收益率(%)': annual_return,
            '最大回撤(%)': max_drawdown,
            '胜率(%)': win_rate,
            '总交易笔数': len(results['trades']),
            '平均每日交易笔数': avg_daily_trades,
            '交易天数': len(daily_trades),
            '总天数': len(trading_dates),
            '交易频率(%)': len(daily_trades) / len(trading_dates) * 100 if trading_dates else 0
        }

    return results
