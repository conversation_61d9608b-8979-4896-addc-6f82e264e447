import requests
import pandas as pd
import json
import time
from datetime import datetime
import random
import os

class StockCrawler:
    """
    股票数据爬虫类，用于从权威网站获取行业和龙头股数据
    """
    
    def __init__(self):
        """初始化爬虫"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        # 创建数据目录
        if not os.path.exists('data'):
            os.makedirs('data')
    
    def get_industry_list(self):
        """
        从东方财富网获取行业列表
        返回行业代码和名称的字典
        """
        print("正在获取行业列表...")
        url = "http://push2.eastmoney.com/api/qt/clist/get"
        params = {
            'pn': '1',
            'pz': '100',
            'po': '1',
            'np': '1',
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': '2',
            'invt': '2',
            'fid': 'f3',
            'fs': 'm:90+t:2',
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18'
        }
        
        try:
            response = requests.get(url, params=params, headers=self.headers)
            data = json.loads(response.text)
            industry_list = {}
            
            if 'data' in data and 'diff' in data['data']:
                for item in data['data']['diff']:
                    industry_code = item['f12']
                    industry_name = item['f14']
                    industry_list[industry_code] = industry_name
            
            print(f"成功获取 {len(industry_list)} 个行业信息")
            return industry_list
        except Exception as e:
            print(f"获取行业列表失败: {e}")
            return {}
    
    def get_industry_stocks(self, industry_code, industry_name):
        """
        获取指定行业的股票列表
        返回该行业的股票DataFrame
        """
        print(f"正在获取 {industry_name}({industry_code}) 行业的股票...")
        url = "http://push2.eastmoney.com/api/qt/clist/get"
        params = {
            'pn': '1',
            'pz': '500',  # 获取该行业下最多500只股票
            'po': '1',
            'np': '1',
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': '2',
            'invt': '2',
            'fid': 'f3',
            'fs': f'b:{industry_code}',
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f23'
        }
        
        try:
            response = requests.get(url, params=params, headers=self.headers)
            data = json.loads(response.text)
            stocks = []
            
            if 'data' in data and 'diff' in data['data']:
                for item in data['data']['diff']:
                    stock = {
                        '股票代码': item['f12'],
                        '股票名称': item['f14'],
                        '行业': industry_name,
                        '行业代码': industry_code,
                        '最新价': item['f2'],
                        '涨跌幅': item['f3'] / 100,  # 转换为小数
                        '涨跌额': item['f4'],
                        '成交量(手)': item['f5'],
                        '成交额(万元)': item['f6'] / 10000,  # 转换为万元
                        '振幅': item['f7'] / 100,  # 转换为小数
                        '最高价': item['f15'],
                        '最低价': item['f16'],
                        '今开': item['f17'],
                        '昨收': item['f18'],
                        '市盈率': item['f9'],
                        '市净率': item['f23']
                    }
                    stocks.append(stock)
            
            df = pd.DataFrame(stocks)
            print(f"成功获取 {len(df)} 只 {industry_name} 行业的股票")
            return df
        except Exception as e:
            print(f"获取 {industry_name} 行业股票失败: {e}")
            return pd.DataFrame()
    
    def get_all_industry_stocks(self):
        """
        获取所有行业的股票数据
        返回包含所有股票的DataFrame
        """
        industry_list = self.get_industry_list()
        all_stocks = pd.DataFrame()
        
        for code, name in industry_list.items():
            industry_stocks = self.get_industry_stocks(code, name)
            all_stocks = pd.concat([all_stocks, industry_stocks], ignore_index=True)
            # 随机暂停1-3秒，避免请求过于频繁
            time.sleep(random.uniform(1, 3))
        
        # 保存所有股票数据
        today = datetime.now().strftime('%Y%m%d')
        all_stocks.to_excel(f'data/all_stocks_{today}.xlsx', index=False)
        print(f"已将所有股票数据保存至 data/all_stocks_{today}.xlsx")
        
        return all_stocks
    
    def get_industry_rank(self):
        """
        获取行业涨跌幅排名
        返回行业排名DataFrame
        """
        print("正在获取行业涨跌幅排名...")
        url = "http://push2.eastmoney.com/api/qt/clist/get"
        params = {
            'pn': '1',
            'pz': '100',
            'po': '1',
            'np': '1',
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': '2',
            'invt': '2',
            'fid': 'f3',
            'fs': 'm:90+t:2',
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18'
        }
        
        try:
            response = requests.get(url, params=params, headers=self.headers)
            data = json.loads(response.text)
            industries = []
            
            if 'data' in data and 'diff' in data['data']:
                for item in data['data']['diff']:
                    industry = {
                        '行业代码': item['f12'],
                        '行业名称': item['f14'],
                        '最新价': item['f2'],
                        '涨跌幅': item['f3'] / 100,  # 转换为小数
                        '涨跌额': item['f4'],
                        '成交量(手)': item['f5'],
                        '成交额(万元)': item['f6'] / 10000,  # 转换为万元
                        '振幅': item['f7'] / 100,  # 转换为小数
                        '最高价': item['f15'],
                        '最低价': item['f16'],
                        '今开': item['f17'],
                        '昨收': item['f18']
                    }
                    industries.append(industry)
            
            df = pd.DataFrame(industries)
            # 按涨跌幅排序
            df = df.sort_values('涨跌幅', ascending=False).reset_index(drop=True)
            # 添加排名列
            df['排名'] = df.index + 1
            
            # 保存行业排名数据
            today = datetime.now().strftime('%Y%m%d')
            df.to_excel(f'data/industry_rank_{today}.xlsx', index=False)
            print(f"已将行业排名数据保存至 data/industry_rank_{today}.xlsx")
            
            return df
        except Exception as e:
            print(f"获取行业排名失败: {e}")
            return pd.DataFrame()
    
    def identify_industry_leaders(self, all_stocks, top_n=3):
        """
        识别每个行业的龙头股
        参数:
            all_stocks: 所有股票的DataFrame
            top_n: 每个行业选取的龙头股数量
        返回包含龙头股标记的DataFrame
        """
        print(f"正在识别每个行业的前{top_n}名龙头股...")
        
        # 确保有市值数据，如果没有，使用成交额作为替代指标
        if '市值(亿元)' not in all_stocks.columns:
            print("警告: 没有市值数据，使用成交额作为替代指标")
            value_column = '成交额(万元)'
        else:
            value_column = '市值(亿元)'
        
        # 为每个行业的股票按市值(或成交额)排序，并标记龙头股
        all_stocks['是否行业龙头'] = False
        
        for industry, group in all_stocks.groupby('行业'):
            # 按市值(或成交额)排序
            sorted_group = group.sort_values(value_column, ascending=False)
            # 获取前top_n名股票的代码
            leaders = sorted_group.iloc[:top_n]['股票代码'].tolist()
            # 标记龙头股
            all_stocks.loc[all_stocks['股票代码'].isin(leaders) & (all_stocks['行业'] == industry), '是否行业龙头'] = True
        
        # 保存带有龙头股标记的数据
        today = datetime.now().strftime('%Y%m%d')
        all_stocks.to_excel(f'data/stocks_with_leaders_{today}.xlsx', index=False)
        print(f"已将带有龙头股标记的数据保存至 data/stocks_with_leaders_{today}.xlsx")
        
        return all_stocks
    
    def identify_strong_industries(self, industry_rank, top_n=5):
        """
        识别强势行业
        参数:
            industry_rank: 行业排名DataFrame
            top_n: 选取的强势行业数量
        返回强势行业列表
        """
        print(f"正在识别前{top_n}名强势行业...")
        
        # 选取涨跌幅排名前top_n的行业
        strong_industries = industry_rank.head(top_n)['行业名称'].tolist()
        
        print(f"强势行业: {', '.join(strong_industries)}")
        return strong_industries
    
    def mark_strong_industry_stocks(self, all_stocks, strong_industries):
        """
        标记属于强势行业的股票
        参数:
            all_stocks: 所有股票的DataFrame
            strong_industries: 强势行业列表
        返回带有强势行业标记的DataFrame
        """
        print("正在标记属于强势行业的股票...")
        
        # 标记属于强势行业的股票
        all_stocks['是否强势行业'] = all_stocks['行业'].isin(strong_industries)
        
        # 保存带有强势行业标记的数据
        today = datetime.now().strftime('%Y%m%d')
        all_stocks.to_excel(f'data/stocks_with_strong_industries_{today}.xlsx', index=False)
        print(f"已将带有强势行业标记的数据保存至 data/stocks_with_strong_industries_{today}.xlsx")
        
        return all_stocks
    
    def run(self):
        """运行爬虫，获取所有数据并进行分析"""
        print("开始运行股票数据爬虫...")
        
        # 获取所有行业的股票数据
        all_stocks = self.get_all_industry_stocks()
        
        # 获取行业排名
        industry_rank = self.get_industry_rank()
        
        # 识别行业龙头股
        all_stocks = self.identify_industry_leaders(all_stocks)
        
        # 识别强势行业
        strong_industries = self.identify_strong_industries(industry_rank)
        
        # 标记强势行业的股票
        all_stocks = self.mark_strong_industry_stocks(all_stocks, strong_industries)
        
        print("股票数据爬虫运行完成!")
        return all_stocks

if __name__ == "__main__":
    crawler = StockCrawler()
    result = crawler.run()
    
    # 输出统计信息
    print("\n数据统计:")
    print(f"总股票数量: {len(result)}")
    print(f"行业龙头股数量: {result['是否行业龙头'].sum()}")
    print(f"强势行业股票数量: {result['是否强势行业'].sum()}")
    print(f"既是行业龙头又属于强势行业的股票数量: {(result['是否行业龙头'] & result['是否强势行业']).sum()}")
