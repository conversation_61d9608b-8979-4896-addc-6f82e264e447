"""
简单股票策略GUI

提供简单的界面来训练模型和执行不同的股票策略
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import os
import sys
import subprocess
from datetime import datetime, timedelta
import threading

class SimpleStockGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("股票策略选择器")
        self.root.geometry("800x600")
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding=10)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左侧控制面板
        self.control_frame = ttk.LabelFrame(self.main_frame, text="控制面板", padding=10)
        self.control_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 10))
        
        # 数据文件选择
        ttk.Label(self.control_frame, text="数据文件:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        self.data_file_var = tk.StringVar(value="股票明细.xlsx")
        self.data_file_entry = ttk.Entry(self.control_frame, textvariable=self.data_file_var, width=25)
        self.data_file_entry.grid(row=0, column=1, sticky=tk.W, pady=(0, 5))
        
        self.browse_button = ttk.Button(self.control_frame, text="浏览...", command=self.browse_file)
        self.browse_button.grid(row=0, column=2, sticky=tk.W, pady=(0, 5), padx=(5, 0))
        
        # 策略选择
        ttk.Label(self.control_frame, text="选择操作:").grid(row=1, column=0, sticky=tk.W, pady=(10, 5))
        
        # 创建策略选择变量
        self.train_model_var = tk.BooleanVar(value=False)
        self.strategy_1_var = tk.BooleanVar(value=False)
        self.strategy_A_var = tk.BooleanVar(value=False)
        self.strategy_B_var = tk.BooleanVar(value=False)
        self.strategy_B2_var = tk.BooleanVar(value=False)
        self.strategy_C_var = tk.BooleanVar(value=False)
        
        # 创建策略选择复选框
        ttk.Checkbutton(self.control_frame, text="训练模型", variable=self.train_model_var).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Checkbutton(self.control_frame, text="策略1: 100%高胜率策略", variable=self.strategy_1_var).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Checkbutton(self.control_frame, text="策略A: 最高胜率策略", variable=self.strategy_A_var).grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Checkbutton(self.control_frame, text="策略B: 最高收益率策略", variable=self.strategy_B_var).grid(row=5, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Checkbutton(self.control_frame, text="策略B2: 高收益率策略(放宽条件)", variable=self.strategy_B2_var).grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Checkbutton(self.control_frame, text="策略C: 平衡策略", variable=self.strategy_C_var).grid(row=7, column=0, columnspan=3, sticky=tk.W, pady=2)
        
        # 执行按钮
        self.execute_button = ttk.Button(self.control_frame, text="执行选定的操作", command=self.execute_operations)
        self.execute_button.grid(row=8, column=0, columnspan=3, sticky=tk.EW, pady=(10, 0))
        
        # 创建右侧输出区域
        self.output_frame = ttk.LabelFrame(self.main_frame, text="输出", padding=10)
        self.output_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建输出文本框
        self.output_text = scrolledtext.ScrolledText(self.output_frame, wrap=tk.WORD, width=60, height=30)
        self.output_text.pack(fill=tk.BOTH, expand=True)
        
        # 创建状态栏
        self.status_var = tk.StringVar(value="就绪")
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建进度条
        self.progress = ttk.Progressbar(self.root, orient=tk.HORIZONTAL, length=100, mode='indeterminate')
        self.progress.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
    
    def browse_file(self):
        """浏览文件对话框"""
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=(("Excel files", "*.xlsx"), ("All files", "*.*"))
        )
        if filename:
            self.data_file_var.set(filename)
    
    def execute_operations(self):
        """执行选定的操作"""
        # 检查是否选择了至少一个操作
        if not (self.train_model_var.get() or self.strategy_1_var.get() or 
                self.strategy_A_var.get() or self.strategy_B_var.get() or 
                self.strategy_B2_var.get() or self.strategy_C_var.get()):
            messagebox.showwarning("警告", "请至少选择一个操作")
            return
        
        # 获取参数
        data_file = self.data_file_var.get()
        
        # 检查文件是否存在
        if not os.path.exists(data_file):
            messagebox.showerror("错误", f"数据文件不存在: {data_file}")
            return
        
        # 清空输出
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, f"数据文件: {data_file}\n\n")
        self.output_text.insert(tk.END, "开始执行操作...\n\n")
        
        # 禁用执行按钮
        self.execute_button.config(state=tk.DISABLED)
        
        # 启动进度条
        self.progress.start()
        
        # 创建线程执行操作
        thread = threading.Thread(target=self.run_operations, args=(data_file,))
        thread.daemon = True
        thread.start()
    
    def run_operations(self, data_file):
        """在单独的线程中运行操作"""
        try:
            # 训练模型
            if self.train_model_var.get():
                self.update_status("正在训练模型...")
                self.update_output("正在训练模型...\n")
                
                cmd = ["python", "stock_predictor_original.py", "1", "--data", data_file]
                self.run_command(cmd)
            
            # 执行策略1
            if self.strategy_1_var.get():
                self.update_status("正在执行策略1...")
                self.update_output("\n正在执行策略1...\n")
                
                cmd = ["python", "stock_predictor_original.py", "2", "--data", data_file]
                self.run_command(cmd)
            
            # 执行策略A
            if self.strategy_A_var.get():
                self.update_status("正在执行策略A...")
                self.update_output("\n正在执行策略A...\n")
                
                cmd = ["python", "stock_predictor_original.py", "3", "--data", data_file]
                self.run_command(cmd)
            
            # 执行策略B
            if self.strategy_B_var.get():
                self.update_status("正在执行策略B...")
                self.update_output("\n正在执行策略B...\n")
                
                cmd = ["python", "stock_predictor_original.py", "4", "--data", data_file]
                self.run_command(cmd)
            
            # 执行策略B2
            if self.strategy_B2_var.get():
                self.update_status("正在执行策略B2...")
                self.update_output("\n正在执行策略B2...\n")
                
                cmd = ["python", "stock_predictor_original.py", "6", "--data", data_file]
                self.run_command(cmd)
            
            # 执行策略C
            if self.strategy_C_var.get():
                self.update_status("正在执行策略C...")
                self.update_output("\n正在执行策略C...\n")
                
                cmd = ["python", "stock_predictor_original.py", "5", "--data", data_file]
                self.run_command(cmd)
            
            self.update_status("执行完成")
            self.update_output("\n所有操作执行完成\n")
            
            # 打开结果目录
            self.update_output("\n结果已保存在 strategy_results 目录中\n")
            
        except Exception as e:
            self.update_status("执行出错")
            self.update_output(f"\n执行出错: {str(e)}\n")
        
        finally:
            # 停止进度条
            self.root.after(0, self.progress.stop)
            
            # 启用执行按钮
            self.root.after(0, lambda: self.execute_button.config(state=tk.NORMAL))
    
    def run_command(self, cmd):
        """运行命令并捕获输出"""
        # 打印完整命令，用于调试
        cmd_str = " ".join(cmd)
        self.update_output(f"执行命令: {cmd_str}\n")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 读取标准输出
        for line in process.stdout:
            self.update_output(line)
        
        # 等待进程结束
        process.wait()
        
        # 读取错误输出
        stderr = process.stderr.read()
        if stderr:
            self.update_output("错误信息:\n" + stderr + "\n")
    
    def update_output(self, text):
        """更新输出文本框"""
        self.root.after(0, lambda: self._update_output(text))
    
    def _update_output(self, text):
        """实际更新输出文本框的方法"""
        self.output_text.insert(tk.END, text)
        self.output_text.see(tk.END)
    
    def update_status(self, status):
        """更新状态栏"""
        self.root.after(0, lambda: self.status_var.set(status))

if __name__ == "__main__":
    root = tk.Tk()
    app = SimpleStockGUI(root)
    root.mainloop()
