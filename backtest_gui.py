import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import subprocess
import threading
import os
import time
import pandas as pd
import json
from datetime import datetime
import sys

# 添加当前目录到Python路径，确保能够导入config模块
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

# 尝试导入配置模块
try:
    import config
    has_config = True
    print("成功导入配置模块")
except ImportError:
    has_config = False
    print("警告: 无法导入config模块，将使用默认配置")

class BacktestGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("股票回测系统")
        self.root.geometry("800x600")  # 增加窗口大小
        self.root.resizable(True, True)

        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", font=("Arial", 12))
        self.style.configure("TLabel", font=("Arial", 12))
        self.style.configure("TEntry", font=("Arial", 12))

        # 设置日志级别
        self.log_level = tk.StringVar(value="详细")  # 默认为详细

        # 设置脚本路径
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.working_dir = os.getcwd()

        # 如果是打包后的可执行文件，脚本目录可能不正确，尝试使用工作目录
        if not os.path.exists(os.path.join(self.script_dir, "backtest_single.py")):
            # 尝试在上一级目录查找
            parent_dir = os.path.dirname(self.script_dir)
            if os.path.exists(os.path.join(parent_dir, "backtest_single.py")):
                self.script_dir = parent_dir
            # 尝试在工作目录的上一级目录查找
            elif os.path.exists(os.path.join(os.path.dirname(self.working_dir), "backtest_single.py")):
                self.script_dir = os.path.dirname(self.working_dir)
            # 如果都找不到，使用工作目录
            else:
                self.script_dir = self.working_dir

        # 设置数据目录
        # 首先检查是否有配置模块
        if has_config:
            # 加载配置文件
            config_file = os.path.join(script_dir, "config.json")
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        saved_config = json.load(f)

                    if 'data_dir' in saved_config:
                        # 更新配置模块中的路径
                        paths = config.set_data_dir(saved_config['data_dir'])
                        self.base_dir = paths['data_dir']
                        self.data_dir = paths['stock_data_dir']
                        self.log(f"从配置文件加载数据目录: {self.base_dir}", "info")
                    else:
                        # 使用配置模块中的默认路径
                        paths = config.get_data_paths()
                        self.base_dir = paths['data_dir']
                        self.data_dir = paths['stock_data_dir']
                        self.log(f"使用配置模块中的默认数据目录: {self.base_dir}", "info")
                except Exception as e:
                    self.log(f"加载配置文件时出错: {e}", "error")
                    # 使用配置模块中的默认路径
                    paths = config.get_data_paths()
                    self.base_dir = paths['data_dir']
                    self.data_dir = paths['stock_data_dir']
                    self.log(f"使用配置模块中的默认数据目录: {self.base_dir}", "info")
            else:
                # 使用配置模块中的默认路径
                paths = config.get_data_paths()
                self.base_dir = paths['data_dir']
                self.data_dir = paths['stock_data_dir']
                self.log(f"使用配置模块中的默认数据目录: {self.base_dir}", "info")
        else:
            # 没有配置模块，使用传统方式设置数据目录
            # 首先尝试在脚本目录下查找
            self.data_dir = os.path.join(self.script_dir, "complete_excel_results", "stock_data")

            # 如果不存在，尝试在上一级目录查找
            if not os.path.exists(self.data_dir):
                self.data_dir = os.path.join(os.path.dirname(self.script_dir), "complete_excel_results", "stock_data")

            # 如果仍然不存在，尝试在常见位置查找
            if not os.path.exists(self.data_dir):
                common_locations = [
                    r"E:\机器学习\complete_excel_results\stock_data",
                    r"D:\机器学习\complete_excel_results\stock_data",
                    r"C:\机器学习\complete_excel_results\stock_data"
                ]
                for location in common_locations:
                    if os.path.exists(location):
                        self.data_dir = location
                        break

            # 设置基础数据目录
            self.base_dir = os.path.dirname(self.data_dir)
            self.log(f"使用传统方式设置数据目录: {self.data_dir}", "info")

        self.log(f"使用数据目录: {self.data_dir}", "info")
        self.log(f"使用基础数据目录: {self.base_dir}", "info")

        # 检查环境变量中是否有指定的数据目录
        if 'STOCK_DATA_BASE_DIR' in os.environ:
            env_data_dir = os.environ['STOCK_DATA_BASE_DIR']
            if os.path.exists(env_data_dir):
                self.base_dir = env_data_dir
                self.data_dir = os.path.join(self.base_dir, 'stock_data')
                self.log(f"使用环境变量指定的数据目录: {self.data_dir}", "info")
                self.log(f"使用环境变量指定的基础数据目录: {self.base_dir}", "info")
                # 如果有配置模块，更新配置
                if has_config:
                    try:
                        config.set_data_dir(env_data_dir)
                        self.log(f"已更新配置模块中的数据目录: {env_data_dir}", "info")
                    except Exception as e:
                        self.log(f"更新配置模块中的数据目录时出错: {e}", "error")

        # 创建主框架
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建数据目录框架
        data_dir_frame = ttk.LabelFrame(main_frame, text="数据目录", padding="10")
        data_dir_frame.pack(fill=tk.X, pady=10)

        # 数据目录
        ttk.Label(data_dir_frame, text="数据目录:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.data_dir_var = tk.StringVar(value=self.base_dir)
        data_dir_entry = ttk.Entry(data_dir_frame, textvariable=self.data_dir_var, width=50)
        data_dir_entry.grid(row=0, column=1, sticky=tk.W, pady=5)

        # 浏览按钮
        browse_button = ttk.Button(data_dir_frame, text="浏览...", command=self.browse_data_dir)
        browse_button.grid(row=0, column=2, padx=5, pady=5)

        # 应用按钮
        apply_button = ttk.Button(data_dir_frame, text="应用", command=self.apply_data_dir)
        apply_button.grid(row=0, column=3, padx=5, pady=5)



        # 创建输入框架
        input_frame = ttk.LabelFrame(main_frame, text="回测参数", padding="10")
        input_frame.pack(fill=tk.X, pady=10)

        # 起始策略编号
        ttk.Label(input_frame, text="起始策略编号:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.start_id = tk.StringVar(value="1")
        ttk.Entry(input_frame, textvariable=self.start_id, width=10).grid(row=0, column=1, sticky=tk.W, pady=5)

        # 终止策略编号
        ttk.Label(input_frame, text="终止策略编号:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.end_id = tk.StringVar(value="10")
        ttk.Entry(input_frame, textvariable=self.end_id, width=10).grid(row=1, column=1, sticky=tk.W, pady=5)

        # 批处理大小
        ttk.Label(input_frame, text="批处理大小:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.batch_size = tk.StringVar(value="1")
        ttk.Entry(input_frame, textvariable=self.batch_size, width=10).grid(row=2, column=1, sticky=tk.W, pady=5)

        # 日志级别
        ttk.Label(input_frame, text="日志级别:").grid(row=3, column=0, sticky=tk.W, pady=5)
        log_level_frame = ttk.Frame(input_frame)
        log_level_frame.grid(row=3, column=1, sticky=tk.W, pady=5)

        ttk.Radiobutton(log_level_frame, text="简洁", variable=self.log_level, value="简洁").pack(side=tk.LEFT)
        ttk.Radiobutton(log_level_frame, text="详细", variable=self.log_level, value="详细").pack(side=tk.LEFT)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)

        # 开始回测按钮
        self.start_button = ttk.Button(button_frame, text="开始回测", command=self.start_backtest)
        self.start_button.pack(side=tk.LEFT, padx=5)

        # 停止回测按钮
        self.stop_button = ttk.Button(button_frame, text="停止回测", command=self.stop_backtest, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        # 查看结果按钮
        self.view_results_button = ttk.Button(button_frame, text="查看结果", command=self.view_results)
        self.view_results_button.pack(side=tk.LEFT, padx=5)

        # 直接执行按钮
        self.direct_exec_button = ttk.Button(button_frame, text="直接执行", command=self.direct_execute)
        self.direct_exec_button.pack(side=tk.LEFT, padx=5)

        # 进度框架
        progress_frame = ttk.LabelFrame(main_frame, text="回测进度", padding="10")
        progress_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=5)

        # 当前策略标签
        self.current_strategy_label = ttk.Label(progress_frame, text="当前策略: 无")
        self.current_strategy_label.pack(anchor=tk.W, pady=5)

        # 已完成策略标签
        self.completed_strategies_label = ttk.Label(progress_frame, text="已完成策略: 0")
        self.completed_strategies_label.pack(anchor=tk.W, pady=5)

        # 剩余时间标签
        self.remaining_time_label = ttk.Label(progress_frame, text="预计剩余时间: 未知")
        self.remaining_time_label.pack(anchor=tk.W, pady=5)

        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="回测日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # 日志控制框架
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, side=tk.TOP, pady=(0, 5))

        # 清空日志按钮
        self.clear_log_button = ttk.Button(log_control_frame, text="清空日志", command=self.clear_log)
        self.clear_log_button.pack(side=tk.LEFT, padx=5)

        # 保存日志按钮
        self.save_log_button = ttk.Button(log_control_frame, text="保存日志", command=self.save_log)
        self.save_log_button.pack(side=tk.LEFT, padx=5)

        # 自动滚动选项
        self.auto_scroll = tk.BooleanVar(value=True)
        ttk.Checkbutton(log_control_frame, text="自动滚动", variable=self.auto_scroll).pack(side=tk.LEFT, padx=5)

        # 日志文本框
        self.log_text = tk.Text(log_frame, height=15, width=80, font=("Courier", 10))
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

        # 设置日志标签
        self.log_text.tag_configure("error", foreground="red")
        self.log_text.tag_configure("warning", foreground="orange")
        self.log_text.tag_configure("success", foreground="green")
        self.log_text.tag_configure("info", foreground="blue")

        # 初始化变量
        self.running = False
        self.process = None
        self.backtest_thread = None
        self.start_time = None
        self.completed_strategies = 0
        self.total_strategies = 0

        # GUI初始化完成
        self.log("GUI初始化完成", "info")
        self.log("可以直接开始回测", "info")

    def start_backtest(self):
        """开始回测"""
        try:
            start_id = int(self.start_id.get())
            end_id = int(self.end_id.get())
            batch_size = int(self.batch_size.get())

            if start_id <= 0 or end_id <= 0 or batch_size <= 0:
                messagebox.showerror("输入错误", "策略编号和批处理大小必须为正整数")
                return

            if start_id > end_id:
                messagebox.showerror("输入错误", "起始策略编号必须小于或等于终止策略编号")
                return

            self.total_strategies = end_id - start_id + 1
            self.completed_strategies = 0
            self.progress_var.set(0)
            self.log_text.delete(1.0, tk.END)
            self.current_strategy_label.config(text=f"当前策略: {start_id}")
            self.completed_strategies_label.config(text=f"已完成策略: 0/{self.total_strategies}")
            self.remaining_time_label.config(text="预计剩余时间: 计算中...")

            self.running = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)

            self.start_time = time.time()

            # 在新线程中执行回测
            self.backtest_thread = threading.Thread(
                target=self.run_backtest,
                args=(start_id, end_id, batch_size)
            )
            self.backtest_thread.daemon = True
            self.backtest_thread.start()

        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的整数")

    def run_backtest(self, start_id, end_id, batch_size):
        """在新线程中执行回测"""
        current_id = start_id

        # 记录回测参数
        self.log(f"开始批量回测，起始策略: {start_id}，终止策略: {end_id}", "info")
        self.log(f"批处理大小: {batch_size}（当前版本每次执行一个策略）", "info")
        self.log(f"日志级别: {self.log_level.get()}", "info")
        self.log(f"当前工作目录: {os.getcwd()}", "debug")

        # 检查必要文件是否存在
        self.log("检查必要文件...", "debug")
        backtest_script = os.path.join(self.script_dir, "backtest_single.py")
        if not os.path.exists(backtest_script):
            self.log(f"找不到backtest_single.py文件", "error")
            self.log(f"当前脚本目录: {self.script_dir}", "error")
            self.log(f"当前工作目录: {self.working_dir}", "error")
            self.log(f"尝试手动指定脚本路径...", "info")

            # 尝试在常见位置查找脚本
            possible_locations = [
                self.working_dir,
                os.path.dirname(self.working_dir),
                os.path.join(os.path.dirname(os.path.dirname(self.working_dir)), "机器学习"),
                os.path.join(os.path.dirname(self.working_dir), "dist"),
                os.path.join(os.path.dirname(self.working_dir), "build")
            ]

            for location in possible_locations:
                test_path = os.path.join(location, "backtest_single.py")
                if os.path.exists(test_path):
                    self.script_dir = location
                    backtest_script = test_path
                    self.log(f"找到脚本: {backtest_script}", "success")
                    break

            if not os.path.exists(backtest_script):
                self.log("无法找到backtest_single.py文件，请确保该文件存在", "error")
                self.reset_ui()
                return

        # 检查数据目录是否存在
        if not os.path.exists(self.data_dir):
            self.log(f"警告: 找不到数据目录: {self.data_dir}", "warning")
            self.log("尝试创建数据目录...", "info")
            try:
                os.makedirs(self.data_dir, exist_ok=True)
                self.log(f"成功创建数据目录: {self.data_dir}", "success")
            except Exception as e:
                self.log(f"创建数据目录时出错: {str(e)}", "error")
                self.log("将使用默认数据目录", "warning")

        self.log("必要文件检查通过", "success")
        self.log("开始执行回测...", "info")

        # 尝试使用内部模块模式（一次加载，重复使用）
        try:
            import backtest_local
            self.log("成功导入回测模块，使用内部模式（高效）", "success")
            use_internal_mode = True
        except Exception as e:
            self.log(f"无法导入回测模块: {e}，使用外部进程模式", "warning")
            use_internal_mode = False

        # 如果使用内部模式，先预加载数据
        if use_internal_mode:
            self.log("开始一次性预加载所有数据...", "info")
            try:
                # 设置数据目录
                if has_config:
                    config.set_data_dir(self.base_dir)

                # 预加载数据
                backtest_local.preload_data()
                self.log("✅ 数据预加载完成！后续策略将直接使用预加载数据", "success")

                # 使用内部模式执行所有策略
                self.execute_strategies_internal(start_id, end_id, backtest_local)
                return

            except Exception as e:
                self.log(f"❌ 数据预加载失败: {e}，回退到外部进程模式", "error")
                use_internal_mode = False

        while current_id <= end_id and self.running:
            # 更新UI
            self.root.after(0, self.update_current_strategy, current_id)

            # 检查backtest_single.py文件是否存在
            backtest_script = os.path.join(self.script_dir, "backtest_single.py")
            if not os.path.exists(backtest_script):
                self.log(f"错误: 找不到backtest_single.py文件", "error")
                self.reset_ui()
                return

            # 设置环境变量
            os.environ['STOCK_DATA_DIR'] = self.data_dir

            # 构建单个策略命令（数据已预加载，执行应该很快）
            cmd = f"python \"{backtest_script}\" --id {current_id} --preload --data_dir \"{self.base_dir}\""
            self.log(f"执行策略 {current_id}（使用预加载数据）", "info")

            try:
                # 记录开始时间
                start_time_strategy = time.time()
                self.log(f"开始执行策略 {current_id} 回测...", "info")

                # 记录策略信息
                try:
                    summary_file = os.path.join(self.base_dir, '所有策略汇总_已回测.xlsx')
                    if os.path.exists(summary_file):
                        summary_df = pd.read_excel(summary_file)
                        strategy_data = summary_df[summary_df['策略编号'] == current_id]
                        if not strategy_data.empty and '策略条件描述' in strategy_data.columns:
                            strategy_desc = strategy_data['策略条件描述'].values[0]
                            self.log(f"策略 {current_id} 条件描述: {strategy_desc}", "info")
                except Exception as e:
                    self.log(f"获取策略信息时出错: {str(e)}", "warning")

                # 创建进程但不立即启动
                self.log(f"创建进程: {cmd}", "debug")
                try:
                    # 使用脚本目录作为工作目录，确保能找到所有相关文件
                    self.process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.STDOUT,
                        text=True,
                        shell=True,
                        cwd=self.script_dir  # 使用脚本目录作为工作目录
                    )
                    self.log(f"进程创建成功，PID: {self.process.pid}", "success")
                except Exception as e:
                    self.log(f"创建进程时出错: {str(e)}", "error")
                    import traceback
                    self.log(traceback.format_exc(), "error")
                    return

                # 读取输出，添加超时处理
                self.log(f"开始读取进程输出...", "debug")
                output_lines = []
                start_read_time = time.time()
                max_read_time = 300  # 最多读取5分钟
                self.log(f"最大读取时间: {max_read_time}秒", "debug")

                import io

                # Windows系统不支持fcntl非阻塞模式
                self.log("将使用阻塞模式读取进程输出", "debug")

                # 使用超时读取
                has_output = False
                while True:
                    # 检查是否超时
                    if time.time() - start_read_time > max_read_time:
                        self.log(f"读取输出超时（{max_read_time}秒）", "warning")
                        break

                    # 检查进程是否仍在运行
                    if self.process.poll() is not None:
                        if self.process.returncode == 0:
                            self.log(f"进程已正常结束，返回码: {self.process.returncode}", "success")
                        else:
                            self.log(f"进程已异常结束，返回码: {self.process.returncode}", "error")
                        break

                    # 尝试读取一行
                    try:
                        line = self.process.stdout.readline()
                        if line:
                            has_output = True
                            line_str = line.strip() if isinstance(line, str) else line.decode('utf-8', errors='ignore').strip()
                            self.log(line_str)
                            output_lines.append(line_str)

                            # 检查是否包含完成标记
                            if "策略回测完成" in line_str or "回测完成" in line_str:
                                self.log(f"检测到策略 {current_id} 完成标记", "success")
                        else:
                            # 没有更多输出，等待一会儿
                            time.sleep(0.1)
                    except (IOError, io.UnsupportedOperation) as e:
                        # 可能是非阻塞模式下没有数据可读
                        time.sleep(0.1)
                    except Exception as e:
                        self.log(f"读取输出时出错: {str(e)}", "error")
                        import traceback
                        self.log(traceback.format_exc(), "error")
                        break

                # 如果没有任何输出，尝试使用communicate获取所有输出
                if not has_output:
                    self.log("没有读取到任何输出，尝试使用communicate...", "warning")
                    try:
                        stdout, stderr = self.process.communicate(timeout=60)
                        if stdout:
                            stdout_str = stdout.strip() if isinstance(stdout, str) else stdout.decode('utf-8', errors='ignore').strip()
                            self.log(f"communicate获取的输出: {stdout_str}", "info")
                            output_lines.append(stdout_str)
                        if stderr:
                            stderr_str = stderr.strip() if isinstance(stderr, str) else stderr.decode('utf-8', errors='ignore').strip()
                            self.log(f"communicate获取的错误: {stderr_str}", "error")
                    except subprocess.TimeoutExpired:
                        self.log("communicate超时", "warning")
                    except Exception as e:
                        self.log(f"communicate出错: {str(e)}", "error")
                        import traceback
                        self.log(traceback.format_exc(), "error")

                # 等待进程完成，最多等待60秒
                self.log("等待进程完成...", "debug")
                try:
                    return_code = self.process.wait(timeout=60)
                    if return_code == 0:
                        self.log(f"进程已正常完成，返回码: {return_code}", "success")
                    else:
                        self.log(f"进程已异常完成，返回码: {return_code}", "error")
                except subprocess.TimeoutExpired:
                    self.log("等待进程完成超时，强制终止进程", "warning")
                    self.process.terminate()
                    try:
                        return_code = self.process.wait(timeout=5)
                        self.log(f"进程已终止，返回码: {return_code}", "warning")
                    except subprocess.TimeoutExpired:
                        self.log("进程无法终止，强制杀死进程", "error")
                        self.process.kill()
                        return_code = self.process.wait()
                        self.log(f"进程已杀死，返回码: {return_code}", "error")

                # 检查详细分析文件是否生成
                strategy_file = os.path.join(self.base_dir, f"new_strategy_details/strategy_{current_id}.xlsx")
                file_exists = os.path.exists(strategy_file)

                if return_code == 0 and file_exists:
                    self.log(f"策略 {current_id} 回测完成，详细分析文件已生成")

                    # 额外检查：确认文件大小大于0
                    file_size = os.path.getsize(strategy_file) if file_exists else 0
                    if file_size > 0:
                        self.log(f"策略 {current_id} 详细分析文件大小: {file_size} 字节")
                    else:
                        self.log(f"警告: 策略 {current_id} 详细分析文件大小为0，可能未完全生成")
                        # 等待一段时间，让文件系统完成写入
                        time.sleep(5)

                        # 再次检查文件大小
                        if os.path.exists(strategy_file):
                            file_size = os.path.getsize(strategy_file)
                            self.log(f"再次检查: 策略 {current_id} 详细分析文件大小: {file_size} 字节")
                            if file_size == 0:
                                self.log(f"错误: 策略 {current_id} 详细分析文件仍为空，跳过此策略")
                                continue
                else:
                    if return_code != 0:
                        self.log(f"策略 {current_id} 回测失败，返回码: {return_code}")
                    if not file_exists:
                        self.log(f"策略 {current_id} 详细分析文件未生成")

                    # 如果回测失败或文件未生成，等待一段时间后再次检查
                    time.sleep(5)
                    if os.path.exists(strategy_file):
                        file_size = os.path.getsize(strategy_file)
                        self.log(f"延迟检查: 策略 {current_id} 详细分析文件大小: {file_size} 字节")
                        if file_size > 0:
                            self.log(f"策略 {current_id} 详细分析文件已生成，继续执行")
                        else:
                            self.log(f"错误: 策略 {current_id} 详细分析文件为空，跳过此策略")
                            continue
                    else:
                        self.log(f"错误: 策略 {current_id} 详细分析文件仍未生成，跳过此策略")
                        continue

                # 检查汇总表是否更新
                try:
                    summary_file = os.path.join(self.base_dir, '所有策略汇总_已回测.xlsx')
                    if os.path.exists(summary_file):
                        summary_df = pd.read_excel(summary_file)
                        strategy_data = summary_df[summary_df['策略编号'] == current_id]
                        if not strategy_data.empty and not pd.isna(strategy_data['平均胜率(%)'].values[0]):
                            self.log(f"策略 {current_id} 已更新到汇总表")
                        else:
                            self.log(f"警告: 策略 {current_id} 未更新到汇总表或数据不完整")
                except Exception as e:
                    self.log(f"检查汇总表时出错: {str(e)}")

                # 更新进度
                self.completed_strategies += 1
                progress = (self.completed_strategies / self.total_strategies) * 100
                self.root.after(0, self.update_progress, progress)

                # 更新剩余时间
                elapsed_time = time.time() - self.start_time
                strategies_per_second = self.completed_strategies / elapsed_time if elapsed_time > 0 else 0
                remaining_strategies = self.total_strategies - self.completed_strategies
                remaining_seconds = remaining_strategies / strategies_per_second if strategies_per_second > 0 else 0

                self.root.after(0, self.update_remaining_time, remaining_seconds)

                # 计算策略执行时间
                end_time_strategy = time.time()
                strategy_elapsed_time = end_time_strategy - start_time_strategy
                minutes, seconds = divmod(strategy_elapsed_time, 60)
                self.log(f"策略 {current_id} 执行完成，耗时: {int(minutes)}分钟 {int(seconds)}秒")

                # 移动到下一个策略
                current_id += 1

            except Exception as e:
                self.log(f"执行出错: {str(e)}")
                # 记录详细的错误信息
                import traceback
                self.log(traceback.format_exc())
                # 等待一段时间后继续下一个策略
                time.sleep(5)
                current_id += 1

        if not self.running:
            self.log("回测已停止")
        else:
            self.log("所有策略回测完成")

        self.root.after(0, self.reset_ui)

    def execute_strategies_internal(self, start_id, end_id, backtest_local):
        """使用内部模式执行策略（真正的一次加载，重复利用）"""
        current_id = start_id

        while current_id <= end_id and self.running:
            # 更新UI
            self.root.after(0, self.update_current_strategy, current_id)

            try:
                # 记录开始时间
                start_time_strategy = time.time()

                self.log(f"执行策略 {current_id}（内部模式，使用预加载数据）", "info")

                # 直接调用回测函数，传递预加载的数据
                # 使用process_strategy而不是backtest_strategy_manually，这样会生成Excel文件
                result = backtest_local.process_strategy(
                    current_id,
                    backtest_local.next_trading_day_map,
                    backtest_local.date_to_data
                )

                # 计算策略执行时间
                end_time_strategy = time.time()
                strategy_elapsed_time = end_time_strategy - start_time_strategy
                minutes, seconds = divmod(strategy_elapsed_time, 60)

                if result:
                    self.log(f"策略 {current_id} 执行完成，耗时: {int(minutes)}分钟 {int(seconds)}秒", "success")
                else:
                    self.log(f"策略 {current_id} 执行失败，耗时: {int(minutes)}分钟 {int(seconds)}秒", "warning")

                # 更新进度
                self.completed_strategies += 1
                progress = (self.completed_strategies / self.total_strategies) * 100
                self.root.after(0, self.update_progress, progress)

                # 更新剩余时间
                elapsed_time = time.time() - self.start_time
                strategies_per_second = self.completed_strategies / elapsed_time if elapsed_time > 0 else 0
                remaining_strategies = self.total_strategies - self.completed_strategies
                remaining_seconds = remaining_strategies / strategies_per_second if strategies_per_second > 0 else 0
                self.root.after(0, self.update_remaining_time, remaining_seconds)

                # 移动到下一个策略
                current_id += 1

                # 让UI有机会更新
                self.root.update()

            except Exception as e:
                self.log(f"策略 {current_id} 执行出错: {str(e)}", "error")
                import traceback
                self.log(traceback.format_exc(), "error")
                current_id += 1

        if not self.running:
            self.log("回测已停止", "warning")
        else:
            self.log("所有策略回测完成", "success")

        self.root.after(0, self.reset_ui)

    def stop_backtest(self):
        """停止回测"""
        self.running = False
        if self.process:
            self.process.terminate()
        self.log("正在停止回测...")

    def reset_ui(self):
        """重置UI状态"""
        self.running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

    def update_progress(self, progress):
        """更新进度条"""
        self.progress_var.set(progress)
        self.completed_strategies_label.config(
            text=f"已完成策略: {self.completed_strategies}/{self.total_strategies}"
        )

    def update_current_strategy(self, strategy_id):
        """更新当前策略标签"""
        self.current_strategy_label.config(text=f"当前策略: {strategy_id}")

    def update_remaining_time(self, seconds):
        """更新剩余时间标签"""
        hours, remainder = divmod(int(seconds), 3600)
        minutes, seconds = divmod(remainder, 60)
        time_str = f"{hours}小时 {minutes}分钟 {seconds}秒"
        self.remaining_time_label.config(text=f"预计剩余时间: {time_str}")

    def log(self, message, level="info"):
        """添加日志消息"""
        self.root.after(0, self._append_log, message, level)

    def _append_log(self, message, level="info"):
        """在UI线程中添加日志消息"""
        # 根据日志级别决定是否显示详细信息
        if self.log_level.get() == "简洁" and level == "debug":
            return

        # 获取当前时间
        current_time = datetime.now().strftime("%H:%M:%S")

        # 根据级别设置标签
        tag = None
        if level == "error":
            tag = "error"
            prefix = "[错误]"
        elif level == "warning":
            tag = "warning"
            prefix = "[警告]"
        elif level == "success":
            tag = "success"
            prefix = "[成功]"
        elif level == "info":
            tag = "info"
            prefix = "[信息]"
        else:
            prefix = "[调试]"

        # 添加带时间戳和级别的消息
        log_message = f"{current_time} {prefix} {message}\n"

        if tag:
            self.log_text.insert(tk.END, log_message, tag)
        else:
            self.log_text.insert(tk.END, log_message)

        # 如果启用了自动滚动，则滚动到底部
        if self.auto_scroll.get():
            self.log_text.see(tk.END)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("日志已清空", "info")

    def browse_data_dir(self):
        """浏览并选择数据目录"""
        # 打开文件夹对话框
        directory = filedialog.askdirectory(initialdir=self.base_dir)
        if directory:
            self.data_dir_var.set(directory)

    def apply_data_dir(self):
        """应用选择的数据目录"""
        new_base_dir = self.data_dir_var.get()

        if not os.path.exists(new_base_dir):
            messagebox.showerror("错误", f"目录不存在: {new_base_dir}")
            return

        # 更新数据目录
        self.base_dir = new_base_dir
        self.data_dir = os.path.join(self.base_dir, 'stock_data')

        # 设置环境变量，确保其他脚本能找到数据目录
        os.environ['STOCK_DATA_BASE_DIR'] = self.base_dir

        # 如果有配置模块，更新配置
        if has_config:
            try:
                # 更新配置模块中的路径
                paths = config.set_data_dir(new_base_dir)
                self.log(f"已更新配置模块中的数据目录: {paths['data_dir']}", "success")

                # 保存配置到文件
                config_file = os.path.join(script_dir, "config.json")
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump({'data_dir': new_base_dir}, f, ensure_ascii=False, indent=4)
                self.log(f"已保存配置到文件: {config_file}", "success")
            except Exception as e:
                self.log(f"更新配置时出错: {str(e)}", "error")

        # 检查stock_data目录是否存在，如果不存在则创建
        if not os.path.exists(self.data_dir):
            try:
                os.makedirs(self.data_dir)
                self.log(f"已创建数据目录: {self.data_dir}", "success")
            except Exception as e:
                self.log(f"创建数据目录时出错: {str(e)}", "error")
                messagebox.showerror("错误", f"创建数据目录时出错: {str(e)}")
                return

        self.log(f"已应用新的数据目录: {self.base_dir}", "success")
        self.log(f"股票数据目录: {self.data_dir}", "info")

        # 重置预加载状态
        self.data_preloaded = False
        self.log("数据目录已更改，需要重新预加载数据", "warning")

        messagebox.showinfo("成功", f"已应用新的数据目录: {self.base_dir}")

    def preload_data(self):
        """预加载数据"""
        # 尝试导入回测模块
        if not self.backtest_module:
            self.log("正在导入回测模块...", "info")
            try:
                import backtest_local
                self.backtest_module = backtest_local
                self.log("成功导入回测模块", "success")
            except Exception as e:
                self.log(f"无法导入回测模块: {e}", "error")
                messagebox.showerror("错误", f"无法导入回测模块:\n{e}")
                return

        if self.data_preloaded:
            response = messagebox.askyesno("确认", "数据已预加载，是否重新加载？")
            if not response:
                return

        self.log("开始预加载数据...", "info")

        # 在新线程中预加载数据
        def preload_thread():
            try:
                # 设置数据目录
                if has_config:
                    config.set_data_dir(self.base_dir)

                # 调用回测模块的预加载函数
                self.backtest_module.preload_data()

                self.data_preloaded = True
                self.log("✅ 数据预加载完成！现在可以快速执行策略回测", "success")

                # 在主线程中更新UI
                self.root.after(0, lambda: messagebox.showinfo("成功", "数据预加载完成！\n现在执行策略回测将会非常快速。"))

            except Exception as e:
                self.log(f"❌ 数据预加载失败: {e}", "error")
                import traceback
                self.log(traceback.format_exc(), "error")
                self.root.after(0, lambda: messagebox.showerror("错误", f"数据预加载失败:\n{e}"))

        thread = threading.Thread(target=preload_thread)
        thread.daemon = True
        thread.start()

    def save_log(self):
        """保存日志到文件"""
        try:
            from tkinter import filedialog
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                initialdir=self.base_dir,
                title="保存日志"
            )

            if file_path:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.log(f"日志已保存到 {file_path}", "success")
        except Exception as e:
            self.log(f"保存日志时出错: {str(e)}", "error")

    def view_results(self):
        """查看回测结果"""
        try:
            # 打开汇总表
            summary_file = os.path.join(self.base_dir, '所有策略汇总_已回测.xlsx')
            if os.path.exists(summary_file):
                os.startfile(summary_file)
            else:
                messagebox.showerror("错误", f"找不到汇总表文件: {summary_file}")
        except Exception as e:
            messagebox.showerror("错误", f"打开汇总表时出错: {str(e)}")

    def direct_execute(self):
        """直接执行Python脚本"""
        try:
            # 获取策略ID
            start_id = int(self.start_id.get())

            # 创建直接执行窗口
            exec_window = tk.Toplevel(self.root)
            exec_window.title(f"直接执行策略 {start_id}")
            exec_window.geometry("600x400")

            # 创建文本框
            text_frame = ttk.Frame(exec_window, padding="10")
            text_frame.pack(fill=tk.BOTH, expand=True)

            # 日志文本框
            log_text = tk.Text(text_frame, height=20, width=70, font=("Courier", 10))
            log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            # 滚动条
            scrollbar = ttk.Scrollbar(text_frame, command=log_text.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            log_text.config(yscrollcommand=scrollbar.set)

            # 按钮框架
            button_frame = ttk.Frame(exec_window, padding="10")
            button_frame.pack(fill=tk.X)

            # 执行按钮
            exec_button = ttk.Button(button_frame, text="执行",
                                    command=lambda: self.execute_script(start_id, log_text, exec_button, stop_button))
            exec_button.pack(side=tk.LEFT, padx=5)

            # 停止按钮
            stop_button = ttk.Button(button_frame, text="停止", state=tk.DISABLED,
                                    command=lambda: self.stop_script(log_text, exec_button, stop_button))
            stop_button.pack(side=tk.LEFT, padx=5)

            # 关闭按钮
            close_button = ttk.Button(button_frame, text="关闭", command=exec_window.destroy)
            close_button.pack(side=tk.RIGHT, padx=5)

            # 显示当前工作目录和可用脚本
            log_text.insert(tk.END, f"当前工作目录: {os.getcwd()}\n")
            log_text.insert(tk.END, f"可用Python脚本:\n")
            for file in os.listdir('.'):
                if file.endswith('.py'):
                    log_text.insert(tk.END, f"  - {file}\n")
            log_text.insert(tk.END, f"\n准备执行策略 {start_id}...\n")
            log_text.see(tk.END)

        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的整数")

    def execute_script(self, strategy_id, log_text, exec_button, stop_button):
        """在新窗口中执行Python脚本"""
        # 禁用执行按钮，启用停止按钮
        exec_button.config(state=tk.DISABLED)
        stop_button.config(state=tk.NORMAL)

        # 清空日志
        log_text.delete(1.0, tk.END)
        log_text.insert(tk.END, f"执行策略 {strategy_id}...\n\n")

        # 检查backtest_single.py文件是否存在
        backtest_script = os.path.join(self.script_dir, "backtest_single.py")
        if not os.path.exists(backtest_script):
            log_text.insert(tk.END, f"错误: 找不到backtest_single.py文件\n")
            log_text.insert(tk.END, f"当前脚本目录: {self.script_dir}\n")
            log_text.insert(tk.END, f"当前工作目录: {os.getcwd()}\n")
            log_text.insert(tk.END, f"目录内容: {os.listdir('.')}\n")

            # 尝试在常见位置查找脚本
            possible_locations = [
                os.getcwd(),
                os.path.dirname(os.getcwd()),
                os.path.join(os.path.dirname(os.path.dirname(os.getcwd())), "机器学习"),
                os.path.join(os.path.dirname(os.getcwd()), "dist"),
                os.path.join(os.path.dirname(os.getcwd()), "build")
            ]

            for location in possible_locations:
                test_path = os.path.join(location, "backtest_single.py")
                if os.path.exists(test_path):
                    backtest_script = test_path
                    log_text.insert(tk.END, f"找到脚本: {backtest_script}\n")
                    break

            if not os.path.exists(backtest_script):
                log_text.insert(tk.END, f"无法找到backtest_single.py文件，请确保该文件存在\n")
                exec_button.config(state=tk.NORMAL)
                stop_button.config(state=tk.DISABLED)
                return

        # 检查数据目录是否存在
        if not os.path.exists(self.data_dir):
            log_text.insert(tk.END, f"警告: 找不到数据目录: {self.data_dir}\n")
            log_text.insert(tk.END, "尝试创建数据目录...\n")
            try:
                os.makedirs(self.data_dir, exist_ok=True)
                log_text.insert(tk.END, f"成功创建数据目录: {self.data_dir}\n")
            except Exception as e:
                log_text.insert(tk.END, f"创建数据目录时出错: {str(e)}\n")
                log_text.insert(tk.END, "将使用默认数据目录\n")

        # 设置环境变量，确保脚本能找到数据目录
        os.environ['STOCK_DATA_DIR'] = self.data_dir
        log_text.insert(tk.END, f"设置数据目录环境变量: {self.data_dir}\n")

        # 构建命令，传递数据目录参数和预加载参数
        cmd = f"python \"{backtest_script}\" --id {strategy_id} --preload --data_dir \"{self.base_dir}\""
        log_text.insert(tk.END, f"执行命令: {cmd}\n")
        log_text.insert(tk.END, f"数据目录: {self.data_dir}\n")
        log_text.see(tk.END)

        # 尝试直接导入模块并执行
        log_text.insert(tk.END, "尝试直接导入模块并执行...\n")
        log_text.see(tk.END)

        # 在新线程中执行命令
        self.exec_thread = threading.Thread(
            target=self.run_script,
            args=(cmd, log_text, exec_button, stop_button, strategy_id)
        )
        self.exec_thread.daemon = True
        self.exec_thread.start()

    def run_script(self, cmd, log_text, exec_button, stop_button, strategy_id=None):
        """在新线程中执行命令"""
        try:
            # 提取策略ID（如果未提供）
            if strategy_id is None:
                import re
                strategy_id_match = re.search(r'--id (\d+)', cmd)
                strategy_id = int(strategy_id_match.group(1)) if strategy_id_match else 0

            log_text.insert(tk.END, f"策略ID: {strategy_id}\n")
            log_text.see(tk.END)

            # 尝试直接导入模块并执行
            try:
                log_text.insert(tk.END, "尝试直接导入backtest_with_baostock模块...\n")
                log_text.see(tk.END)

                # 检查模块是否存在
                import importlib.util
                spec = importlib.util.find_spec("backtest_with_baostock")
                if spec is None:
                    log_text.insert(tk.END, "模块不存在，将使用subprocess执行\n")
                    log_text.see(tk.END)
                else:
                    log_text.insert(tk.END, "模块存在，尝试导入...\n")
                    log_text.see(tk.END)

                    # 尝试导入模块
                    try:
                        import sys
                        sys.argv = ["backtest_with_baostock.py", "--id", str(strategy_id)]
                        import backtest_with_baostock
                        # 尝试执行模块的main函数
                        if hasattr(backtest_with_baostock, 'main'):
                            log_text.insert(tk.END, "调用模块的main函数...\n")
                            log_text.see(tk.END)
                            backtest_with_baostock.main()
                        log_text.insert(tk.END, "模块导入成功，执行完成\n")
                        log_text.see(tk.END)
                        return
                    except Exception as e:
                        log_text.insert(tk.END, f"导入模块时出错: {str(e)}\n")
                        log_text.insert(tk.END, "将使用subprocess执行\n")
                        log_text.see(tk.END)
            except Exception as e:
                log_text.insert(tk.END, f"检查模块时出错: {str(e)}\n")
                log_text.insert(tk.END, "将使用subprocess执行\n")
                log_text.see(tk.END)

            # 创建进程
            log_text.insert(tk.END, f"创建进程: {cmd}\n")
            log_text.see(tk.END)

            # 尝试使用不同的方法执行命令
            try:
                # 方法1：使用subprocess.run
                log_text.insert(tk.END, "尝试使用subprocess.run执行...\n")
                log_text.see(tk.END)

                result = subprocess.run(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    shell=True,
                    cwd=self.script_dir  # 使用脚本目录作为工作目录
                )

                log_text.insert(tk.END, f"subprocess.run执行完成，返回码: {result.returncode}\n")
                if result.stdout:
                    log_text.insert(tk.END, f"输出:\n{result.stdout}\n")
                log_text.see(tk.END)

                # 检查详细分析文件是否生成
                strategy_file = f"E:/机器学习/complete_excel_results/new_strategy_details/strategy_{strategy_id}.xlsx"
                if os.path.exists(strategy_file):
                    file_size = os.path.getsize(strategy_file)
                    log_text.insert(tk.END, f"详细分析文件已生成，大小: {file_size} 字节\n")
                    log_text.insert(tk.END, "详细分析文件已生成，回测完成\n")
                    log_text.see(tk.END)
                else:
                    log_text.insert(tk.END, f"详细分析文件未生成\n")

                # 检查汇总表是否更新
                try:
                    summary_file = os.path.join(self.base_dir, '所有策略汇总_已回测.xlsx')
                    if os.path.exists(summary_file):
                        summary_df = pd.read_excel(summary_file)
                        strategy_data = summary_df[summary_df['策略编号'] == strategy_id]
                        if not strategy_data.empty:
                            log_text.insert(tk.END, f"策略 {strategy_id} 已更新到汇总表\n")
                        else:
                            log_text.insert(tk.END, f"警告: 策略 {strategy_id} 未更新到汇总表或数据不完整\n")
                    else:
                        log_text.insert(tk.END, f"警告: 找不到汇总表文件: {summary_file}\n")
                except Exception as e:
                    log_text.insert(tk.END, f"检查汇总表时出错: {str(e)}\n")

                log_text.see(tk.END)
                return
            except Exception as e:
                log_text.insert(tk.END, f"使用subprocess.run执行时出错: {str(e)}\n")
                log_text.insert(tk.END, "尝试使用subprocess.Popen执行...\n")
                log_text.see(tk.END)

            # 方法2：使用subprocess.Popen
            self.exec_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                shell=True,
                cwd=self.script_dir  # 使用脚本目录作为工作目录
            )

            log_text.insert(tk.END, f"进程创建成功，PID: {self.exec_process.pid}\n")
            log_text.see(tk.END)

            # 读取输出
            log_text.insert(tk.END, "开始读取输出...\n")
            log_text.see(tk.END)

            output = []
            while True:
                line = self.exec_process.stdout.readline()
                if not line and self.exec_process.poll() is not None:
                    break
                if line:
                    line_str = line.strip()
                    log_text.insert(tk.END, f"{line_str}\n")
                    log_text.see(tk.END)
                    output.append(line_str)

            # 等待进程完成
            return_code = self.exec_process.wait()
            log_text.insert(tk.END, f"\n进程已完成，返回码: {return_code}\n")

            # 如果没有输出，尝试使用communicate获取所有输出
            if not output:
                log_text.insert(tk.END, "没有读取到任何输出，尝试使用communicate...\n")
                log_text.see(tk.END)

                self.exec_process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    shell=True,
                    cwd=self.script_dir  # 使用脚本目录作为工作目录
                )

                stdout, stderr = self.exec_process.communicate()
                if stdout:
                    log_text.insert(tk.END, f"communicate获取的输出:\n{stdout}\n")
                if stderr:
                    log_text.insert(tk.END, f"communicate获取的错误:\n{stderr}\n")
                log_text.see(tk.END)

            # 检查详细分析文件是否生成
            strategy_file = os.path.join(self.base_dir, f"new_strategy_details/strategy_{strategy_id}.xlsx")
            if os.path.exists(strategy_file):
                file_size = os.path.getsize(strategy_file)
                log_text.insert(tk.END, f"详细分析文件已生成，大小: {file_size} 字节\n")
            else:
                log_text.insert(tk.END, f"详细分析文件未生成\n")

            log_text.see(tk.END)

        except Exception as e:
            log_text.insert(tk.END, f"执行出错: {str(e)}\n")
            import traceback
            log_text.insert(tk.END, traceback.format_exc())
            log_text.see(tk.END)
        finally:
            # 恢复按钮状态
            exec_button.config(state=tk.NORMAL)
            stop_button.config(state=tk.DISABLED)

    def stop_script(self, log_text, exec_button, stop_button):
        """停止执行脚本"""
        if hasattr(self, 'exec_process') and self.exec_process:
            log_text.insert(tk.END, "正在停止进程...\n")
            log_text.see(tk.END)

            try:
                self.exec_process.terminate()
                try:
                    self.exec_process.wait(timeout=5)
                    log_text.insert(tk.END, "进程已终止\n")
                except subprocess.TimeoutExpired:
                    log_text.insert(tk.END, "进程无法终止，强制杀死进程\n")
                    self.exec_process.kill()
                    self.exec_process.wait()
                    log_text.insert(tk.END, "进程已杀死\n")
            except Exception as e:
                log_text.insert(tk.END, f"停止进程时出错: {str(e)}\n")

            log_text.see(tk.END)

            # 恢复按钮状态
            exec_button.config(state=tk.NORMAL)
            stop_button.config(state=tk.DISABLED)

if __name__ == "__main__":
    print("程序开始执行...")
    try:
        print("创建Tk根窗口...")
        root = tk.Tk()
        print("Tk根窗口创建成功")

        print("创建BacktestGUI应用...")
        app = BacktestGUI(root)
        print("BacktestGUI应用创建成功")

        print("启动主循环...")
        root.mainloop()
        print("主循环结束")
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
