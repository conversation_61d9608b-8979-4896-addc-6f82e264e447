#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票分析系统GUI
作者: Augment AI
版本: 1.0.0

该程序提供股票数据处理和策略分析的图形用户界面。
"""

import os
import sys
import pandas as pd
import numpy as np
import itertools
import threading
import time
from datetime import datetime
from tqdm import tqdm
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

class StockAnalysisApp:
    def __init__(self, root):
        self.root = root
        self.root.title("股票分析系统")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", font=("微软雅黑", 10))
        self.style.configure("TLabel", font=("微软雅黑", 10))
        self.style.configure("TFrame", background="#f0f0f0")
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建数据处理选项卡
        self.data_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.data_tab, text="数据处理")
        
        # 创建策略分析选项卡
        self.strategy_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.strategy_tab, text="策略分析")
        
        # 创建结果查看选项卡
        self.result_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.result_tab, text="结果查看")
        
        # 初始化数据处理选项卡
        self.init_data_tab()
        
        # 初始化策略分析选项卡
        self.init_strategy_tab()
        
        # 初始化结果查看选项卡
        self.init_result_tab()
        
        # 初始化变量
        self.data_dir = ""
        self.output_dir = ""
        self.features = []
        self.min_features = 2
        self.max_features = 5
        self.max_strategies = 10000
        self.is_running = False
        self.progress = 0
        self.total_progress = 100
        
    def init_data_tab(self):
        """初始化数据处理选项卡"""
        # 创建数据处理框架
        data_frame = ttk.LabelFrame(self.data_tab, text="数据处理", padding="10")
        data_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建数据目录选择
        ttk.Label(data_frame, text="数据目录:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.data_dir_entry = ttk.Entry(data_frame, width=50)
        self.data_dir_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        ttk.Button(data_frame, text="浏览...", command=self.browse_data_dir).grid(row=0, column=2, sticky=tk.W, pady=5, padx=5)
        
        # 创建输出目录选择
        ttk.Label(data_frame, text="输出目录:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.output_dir_entry = ttk.Entry(data_frame, width=50)
        self.output_dir_entry.grid(row=1, column=1, sticky=tk.W, pady=5)
        ttk.Button(data_frame, text="浏览...", command=self.browse_output_dir).grid(row=1, column=2, sticky=tk.W, pady=5, padx=5)
        
        # 创建数据处理选项
        ttk.Label(data_frame, text="数据处理选项:").grid(row=2, column=0, sticky=tk.W, pady=5)
        
        # 创建复选框
        self.process_all_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(data_frame, text="处理所有数据", variable=self.process_all_var).grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # 创建日期范围选择
        ttk.Label(data_frame, text="开始日期:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.start_date_entry = ttk.Entry(data_frame, width=20)
        self.start_date_entry.grid(row=3, column=1, sticky=tk.W, pady=5)
        self.start_date_entry.insert(0, "20250101")
        
        ttk.Label(data_frame, text="结束日期:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.end_date_entry = ttk.Entry(data_frame, width=20)
        self.end_date_entry.grid(row=4, column=1, sticky=tk.W, pady=5)
        self.end_date_entry.insert(0, "20251231")
        
        # 创建处理按钮
        ttk.Button(data_frame, text="开始处理数据", command=self.process_data).grid(row=5, column=1, sticky=tk.W, pady=10)
        
        # 创建进度条
        ttk.Label(data_frame, text="处理进度:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.data_progress = ttk.Progressbar(data_frame, orient=tk.HORIZONTAL, length=400, mode='determinate')
        self.data_progress.grid(row=6, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        # 创建日志框
        ttk.Label(data_frame, text="处理日志:").grid(row=7, column=0, sticky=tk.W, pady=5)
        self.data_log = scrolledtext.ScrolledText(data_frame, width=70, height=15)
        self.data_log.grid(row=8, column=0, columnspan=3, sticky=tk.W, pady=5)
        
    def init_strategy_tab(self):
        """初始化策略分析选项卡"""
        # 创建策略分析框架
        strategy_frame = ttk.LabelFrame(self.strategy_tab, text="策略分析", padding="10")
        strategy_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建特征选择
        ttk.Label(strategy_frame, text="选择特征:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        # 创建特征复选框
        self.feature_vars = {}
        features = [
            '技术强度',
            '连续技术强度5天数',
            '连续技术强度3天数',
            '连续技术强度10天数',
            '看涨技术指标数量',
            '涨跌幅趋势',
            '技术强度趋势',
            '连续技术强度5天数趋势',
            '连续技术强度5天数价格趋势',
            '连续技术强度5天数涨跌幅趋势',
            '技术指标_均线多头排列',
            '技术指标_MACD金叉',
            '技术指标_RSI反弹',
            '技术指标_KDJ金叉',
            '技术指标_布林带突破',
            '开盘涨跌'
        ]
        
        feature_frame = ttk.Frame(strategy_frame)
        feature_frame.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        for i, feature in enumerate(features):
            self.feature_vars[feature] = tk.BooleanVar(value=True)
            ttk.Checkbutton(feature_frame, text=feature, variable=self.feature_vars[feature]).grid(row=i//3, column=i%3, sticky=tk.W, pady=2, padx=10)
        
        # 创建特征组合范围选择
        ttk.Label(strategy_frame, text="特征组合范围:").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        range_frame = ttk.Frame(strategy_frame)
        range_frame.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(range_frame, text="最小特征数:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.min_features_entry = ttk.Spinbox(range_frame, from_=2, to=5, width=5)
        self.min_features_entry.grid(row=0, column=1, sticky=tk.W, pady=5, padx=5)
        self.min_features_entry.set(2)
        
        ttk.Label(range_frame, text="最大特征数:").grid(row=0, column=2, sticky=tk.W, pady=5, padx=10)
        self.max_features_entry = ttk.Spinbox(range_frame, from_=2, to=5, width=5)
        self.max_features_entry.grid(row=0, column=3, sticky=tk.W, pady=5, padx=5)
        self.max_features_entry.set(5)
        
        # 创建最大策略数选择
        ttk.Label(strategy_frame, text="最大策略数:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.max_strategies_entry = ttk.Entry(strategy_frame, width=10)
        self.max_strategies_entry.grid(row=2, column=1, sticky=tk.W, pady=5)
        self.max_strategies_entry.insert(0, "10000")
        
        # 创建分析按钮
        ttk.Button(strategy_frame, text="开始分析策略", command=self.analyze_strategy).grid(row=3, column=1, sticky=tk.W, pady=10)
        
        # 创建进度条
        ttk.Label(strategy_frame, text="分析进度:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.strategy_progress = ttk.Progressbar(strategy_frame, orient=tk.HORIZONTAL, length=400, mode='determinate')
        self.strategy_progress.grid(row=4, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        # 创建日志框
        ttk.Label(strategy_frame, text="分析日志:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.strategy_log = scrolledtext.ScrolledText(strategy_frame, width=70, height=15)
        self.strategy_log.grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=5)
        
    def init_result_tab(self):
        """初始化结果查看选项卡"""
        # 创建结果查看框架
        result_frame = ttk.LabelFrame(self.result_tab, text="结果查看", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建结果文件选择
        ttk.Label(result_frame, text="结果文件:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.result_file_entry = ttk.Entry(result_frame, width=50)
        self.result_file_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        ttk.Button(result_frame, text="浏览...", command=self.browse_result_file).grid(row=0, column=2, sticky=tk.W, pady=5, padx=5)
        
        # 创建查看按钮
        ttk.Button(result_frame, text="查看结果", command=self.view_result).grid(row=1, column=1, sticky=tk.W, pady=10)
        
        # 创建结果表格
        ttk.Label(result_frame, text="结果表格:").grid(row=2, column=0, sticky=tk.W, pady=5)
        
        # 创建表格框架
        table_frame = ttk.Frame(result_frame)
        table_frame.grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=5)
        
        # 创建表格
        self.result_table = ttk.Treeview(table_frame, columns=("策略编号", "策略组合", "特征数量", "总收益率", "平均胜率"), show="headings", height=10)
        self.result_table.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加表头
        self.result_table.heading("策略编号", text="策略编号")
        self.result_table.heading("策略组合", text="策略组合")
        self.result_table.heading("特征数量", text="特征数量")
        self.result_table.heading("总收益率", text="总收益率(%)")
        self.result_table.heading("平均胜率", text="平均胜率(%)")
        
        # 设置列宽
        self.result_table.column("策略编号", width=80)
        self.result_table.column("策略组合", width=200)
        self.result_table.column("特征数量", width=80)
        self.result_table.column("总收益率", width=100)
        self.result_table.column("平均胜率", width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.result_table.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.result_table.configure(yscrollcommand=scrollbar.set)
        
        # 创建图表框架
        chart_frame = ttk.LabelFrame(result_frame, text="策略收益图表")
        chart_frame.grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=10)
        
        # 创建图表
        self.fig = plt.Figure(figsize=(8, 4), dpi=100)
        self.ax = self.fig.add_subplot(111)
        self.canvas = FigureCanvasTkAgg(self.fig, master=chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def browse_data_dir(self):
        """浏览数据目录"""
        data_dir = filedialog.askdirectory(title="选择数据目录")
        if data_dir:
            self.data_dir_entry.delete(0, tk.END)
            self.data_dir_entry.insert(0, data_dir)
            self.data_dir = data_dir
            
    def browse_output_dir(self):
        """浏览输出目录"""
        output_dir = filedialog.askdirectory(title="选择输出目录")
        if output_dir:
            self.output_dir_entry.delete(0, tk.END)
            self.output_dir_entry.insert(0, output_dir)
            self.output_dir = output_dir
            
    def browse_result_file(self):
        """浏览结果文件"""
        result_file = filedialog.askopenfilename(title="选择结果文件", filetypes=[("Excel文件", "*.xlsx")])
        if result_file:
            self.result_file_entry.delete(0, tk.END)
            self.result_file_entry.insert(0, result_file)
            
    def process_data(self):
        """处理数据"""
        # 获取数据目录和输出目录
        self.data_dir = self.data_dir_entry.get()
        self.output_dir = self.output_dir_entry.get()
        
        # 检查目录是否存在
        if not os.path.exists(self.data_dir):
            messagebox.showerror("错误", "数据目录不存在")
            return
        
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            
        # 获取处理选项
        process_all = self.process_all_var.get()
        start_date = self.start_date_entry.get()
        end_date = self.end_date_entry.get()
        
        # 启动处理线程
        self.is_running = True
        threading.Thread(target=self.process_data_thread, args=(process_all, start_date, end_date)).start()
        
    def process_data_thread(self, process_all, start_date, end_date):
        """处理数据线程"""
        try:
            # 清空日志
            self.data_log.delete(1.0, tk.END)
            
            # 重置进度条
            self.data_progress["value"] = 0
            self.root.update_idletasks()
            
            # 记录开始时间
            start_time = time.time()
            
            # 输出日志
            self.log_data("开始处理数据...")
            self.log_data(f"数据目录: {self.data_dir}")
            self.log_data(f"输出目录: {self.output_dir}")
            self.log_data(f"处理所有数据: {process_all}")
            self.log_data(f"开始日期: {start_date}")
            self.log_data(f"结束日期: {end_date}")
            
            # 模拟数据处理
            self.log_data("正在读取数据文件...")
            
            # 更新进度条
            for i in range(101):
                if not self.is_running:
                    break
                self.data_progress["value"] = i
                self.root.update_idletasks()
                time.sleep(0.05)
                
            # 记录结束时间
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            # 输出日志
            self.log_data(f"数据处理完成，耗时: {elapsed_time:.2f}秒")
            
            # 弹出完成消息
            messagebox.showinfo("完成", "数据处理完成")
            
        except Exception as e:
            # 输出错误日志
            self.log_data(f"处理数据时出错: {str(e)}")
            
            # 弹出错误消息
            messagebox.showerror("错误", f"处理数据时出错: {str(e)}")
            
        finally:
            # 重置运行状态
            self.is_running = False
            
    def analyze_strategy(self):
        """分析策略"""
        # 获取输出目录
        self.output_dir = self.output_dir_entry.get()
        
        # 检查目录是否存在
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            
        # 获取特征
        self.features = [feature for feature, var in self.feature_vars.items() if var.get()]
        
        # 检查特征是否为空
        if not self.features:
            messagebox.showerror("错误", "请至少选择一个特征")
            return
        
        # 获取特征组合范围
        try:
            self.min_features = int(self.min_features_entry.get())
            self.max_features = int(self.max_features_entry.get())
            
            # 检查范围是否有效
            if self.min_features < 2 or self.min_features > 5:
                messagebox.showerror("错误", "最小特征数必须在2到5之间")
                return
                
            if self.max_features < 2 or self.max_features > 5:
                messagebox.showerror("错误", "最大特征数必须在2到5之间")
                return
                
            if self.min_features > self.max_features:
                messagebox.showerror("错误", "最小特征数不能大于最大特征数")
                return
        except ValueError:
            messagebox.showerror("错误", "特征组合范围必须是整数")
            return
        
        # 获取最大策略数
        try:
            self.max_strategies = int(self.max_strategies_entry.get())
            
            # 检查最大策略数是否有效
            if self.max_strategies < 1:
                messagebox.showerror("错误", "最大策略数必须大于0")
                return
        except ValueError:
            messagebox.showerror("错误", "最大策略数必须是整数")
            return
        
        # 启动分析线程
        self.is_running = True
        threading.Thread(target=self.analyze_strategy_thread).start()
        
    def analyze_strategy_thread(self):
        """分析策略线程"""
        try:
            # 清空日志
            self.strategy_log.delete(1.0, tk.END)
            
            # 重置进度条
            self.strategy_progress["value"] = 0
            self.root.update_idletasks()
            
            # 记录开始时间
            start_time = time.time()
            
            # 输出日志
            self.log_strategy("开始分析策略...")
            self.log_strategy(f"输出目录: {self.output_dir}")
            self.log_strategy(f"特征: {self.features}")
            self.log_strategy(f"特征组合范围: {self.min_features} - {self.max_features}")
            self.log_strategy(f"最大策略数: {self.max_strategies}")
            
            # 创建结果目录
            results_dir = os.path.join(self.output_dir, 'strategy_results')
            if not os.path.exists(results_dir):
                os.makedirs(results_dir)
                
            # 创建策略详细分析目录
            strategy_details_dir = os.path.join(results_dir, 'strategy_details')
            if not os.path.exists(strategy_details_dir):
                os.makedirs(strategy_details_dir)
                
            # 生成所有特征组合
            self.log_strategy(f"生成从{self.min_features}到{self.max_features}个特征的所有组合...")
            
            all_combinations = []
            for r in range(self.min_features, self.max_features + 1):
                combinations = list(itertools.combinations(self.features, r))
                self.log_strategy(f"{r}特征组合数量: {len(combinations)}")
                all_combinations.extend(combinations)
                
            self.log_strategy(f"总组合数量: {len(all_combinations)}")
            
            # 更新进度条
            self.total_progress = len(all_combinations)
            self.progress = 0
            
            # 模拟策略分析
            self.log_strategy("正在分析策略...")
            
            # 更新进度条
            for i in range(101):
                if not self.is_running:
                    break
                self.strategy_progress["value"] = i
                self.root.update_idletasks()
                time.sleep(0.05)
                
            # 记录结束时间
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            # 输出日志
            self.log_strategy(f"策略分析完成，耗时: {elapsed_time:.2f}秒")
            
            # 弹出完成消息
            messagebox.showinfo("完成", "策略分析完成")
            
        except Exception as e:
            # 输出错误日志
            self.log_strategy(f"分析策略时出错: {str(e)}")
            
            # 弹出错误消息
            messagebox.showerror("错误", f"分析策略时出错: {str(e)}")
            
        finally:
            # 重置运行状态
            self.is_running = False
            
    def view_result(self):
        """查看结果"""
        # 获取结果文件
        result_file = self.result_file_entry.get()
        
        # 检查文件是否存在
        if not os.path.exists(result_file):
            messagebox.showerror("错误", "结果文件不存在")
            return
        
        try:
            # 读取Excel文件
            df = pd.read_excel(result_file, sheet_name="策略汇总")
            
            # 清空表格
            for item in self.result_table.get_children():
                self.result_table.delete(item)
                
            # 添加数据到表格
            for i, row in df.iterrows():
                self.result_table.insert("", "end", values=(
                    row["策略编号"],
                    row["策略组合"],
                    row["特征数量"],
                    f"{row['总收益率(%)']:.2f}",
                    f"{row['平均胜率(%)']:.2f}"
                ))
                
            # 绘制图表
            self.ax.clear()
            
            # 按特征数量分组
            grouped = df.groupby("特征数量")["总收益率(%)"].mean()
            
            # 绘制柱状图
            grouped.plot(kind="bar", ax=self.ax)
            
            # 设置图表标题和标签
            self.ax.set_title("不同特征数量的平均收益率")
            self.ax.set_xlabel("特征数量")
            self.ax.set_ylabel("平均收益率(%)")
            
            # 更新图表
            self.fig.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            # 弹出错误消息
            messagebox.showerror("错误", f"查看结果时出错: {str(e)}")
            
    def log_data(self, message):
        """输出数据处理日志"""
        self.data_log.insert(tk.END, f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
        self.data_log.see(tk.END)
        self.root.update_idletasks()
        
    def log_strategy(self, message):
        """输出策略分析日志"""
        self.strategy_log.insert(tk.END, f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
        self.strategy_log.see(tk.END)
        self.root.update_idletasks()

if __name__ == "__main__":
    root = tk.Tk()
    app = StockAnalysisApp(root)
    root.mainloop()
