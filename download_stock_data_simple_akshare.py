"""
使用akshare库下载股票数据
简化版本，直接按照指定的日期范围获取数据
"""

import pandas as pd
import akshare as ak
import os
import datetime
import time
import stock_data_manager as sdm

# 设置文件路径
base_dir = r'E:\机器学习\complete_excel_results'
stock_data_dir = os.path.join(base_dir, 'stock_data')
daily_data_dir = os.path.join(stock_data_dir, 'daily')
stock_details_file = os.path.join(base_dir, '股票明细_完整.xlsx')
history_data_file = os.path.join(stock_data_dir, 'stock_history_data.xlsx')

# 确保目录存在
if not os.path.exists(stock_data_dir):
    os.makedirs(stock_data_dir)
if not os.path.exists(daily_data_dir):
    os.makedirs(daily_data_dir)

def download_stock_data(start_date, end_date):
    """
    下载指定日期范围内的所有股票数据
    
    参数:
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD
        
    返回:
        DataFrame: 包含所有股票在日期范围内的数据
    """
    print(f"开始下载从 {start_date} 到 {end_date} 的股票数据")
    start_time = time.time()
    
    try:
        # 使用akshare获取A股历史行情数据
        # 这里使用stock_zh_index_daily_em接口获取上证指数的历史数据，以获取交易日列表
        index_data = ak.stock_zh_index_daily_em(symbol="sh000001", start_date=start_date, end_date=end_date)
        trading_dates = index_data['date'].tolist()
        print(f"在日期范围 {start_date} 到 {end_date} 内找到 {len(trading_dates)} 个交易日")
        
        # 按日期下载数据
        all_data = []
        
        for date_str in trading_dates:
            # 检查该日期的数据文件是否已存在
            daily_file_path = sdm.get_daily_data_path(date_str)
            if os.path.exists(daily_file_path):
                print(f"日期 {date_str} 的数据文件已存在，跳过")
                
                # 读取已存在的数据，用于合并
                try:
                    day_df = sdm.load_daily_data(date_str)
                    if not day_df.empty:
                        all_data.append(day_df)
                        print(f"已加载日期 {date_str} 的现有数据，共 {len(day_df)} 条记录")
                except Exception as e:
                    print(f"加载日期 {date_str} 的现有数据时出错: {e}")
                
                continue
            
            # 下载该日期的数据
            print(f"开始下载日期 {date_str} 的股票数据")
            day_start_time = time.time()
            
            try:
                # 使用akshare获取当日所有A股数据
                stock_zh_a_spot_df = ak.stock_zh_a_spot_em()
                
                # 添加日期列
                stock_zh_a_spot_df['date'] = date_str
                
                # 打印列名，以便了解数据结构
                print(f"原始数据列名: {stock_zh_a_spot_df.columns.tolist()}")
                
                # 重命名列，使其与我们的数据结构一致
                column_mapping = {
                    '代码': 'code',
                    '名称': 'name',
                    '今开': 'open',
                    '最高': 'high',
                    '最低': 'low',
                    '最新价': 'close',
                    '成交量': 'volume',
                    '成交额': 'amount',
                    '涨跌幅': 'pctChg'
                }
                
                # 应用列映射
                renamed_columns = {}
                for old_col, new_col in column_mapping.items():
                    if old_col in stock_zh_a_spot_df.columns:
                        renamed_columns[old_col] = new_col
                
                stock_zh_a_spot_df = stock_zh_a_spot_df.rename(columns=renamed_columns)
                
                # 确保必要的列存在
                required_columns = ['date', 'code', 'name', 'open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']
                for col in required_columns:
                    if col not in stock_zh_a_spot_df.columns:
                        print(f"警告: 列 '{col}' 不存在，将创建空列")
                        stock_zh_a_spot_df[col] = None
                
                # 选择需要的列
                available_columns = [col for col in required_columns if col in stock_zh_a_spot_df.columns]
                day_df = stock_zh_a_spot_df[available_columns]
                
                # 保存该日期的数据
                sdm.save_daily_data(day_df, date_str)
                print(f"日期 {date_str} 的数据已保存，共 {len(day_df)} 条记录")
                
                all_data.append(day_df)
                
                day_end_time = time.time()
                day_elapsed_time = day_end_time - day_start_time
                print(f"日期 {date_str} 的数据下载完成，耗时: {day_elapsed_time:.2f}秒")
            
            except Exception as e:
                print(f"下载日期 {date_str} 的数据时出错: {e}")
                import traceback
                traceback.print_exc()
        
        # 合并所有数据
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            print(f"日期范围 {start_date} 到 {end_date} 的数据下载完成，共 {len(combined_df)} 条记录，耗时: {elapsed_time:.2f}秒")
            
            # 保存合并文件
            history_data_file = os.path.join(stock_data_dir, f'stock_history_data_{start_date.replace("-", "")}_{end_date.replace("-", "")}.xlsx')
            combined_df.to_excel(history_data_file, index=False)
            print(f"合并数据已保存到 {history_data_file}")
            
            return combined_df
        else:
            print(f"日期范围 {start_date} 到 {end_date} 没有下载到任何数据")
            return pd.DataFrame()
    
    except Exception as e:
        print(f"下载股票数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='使用akshare下载股票数据')
    parser.add_argument('--start_date', type=str, default='2025-02-05', help='开始日期，格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, default='2025-02-05', help='结束日期，格式为YYYY-MM-DD')
    
    args = parser.parse_args()
    
    # 执行下载
    download_stock_data(args.start_date, args.end_date)
