#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
处理新结构的股票数据
作者: Augment AI
版本: 1.0.0

该脚本用于处理新结构的股票数据，计算买入日开盘涨跌幅和卖出日开盘涨跌幅。
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import glob

def read_stock_data(file_path):
    """
    读取新结构的股票数据
    
    参数:
        file_path (str): 股票数据文件路径
        
    返回:
        DataFrame: 股票数据
    """
    print(f"正在读取股票数据: {file_path}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 检查必要的列是否存在
        required_columns = ['证券代码', '日期', '开盘价', '收盘价', '前收盘价', '涨跌幅', '股票名称']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"错误: 缺少必要的列: {missing_columns}")
            return None
        
        # 确保日期列是日期类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        print(f"成功读取股票数据，共{len(df)}条记录")
        return df
    except Exception as e:
        print(f"读取股票数据时出错: {str(e)}")
        return None

def read_all_stock_data(directory):
    """
    读取目录中的所有股票数据文件
    
    参数:
        directory (str): 股票数据目录
        
    返回:
        DataFrame: 合并后的股票数据
    """
    print(f"正在读取目录中的所有股票数据: {directory}")
    
    try:
        # 查找所有Excel文件
        file_pattern = os.path.join(directory, "*.xlsx")
        files = glob.glob(file_pattern)
        
        if not files:
            print(f"错误: 目录中没有Excel文件: {directory}")
            return None
        
        print(f"找到 {len(files)} 个Excel文件")
        
        # 读取所有文件并合并
        all_data = []
        for file_path in files:
            df = read_stock_data(file_path)
            if df is not None:
                all_data.append(df)
        
        if not all_data:
            print("错误: 没有成功读取任何数据")
            return None
        
        # 合并数据
        merged_df = pd.concat(all_data, ignore_index=True)
        
        # 去除重复记录
        merged_df = merged_df.drop_duplicates(subset=['证券代码', '日期'])
        
        print(f"成功合并股票数据，共{len(merged_df)}条记录")
        return merged_df
    except Exception as e:
        print(f"读取目录中的所有股票数据时出错: {str(e)}")
        return None

def calculate_indicators(df):
    """
    计算买入日开盘涨跌幅和卖出日开盘涨跌幅
    
    参数:
        df (DataFrame): 股票数据
        
    返回:
        DataFrame: 处理后的股票数据
    """
    print("正在计算买入日开盘涨跌幅和卖出日开盘涨跌幅...")
    
    try:
        # 确保数据按股票代码和日期排序
        df = df.sort_values(['证券代码', '日期'])
        
        # 计算买入日开盘涨跌幅（当日开盘价相对于前一日收盘价的涨跌幅）
        df['买入日开盘涨跌幅'] = (df['开盘价'] - df['前收盘价']) / df['前收盘价'] * 100
        
        # 计算卖出日开盘涨跌幅（下一个交易日的开盘价相对于当日收盘价的涨跌幅）
        # 首先，计算下一个交易日的开盘价
        df['下一日开盘价'] = df.groupby('证券代码')['开盘价'].shift(-1)
        
        # 然后，计算卖出日开盘涨跌幅
        df['卖出日开盘涨跌幅'] = (df['下一日开盘价'] - df['收盘价']) / df['收盘价'] * 100
        
        print("计算完成")
        return df
    except Exception as e:
        print(f"计算指标时出错: {str(e)}")
        return df

def filter_stocks(df, date, min_change=0):
    """
    筛选特定日期的股票，并按买入日开盘涨跌幅排序
    
    参数:
        df (DataFrame): 股票数据
        date (str): 日期，格式为YYYY-MM-DD
        min_change (float): 最小买入日开盘涨跌幅
        
    返回:
        DataFrame: 筛选后的股票数据
    """
    print(f"正在筛选日期为 {date} 的股票...")
    
    try:
        # 转换日期格式
        date = pd.to_datetime(date)
        
        # 筛选特定日期的股票
        filtered_df = df[df['日期'] == date].copy()
        
        # 筛选买入日开盘涨跌幅大于等于指定值的股票
        if min_change > 0:
            filtered_df = filtered_df[filtered_df['买入日开盘涨跌幅'] >= min_change]
        
        # 按买入日开盘涨跌幅降序排序
        filtered_df = filtered_df.sort_values('买入日开盘涨跌幅', ascending=False)
        
        print(f"筛选完成，共{len(filtered_df)}条记录")
        return filtered_df
    except Exception as e:
        print(f"筛选股票时出错: {str(e)}")
        return None

def save_to_excel(df, output_file):
    """
    保存数据到Excel文件
    
    参数:
        df (DataFrame): 数据
        output_file (str): 输出文件路径
        
    返回:
        bool: 是否成功
    """
    print(f"正在保存数据到Excel文件: {output_file}")
    
    try:
        # 创建输出目录（如果不存在）
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 保存到Excel文件
        df.to_excel(output_file, index=False)
        
        print(f"数据已成功保存到: {output_file}")
        return True
    except Exception as e:
        print(f"保存数据时出错: {str(e)}")
        return False

def main():
    """主函数"""
    # 设置股票数据目录
    data_dir = input("请输入股票数据目录 (默认: E:\\机器学习\\complete_excel_results\\stock_data\\daily): ") or "E:\\机器学习\\complete_excel_results\\stock_data\\daily"
    
    # 设置输出文件路径
    output_file = input("请输入输出文件路径 (默认: E:\\机器学习\\complete_excel_results\\股票明细_处理后.xlsx): ") or "E:\\机器学习\\complete_excel_results\\股票明细_处理后.xlsx"
    
    # 设置筛选日期
    filter_date = input("请输入要筛选的日期 (格式: YYYY-MM-DD，留空表示不筛选): ")
    
    # 设置最小买入日开盘涨跌幅
    min_change_str = input("请输入最小买入日开盘涨跌幅 (默认: 0): ") or "0"
    min_change = float(min_change_str)
    
    # 读取所有股票数据
    df = read_all_stock_data(data_dir)
    if df is None:
        return
    
    # 计算指标
    df = calculate_indicators(df)
    
    # 如果指定了筛选日期，则筛选特定日期的股票
    if filter_date:
        filtered_df = filter_stocks(df, filter_date, min_change)
        if filtered_df is not None:
            # 保存筛选后的数据
            save_to_excel(filtered_df, output_file)
    else:
        # 保存所有数据
        save_to_excel(df, output_file)

if __name__ == "__main__":
    main()
