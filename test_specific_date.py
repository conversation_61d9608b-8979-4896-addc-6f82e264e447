"""
测试特定日期的策略表现
"""

import pandas as pd
import numpy as np
import joblib
from datetime import datetime
import os

def load_model(model_dir='trained_models'):
    """加载已训练好的模型"""
    print(f"加载模型...")
    try:
        # 加载最新模型信息
        latest_model_info = joblib.load(f"{model_dir}/latest_model_info.joblib")

        # 加载模型、缩放器和特征
        model = joblib.load(latest_model_info['model_file'])
        scaler = joblib.load(latest_model_info['scaler_file'])
        features = joblib.load(latest_model_info['features_file'])

        print(f"成功加载模型 (训练时间: {latest_model_info['timestamp']})")
        print(f"模型使用的特征: {features}")

        return model, scaler, features
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None, None, None

def load_data(file_path='股票明细.xlsx'):
    """加载股票数据"""
    print(f"加载股票数据: {file_path}")
    try:
        stock_data = pd.read_excel(file_path)
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
        return stock_data
    except Exception as e:
        print(f"加载股票数据失败: {e}")
        return None

def preprocess_data(stock_data):
    """预处理股票数据，计算必要的特征"""
    print("预处理数据...")

    # 转换日期格式
    if isinstance(stock_data['日期'].iloc[0], str):
        stock_data['日期'] = pd.to_datetime(stock_data['日期'])

    # 按股票代码和日期排序
    stock_data = stock_data.sort_values(['股票代码', '日期'])

    # 按股票代码分组处理
    for code, group in stock_data.groupby('股票代码'):
        # 确保数据按日期排序
        group = group.sort_values('日期')

        # 计算连续技术强度天数（连续多少天为100）
        consecutive_days = []
        current_count = 0

        for strength in group['技术强度'].values:
            if strength == 100:
                current_count += 1
            else:
                current_count = 0
            consecutive_days.append(current_count)

        # 更新原始数据
        stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days

        # 计算技术强度累积值（5天）
        cumulative_strength = group['技术强度'].copy()
        for i in range(1, 5):
            cumulative_strength += group['技术强度'].shift(i).fillna(0)

        # 更新原始数据
        stock_data.loc[group.index, '连续技术强度5天数'] = cumulative_strength

        # 计算趋势特征
        stock_data.loc[group.index, '技术强度趋势'] = (
            (group['技术强度'] > group['技术强度'].shift(1)) &
            (group['技术强度'].shift(1) > group['技术强度'].shift(2))
        ).astype(int)

        stock_data.loc[group.index, '价格趋势'] = (
            (group['当前价格'] > group['当前价格'].shift(1)) &
            (group['当前价格'].shift(1) > group['当前价格'].shift(2))
        ).astype(int)

        if '涨跌幅' in group.columns:
            stock_data.loc[group.index, '涨跌幅趋势'] = (
                (group['涨跌幅'] > group['涨跌幅'].shift(1)) &
                (group['涨跌幅'].shift(1) > group['涨跌幅'].shift(2))
            ).astype(int)
        else:
            stock_data.loc[group.index, '涨跌幅趋势'] = 0

        # 计算两日收益率（买入后第二天卖出）
        two_day_later_price = group['当前价格'].shift(-2)
        two_day_return = (two_day_later_price / group['当前价格'] - 1) * 100

        # 更新原始数据
        stock_data.loc[group.index, '两日收益率'] = two_day_return

        # 计算是否盈利（两日收益率>0）
        is_profit = (two_day_return > 0).astype(int)
        stock_data.loc[group.index, '是否盈利'] = is_profit

        # 计算次日涨跌方向（用于判断开盘时是否上涨）
        stock_data.loc[group.index, '次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)

    # 处理技术指标特征
    if '技术指标' in stock_data.columns:
        # 提取常见的技术指标关键词
        tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
                          '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']

        # 为每个技术指标创建一个新列
        for indicator in tech_indicators:
            col_name = f'技术指标_{indicator}'
            # 检查技术指标文本中是否包含该关键词
            if '技术指标' in stock_data.columns:
                stock_data[col_name] = stock_data['技术指标'].str.contains(indicator, na=False).astype(int)
            else:
                stock_data[col_name] = 0
    else:
        # 如果没有技术指标列，创建空的技术指标特征
        tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
                          '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']
        for indicator in tech_indicators:
            stock_data[f'技术指标_{indicator}'] = 0

    print("预处理完成")
    return stock_data

def test_strategy_1_on_date(test_date_str, data_file_path='股票明细.xlsx'):
    """
    测试策略1在特定日期的表现

    参数:
    test_date_str: 测试日期，格式为'YYYY-MM-DD'
    data_file_path: 数据文件路径
    """
    print(f"测试策略1在 {test_date_str} 的表现")
    print("=" * 80)

    # 加载模型
    model, scaler, features = load_model()
    if model is None:
        print("无法加载模型，请先训练模型")
        return

    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return

    # 预处理数据
    processed_data = preprocess_data(stock_data)

    # 转换测试日期
    test_date = pd.to_datetime(test_date_str)

    # 获取测试日期的数据
    test_data = processed_data[processed_data['日期'] == test_date]

    if len(test_data) == 0:
        print(f"在 {test_date_str} 没有找到数据")
        return

    print(f"测试日期数据记录数: {len(test_data)}")

    # 提取特征
    try:
        X_test = test_data[features]

        # 处理测试数据中的缺失值
        valid_test_indices = ~X_test.isnull().any(axis=1)
        X_test = X_test[valid_test_indices]
        test_data_filtered = test_data.loc[valid_test_indices.index[valid_test_indices]]

        if len(X_test) == 0:
            print(f"测试数据不足，无法进行测试")
            return

        # 标准化测试数据
        X_test_scaled = scaler.transform(X_test)

        # 预测盈利概率
        pred_proba = model.predict_proba(X_test_scaled)[:, 1]

        # 创建预测结果DataFrame
        predictions = pd.DataFrame({
            '股票代码': test_data_filtered['股票代码'],
            '股票名称': test_data_filtered['股票名称'],
            '涨跌幅': test_data_filtered['涨跌幅'] if '涨跌幅' in test_data_filtered.columns else 0,
            '技术强度': test_data_filtered['技术强度'],
            '连续技术强度天数': test_data_filtered['连续技术强度天数'],
            '连续技术强度5天数': test_data_filtered['连续技术强度5天数'],
            '预测盈利概率': pred_proba,
            '实际盈利': test_data_filtered['是否盈利'],
            '实际收益率': test_data_filtered['两日收益率'],
            '次日涨跌方向': test_data_filtered['次日涨跌方向']
        })

        # 应用策略1
        strategy_1_stocks = predictions[
            (predictions['预测盈利概率'] > 0.78) &  # 条件1: 预测盈利概率>78%
            (predictions['技术强度'] >= 70) &  # 条件2: 技术强度≥70
            (predictions['连续技术强度5天数'] >= 400)  # 条件3: 5天累积值≥400
        ]

        # 计算策略表现
        if len(strategy_1_stocks) > 0:
            # 只考虑开盘时上涨的股票
            up_stocks = strategy_1_stocks[strategy_1_stocks['次日涨跌方向'] == 1]

            print(f"\n策略1推荐股票数: {len(strategy_1_stocks)}")
            print(f"开盘时上涨的股票数: {len(up_stocks)}")

            if len(up_stocks) > 0:
                win_rate = up_stocks['实际盈利'].mean() * 100
                avg_return = up_stocks['实际收益率'].mean()

                print(f"胜率: {win_rate:.2f}%")
                print(f"平均收益率: {avg_return:.2f}%")

                print("\n推荐买入的股票:")
                print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！")
                print("【交易策略】: 买入后在第二个交易日开盘时卖出")
                print("\n股票列表:")
                for i, row in strategy_1_stocks.iterrows():
                    profit_status = "盈利" if row['实际盈利'] == 1 else "亏损"
                    print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%, 实际收益率={row['实际收益率']:.2f}%, 实际结果={profit_status}")
            else:
                print("没有开盘时上涨的股票")
        else:
            print("\n策略1没有推荐的股票")

        # 保存结果
        if not os.path.exists('test_results'):
            os.makedirs('test_results')

        result_file = f'test_results/strategy_1_{test_date_str}.xlsx'
        strategy_1_stocks.to_excel(result_file, index=False)

        if len(strategy_1_stocks) > 0:
            print(f"\n测试结果已保存至: {result_file}")

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_strategy_1_on_date('2025-05-07')
