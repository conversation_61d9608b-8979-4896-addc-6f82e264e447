#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查最终结果
"""

import pandas as pd

def check_final_result():
    """检查最终的修复结果"""
    
    print("=== 最终修复结果检查 ===")
    
    try:
        # 读取生成的数据
        df = pd.read_excel('E:/机器学习/complete_excel_results/tech_strength/daily/tech_strength_2025-05-15.xlsx')
        
        print(f"数据行数: {len(df)}")
        
        # 检查样本数据
        sample = df.iloc[0]
        print(f"\n样本股票: {sample['股票代码']}")
        
        # 检查所有关键字段
        print("\n=== 关键字段检查 ===")
        
        # 1. 技术指标特征
        tech_feature = sample['技术指标特征']
        print(f"技术指标特征: {tech_feature} (类型: {type(tech_feature)}, 长度: {len(str(tech_feature))})")
        
        # 2. 趋势组合
        trend_combo = sample['趋势组合']
        print(f"趋势组合: {trend_combo} (类型: {type(trend_combo)}, 长度: {len(str(trend_combo))})")
        
        # 3. 连续技术强度
        consecutive_3 = sample['连续技术强度3天数']
        consecutive_5 = sample['连续技术强度5天数']
        consecutive_10 = sample['连续技术强度10天数']
        print(f"连续技术强度: 3天={consecutive_3}, 5天={consecutive_5}, 10天={consecutive_10}")
        
        # 4. 其他字段
        if '买入日开盘涨跌幅' in df.columns:
            buy_change = sample['买入日开盘涨跌幅']
            print(f"买入日开盘涨跌幅: {buy_change} (类型: {type(buy_change)})")
        
        if '日内股票标记' in df.columns:
            intraday_mark = sample['日内股票标记']
            print(f"日内股票标记: {intraday_mark} (类型: {type(intraday_mark)})")
        
        if '卖出日开盘涨跌幅' in df.columns:
            sell_change = sample['卖出日开盘涨跌幅']
            print(f"卖出日开盘涨跌幅: {sell_change} (类型: {type(sell_change)})")
        
        # 检查数据多样性
        print("\n=== 数据多样性检查 ===")
        print(f"技术指标特征唯一值数量: {df['技术指标特征'].nunique()}")
        print(f"趋势组合唯一值数量: {df['趋势组合'].nunique()}")
        
        # 检查连续技术强度的递增关系
        print("\n=== 连续技术强度递增关系检查 ===")
        correct_order_count = 0
        total_count = 0
        
        for i, row in df.head(10).iterrows():
            c3 = row['连续技术强度3天数']
            c5 = row['连续技术强度5天数']
            c10 = row['连续技术强度10天数']
            
            is_correct = c3 <= c5 <= c10
            total_count += 1
            if is_correct:
                correct_order_count += 1
            
            print(f"  股票 {row['股票代码']}: 3天={c3}, 5天={c5}, 10天={c10} {'✅' if is_correct else '❌'}")
        
        print(f"\n递增关系正确率: {correct_order_count}/{total_count} = {correct_order_count/total_count*100:.1f}%")
        
        # 最终评估
        print("\n=== 最终评估 ===")
        
        issues = []
        
        # 检查技术指标特征
        if df['技术指标特征'].dtype == 'object':
            print("✅ 技术指标特征: 类型正确 (object)")
        else:
            print("❌ 技术指标特征: 类型错误")
            issues.append("技术指标特征类型")
        
        # 检查趋势组合
        if df['趋势组合'].dtype == 'object':
            print("✅ 趋势组合: 类型正确 (object)")
        else:
            print("❌ 趋势组合: 类型错误")
            issues.append("趋势组合类型")
        
        # 检查趋势组合多样性
        if df['趋势组合'].nunique() > 10:
            print("✅ 趋势组合: 多样性良好")
        else:
            print("❌ 趋势组合: 多样性不足")
            issues.append("趋势组合多样性")
        
        # 检查连续技术强度
        if correct_order_count / total_count >= 0.8:
            print("✅ 连续技术强度: 递增关系正确")
        else:
            print("❌ 连续技术强度: 递增关系错误")
            issues.append("连续技术强度递增关系")
        
        if len(issues) == 0:
            print("\n🎉 所有问题都已修复！数据生成完全正确！")
        else:
            print(f"\n⚠️ 还有 {len(issues)} 个问题需要解决: {', '.join(issues)}")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_final_result()
