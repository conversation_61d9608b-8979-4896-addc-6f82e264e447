"""
回测策略胜率

检查策略在历史数据上的表现
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import argparse
from stock_predictor import (
    preprocess_data, load_data, apply_strategy_1, 
    apply_strategy_A, apply_strategy_B, apply_strategy_C,
    load_model, print_header, clear_screen
)

def backtest_strategy(strategy_name, strategy_func, start_date_str, end_date_str, data_file_path='股票明细.xlsx'):
    """
    回测策略在历史数据上的表现
    
    参数:
    strategy_name: 策略名称
    strategy_func: 策略函数
    start_date_str: 开始日期，格式为'YYYY-MM-DD'
    end_date_str: 结束日期，格式为'YYYY-MM-DD'
    data_file_path: 数据文件路径
    """
    strategy_descriptions = {
        'strategy_1': "策略1：100%高胜率策略",
        'strategy_A': "策略A：最高胜率策略",
        'strategy_B': "策略B：最高收益率策略",
        'strategy_C': "策略C：平衡策略（胜率和交易机会的平衡）"
    }
    
    print_header(f"回测{strategy_descriptions[strategy_name]}")
    
    # 加载模型
    model, scaler, features = load_model()
    if model is None:
        print("无法加载模型，请先训练模型")
        return
    
    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return
    
    # 预处理数据
    processed_data = preprocess_data(stock_data)
    
    # 转换日期
    start_date = pd.to_datetime(start_date_str)
    end_date = pd.to_datetime(end_date_str)
    
    # 获取日期范围内的所有日期
    date_range = processed_data['日期'].unique()
    date_range = sorted([d for d in date_range if start_date <= d <= end_date])
    
    if not date_range:
        print(f"在{start_date_str}至{end_date_str}之间没有找到数据")
        return
    
    print(f"回测日期范围: {date_range[0]} 至 {date_range[-1]}")
    print(f"共 {len(date_range)} 个交易日")
    
    # 创建结果DataFrame
    results = []
    
    # 对每个日期进行回测
    for test_date in date_range:
        print(f"\n回测日期: {test_date}")
        
        # 获取当天的数据
        current_data = processed_data[processed_data['日期'] == test_date]
        
        # 准备预测数据
        try:
            # 提取特征
            X_pred = current_data[features]
            
            # 处理预测数据中的缺失值
            valid_pred_indices = ~X_pred.isnull().any(axis=1)
            X_pred = X_pred[valid_pred_indices]
            current_data_filtered = current_data.loc[valid_pred_indices.index[valid_pred_indices]]
            
            if len(X_pred) == 0:
                print(f"预测数据不足，无法进行预测")
                continue
            
            # 标准化预测数据
            X_pred_scaled = scaler.transform(X_pred)
            
            # 预测盈利概率
            pred_proba = model.predict_proba(X_pred_scaled)[:, 1]
            
            # 创建预测结果DataFrame
            predictions = pd.DataFrame({
                '股票代码': current_data_filtered['股票代码'],
                '股票名称': current_data_filtered['股票名称'],
                '涨跌幅': current_data_filtered['涨跌幅'] if '涨跌幅' in current_data_filtered.columns else 0,
                '技术强度': current_data_filtered['技术强度'],
                '连续技术强度天数': current_data_filtered['连续技术强度天数'],
                '连续技术强度5天数': current_data_filtered['连续技术强度5天数'],
                '预测盈利概率': pred_proba,
                '实际盈利': current_data_filtered['是否盈利'],
                '实际收益率': current_data_filtered['两日收益率'],
                '次日涨跌方向': current_data_filtered['次日涨跌方向']
            })
            
            # 应用策略
            strategy_stocks = strategy_func(predictions)
            
            # 计算策略表现
            if len(strategy_stocks) > 0:
                # 只考虑开盘时上涨的股票
                up_stocks = strategy_stocks[strategy_stocks['次日涨跌方向'] == 1]
                
                if len(up_stocks) > 0:
                    win_rate = up_stocks['实际盈利'].mean() * 100
                    avg_return = up_stocks['实际收益率'].mean()
                    
                    print(f"推荐股票数: {len(strategy_stocks)}")
                    print(f"开盘时上涨的股票数: {len(up_stocks)}")
                    print(f"胜率: {win_rate:.2f}%")
                    print(f"平均收益率: {avg_return:.2f}%")
                    
                    # 添加到结果
                    results.append({
                        '日期': test_date,
                        '推荐股票数': len(strategy_stocks),
                        '开盘时上涨的股票数': len(up_stocks),
                        '胜率': win_rate,
                        '平均收益率': avg_return
                    })
                else:
                    print(f"推荐股票数: {len(strategy_stocks)}")
                    print("没有开盘时上涨的股票")
            else:
                print("没有推荐的股票")
        
        except Exception as e:
            print(f"回测失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 创建结果DataFrame
    if results:
        results_df = pd.DataFrame(results)
        
        # 计算整体表现
        overall_win_rate = results_df['胜率'].mean()
        overall_return = results_df['平均收益率'].mean()
        total_stocks = results_df['推荐股票数'].sum()
        total_up_stocks = results_df['开盘时上涨的股票数'].sum()
        
        print("\n整体表现:")
        print(f"总推荐股票数: {total_stocks}")
        print(f"总开盘时上涨的股票数: {total_up_stocks}")
        print(f"平均胜率: {overall_win_rate:.2f}%")
        print(f"平均收益率: {overall_return:.2f}%")
        
        # 保存结果
        if not os.path.exists('backtest_results'):
            os.makedirs('backtest_results')
        
        result_file = f'backtest_results/{strategy_name}_{start_date_str}至{end_date_str}.xlsx'
        results_df.to_excel(result_file, index=False)
        
        print(f"\n回测结果已保存至: {result_file}")
    else:
        print("\n没有回测结果")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='回测策略胜率')
    parser.add_argument('strategy', type=int, choices=[1, 2, 3, 4], 
                        help='选择策略: 1=策略1, 2=策略A, 3=策略B, 4=策略C')
    parser.add_argument('start_date', type=str, help='开始日期，格式为YYYY-MM-DD')
    parser.add_argument('end_date', type=str, help='结束日期，格式为YYYY-MM-DD')
    parser.add_argument('--data', type=str, default='股票明细.xlsx',
                        help='数据文件路径 (默认: 股票明细.xlsx)')
    
    args = parser.parse_args()
    
    clear_screen()
    
    strategy_map = {
        1: ('strategy_1', apply_strategy_1),
        2: ('strategy_A', apply_strategy_A),
        3: ('strategy_B', apply_strategy_B),
        4: ('strategy_C', apply_strategy_C)
    }
    
    strategy_name, strategy_func = strategy_map[args.strategy]
    
    backtest_strategy(strategy_name, strategy_func, args.start_date, args.end_date, args.data)

if __name__ == "__main__":
    main()
