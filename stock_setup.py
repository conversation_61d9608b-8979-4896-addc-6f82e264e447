#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
打包脚本
作者: Augment AI
版本: 1.0.0

该脚本用于将股票分析系统打包成可执行文件。
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def create_executable():
    """创建可执行文件"""
    print("开始打包股票分析系统...")
    
    # 创建打包目录
    dist_dir = "dist"
    build_dir = "build"
    
    # 清理旧的打包文件
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    
    # 创建spec文件
    spec_content = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['stock_analysis_app.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='股票分析系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico',
)
    """
    
    # 写入spec文件
    with open("stock_analysis.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    # 创建图标文件
    if not os.path.exists("icon.ico"):
        # 使用默认图标
        pass
    
    # 使用PyInstaller打包
    try:
        subprocess.run(["pyinstaller", "stock_analysis.spec"], check=True)
        print("打包成功！")
        print(f"可执行文件位于: {os.path.join(os.getcwd(), 'dist', '股票分析系统.exe')}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {str(e)}")
        return False
    except Exception as e:
        print(f"打包过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    create_executable()
