import pandas as pd
import os
import warnings
warnings.filterwarnings('ignore')

# 设置数据目录
base_dir = r'E:\机器学习\complete_excel_results'
details_file = os.path.join(base_dir, '股票明细_完整.xlsx')

print(f"读取股票明细文件: {details_file}")
stock_df = pd.read_excel(details_file)

# 将日期列转换为日期类型
stock_df['日期'] = pd.to_datetime(stock_df['日期'])

# 按股票代码和日期排序
stock_df = stock_df.sort_values(['股票代码', '日期'])

print(f"总记录数: {len(stock_df)}")

# 测试您提供的条件
print("\n测试条件: 技术强度 == 28 & 连续技术强度3天数 >= 85 & 连续技术强度5天数 >= 140")
filtered_df = stock_df[(stock_df['技术强度'] == 28) & 
                       (stock_df['连续技术强度3天数'] >= 85) & 
                       (stock_df['连续技术强度5天数'] >= 140)]

print(f"筛选结果记录数: {len(filtered_df)}")

if len(filtered_df) > 0:
    print(f"筛选结果示例 (前10行):")
    display_cols = ['股票代码', '股票名称', '日期', '技术强度', '连续技术强度3天数', '连续技术强度5天数']
    print(filtered_df[display_cols].head(10))
    
    # 检查这些记录的技术指标特征
    if '技术指标特征' in filtered_df.columns:
        # 确保以字符串形式比较
        filtered_df['技术指标特征'] = filtered_df['技术指标特征'].astype(str)
        
        # 统计技术指标特征的分布
        tech_indicator_counts = filtered_df['技术指标特征'].value_counts()
        print("\n技术指标特征分布:")
        print(tech_indicator_counts)
        
        # 检查是否有技术指标特征为111111的记录
        has_111111 = '111111' in filtered_df['技术指标特征'].values
        print(f"\n是否存在技术指标特征为111111的记录: {has_111111}")
        
        if has_111111:
            # 显示技术指标特征为111111的记录
            print("\n技术指标特征为111111的记录:")
            print(filtered_df[filtered_df['技术指标特征'] == '111111'][display_cols + ['技术指标特征']].head())
else:
    print("警告: 没有筛选出任何记录")

# 测试放宽条件
print("\n测试放宽条件: 技术强度 == 28 & 连续技术强度3天数 >= 85")
filtered_df2 = stock_df[(stock_df['技术强度'] == 28) & 
                        (stock_df['连续技术强度3天数'] >= 85)]

print(f"筛选结果记录数: {len(filtered_df2)}")

if len(filtered_df2) > 0:
    print(f"筛选结果示例 (前5行):")
    display_cols = ['股票代码', '股票名称', '日期', '技术强度', '连续技术强度3天数']
    print(filtered_df2[display_cols].head())
    
    # 检查这些记录的技术指标特征
    if '技术指标特征' in filtered_df2.columns:
        # 确保以字符串形式比较
        filtered_df2['技术指标特征'] = filtered_df2['技术指标特征'].astype(str)
        
        # 统计技术指标特征的分布
        tech_indicator_counts = filtered_df2['技术指标特征'].value_counts()
        print("\n技术指标特征分布:")
        print(tech_indicator_counts.head(10))
else:
    print("警告: 没有筛选出任何记录")

print("\n所有测试完成")
