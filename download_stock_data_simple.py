"""
使用Baostock API下载股票数据
按照官方文档的正确用法实现
简化版本，不使用多线程
"""

import pandas as pd
import baostock as bs
import os
import datetime
import time
import stock_data_manager as sdm

# 设置文件路径
base_dir = r'E:\机器学习\complete_excel_results'
stock_data_dir = os.path.join(base_dir, 'stock_data')
daily_data_dir = os.path.join(stock_data_dir, 'daily')
stock_details_file = os.path.join(base_dir, '股票明细_完整.xlsx')
history_data_file = os.path.join(stock_data_dir, 'stock_history_data.xlsx')

# 确保目录存在
if not os.path.exists(stock_data_dir):
    os.makedirs(stock_data_dir)
if not os.path.exists(daily_data_dir):
    os.makedirs(daily_data_dir)

def get_all_stock_codes():
    """获取所有股票代码"""
    try:
        # 尝试从股票明细文件中读取股票代码
        stock_df = pd.read_excel(stock_details_file)
        if '股票代码' in stock_df.columns:
            stock_codes = stock_df['股票代码'].tolist()
            print(f"从股票明细文件中读取到 {len(stock_codes)} 只股票")
            return stock_codes
    except Exception as e:
        print(f"读取股票明细文件时出错: {e}")
    
    # 如果无法从文件中读取，则获取上证和深证所有股票列表
    rs = bs.query_all_stock(day=datetime.datetime.now().strftime('%Y-%m-%d'))
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    stock_list_df = pd.DataFrame(data_list, columns=rs.fields)
    stock_codes = stock_list_df['code'].tolist()
    print(f"使用默认股票列表，共 {len(stock_codes)} 只股票")
    
    return stock_codes

def download_trading_calendar(start_date, end_date):
    """下载交易日历"""
    rs = bs.query_trade_dates(start_date=start_date, end_date=end_date)
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    calendar_df = pd.DataFrame(data_list, columns=rs.fields)
    
    # 转换日期列为日期类型
    calendar_df['calendar_date'] = pd.to_datetime(calendar_df['calendar_date'])
    
    # 转换交易日标志为整数
    calendar_df['is_trading_day'] = calendar_df['is_trading_day'].apply(lambda x: 1 if x == '1' else 0)
    
    return calendar_df

def download_stock_data_for_date(date_str):
    """
    下载指定日期的所有股票数据
    
    参数:
        date_str: 日期字符串，格式为YYYY-MM-DD
        
    返回:
        DataFrame: 包含所有股票在该日期的数据
    """
    print(f"开始下载日期 {date_str} 的股票数据")
    start_time = time.time()
    
    # 获取所有股票代码
    stock_codes = get_all_stock_codes()
    
    # 创建一个空的DataFrame来存储所有数据
    all_data = []
    success_count = 0
    error_count = 0
    
    # 遍历所有股票代码
    for i, code in enumerate(stock_codes):
        try:
            # 使用query_history_k_data_plus接口获取日K数据
            rs = bs.query_history_k_data_plus(
                code,
                "date,code,open,high,low,close,volume,amount,pctChg",
                start_date=date_str,
                end_date=date_str,
                frequency="d",
                adjustflag="3"  # 3表示前复权
            )
            
            # 检查API调用是否成功
            if rs.error_code != '0':
                error_count += 1
                continue
            
            # 处理数据
            data_list = []
            while rs.next():
                data_list.append(rs.get_row_data())
            
            # 如果有数据，添加到结果中
            if data_list:
                df = pd.DataFrame(data_list, columns=rs.fields)
                all_data.append(df)
                success_count += 1
        except Exception as e:
            error_count += 1
        
        # 每处理100只股票打印一次进度
        if (i + 1) % 100 == 0:
            print(f"已处理 {i+1}/{len(stock_codes)} 只股票，成功: {success_count}，失败: {error_count}")
    
    # 合并所有数据
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 转换数据类型
        for field in ['open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']:
            if field in combined_df.columns:
                combined_df[field] = pd.to_numeric(combined_df[field], errors='coerce')
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"日期 {date_str} 的股票数据下载完成，共 {len(combined_df)} 条记录，成功: {success_count}，失败: {error_count}，耗时: {elapsed_time:.2f}秒")
        
        return combined_df
    else:
        print(f"日期 {date_str} 没有下载到任何数据")
        return pd.DataFrame()

def download_date_range(start_date, end_date):
    """
    下载指定日期范围内的所有股票日K数据
    
    参数:
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD
    """
    print(f"开始下载从 {start_date} 到 {end_date} 的日K数据")
    
    # 登录baostock
    print("登录baostock...")
    lg = bs.login()
    print(f"登录状态: {lg.error_code}, {lg.error_msg}")
    
    try:
        # 下载交易日历
        calendar_df = download_trading_calendar(start_date, end_date)
        print(f"交易日历下载完成，共 {len(calendar_df)} 天")
        
        # 保存交易日历
        calendar_file = os.path.join(stock_data_dir, f'trading_calendar_{start_date.replace("-", "")}_{end_date.replace("-", "")}.xlsx')
        calendar_df.to_excel(calendar_file, index=False)
        print(f"交易日历已保存到 {calendar_file}")
        
        # 提取交易日列表
        trading_days = calendar_df[calendar_df['is_trading_day'] == 1]['calendar_date'].tolist()
        print(f"交易日历中包含 {len(trading_days)} 个交易日")
        
        # 按日期下载数据
        all_data = []
        total_records = 0
        
        for day in trading_days:
            day_str = day.strftime('%Y-%m-%d')
            
            # 检查该日期的数据文件是否已存在
            daily_file_path = sdm.get_daily_data_path(day_str)
            if os.path.exists(daily_file_path):
                print(f"日期 {day_str} 的数据文件已存在，跳过")
                
                # 读取已存在的数据，用于合并
                try:
                    day_df = sdm.load_daily_data(day_str)
                    if not day_df.empty:
                        all_data.append(day_df)
                        total_records += len(day_df)
                        print(f"已加载日期 {day_str} 的现有数据，共 {len(day_df)} 条记录")
                except Exception as e:
                    print(f"加载日期 {day_str} 的现有数据时出错: {e}")
                
                continue
            
            # 下载该日期的数据
            day_df = download_stock_data_for_date(day_str)
            
            # 保存该日期的数据
            if not day_df.empty:
                sdm.save_daily_data(day_df, day_str)
                print(f"日期 {day_str} 的数据已保存")
                
                all_data.append(day_df)
                total_records += len(day_df)
        
        # 是否生成合并文件
        if all_data:
            print("开始生成合并文件...")
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # 保存合并文件
            history_data_file = os.path.join(stock_data_dir, f'stock_history_data_{start_date.replace("-", "")}_{end_date.replace("-", "")}.xlsx')
            combined_df.to_excel(history_data_file, index=False)
            print(f"合并数据已保存到 {history_data_file}，共 {len(combined_df)} 条记录")
        
        print(f"下载完成，共处理 {len(trading_days)} 个交易日，总记录数: {total_records}")
    
    finally:
        # 登出baostock
        bs.logout()
        print("已登出baostock")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='下载股票日K数据')
    parser.add_argument('--start_date', type=str, default='2025-02-05', help='开始日期，格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, default='2025-02-05', help='结束日期，格式为YYYY-MM-DD')
    
    args = parser.parse_args()
    
    # 执行下载
    download_date_range(args.start_date, args.end_date)
