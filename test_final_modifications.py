#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修改后的回测程序
"""

import pandas as pd
import os
import sys

# 添加当前目录到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

def test_load_all_daily_data():
    """测试一次性加载所有按日期存储的数据"""
    print("=== 测试一次性加载所有按日期存储的数据 ===")
    
    # 手动实现加载逻辑进行测试
    import glob
    
    base_dir = r'E:\机器学习\complete_excel_results'
    tech_strength_daily_dir = os.path.join(base_dir, 'tech_strength', 'daily')
    
    print(f"检查目录: {tech_strength_daily_dir}")
    
    if not os.path.exists(tech_strength_daily_dir):
        print(f"目录不存在: {tech_strength_daily_dir}")
        return False
    
    # 查找所有技术强度文件
    pattern = os.path.join(tech_strength_daily_dir, "tech_strength_strong_*_smart.xlsx")
    tech_files = glob.glob(pattern)
    
    if not tech_files:
        print(f"在 {tech_strength_daily_dir} 中没有找到技术强度文件")
        return False
    
    print(f"找到 {len(tech_files)} 个技术强度文件")
    
    # 测试加载前几个文件
    test_files = tech_files[:3]  # 只测试前3个文件
    all_daily_data = {}
    available_dates = []
    total_records = 0
    
    for i, file_path in enumerate(test_files, 1):
        try:
            # 从文件名中提取日期
            filename = os.path.basename(file_path)
            date_part = filename.replace('tech_strength_strong_', '').replace('_smart.xlsx', '')
            date_obj = pd.to_datetime(date_part)
            
            print(f"  [{i}/{len(test_files)}] 测试加载 {date_part} 的数据...")
            
            # 读取文件
            df = pd.read_excel(file_path)
            df['日期'] = date_obj
            
            # 存储到字典中
            all_daily_data[date_obj] = df
            available_dates.append(date_obj)
            total_records += len(df)
            
            print(f"    成功加载 {len(df)} 条记录")
            
            # 显示前几行数据
            if len(df) > 0:
                print(f"    列名: {list(df.columns)}")
                display_columns = ['股票代码', '股票名称', '技术强度']
                available_columns = [col for col in display_columns if col in df.columns]
                if available_columns:
                    print(f"    前3行数据:")
                    print(df[available_columns].head(3).to_string(index=False))
            
        except Exception as e:
            print(f"    读取文件 {file_path} 时出错: {e}")
            continue
    
    if not all_daily_data:
        print("没有成功加载任何技术强度文件")
        return False
    
    available_dates.sort()
    print(f"\n测试加载完成！")
    print(f"  总文件数: {len(all_daily_data)}")
    print(f"  总记录数: {total_records:,}")
    print(f"  日期范围: {available_dates[0].strftime('%Y-%m-%d')} 到 {available_dates[-1].strftime('%Y-%m-%d')}")
    
    # 测试从预加载数据中获取指定日期的数据
    print(f"\n测试从预加载数据中获取指定日期的数据...")
    test_date = available_dates[0]
    if test_date in all_daily_data:
        test_data = all_daily_data[test_date].copy()
        print(f"成功获取 {test_date.strftime('%Y-%m-%d')} 的数据，共 {len(test_data)} 条记录")
    else:
        print(f"无法获取 {test_date.strftime('%Y-%m-%d')} 的数据")
    
    return True

def main():
    """主测试函数"""
    print("开始测试最终修改后的回测程序...")
    
    # 测试一次性加载所有按日期存储的数据
    success = test_load_all_daily_data()
    
    if success:
        print("\n✓ 测试通过")
        print("\n最终修改说明:")
        print("1. 程序启动时会一次性加载所有按日期存储的数据到内存中")
        print("2. 数据存储在 all_daily_data 字典中，键为日期，值为对应的DataFrame")
        print("3. 使用 get_daily_stock_data() 函数从预加载的数据中快速获取指定日期的数据")
        print("4. 这样既避免了Excel 100万行限制，又提高了查询速度")
        print("5. 如果没有找到按日期存储的文件，程序会自动回退到原始的单文件模式")
    else:
        print("\n✗ 测试失败")
    
    print("\n测试完成")

if __name__ == "__main__":
    main()
