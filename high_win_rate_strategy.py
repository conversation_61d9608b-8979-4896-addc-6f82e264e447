import pandas as pd
import numpy as np
import os
import joblib
from datetime import datetime, timedelta
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

def analyze_high_win_rate_strategy():
    """
    分析高胜率组合策略的效果

    组合策略包括：
    1. 机器学习模型预测盈利概率>70%
    2. 优先选择技术强度=100且连续2天及以上的股票
    3. 只买入开盘时上涨的股票
    4. 要求同时具有至少2个看涨技术指标
    5. 只在大盘5日均线向上时交易
    6. 优先选择近5日涨幅排名前3的行业中的股票
    7. 要求成交量较前一日有所增加
    """
    print("开始分析高胜率组合策略...")

    # 创建结果目录（使用新目录名以区分新的测试结果）
    if not os.path.exists('high_win_rate_results_extended'):
        os.makedirs('high_win_rate_results_extended')

    # 加载股票数据（包括新的历史数据）
    print("\n加载股票数据...")
    try:
        # 尝试加载新的历史数据文件
        try:
            stock_data = pd.read_excel('股票历史数据.xlsx')
            print("成功加载新的历史数据文件")
        except:
            # 如果新文件不存在，则加载原始文件
            stock_data = pd.read_excel('股票明细.xlsx')
            print("加载原始数据文件")
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")

        # 转换日期格式
        if isinstance(stock_data['日期'].iloc[0], str):
            stock_data['日期'] = pd.to_datetime(stock_data['日期'])

        # 获取所有日期并排序
        all_dates = sorted(stock_data['日期'].unique())
        print(f"数据集中的日期范围: {all_dates[0]} 到 {all_dates[-1]}")

        # 按股票代码和日期排序
        stock_data = stock_data.sort_values(['股票代码', '日期'])

        # 数据预处理
        print("\n数据预处理...")

        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 计算连续技术强度天数（连续多少天为100）
            consecutive_days = []
            current_count = 0

            for strength in group['技术强度'].values:
                if strength == 100:
                    current_count += 1
                else:
                    current_count = 0
                consecutive_days.append(current_count)

            # 更新原始数据
            stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days

            # 计算技术强度累积值（5天）
            cumulative_strength = group['技术强度'].copy()
            for i in range(1, 5):
                cumulative_strength += group['技术强度'].shift(i).fillna(0)

            # 更新原始数据
            stock_data.loc[group.index, '连续技术强度5天数'] = cumulative_strength

            # 计算趋势特征
            stock_data.loc[group.index, '技术强度趋势'] = (
                (group['技术强度'] > group['技术强度'].shift(1)) &
                (group['技术强度'].shift(1) > group['技术强度'].shift(2))
            ).astype(int)

            stock_data.loc[group.index, '价格趋势'] = (
                (group['当前价格'] > group['当前价格'].shift(1)) &
                (group['当前价格'].shift(1) > group['当前价格'].shift(2))
            ).astype(int)

            if '涨跌幅' in group.columns:
                stock_data.loc[group.index, '涨跌幅趋势'] = (
                    (group['涨跌幅'] > group['涨跌幅'].shift(1)) &
                    (group['涨跌幅'].shift(1) > group['涨跌幅'].shift(2))
                ).astype(int)

            # 计算成交量变化率（如果有成交量列）
            if '成交量' in group.columns:
                stock_data.loc[group.index, '成交量变化率'] = group['成交量'] / group['成交量'].shift(1).replace(0, 1) - 1

            # 计算次日涨跌方向和收益率
            group['次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)
            group['次日收益率'] = (group['当前价格'].shift(-1) / group['当前价格'] - 1) * 100

            # 计算后日涨跌方向和收益率（买入后的第二天，即卖出日）
            group['后日涨跌方向'] = (group['当前价格'].shift(-2) > group['当前价格'].shift(-1)).astype(int)
            group['后日收益率'] = (group['当前价格'].shift(-2) / group['当前价格'].shift(-1) - 1) * 100

            # 计算两日收益率（买入后持有两天的总收益）
            group['两日收益率'] = (group['当前价格'].shift(-2) / group['当前价格'] - 1) * 100

            # 计算是否盈利（两日收益率为正）
            group['是否盈利'] = (group['两日收益率'] > 0).astype(int)

            # 更新原始数据
            stock_data.loc[group.index, '次日涨跌方向'] = group['次日涨跌方向']
            stock_data.loc[group.index, '次日收益率'] = group['次日收益率']
            stock_data.loc[group.index, '后日涨跌方向'] = group['后日涨跌方向']
            stock_data.loc[group.index, '后日收益率'] = group['后日收益率']
            stock_data.loc[group.index, '两日收益率'] = group['两日收益率']
            stock_data.loc[group.index, '是否盈利'] = group['是否盈利']

        # 处理技术指标特征
        if '技术指标' in stock_data.columns:
            # 提取常见的技术指标关键词
            tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
                              '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']

            # 为每个技术指标创建一个新列
            for indicator in tech_indicators:
                col_name = f'技术指标_{indicator}'
                # 检查技术指标文本中是否包含该关键词
                stock_data[col_name] = stock_data['技术指标'].str.contains(indicator, na=False).astype(int)

            # 计算看涨技术指标数量
            bullish_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破']
            stock_data['看涨技术指标数量'] = 0
            for indicator in bullish_indicators:
                stock_data['看涨技术指标数量'] += stock_data[f'技术指标_{indicator}']

        # 计算行业5日涨幅（如果有行业列）
        if '行业' in stock_data.columns:
            # 按日期和行业分组，计算平均涨跌幅
            industry_perf = stock_data.groupby(['日期', '行业'])['涨跌幅'].mean().reset_index()

            # 计算行业5日涨幅
            for date in all_dates:
                # 获取过去5个交易日
                past_dates = [d for d in all_dates if d < date][-5:]
                if len(past_dates) < 5:
                    continue

                # 计算每个行业在过去5个交易日的累计涨幅
                industry_5d_perf = {}
                for industry in industry_perf['行业'].unique():
                    industry_data = industry_perf[(industry_perf['行业'] == industry) & (industry_perf['日期'].isin(past_dates))]
                    if len(industry_data) == 5:
                        cumulative_return = (1 + industry_data['涨跌幅'] / 100).prod() - 1
                        industry_5d_perf[industry] = cumulative_return * 100

                # 对行业按5日涨幅排序
                sorted_industries = sorted(industry_5d_perf.items(), key=lambda x: x[1], reverse=True)
                top3_industries = [ind[0] for ind in sorted_industries[:3]]

                # 标记当日属于前3行业的股票
                stock_data.loc[(stock_data['日期'] == date) & (stock_data['行业'].isin(top3_industries)), '行业排名前3'] = 1
                stock_data.loc[(stock_data['日期'] == date) & (~stock_data['行业'].isin(top3_industries)), '行业排名前3'] = 0

        # 计算大盘5日均线方向（如果有大盘指数数据）
        # 这里假设没有大盘数据，所以我们使用所有股票的平均涨跌幅作为大盘指标
        market_data = stock_data.groupby('日期')['涨跌幅'].mean().reset_index()
        market_data['5日均线'] = market_data['涨跌幅'].rolling(5).mean()
        market_data['5日均线方向'] = (market_data['5日均线'] > market_data['5日均线'].shift(1)).astype(int)

        # 将大盘5日均线方向合并到原始数据
        stock_data = pd.merge(stock_data, market_data[['日期', '5日均线方向']], on='日期', how='left')

        # 删除没有完整数据的记录
        stock_data = stock_data.dropna(subset=['次日涨跌方向', '次日收益率', '后日涨跌方向', '后日收益率', '两日收益率', '是否盈利'])

        print(f"处理后的数据集大小: {len(stock_data)} 条记录")

        # 创建回测结果DataFrame
        backtest_results = pd.DataFrame(columns=[
            '日期', '推荐股票数', '高胜率策略股票数',
            '所有推荐股票胜率', '高胜率策略股票胜率',
            '所有推荐股票平均收益率', '高胜率策略股票平均收益率'
        ])

        # 提取特征
        features = [
            '技术强度', '连续技术强度5天数',
            '技术强度趋势', '价格趋势', '涨跌幅趋势',
            '涨跌幅'
        ]

        # 添加技术指标特征
        if '技术指标' in stock_data.columns:
            for indicator in tech_indicators:
                features.append(f'技术指标_{indicator}')

        # 回测每个日期
        for i in range(len(all_dates) - 2):  # 需要有后两天的数据
            current_date = all_dates[i]

            print(f"\n回测日期: {current_date}")

            # 获取当前日期的数据
            current_data = stock_data[stock_data['日期'] == current_date]

            # 准备训练数据（使用当前日期之前的所有数据）
            train_data = stock_data[stock_data['日期'] < current_date]

            if len(train_data) < 1000:  # 确保有足够的训练数据
                print(f"训练数据不足，跳过日期 {current_date}")
                continue

            # 特征和目标变量
            X_train = train_data[features]
            y_train = train_data['是否盈利']

            # 处理缺失值
            valid_indices = ~X_train.isnull().any(axis=1)
            X_train = X_train[valid_indices]
            y_train = y_train[valid_indices]

            # 标准化特征
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)

            # 训练梯度提升模型
            gb_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
            gb_model.fit(X_train_scaled, y_train)

            # 准备预测数据
            X_pred = current_data[features]

            # 处理预测数据中的缺失值
            valid_pred_indices = ~X_pred.isnull().any(axis=1)
            X_pred = X_pred[valid_pred_indices]
            current_data_filtered = current_data.loc[valid_pred_indices.index[valid_pred_indices]]

            if len(X_pred) == 0:
                print(f"预测数据不足，跳过日期 {current_date}")
                continue

            # 标准化预测数据
            X_pred_scaled = scaler.transform(X_pred)

            # 预测盈利概率
            pred_proba = gb_model.predict_proba(X_pred_scaled)[:, 1]

            # 创建预测结果DataFrame
            predictions = pd.DataFrame({
                '股票代码': current_data_filtered['股票代码'],
                '股票名称': current_data_filtered['股票名称'],
                '涨跌幅': current_data_filtered['涨跌幅'],
                '技术强度': current_data_filtered['技术强度'],
                '连续技术强度天数': current_data_filtered['连续技术强度天数'],
                '连续技术强度5天数': current_data_filtered['连续技术强度5天数'],
                '看涨技术指标数量': current_data_filtered['看涨技术指标数量'] if '看涨技术指标数量' in current_data_filtered.columns else 0,
                '成交量变化率': current_data_filtered['成交量变化率'] if '成交量变化率' in current_data_filtered.columns else 0,
                '行业排名前3': current_data_filtered['行业排名前3'] if '行业排名前3' in current_data_filtered.columns else 0,
                '5日均线方向': current_data_filtered['5日均线方向'],
                '预测盈利概率': pred_proba,
                '次日涨跌方向': current_data_filtered['次日涨跌方向'],
                '次日收益率': current_data_filtered['次日收益率'],
                '实际是否盈利': current_data_filtered['是否盈利'],
                '两日收益率': current_data_filtered['两日收益率']
            })

            # 按预测盈利概率降序排序
            predictions = predictions.sort_values('预测盈利概率', ascending=False)

            # 选择预测盈利概率最高的前10%股票作为推荐
            top_percent = 0.1
            top_n = max(int(len(predictions) * top_percent), 10)  # 至少10只股票
            recommended_stocks = predictions.head(top_n)

            # 应用我们发现的高胜率组合策略（提高预测盈利概率阈值）
            high_win_rate_stocks = recommended_stocks[
                (recommended_stocks['预测盈利概率'] > 0.78) &  # 条件1: 预测盈利概率>78%（提高阈值）
                (recommended_stocks['技术强度'] >= 70) &  # 条件2: 技术强度>=70
                (recommended_stocks['次日涨跌方向'] == 1)  # 条件3: 只买入开盘时上涨的股票
            ]

            # 如果有看涨技术指标数据，进一步筛选
            if '看涨技术指标数量' in recommended_stocks.columns:
                high_win_rate_stocks = high_win_rate_stocks[
                    (high_win_rate_stocks['看涨技术指标数量'] >= 1)  # 条件4: 至少1个看涨技术指标
                ]

            # 筛选连续技术强度5天累积值>=400的股票
            high_win_rate_stocks = high_win_rate_stocks[
                (high_win_rate_stocks['连续技术强度5天数'] >= 400)  # 条件5: 5天累积值>=400
            ]

            # 计算各类股票的胜率和平均收益率
            total_count = len(recommended_stocks)
            high_win_rate_count = len(high_win_rate_stocks)

            total_win_count = sum(recommended_stocks['实际是否盈利'])
            high_win_rate_win_count = sum(high_win_rate_stocks['实际是否盈利']) if high_win_rate_count > 0 else 0

            total_win_rate = total_win_count / total_count if total_count > 0 else 0
            high_win_rate_win_rate = high_win_rate_win_count / high_win_rate_count if high_win_rate_count > 0 else 0

            total_avg_return = recommended_stocks['两日收益率'].mean()
            high_win_rate_avg_return = high_win_rate_stocks['两日收益率'].mean() if high_win_rate_count > 0 else 0

            print(f"推荐股票数: {total_count}")
            print(f"高胜率策略股票数: {high_win_rate_count}")
            print(f"所有推荐股票胜率: {total_win_rate:.4f} ({total_win_rate*100:.2f}%)")
            if high_win_rate_count > 0:
                print(f"高胜率策略股票胜率: {high_win_rate_win_rate:.4f} ({high_win_rate_win_rate*100:.2f}%)")
            print(f"所有推荐股票平均收益率: {total_avg_return:.4f}%")
            if high_win_rate_count > 0:
                print(f"高胜率策略股票平均收益率: {high_win_rate_avg_return:.4f}%")

            # 添加到回测结果
            new_row = pd.DataFrame({
                '日期': [current_date],
                '推荐股票数': [total_count],
                '高胜率策略股票数': [high_win_rate_count],
                '所有推荐股票胜率': [total_win_rate],
                '高胜率策略股票胜率': [high_win_rate_win_rate if high_win_rate_count > 0 else np.nan],
                '所有推荐股票平均收益率': [total_avg_return],
                '高胜率策略股票平均收益率': [high_win_rate_avg_return if high_win_rate_count > 0 else np.nan]
            })
            backtest_results = pd.concat([backtest_results, new_row], ignore_index=True)

            # 保存当日推荐股票
            recommended_stocks.to_excel(f'high_win_rate_results_extended/{current_date.strftime("%Y-%m-%d")}_推荐股票.xlsx', index=False)
            if high_win_rate_count > 0:
                high_win_rate_stocks.to_excel(f'high_win_rate_results_extended/{current_date.strftime("%Y-%m-%d")}_高胜率策略股票.xlsx', index=False)

        # 保存回测结果
        backtest_results.to_excel('high_win_rate_results_extended/回测结果.xlsx', index=False)

        # 计算整体统计
        total_recommended = backtest_results['推荐股票数'].sum()
        total_high_win_rate = backtest_results['高胜率策略股票数'].sum()

        # 计算加权平均胜率
        overall_win_rate = (backtest_results['推荐股票数'] * backtest_results['所有推荐股票胜率']).sum() / total_recommended

        valid_high_win_rate = backtest_results.dropna(subset=['高胜率策略股票胜率'])
        if len(valid_high_win_rate) > 0:
            high_win_rate_win_rate = (valid_high_win_rate['高胜率策略股票数'] * valid_high_win_rate['高胜率策略股票胜率']).sum() / valid_high_win_rate['高胜率策略股票数'].sum()
        else:
            high_win_rate_win_rate = np.nan

        # 计算平均收益率
        overall_avg_return = backtest_results['所有推荐股票平均收益率'].mean()

        valid_high_win_rate_return = backtest_results.dropna(subset=['高胜率策略股票平均收益率'])
        if len(valid_high_win_rate_return) > 0:
            high_win_rate_avg_return = valid_high_win_rate_return['高胜率策略股票平均收益率'].mean()
        else:
            high_win_rate_avg_return = np.nan

        print("\n整体统计:")
        print(f"总推荐股票数: {total_recommended}")
        print(f"高胜率策略股票数: {total_high_win_rate} ({total_high_win_rate/total_recommended*100:.2f}%)")
        print(f"所有推荐股票整体胜率: {overall_win_rate:.4f} ({overall_win_rate*100:.2f}%)")
        if not np.isnan(high_win_rate_win_rate):
            print(f"高胜率策略股票整体胜率: {high_win_rate_win_rate:.4f} ({high_win_rate_win_rate*100:.2f}%)")
        print(f"所有推荐股票平均收益率: {overall_avg_return:.4f}%")
        if not np.isnan(high_win_rate_avg_return):
            print(f"高胜率策略股票平均收益率: {high_win_rate_avg_return:.4f}%")

        # 绘制胜率比较图表
        plt.figure(figsize=(12, 6))
        plt.plot(backtest_results['日期'], backtest_results['所有推荐股票胜率'], marker='o', linestyle='-', label='所有推荐股票')
        plt.plot(valid_high_win_rate['日期'], valid_high_win_rate['高胜率策略股票胜率'], marker='x', linestyle='--', color='green', label='高胜率策略股票')
        plt.title('高胜率策略与所有推荐股票的胜率比较')
        plt.xlabel('日期')
        plt.ylabel('胜率')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('high_win_rate_results_extended/胜率比较.png')

        # 绘制收益率比较图表
        plt.figure(figsize=(12, 6))
        plt.plot(backtest_results['日期'], backtest_results['所有推荐股票平均收益率'], marker='o', linestyle='-', label='所有推荐股票')
        plt.plot(valid_high_win_rate_return['日期'], valid_high_win_rate_return['高胜率策略股票平均收益率'], marker='x', linestyle='--', color='green', label='高胜率策略股票')
        plt.title('高胜率策略与所有推荐股票的平均收益率比较')
        plt.xlabel('日期')
        plt.ylabel('平均收益率 (%)')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('high_win_rate_results_extended/收益率比较.png')

        print("\n分析完成！结果已保存至 high_win_rate_results_extended 目录")

        return backtest_results

    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    analyze_high_win_rate_strategy()
