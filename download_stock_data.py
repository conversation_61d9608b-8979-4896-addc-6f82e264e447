import pandas as pd
import baostock as bs
import os
import datetime
import time
from tqdm import tqdm
import stock_data_manager as sdm

# 设置文件路径
base_dir = r'E:\机器学习\complete_excel_results'
stock_data_dir = os.path.join(base_dir, 'stock_data')
daily_data_dir = os.path.join(stock_data_dir, 'daily')
stock_details_file = os.path.join(base_dir, '股票明细_完整.xlsx')
history_data_file = os.path.join(stock_data_dir, 'stock_history_data.xlsx')

# 确保目录存在
if not os.path.exists(stock_data_dir):
    os.makedirs(stock_data_dir)
if not os.path.exists(daily_data_dir):
    os.makedirs(daily_data_dir)

def get_all_stock_codes():
    """从股票明细文件中获取所有股票代码"""
    try:
        df = pd.read_excel(stock_details_file)
        return df['股票代码'].unique().tolist()
    except Exception as e:
        print(f"读取股票明细文件时出错: {e}")
        return []

def get_date_range():
    """获取日期范围"""
    try:
        df = pd.read_excel(stock_details_file)
        min_date = df['日期'].min()
        max_date = df['日期'].max()

        # 向前和向后各扩展30天，确保覆盖所有可能的交易日
        start_date = (min_date - datetime.timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = (max_date + datetime.timedelta(days=30)).strftime('%Y-%m-%d')

        return start_date, end_date
    except Exception as e:
        print(f"获取日期范围时出错: {e}")
        # 默认使用最近一年的数据
        end_date = datetime.datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.datetime.now() - datetime.timedelta(days=365)).strftime('%Y-%m-%d')
        return start_date, end_date

def download_trading_calendar(start_date, end_date):
    """下载交易日历"""
    print("下载交易日历...")
    rs = bs.query_trade_dates(start_date=start_date, end_date=end_date)
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())

    calendar_df = pd.DataFrame(data_list, columns=rs.fields)
    calendar_df['is_trading_day'] = calendar_df['is_trading_day'].astype(int)

    return calendar_df

def download_stock_data(stock_code, start_date, end_date):
    """下载单只股票的历史数据"""
    try:
        rs = bs.query_history_k_data_plus(
            stock_code,
            "date,code,open,high,low,close,volume,amount,pctChg",
            start_date=start_date,
            end_date=end_date,
            frequency="d",
            adjustflag="3"  # 3表示前复权
        )

        # 检查API调用是否成功
        if rs.error_code != '0':
            print(f"API调用失败: {rs.error_code}, {rs.error_msg}")
            return None

        data_list = []
        while rs.next():
            try:
                row_data = rs.get_row_data()
                if row_data and len(row_data) > 0:
                    data_list.append(row_data)
            except Exception as e:
                print(f"处理行数据时出错: {e}")

        if data_list:
            try:
                df = pd.DataFrame(data_list, columns=rs.fields)
                # 转换数据类型
                for field in ['open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']:
                    if field in df.columns:
                        df[field] = pd.to_numeric(df[field], errors='coerce')
                return df
            except Exception as e:
                print(f"创建DataFrame时出错: {e}")
                return None
        else:
            # 没有数据，可能是非交易日或股票停牌
            return pd.DataFrame(columns=rs.fields)

    except Exception as e:
        print(f"下载股票 {stock_code} 数据时出错: {e}")
        return None

def main():
    # 登录baostock
    print("登录baostock...")
    bs_login = bs.login()
    print(f"登录状态: {bs_login.error_code}, {bs_login.error_msg}")

    try:
        # 获取所有股票代码
        stock_codes = get_all_stock_codes()
        print(f"共找到 {len(stock_codes)} 只股票")

        # 获取日期范围
        start_date, end_date = get_date_range()
        print(f"日期范围: {start_date} 到 {end_date}")

        # 下载交易日历
        calendar_df = download_trading_calendar(start_date, end_date)
        print(f"交易日历下载完成，共 {len(calendar_df)} 天")

        # 保存交易日历
        calendar_file = os.path.join(stock_data_dir, 'trading_calendar.xlsx')
        calendar_df.to_excel(calendar_file, index=False)
        print(f"交易日历已保存到 {calendar_file}")

        # 提取交易日列表
        trading_days = calendar_df[calendar_df['is_trading_day'] == 1]['calendar_date'].tolist()
        print(f"交易日历中包含 {len(trading_days)} 个交易日")

        # 按日期下载和保存股票数据
        for trading_day in tqdm(trading_days, desc="按日期下载数据"):
            day_str = trading_day.strftime('%Y-%m-%d')

            # 检查该日期的数据文件是否已存在
            daily_file_path = sdm.get_daily_data_path(day_str)
            if os.path.exists(daily_file_path):
                print(f"日期 {day_str} 的数据文件已存在，跳过")
                continue

            # 检查是否为交易日
            is_trading_day = True
            if 'is_trading_day' in calendar_df.columns:
                day_trading_status = calendar_df[calendar_df['calendar_date'] == pd.to_datetime(day_str)]
                if not day_trading_status.empty and day_trading_status['is_trading_day'].iloc[0] == 0:
                    is_trading_day = False
                    print(f"日期 {day_str} 不是交易日，跳过")
                    continue

            # 下载该日期的所有股票数据
            day_stock_data = []
            error_count = 0
            success_count = 0
            total_stocks = len(stock_codes)

            for i, stock_code in enumerate(stock_codes):
                try:
                    # 下载单只股票单日数据
                    stock_data = download_stock_data(stock_code, day_str, day_str)
                    if stock_data is not None and not stock_data.empty:
                        day_stock_data.append(stock_data)
                        success_count += 1

                    # 每下载100只股票显示一次进度，不暂停
                    if (i + 1) % 100 == 0:
                        print(f"日期 {day_str} - 已处理 {i+1}/{total_stocks} 只股票，成功: {success_count}，失败: {error_count}")
                except Exception as e:
                    error_count += 1
                    error_msg = str(e)
                    # 只记录不同类型的错误，避免日志过于冗长
                    if "list index out of range" in error_msg:
                        if error_count <= 5 or error_count % 100 == 0:
                            print(f"下载股票 {stock_code} 在日期 {day_str} 的数据时出错: {error_msg}")
                    else:
                        print(f"下载股票 {stock_code} 在日期 {day_str} 的数据时出错: {error_msg}")

            # 合并并保存该日期的所有股票数据
            if day_stock_data:
                day_combined_df = pd.concat(day_stock_data, ignore_index=True)
                print(f"日期 {day_str} 的股票数据下载完成，共 {len(day_combined_df)} 条记录，成功: {success_count}，失败: {error_count}")

                # 保存到按日期存储的文件
                sdm.save_daily_data(day_combined_df, day_str)
                print(f"日期 {day_str} 的股票数据已保存")
            else:
                print(f"日期 {day_str} 没有下载到任何股票数据，可能是非交易日或所有股票都无数据")

        # 是否同时生成合并文件
        create_combined_file = True
        if create_combined_file:
            print("开始生成合并文件...")
            sdm.merge_daily_data_to_combined(history_data_file)

    finally:
        # 登出baostock
        bs.logout()
        print("已登出baostock")

if __name__ == "__main__":
    main()
