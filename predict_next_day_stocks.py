import pandas as pd
import numpy as np
import os
import joblib
from datetime import datetime, timedelta
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler

def predict_next_day_stocks(prediction_date_str):
    """
    使用指定日期的数据预测下一个交易日应该买入的股票

    参数:
    prediction_date_str: 预测基准日期，格式为'YYYY-MM-DD'

    返回:
    推荐买入的股票列表
    """
    print(f"开始预测{prediction_date_str}后的下一个交易日应该买入的股票...")

    # 创建结果目录
    if not os.path.exists('prediction_results'):
        os.makedirs('prediction_results')

    # 加载股票数据
    print("\n加载股票数据...")
    try:
        stock_data = pd.read_excel('股票明细.xlsx')
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")

        # 转换日期格式
        if isinstance(stock_data['日期'].iloc[0], str):
            stock_data['日期'] = pd.to_datetime(stock_data['日期'])

        # 获取所有日期并排序
        all_dates = sorted(stock_data['日期'].unique())
        print(f"数据集中的日期范围: {all_dates[0]} 到 {all_dates[-1]}")

        # 将预测日期转换为datetime
        prediction_date = pd.to_datetime(prediction_date_str)

        # 检查预测日期是否在数据集中
        if prediction_date not in all_dates:
            print(f"错误: 预测日期 {prediction_date_str} 不在数据集中")
            return None

        # 按股票代码和日期排序
        stock_data = stock_data.sort_values(['股票代码', '日期'])

        # 数据预处理
        print("\n数据预处理...")

        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 计算连续技术强度天数（连续多少天为100）
            consecutive_days = []
            current_count = 0

            for strength in group['技术强度'].values:
                if strength == 100:
                    current_count += 1
                else:
                    current_count = 0
                consecutive_days.append(current_count)

            # 更新原始数据
            stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days

            # 计算技术强度累积值（5天）
            cumulative_strength = group['技术强度'].copy()
            for i in range(1, 5):
                cumulative_strength += group['技术强度'].shift(i).fillna(0)

            # 更新原始数据
            stock_data.loc[group.index, '连续技术强度5天数'] = cumulative_strength

            # 计算趋势特征
            stock_data.loc[group.index, '技术强度趋势'] = (
                (group['技术强度'] > group['技术强度'].shift(1)) &
                (group['技术强度'].shift(1) > group['技术强度'].shift(2))
            ).astype(int)

            stock_data.loc[group.index, '价格趋势'] = (
                (group['当前价格'] > group['当前价格'].shift(1)) &
                (group['当前价格'].shift(1) > group['当前价格'].shift(2))
            ).astype(int)

            if '涨跌幅' in group.columns:
                stock_data.loc[group.index, '涨跌幅趋势'] = (
                    (group['涨跌幅'] > group['涨跌幅'].shift(1)) &
                    (group['涨跌幅'].shift(1) > group['涨跌幅'].shift(2))
                ).astype(int)

        # 处理技术指标特征
        if '技术指标' in stock_data.columns:
            # 提取常见的技术指标关键词
            tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
                              '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']

            # 为每个技术指标创建一个新列
            for indicator in tech_indicators:
                col_name = f'技术指标_{indicator}'
                # 检查技术指标文本中是否包含该关键词
                stock_data[col_name] = stock_data['技术指标'].str.contains(indicator, na=False).astype(int)

            # 计算看涨技术指标数量
            bullish_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破']
            stock_data['看涨技术指标数量'] = 0
            for indicator in bullish_indicators:
                stock_data['看涨技术指标数量'] += stock_data[f'技术指标_{indicator}']

        # 提取特征
        features = [
            '技术强度', '连续技术强度5天数',
            '技术强度趋势', '价格趋势', '涨跌幅趋势',
            '涨跌幅'
        ]

        # 添加技术指标特征
        if '技术指标' in stock_data.columns:
            for indicator in tech_indicators:
                features.append(f'技术指标_{indicator}')

        # 获取预测日期的数据
        prediction_date_data = stock_data[stock_data['日期'] == prediction_date]

        # 准备训练数据（使用预测日期之前的所有数据）
        train_data = stock_data[stock_data['日期'] < prediction_date]

        if len(train_data) < 1000:  # 确保有足够的训练数据
            print(f"训练数据不足，无法进行预测")
            return None

        # 特征和目标变量
        X_train = train_data[features]

        # 计算目标变量（是否盈利）- 使用与高胜率策略一致的方法
        print("注意: 计算两日收益率作为盈利指标")

        # 按股票代码分组计算两日收益率和是否盈利
        train_data['两日收益率'] = np.nan
        train_data['是否盈利'] = np.nan

        for code, group in train_data.groupby('股票代码'):
            group = group.sort_values('日期')

            # 计算两日后的价格（买入后第二天卖出）
            group['两日后价格'] = group['当前价格'].shift(-2)

            # 计算两日收益率
            group['两日收益率'] = (group['两日后价格'] / group['当前价格'] - 1) * 100

            # 计算是否盈利（两日收益率>0）
            group['是否盈利'] = (group['两日收益率'] > 0).astype(int)

            # 更新原始数据
            train_data.loc[group.index, '两日收益率'] = group['两日收益率']
            train_data.loc[group.index, '是否盈利'] = group['是否盈利']

        # 删除没有完整数据的记录
        train_data = train_data.dropna(subset=['是否盈利'])

        # 使用是否盈利作为目标变量
        y_train = train_data['是否盈利']

        # 处理缺失值
        valid_indices = ~X_train.isnull().any(axis=1)
        X_train = X_train[valid_indices]
        y_train = y_train[valid_indices]

        # 标准化特征
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)

        # 训练梯度提升模型
        gb_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
        gb_model.fit(X_train_scaled, y_train)

        # 准备预测数据
        X_pred = prediction_date_data[features]

        # 处理预测数据中的缺失值
        valid_pred_indices = ~X_pred.isnull().any(axis=1)
        X_pred = X_pred[valid_pred_indices]
        prediction_date_data_filtered = prediction_date_data.loc[valid_pred_indices.index[valid_pred_indices]]

        if len(X_pred) == 0:
            print(f"预测数据不足，无法进行预测")
            return None

        # 标准化预测数据
        X_pred_scaled = scaler.transform(X_pred)

        # 预测盈利概率
        pred_proba = gb_model.predict_proba(X_pred_scaled)[:, 1]

        # 创建预测结果DataFrame
        predictions = pd.DataFrame({
            '股票代码': prediction_date_data_filtered['股票代码'],
            '股票名称': prediction_date_data_filtered['股票名称'],
            '涨跌幅': prediction_date_data_filtered['涨跌幅'],
            '技术强度': prediction_date_data_filtered['技术强度'],
            '连续技术强度天数': prediction_date_data_filtered['连续技术强度天数'],
            '连续技术强度5天数': prediction_date_data_filtered['连续技术强度5天数'],
            '看涨技术指标数量': prediction_date_data_filtered['看涨技术指标数量'] if '看涨技术指标数量' in prediction_date_data_filtered.columns else 0,
            '预测盈利概率': pred_proba
        })

        # 按预测盈利概率降序排序
        predictions = predictions.sort_values('预测盈利概率', ascending=False)

        # 选择预测盈利概率最高的前10%股票作为推荐
        top_percent = 0.1
        top_n = max(int(len(predictions) * top_percent), 10)  # 至少10只股票
        recommended_stocks = predictions.head(top_n)

        # 查看预测盈利概率的分布
        prob_distribution = recommended_stocks['预测盈利概率'].describe()
        print("\n预测盈利概率分布:")
        print(prob_distribution)

        # 找出预测盈利概率最高的10只股票
        top_prob_stocks = recommended_stocks.sort_values('预测盈利概率', ascending=False).head(10)
        print("\n预测盈利概率最高的10只股票:")
        for i, row in top_prob_stocks.iterrows():
            print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")

        # 应用策略1（100%高胜率组合策略）
        strategy1_stocks = recommended_stocks[
            (recommended_stocks['预测盈利概率'] > 0.78) &  # 条件1: 预测盈利概率>78%
            (recommended_stocks['技术强度'] >= 70) &  # 条件2: 技术强度>=70
            (recommended_stocks['连续技术强度5天数'] >= 400)  # 条件3: 5天累积值>=400
        ]

        print(f"\n策略1（100%高胜率策略）找到 {len(strategy1_stocks)} 只符合条件的股票")

        if len(strategy1_stocks) > 0:
            print("\n策略1推荐买入的股票:")
            print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持100%胜率的关键条件！")
            print("【交易策略】: 买入后在第二个交易日开盘时卖出")
            print("【筛选条件】: 预测盈利概率>78%, 技术强度≥70, 连续技术强度5天数≥400, 开盘时上涨")
            print("\n股票列表:")
            for i, row in strategy1_stocks.iterrows():
                print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")

            # 使用策略1的股票作为高胜率策略股票
            high_win_rate_stocks = strategy1_stocks
        else:
            # 如果策略1没有找到股票，应用策略2（高胜率备选策略）
            print("\n策略1没有找到符合条件的股票，尝试策略2（高胜率备选策略）:")

            # 应用策略2的条件（进一步放宽）
            strategy2_candidates = recommended_stocks[
                (recommended_stocks['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
                (recommended_stocks['技术强度'] >= 60)  # 条件2: 技术强度≥60
                # 移除连续技术强度5天数的条件
            ]

            # 如果仍然没有符合条件的股票，进一步放宽条件
            if len(strategy2_candidates) == 0:
                strategy2_candidates = recommended_stocks[
                    (recommended_stocks['预测盈利概率'] > 0.75)  # 只保留预测盈利概率>75%的条件
                ]

            # 如果有符合条件的股票，选择预测盈利概率最高的前5只
            if len(strategy2_candidates) > 0:
                strategy2_stocks = strategy2_candidates.sort_values('预测盈利概率', ascending=False).head(5)
                print(f"策略2找到 {len(strategy2_stocks)} 只符合条件的股票")

                print("\n策略2推荐买入的股票:")
                print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！")
                print("【交易策略】: 买入后在第二个交易日开盘时卖出")

                # 根据实际使用的条件显示不同的筛选条件描述
                if 'recommended_stocks["技术强度"] >= 60' in str(strategy2_candidates):
                    print("【筛选条件】: 预测盈利概率>75%, 技术强度≥60, 开盘时上涨, 预测盈利概率排名前5")
                else:
                    print("【筛选条件】: 预测盈利概率>75%, 开盘时上涨, 预测盈利概率排名前5")

                print("【风险提示】: 策略2的胜率可能低于策略1的100%胜率，请谨慎操作")
                print("\n股票列表:")
                for i, row in strategy2_stocks.iterrows():
                    print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")

                # 使用策略2的股票作为高胜率策略股票
                high_win_rate_stocks = strategy2_stocks
            else:
                print("策略2也没有找到符合条件的股票")
                high_win_rate_stocks = pd.DataFrame()

        # 保存预测结果
        predictions.to_excel(f'prediction_results/{prediction_date_str}_预测结果.xlsx', index=False)
        recommended_stocks.to_excel(f'prediction_results/{prediction_date_str}_推荐股票.xlsx', index=False)

        # 保存策略1的推荐股票
        if len(strategy1_stocks) > 0:
            strategy1_stocks.to_excel(f'prediction_results/{prediction_date_str}_策略1推荐股票.xlsx', index=False)

        # 如果使用了策略2，保存策略2的推荐股票
        if 'strategy2_stocks' in locals() and len(strategy2_stocks) > 0:
            strategy2_stocks.to_excel(f'prediction_results/{prediction_date_str}_策略2推荐股票.xlsx', index=False)

        # 保存高胜率策略股票（可能是策略1或策略2的结果）
        if len(high_win_rate_stocks) > 0:
            high_win_rate_stocks.to_excel(f'prediction_results/{prediction_date_str}_高胜率策略股票.xlsx', index=False)

        print(f"\n预测完成！结果已保存至 prediction_results 目录")
        print(f"推荐股票数: {len(recommended_stocks)}")
        print(f"高胜率策略股票数: {len(high_win_rate_stocks)}")

        if len(high_win_rate_stocks) > 0:
            print("\n100%高胜率策略推荐买入的股票:")
            print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持100%胜率的关键条件！")
            print("【交易策略】: 买入后在第二个交易日开盘时卖出")
            print("【筛选条件】: 预测盈利概率>78%, 技术强度≥70, 连续技术强度5天数≥400, 开盘时上涨")
            print("\n股票列表:")
            for i, row in high_win_rate_stocks.iterrows():
                print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")

            # 返回高胜率策略股票
            return high_win_rate_stocks
        else:
            print("\n没有符合高胜率策略条件的股票")
            return None

    except Exception as e:
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 使用5月8日的数据预测5月9日应该买入的股票
    predict_next_day_stocks('2025-05-08')

    # 使用5月9日的数据预测5月10日应该买入的股票
    predict_next_day_stocks('2025-05-09')
