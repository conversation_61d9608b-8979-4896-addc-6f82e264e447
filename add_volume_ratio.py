import pandas as pd
import baostock as bs
import datetime
import time
import os
import numpy as np

def get_stock_volume_data(stock_code, start_date, end_date):
    """
    获取指定股票在指定日期范围内的成交量数据
    """
    # 登录系统
    lg = bs.login()
    
    # 获取股票日K线数据
    rs = bs.query_history_k_data_plus(
        stock_code,
        "date,code,volume",
        start_date=start_date,
        end_date=end_date,
        frequency="d",
        adjustflag="3"  # 复权类型，3表示不复权
    )
    
    # 处理结果集
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    
    # 登出系统
    bs.logout()
    
    # 转换为DataFrame
    result = pd.DataFrame(data_list, columns=['date', 'code', 'volume'])
    result['volume'] = result['volume'].astype(float)
    result['date'] = pd.to_datetime(result['date'])
    
    return result

def calculate_volume_ratio(df):
    """
    计算每日成交量与前一日的比值，并按0.5倍数标识，超过2.5倍显示为2.5
    """
    # 按股票代码和日期排序
    df = df.sort_values(['股票代码', '日期'])
    
    # 获取所有唯一的股票代码
    stock_codes = df['股票代码'].unique()
    
    # 获取最早和最晚的日期，向前多取30天以确保有足够的历史数据
    min_date = df['日期'].min() - datetime.timedelta(days=30)
    max_date = df['日期'].max()
    
    start_date = min_date.strftime('%Y-%m-%d')
    end_date = max_date.strftime('%Y-%m-%d')
    
    # 创建一个空的DataFrame来存储所有股票的成交量数据
    all_volume_data = pd.DataFrame()
    
    # 获取每只股票的成交量数据
    total_stocks = len(stock_codes)
    for i, code in enumerate(stock_codes):
        print(f"正在处理 {i+1}/{total_stocks}: {code}")
        try:
            volume_data = get_stock_volume_data(code, start_date, end_date)
            all_volume_data = pd.concat([all_volume_data, volume_data])
            # 每获取10只股票的数据后暂停一下，避免请求过于频繁
            if (i + 1) % 10 == 0:
                time.sleep(1)
        except Exception as e:
            print(f"获取 {code} 数据时出错: {e}")
    
    # 计算每只股票每天的成交量比值
    volume_ratios = {}
    
    for code in stock_codes:
        stock_volume = all_volume_data[all_volume_data['code'] == code].copy()
        if len(stock_volume) <= 1:
            continue
            
        # 计算与前一天的成交量比值
        stock_volume['prev_volume'] = stock_volume['volume'].shift(1)
        stock_volume['volume_ratio'] = stock_volume['volume'] / stock_volume['prev_volume']
        
        # 将比值按0.5倍数标识，超过2.5倍显示为2.5
        stock_volume['volume_ratio'] = stock_volume['volume_ratio'].apply(
            lambda x: min(round(x * 2) / 2, 2.5) if not pd.isna(x) else np.nan
        )
        
        # 存储结果
        for _, row in stock_volume.iterrows():
            if not pd.isna(row['volume_ratio']):
                volume_ratios[(row['code'], row['date'].strftime('%Y-%m-%d'))] = row['volume_ratio']
    
    # 更新原始DataFrame
    df['成交量是前一日几倍'] = df.apply(
        lambda row: volume_ratios.get((row['股票代码'], row['日期'].strftime('%Y-%m-%d')), np.nan), 
        axis=1
    )
    
    return df

# 主程序
def main():
    # 读取Excel文件
    file_path = r"E:\机器学习\complete_excel_results\股票明细_完整.xlsx"
    output_path = r"E:\机器学习\complete_excel_results\股票明细_完整_带成交量比.xlsx"
    
    print(f"正在读取文件: {file_path}")
    df = pd.read_excel(file_path)
    
    # 计算成交量比值
    print("正在计算成交量比值...")
    df = calculate_volume_ratio(df)
    
    # 保存结果
    print(f"正在保存结果到: {output_path}")
    df.to_excel(output_path, index=False)
    print("处理完成!")

if __name__ == "__main__":
    main()
