"""
测试买入日开盘涨跌幅修复
"""

import pandas as pd
import os

def test_open_pct_calculation():
    """测试开盘涨跌幅计算修复"""
    print("🔍 测试买入日开盘涨跌幅计算修复")
    print("=" * 50)
    
    # 读取茂业商业的原始数据
    stock_files = [
        'complete_excel_results/stock_data/daily/stock_data_20250423.xlsx',
        'complete_excel_results/stock_data/daily/stock_data_20250424.xlsx'
    ]
    
    print("📊 原始股票数据:")
    for file_path in stock_files:
        if os.path.exists(file_path):
            df = pd.read_excel(file_path)
            print(f"\n=== {os.path.basename(file_path)} ===")
            
            # 查找茂业商业的记录
            maoyeshangye = df[df['股票名称'].str.contains('茂业商业', na=False)]
            if not maoyeshangye.empty:
                for idx, row in maoyeshangye.iterrows():
                    print(f"股票代码: {row.get('证券代码', 'N/A')}")
                    print(f"股票名称: {row.get('股票名称', 'N/A')}")
                    print(f"日期: {row.get('日期', 'N/A')}")
                    print(f"开盘价: {row.get('开盘价', 'N/A')}")
                    print(f"收盘价: {row.get('收盘价', 'N/A')}")
                    print(f"前收盘价: {row.get('前收盘价', 'N/A')}")
                    print(f"涨跌幅: {row.get('涨跌幅', 'N/A')}")
                    
                    # 计算正确的买入日开盘涨跌幅
                    if pd.notna(row.get('开盘价')) and pd.notna(row.get('前收盘价')):
                        open_price = float(row.get('开盘价'))
                        prev_close = float(row.get('前收盘价'))
                        if prev_close > 0:
                            correct_open_pct = (open_price - prev_close) / prev_close * 100
                            print(f"✅ 正确的买入日开盘涨跌幅: {correct_open_pct:.2f}%")
                    print("---")
    
    print("\n🔧 测试修复后的计算逻辑:")
    
    # 模拟修复后的计算逻辑
    test_data = {
        '证券代码': ['sz.000031', 'sz.000031'],
        '股票名称': ['茂业商业', '茂业商业'],
        '日期': ['2025-04-23', '2025-04-24'],
        '开盘价': [3.87, 3.66],
        '收盘价': [3.59, 3.59],
        '前收盘价': [3.81, 3.59],
        '涨跌幅': [-5.77, 0.0]
    }
    
    test_df = pd.DataFrame(test_data)
    
    # 应用修复后的逻辑
    test_df['code'] = test_df['证券代码']
    test_df['date'] = pd.to_datetime(test_df['日期'])
    test_df['open'] = test_df['开盘价']
    test_df['close'] = test_df['收盘价']
    test_df['prev_close_original'] = test_df['前收盘价']
    
    # 使用修复后的计算方法
    test_df['prev_close'] = test_df['prev_close_original']  # 使用原始前收盘价
    test_df['open_pct_change'] = (test_df['open'] - test_df['prev_close']) / test_df['prev_close'] * 100
    test_df['open_pct_change'] = test_df['open_pct_change'].round(2)
    
    print("修复后的计算结果:")
    for idx, row in test_df.iterrows():
        print(f"日期: {row['日期']}")
        print(f"开盘价: {row['开盘价']}")
        print(f"前收盘价: {row['前收盘价']}")
        print(f"修复后的开盘涨跌幅: {row['open_pct_change']}%")
        print("---")
    
    print("\n📋 对比结果:")
    print("茂业商业 2025-04-23:")
    print("  原始数据: 开盘价3.87, 前收盘价3.81")
    print("  正确计算: (3.87-3.81)/3.81*100 = 1.57%")
    print("  策略189显示: 5.01% (错误)")
    print("  修复后应显示: 1.57% (正确)")
    
    print("\n茂业商业 2025-04-24:")
    print("  原始数据: 开盘价3.66, 前收盘价3.59")
    print("  正确计算: (3.66-3.59)/3.59*100 = 1.95%")
    print("  修复后应显示: 1.95% (正确)")
    
    return True

def test_strategy_189_fix():
    """测试策略189的修复"""
    print("\n🎯 测试策略189修复")
    print("=" * 50)
    
    try:
        # 导入修复后的回测模块
        import backtest_local
        
        print("✅ 成功导入修复后的回测模块")
        print("现在可以重新运行策略189来验证修复效果")
        
        # 提供测试建议
        print("\n💡 测试建议:")
        print("1. 重新运行策略189")
        print("2. 查看茂业商业2025-04-24的买入日开盘涨跌幅")
        print("3. 应该显示1.95%而不是5.01%")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入回测模块失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 买入日开盘涨跌幅计算修复测试")
    print("=" * 60)
    
    # 测试计算逻辑
    test_open_pct_calculation()
    
    # 测试策略修复
    test_strategy_189_fix()
    
    print("\n✅ 修复测试完成！")
    print("现在可以重新运行策略189来验证修复效果。")
    
    input("\n按回车键退出...")
