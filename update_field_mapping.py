#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
更新字段映射规则
作者: Augment AI
版本: 1.0.0

该脚本用于更新所有涉及股票数据读取的程序，适应新的中文字段名。
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import glob

# 新的字段映射规则
FIELD_MAPPING = {
    # 旧字段名 -> 新字段名
    'stock_code': '证券代码',
    'date': '日期', 
    'open': '开盘价',
    'high': '最高价',
    'low': '最低价',
    'close': '收盘价',
    'pre_close': '前收盘价',
    'volume': '成交量',
    'amount': '成交额',
    'turnover': '换手率',
    'pct_chg': '涨跌幅',
    'name': '股票名称',
    'is_st': '是否ST股',
    'trade_status': '交易状态',
    'adj_factor': '复权状态'
}

def read_stock_data_new_format(file_path):
    """
    读取新格式的股票数据
    
    参数:
        file_path (str): 股票数据文件路径
        
    返回:
        DataFrame: 股票数据
    """
    print(f"正在读取股票数据: {file_path}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 检查必要的列是否存在
        required_columns = ['证券代码', '日期', '开盘价', '收盘价', '前收盘价', '涨跌幅', '股票名称']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"错误: 缺少必要的列: {missing_columns}")
            return None
        
        # 确保日期列是日期类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 计算买入日开盘涨跌幅（当日开盘价相对于前一日收盘价的涨跌幅）
        df['买入日开盘涨跌幅'] = (df['开盘价'] - df['前收盘价']) / df['前收盘价'] * 100
        
        # 按股票代码分组，计算卖出日开盘涨跌幅
        df = df.sort_values(['证券代码', '日期'])
        df['下一日开盘价'] = df.groupby('证券代码')['开盘价'].shift(-1)
        df['卖出日开盘涨跌幅'] = (df['下一日开盘价'] - df['收盘价']) / df['收盘价'] * 100
        
        print(f"成功读取股票数据，共{len(df)}条记录")
        return df
    except Exception as e:
        print(f"读取股票数据时出错: {str(e)}")
        return None

def backtest_strategy_new_format(stock_data, strategy_conditions, start_date=None, end_date=None):
    """
    使用新格式数据进行策略回测
    
    参数:
        stock_data (DataFrame): 股票数据
        strategy_conditions (list): 策略条件列表
        start_date (str): 开始日期
        end_date (str): 结束日期
        
    返回:
        dict: 回测结果
    """
    print("正在进行策略回测...")
    
    try:
        # 转换日期格式
        if start_date:
            start_date = pd.to_datetime(start_date)
        else:
            start_date = stock_data['日期'].min()
            
        if end_date:
            end_date = pd.to_datetime(end_date)
        else:
            end_date = stock_data['日期'].max()
        
        # 筛选日期范围内的数据
        mask = (stock_data['日期'] >= start_date) & (stock_data['日期'] <= end_date)
        data = stock_data[mask].copy()
        
        if len(data) == 0:
            print("选定日期范围内没有数据")
            return None
        
        # 获取交易日期列表
        trading_dates = sorted(data['日期'].unique())
        
        # 初始化回测结果
        results = {
            'daily_performance': [],
            'trades': [],
            'summary': {}
        }
        
        # 初始化资金
        initial_capital = 1000000
        current_capital = initial_capital
        positions = {}
        
        # 遍历每个交易日
        for i, date in enumerate(trading_dates[:-1]):
            current_day_data = data[data['日期'] == date]
            next_date = trading_dates[i + 1]
            next_day_data = data[data['日期'] == next_date]
            
            # 卖出昨日持仓（次日早盘卖出）
            if positions:
                for stock_code, position in list(positions.items()):
                    next_stock_data = next_day_data[next_day_data['证券代码'] == stock_code]
                    
                    if len(next_stock_data) > 0:
                        sell_price = next_stock_data['开盘价'].values[0]
                        sell_value = position['quantity'] * sell_price
                        profit = sell_value - position['cost']
                        profit_rate = profit / position['cost'] * 100
                        
                        current_capital += sell_value
                        
                        # 记录交易
                        results['trades'].append({
                            '日期': next_date,
                            '交易时间': '09:30',
                            '股票代码': stock_code,
                            '股票名称': next_stock_data['股票名称'].values[0],
                            '操作': '卖出',
                            '价格': round(sell_price, 2),
                            '数量': position['quantity'],
                            '金额': round(sell_value, 2),
                            '涨跌幅(%)': round(next_stock_data['买入日开盘涨跌幅'].values[0], 2),
                            '收益': round(profit, 2),
                            '收益率(%)': round(profit_rate, 2)
                        })
                        
                        del positions[stock_code]
            
            # 应用策略条件筛选股票
            filtered_data = current_day_data.copy()
            
            for condition in strategy_conditions:
                feature = condition['feature']
                operator = condition['condition']
                threshold = condition['threshold']
                
                if feature in filtered_data.columns:
                    if operator == '>=':
                        filtered_data = filtered_data[filtered_data[feature] >= threshold]
                    elif operator == '==':
                        filtered_data = filtered_data[filtered_data[feature] == threshold]
            
            # 买入股票（次日早盘买入）
            if len(filtered_data) > 0:
                max_stocks = 10
                num_stocks = min(len(filtered_data), max_stocks)
                per_stock_value = current_capital / num_stocks
                
                for _, stock in filtered_data.head(max_stocks).iterrows():
                    stock_code = stock['证券代码']
                    next_stock_data = next_day_data[next_day_data['证券代码'] == stock_code]
                    
                    if len(next_stock_data) > 0:
                        buy_price = next_stock_data['开盘价'].values[0]
                        quantity = int(per_stock_value / buy_price / 100) * 100
                        
                        if quantity > 0:
                            cost = quantity * buy_price
                            current_capital -= cost
                            
                            positions[stock_code] = {
                                'quantity': quantity,
                                'price': buy_price,
                                'cost': cost,
                                'buy_date': next_date
                            }
                            
                            # 记录交易
                            results['trades'].append({
                                '日期': next_date,
                                '交易时间': '09:30',
                                '股票代码': stock_code,
                                '股票名称': next_stock_data['股票名称'].values[0],
                                '操作': '买入',
                                '价格': round(buy_price, 2),
                                '数量': quantity,
                                '金额': round(cost, 2),
                                '涨跌幅(%)': round(next_stock_data['买入日开盘涨跌幅'].values[0], 2),
                                '收益': 0,
                                '收益率(%)': 0
                            })
            
            # 计算当日总资产
            total_position_value = sum([position['quantity'] * position['price'] for position in positions.values()])
            total_assets = current_capital + total_position_value
            
            # 计算当日收益率
            if i == 0:
                daily_return = 0
            else:
                prev_assets = results['daily_performance'][-1]['总资产']
                daily_return = (total_assets - prev_assets) / prev_assets * 100
            
            # 记录每日表现
            results['daily_performance'].append({
                '日期': date,
                '现金': current_capital,
                '持仓市值': total_position_value,
                '总资产': total_assets,
                '日收益率(%)': daily_return,
                '持仓数量': len(positions)
            })
        
        # 计算汇总统计
        if results['daily_performance']:
            initial_assets = initial_capital
            final_assets = results['daily_performance'][-1]['总资产']
            total_return = (final_assets - initial_assets) / initial_assets * 100
            
            days = (trading_dates[-1] - trading_dates[0]).days
            annual_return = total_return * 365 / days if days > 0 else 0
            
            # 计算胜率
            if results['trades']:
                win_trades = [trade for trade in results['trades'] if trade['操作'] == '卖出' and trade['收益'] > 0]
                sell_trades = [trade for trade in results['trades'] if trade['操作'] == '卖出']
                win_rate = len(win_trades) / len(sell_trades) * 100 if sell_trades else 0
            else:
                win_rate = 0
            
            # 计算平均每日交易笔数
            daily_trades = {}
            for trade in results['trades']:
                date = trade['日期']
                if date not in daily_trades:
                    daily_trades[date] = 0
                daily_trades[date] += 1
            
            avg_daily_trades = sum(daily_trades.values()) / len(trading_dates) if trading_dates else 0
            
            results['summary'] = {
                '初始资金': initial_capital,
                '最终资金': final_assets,
                '总收益率(%)': total_return,
                '年化收益率(%)': annual_return,
                '胜率(%)': win_rate,
                '总交易笔数': len(results['trades']),
                '平均每日交易笔数': avg_daily_trades,
                '交易天数': len(daily_trades),
                '总天数': len(trading_dates),
                '交易频率(%)': len(daily_trades) / len(trading_dates) * 100 if trading_dates else 0
            }
        
        print(f"回测完成，总收益率: {results['summary']['总收益率(%)']}%，胜率: {results['summary']['胜率(%)']}%")
        return results
    except Exception as e:
        print(f"策略回测时出错: {str(e)}")
        return None

def main():
    """主函数"""
    print("字段映射规则更新完成")
    print("新的字段映射:")
    for old_field, new_field in FIELD_MAPPING.items():
        print(f"  {old_field} -> {new_field}")

if __name__ == "__main__":
    main()
