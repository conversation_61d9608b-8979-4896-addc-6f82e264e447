import pandas as pd
import baostock as bs
import datetime
import time
import os
import numpy as np
from collections import defaultdict

def get_stock_volume_data_batch(stock_codes, start_date, end_date):
    """
    批量获取多只股票在指定日期范围内的成交量数据
    """
    # 登录系统
    lg = bs.login()

    # 存储所有股票的成交量数据
    all_volume_data = defaultdict(dict)

    # 每次处理的股票数量
    batch_size = 50

    # 分批处理股票
    for i in range(0, len(stock_codes), batch_size):
        batch_codes = stock_codes[i:i+batch_size]
        print(f"正在处理第 {i//batch_size + 1} 批，共 {len(batch_codes)} 只股票")

        for code in batch_codes:
            try:
                # 获取股票日K线数据
                rs = bs.query_history_k_data_plus(
                    code,
                    "date,code,volume",
                    start_date=start_date,
                    end_date=end_date,
                    frequency="d",
                    adjustflag="3"  # 复权类型，3表示不复权
                )

                # 处理结果集
                while (rs.error_code == '0') & rs.next():
                    row = rs.get_row_data()
                    date_str = row[0]
                    volume = row[2]

                    # 只有当成交量不为空时才添加数据
                    if volume and volume.strip():
                        all_volume_data[code][date_str] = float(volume)

            except Exception as e:
                print(f"获取 {code} 数据时出错: {e}")

        # 每批处理完后暂停一下，避免请求过于频繁
        time.sleep(1)

    # 登出系统
    bs.logout()

    return all_volume_data

def calculate_volume_ratio(df):
    """
    计算每日成交量与前一日的比值，并按0.5倍数标识，超过3.5倍显示为3.5
    """
    # 获取唯一的股票代码和日期
    stock_dates = df[['股票代码', '日期']].drop_duplicates()
    stock_codes = stock_dates['股票代码'].unique().tolist()

    # 获取最早和最晚的日期，向前多取30天以确保有足够的历史数据
    min_date = stock_dates['日期'].min() - datetime.timedelta(days=30)
    max_date = stock_dates['日期'].max()

    start_date = min_date.strftime('%Y-%m-%d')
    end_date = max_date.strftime('%Y-%m-%d')

    print(f"获取 {len(stock_codes)} 只股票从 {start_date} 到 {end_date} 的成交量数据")

    # 批量获取所有股票的成交量数据
    all_volume_data = get_stock_volume_data_batch(stock_codes, start_date, end_date)

    # 创建一个字典，用于存储每只股票每天的成交量比值
    volume_ratios = {}

    # 计算每只股票每天的成交量比值
    for code in stock_codes:
        if code not in all_volume_data:
            continue

        # 获取该股票的所有日期和成交量
        stock_volume_data = all_volume_data[code]
        if len(stock_volume_data) <= 1:
            continue

        # 按日期排序
        dates = sorted(stock_volume_data.keys())

        # 计算与前一天的成交量比值
        for i in range(1, len(dates)):
            curr_date = dates[i]
            prev_date = dates[i-1]

            curr_volume = stock_volume_data[curr_date]
            prev_volume = stock_volume_data[prev_date]

            if prev_volume > 0:
                ratio = curr_volume / prev_volume
                # 将比值按0.5倍数标识，超过3.5倍显示为3.5
                ratio = min(round(ratio * 2) / 2, 3.5)
                volume_ratios[(code, curr_date)] = ratio

    # 更新原始DataFrame
    df['成交量是前一日几倍'] = df.apply(
        lambda row: volume_ratios.get((row['股票代码'], row['日期'].strftime('%Y-%m-%d')), np.nan),
        axis=1
    )

    return df

# 主程序
def main():
    # 读取Excel文件
    file_path = r"E:\机器学习\complete_excel_results\股票明细_完整.xlsx"
    output_path = r"E:\机器学习\complete_excel_results\股票明细_完整_带成交量比.xlsx"

    print(f"正在读取文件: {file_path}")
    df = pd.read_excel(file_path)

    # 计算成交量比值
    print("正在计算成交量比值...")
    df = calculate_volume_ratio(df)

    # 保存结果
    print(f"正在保存结果到: {output_path}")
    df.to_excel(output_path, index=False)
    print("处理完成!")

if __name__ == "__main__":
    main()
