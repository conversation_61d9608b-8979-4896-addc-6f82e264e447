"""
整合A股强势股选股系统中的所有Excel数据，用于训练模型
"""

import os
import pandas as pd
import glob
from datetime import datetime
import numpy as np

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header(title):
    """打印标题"""
    print("\n" + " " * 24 + title)
    print("=" * 80 + "\n")

def integrate_stock_data(base_dir):
    """整合所有日期文件夹中的Excel数据"""
    print_header("整合股票数据")
    
    # 获取所有日期文件夹
    date_folders = glob.glob(os.path.join(base_dir, "选股结果_*"))
    date_folders.sort()
    
    print(f"找到 {len(date_folders)} 个日期文件夹")
    
    # 创建一个空的DataFrame来存储所有数据
    all_data = pd.DataFrame()
    
    # 遍历所有日期文件夹
    for folder in date_folders:
        # 从文件夹名称中提取日期
        date_str = folder.split("_")[-1]
        
        # 构建Excel文件路径
        excel_file = os.path.join(folder, f"强势股选股结果_{date_str}.xlsx")
        
        # 检查文件是否存在
        if os.path.exists(excel_file):
            try:
                # 读取Excel文件
                df = pd.read_excel(excel_file)
                
                # 添加日期列
                df['日期'] = pd.to_datetime(date_str)
                
                print(f"读取 {date_str} 的数据，共 {len(df)} 行")
                
                # 将数据添加到总DataFrame中
                all_data = pd.concat([all_data, df], ignore_index=True)
            except Exception as e:
                print(f"读取 {date_str} 的数据时出错: {e}")
        else:
            print(f"文件不存在: {excel_file}")
    
    print(f"\n整合完成，共 {len(all_data)} 行数据")
    
    return all_data

def preprocess_data(df):
    """预处理数据"""
    print_header("预处理数据")
    
    # 确保日期列是datetime类型
    if '日期' in df.columns:
        df['日期'] = pd.to_datetime(df['日期'])
    
    # 生成目标变量：如果涨跌幅>0，则为1，否则为0
    if '涨跌幅' in df.columns and '是否盈利' not in df.columns:
        df['是否盈利'] = (df['涨跌幅'] > 0).astype(int)
    
    # 计算连续技术强度5天数
    if '技术强度' in df.columns and '连续技术强度5天数' not in df.columns:
        print("计算连续技术强度5天数...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')
        
        # 初始化连续技术强度5天数列
        df['连续技术强度5天数'] = 0
        
        # 对每个股票计算连续技术强度5天数
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')
            
            # 获取技术强度列
            strength = group['技术强度'].values
            
            # 计算连续5天的技术强度累积值
            for i in range(len(group)):
                if i < 4:  # 不足5天的情况
                    cumulative = sum(strength[max(0, i-4):i+1])
                else:  # 5天及以上的情况
                    cumulative = sum(strength[i-4:i+1])
                
                # 更新连续技术强度5天数
                df.loc[group.index[i], '连续技术强度5天数'] = cumulative
        
        print("连续技术强度5天数计算完成")
    
    # 计算技术强度趋势
    if '技术强度' in df.columns and '技术强度趋势' not in df.columns:
        print("计算技术强度趋势...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')
        
        # 初始化技术强度趋势列
        df['技术强度趋势'] = 0
        
        # 对每个股票计算技术强度趋势
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')
            
            # 获取技术强度列
            strength = group['技术强度'].values
            
            # 计算连续3天的技术强度趋势
            for i in range(len(group)):
                if i >= 2:  # 至少需要3天数据
                    trend = 1 if (strength[i] > strength[i-1] and strength[i-1] > strength[i-2]) else 0
                    df.loc[group.index[i], '技术强度趋势'] = trend
        
        print("技术强度趋势计算完成")
    
    # 计算价格趋势
    if '当前价格' in df.columns and '价格趋势' not in df.columns:
        print("计算价格趋势...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')
        
        # 初始化价格趋势列
        df['价格趋势'] = 0
        
        # 对每个股票计算价格趋势
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')
            
            # 获取价格列
            prices = group['当前价格'].values
            
            # 计算连续3天的价格趋势
            for i in range(len(group)):
                if i >= 2:  # 至少需要3天数据
                    trend = 1 if (prices[i] > prices[i-1] and prices[i-1] > prices[i-2]) else 0
                    df.loc[group.index[i], '价格趋势'] = trend
        
        print("价格趋势计算完成")
    
    # 计算涨跌幅趋势
    if '涨跌幅' in df.columns and '涨跌幅趋势' not in df.columns:
        print("计算涨跌幅趋势...")
        # 按股票代码分组
        grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')
        
        # 初始化涨跌幅趋势列
        df['涨跌幅趋势'] = 0
        
        # 对每个股票计算涨跌幅趋势
        for name, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('日期')
            
            # 获取涨跌幅列
            changes = group['涨跌幅'].values
            
            # 计算连续3天的涨跌幅趋势
            for i in range(len(group)):
                if i >= 2:  # 至少需要3天数据
                    trend = 1 if (changes[i] > changes[i-1] and changes[i-1] > changes[i-2]) else 0
                    df.loc[group.index[i], '涨跌幅趋势'] = trend
        
        print("涨跌幅趋势计算完成")
    
    # 解析技术指标
    if '技术指标' in df.columns:
        print("解析技术指标...")
        # 初始化技术指标列
        df['技术指标_均线多头排列'] = 0
        df['技术指标_MACD金叉'] = 0
        df['技术指标_RSI反弹'] = 0
        df['技术指标_KDJ金叉'] = 0
        df['技术指标_布林带突破'] = 0
        
        # 解析技术指标
        for i, row in df.iterrows():
            indicators = str(row['技术指标'])
            df.loc[i, '技术指标_均线多头排列'] = 1 if '均线多头排列' in indicators else 0
            df.loc[i, '技术指标_MACD金叉'] = 1 if 'MACD金叉' in indicators else 0
            df.loc[i, '技术指标_RSI反弹'] = 1 if 'RSI 反弹' in indicators or 'RSI反弹' in indicators else 0
            df.loc[i, '技术指标_KDJ金叉'] = 1 if 'KDJ金叉' in indicators else 0
            df.loc[i, '技术指标_布林带突破'] = 1 if '布林带突破' in indicators else 0
        
        print("技术指标解析完成")
    
    # 计算看涨技术指标数量
    if all(col in df.columns for col in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', '技术指标_KDJ金叉', '技术指标_布林带突破']) and '看涨技术指标数量' not in df.columns:
        print("计算看涨技术指标数量...")
        df['看涨技术指标数量'] = df['技术指标_均线多头排列'] + df['技术指标_MACD金叉'] + df['技术指标_RSI反弹'] + df['技术指标_KDJ金叉'] + df['技术指标_布林带突破']
        print("看涨技术指标数量计算完成")
    
    # 添加开盘涨跌列（模拟开盘时是否上涨）
    if '开盘涨跌' not in df.columns:
        print("生成开盘涨跌列...")
        # 这里我们使用一个简化的逻辑：如果技术强度>50，则认为开盘时上涨
        if '技术强度' in df.columns:
            df['开盘涨跌'] = (df['技术强度'] > 50).astype(int)
        else:
            df['开盘涨跌'] = 0
        print("开盘涨跌列生成完成")
    
    # 计算次日买后日卖的收益率
    print("计算次日买后日卖的收益率...")
    # 按股票代码分组
    grouped = df.sort_values(['股票代码', '日期']).groupby('股票代码')
    
    # 初始化收益率列
    df['次日买后日卖收益率'] = np.nan
    
    # 对每个股票计算次日买后日卖的收益率
    for name, group in grouped:
        # 确保数据按日期排序
        group = group.sort_values('日期')
        
        # 计算次日买后日卖的收益率
        for i in range(len(group) - 2):
            # 买入价格（次日开盘价，用当日收盘价近似）
            buy_price = group.iloc[i+1]['当前价格']
            
            # 卖出价格（后日开盘价，用后日收盘价近似）
            sell_price = group.iloc[i+2]['当前价格']
            
            # 计算收益率
            return_rate = (sell_price / buy_price) - 1
            
            # 更新收益率
            df.loc[group.index[i], '次日买后日卖收益率'] = return_rate
    
    print("次日买后日卖的收益率计算完成")
    
    print("预处理完成")
    return df

def main():
    """主函数"""
    clear_screen()
    
    # 设置基础目录
    base_dir = r"E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0"
    
    # 整合数据
    all_data = integrate_stock_data(base_dir)
    
    # 预处理数据
    processed_data = preprocess_data(all_data)
    
    # 保存整合后的数据
    output_file = "股票明细.xlsx"
    processed_data.to_excel(output_file, index=False)
    
    print(f"\n数据已保存到 {output_file}，共 {len(processed_data)} 行")

if __name__ == "__main__":
    main()
