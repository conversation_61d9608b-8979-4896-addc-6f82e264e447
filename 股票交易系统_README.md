# 股票交易系统 - 高收益高胜率策略

这是一个基于技术指标和机器学习的股票交易系统，旨在实现高收益和高胜率的交易策略。

## 系统组成

系统包含以下文件：

1. **stock_trading_system.py** - 核心库文件，包含所有策略和功能实现
2. **stock_trading_system_main.py** - 主程序，提供命令行界面
3. **data_processor.py** - 数据处理模块，用于处理原始数据
4. **股票交易系统_README.md** - 使用说明文档

## 功能特点

- **多种交易策略**：包含多种规则型策略和机器学习策略
- **回测功能**：支持对历史数据进行回测，评估策略表现
- **股票推荐**：根据选定策略推荐当日交易股票
- **数据处理**：支持从原始数据文件中提取和处理股票数据
- **可视化分析**：生成收益曲线图，直观展示策略表现
- **详细报告**：提供详细的交易记录和统计分析

## 策略说明

### 规则型策略

1. **规则策略1**：技术强度=85 + 看涨技术指标数量=5 + 涨跌幅趋势=1
   - 收益率：27.16%
   - 胜率：50.59%

2. **规则策略4**：技术强度>=85 + 看涨技术指标数量=5 + 涨跌幅趋势=1 + 连续技术强度5天数>=450
   - 收益率：30.98%
   - 胜率：45.45%
   - 平均涨跌幅：4.62%

3. **规则策略4+**：在策略4的基础上优先选择技术强度=100的股票，并按连续技术强度5天数降序排序
   - 进一步提高策略4的表现

### 机器学习策略

1. **ML策略**：使用随机森林模型预测股票涨跌，选择预测概率最高的股票
   - 模型准确率：92.04%
   - 收益率：0.31%
   - 胜率：44.82%

2. **混合策略**：结合规则策略和机器学习策略，计算综合得分
   - 规则得分：技术强度、看涨技术指标数量、涨跌幅趋势、连续技术强度5天数
   - 预测概率：机器学习模型预测的上涨概率
   - 综合得分：规则得分*0.7 + 预测概率*0.3

## 安装与使用

### 环境要求

- Python 3.6+
- pandas
- numpy
- scikit-learn
- matplotlib
- joblib

### 安装依赖

```bash
pip install pandas numpy scikit-learn matplotlib joblib
```

### 使用方法

#### 1. 数据处理

```bash
# 处理目录中的所有数据文件
python data_processor.py --mode process --data_dir "E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0" --output 股票明细.xlsx

# 合并多个数据文件
python data_processor.py --mode merge --file_pattern "*.xlsx" --output 股票明细.xlsx

# 计算额外的特征
python data_processor.py --mode features --input 股票明细.xlsx --output 股票明细_增强.xlsx

# 添加次日涨跌幅
python data_processor.py --mode returns --input 股票明细.xlsx --output 股票明细_带次日涨跌幅.xlsx
```

#### 2. 训练模型

```bash
python stock_trading_system_main.py --mode train --data 股票明细.xlsx
```

#### 3. 回测策略

```bash
python stock_trading_system_main.py --mode backtest --strategy rule4plus --data 股票明细.xlsx --start_date 2025-04-01 --end_date 2025-04-30 --capital 10000 --output backtest_results.txt
```

#### 4. 推荐股票

```bash
python stock_trading_system_main.py --mode recommend --strategy rule4plus --data 股票明细.xlsx
```

### 参数说明

#### 数据处理器参数 (data_processor.py)

- `--mode`：处理模式，可选 process（处理目录）、merge（合并文件）、features（计算特征）、returns（添加次日涨跌幅）
- `--data_dir`：数据目录路径，用于process模式
- `--file_pattern`：文件匹配模式，用于merge模式
- `--input`：输入文件路径，用于features和returns模式
- `--output`：输出文件路径

#### 交易系统参数 (stock_trading_system_main.py)

- `--mode`：运行模式，可选 train（训练模型）、backtest（回测策略）、recommend（推荐股票）
- `--data`：数据文件路径
- `--strategy`：策略选择，可选 rule1、rule4、rule4plus、ml、hybrid
- `--start_date`：回测开始日期（YYYY-MM-DD）
- `--end_date`：回测结束日期（YYYY-MM-DD）
- `--capital`：初始资金
- `--output`：输出文件路径

## 数据格式要求

系统需要的数据格式为Excel文件，包含以下字段：

- `日期`：交易日期
- `股票代码`：股票代码
- `股票名称`：股票名称
- `技术强度`：技术强度指标（0-100）
- `连续技术强度5天数`：连续5天的技术强度累计值
- `看涨技术指标数量`：看涨的技术指标数量（0-5）
- `价格趋势`：价格趋势指标
- `涨跌幅趋势`：涨跌幅趋势指标（1表示上升）
- `涨跌幅`：次日涨跌幅（百分比）

## 策略选择建议

1. **追求高收益**：选择规则策略4或规则策略4+
2. **追求高胜率**：选择规则策略1
3. **平衡收益和胜率**：选择混合策略
4. **资金分配**：
   - 70% 资金使用规则策略4+
   - 20% 资金使用规则策略1
   - 10% 资金使用机器学习策略

## 注意事项

1. 本系统仅供学习和研究使用，不构成投资建议
2. 股票市场有风险，投资需谨慎
3. 建议在实盘交易前充分回测和验证策略
4. 实际交易中应考虑交易成本、滑点等因素

## 作者

Augment AI

## 版本历史

- v1.0.0：初始版本，包含基本功能和策略
