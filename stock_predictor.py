"""
股票预测工具

支持通过命令行参数选择不同的操作：
1. 训练模型
2. 使用策略1预测
3. 使用策略A预测
4. 使用策略B预测
5. 使用策略C预测
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib
import argparse
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

def clear_screen():
    """清除屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header(title):
    """打印界面标题"""
    print("=" * 80)
    print(f"                        {title}")
    print("=" * 80)
    print()

def load_model(model_dir='trained_models'):
    """加载已训练好的模型"""
    print(f"加载模型...")
    try:
        # 加载最新模型信息
        latest_model_info = joblib.load(f"{model_dir}/latest_model_info.joblib")

        # 加载模型、缩放器和特征
        model = joblib.load(latest_model_info['model_file'])
        scaler = joblib.load(latest_model_info['scaler_file'])
        features = joblib.load(latest_model_info['features_file'])

        print(f"成功加载模型 (训练时间: {latest_model_info['timestamp']})")
        print(f"模型使用的特征: {features}")

        return model, scaler, features
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None, None, None

def load_data(file_path='股票明细.xlsx'):
    """加载股票数据"""
    print(f"加载股票数据: {file_path}")
    try:
        stock_data = pd.read_excel(file_path)
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
        return stock_data
    except Exception as e:
        print(f"加载股票数据失败: {e}")
        return None

def preprocess_data(stock_data):
    """预处理股票数据，计算必要的特征"""
    print("预处理数据...")

    # 转换日期格式
    if isinstance(stock_data['日期'].iloc[0], str):
        stock_data['日期'] = pd.to_datetime(stock_data['日期'])

    # 按股票代码和日期排序
    stock_data = stock_data.sort_values(['股票代码', '日期'])

    # 按股票代码分组处理
    for code, group in stock_data.groupby('股票代码'):
        # 确保数据按日期排序
        group = group.sort_values('日期')

        # 计算连续技术强度天数（连续多少天为100）
        consecutive_days = []
        current_count = 0

        for strength in group['技术强度'].values:
            if strength == 100:
                current_count += 1
            else:
                current_count = 0
            consecutive_days.append(current_count)

        # 更新原始数据
        stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days

        # 计算技术强度累积值（5天）
        cumulative_strength = group['技术强度'].copy()
        for i in range(1, 5):
            cumulative_strength += group['技术强度'].shift(i).fillna(0)

        # 更新原始数据
        stock_data.loc[group.index, '连续技术强度5天数'] = cumulative_strength

        # 计算趋势特征
        stock_data.loc[group.index, '技术强度趋势'] = (
            (group['技术强度'] > group['技术强度'].shift(1)) &
            (group['技术强度'].shift(1) > group['技术强度'].shift(2))
        ).astype(int)

        stock_data.loc[group.index, '价格趋势'] = (
            (group['当前价格'] > group['当前价格'].shift(1)) &
            (group['当前价格'].shift(1) > group['当前价格'].shift(2))
        ).astype(int)

        if '涨跌幅' in group.columns:
            stock_data.loc[group.index, '涨跌幅趋势'] = (
                (group['涨跌幅'] > group['涨跌幅'].shift(1)) &
                (group['涨跌幅'].shift(1) > group['涨跌幅'].shift(2))
            ).astype(int)
        else:
            stock_data.loc[group.index, '涨跌幅趋势'] = 0

        # 计算两日收益率（买入后第二天卖出）
        two_day_later_price = group['当前价格'].shift(-2)
        two_day_return = (two_day_later_price / group['当前价格'] - 1) * 100

        # 更新原始数据
        stock_data.loc[group.index, '两日收益率'] = two_day_return

        # 计算是否盈利（两日收益率>0）
        is_profit = (two_day_return > 0).astype(int)
        stock_data.loc[group.index, '是否盈利'] = is_profit

        # 计算次日涨跌方向（用于判断开盘时是否上涨）
        stock_data.loc[group.index, '次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)

    # 处理技术指标特征
    if '技术指标' in stock_data.columns:
        # 提取常见的技术指标关键词
        tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
                          '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']

        # 为每个技术指标创建一个新列
        for indicator in tech_indicators:
            col_name = f'技术指标_{indicator}'
            # 检查技术指标文本中是否包含该关键词
            if '技术指标' in stock_data.columns:
                stock_data[col_name] = stock_data['技术指标'].str.contains(indicator, na=False).astype(int)
            else:
                stock_data[col_name] = 0
    else:
        # 如果没有技术指标列，创建空的技术指标特征
        tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
                          '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']
        for indicator in tech_indicators:
            stock_data[f'技术指标_{indicator}'] = 0

    # 删除没有完整数据的记录
    stock_data = stock_data.dropna(subset=['两日收益率', '是否盈利', '次日涨跌方向'])

    print("预处理完成")
    return stock_data

def get_latest_data(stock_data):
    """获取最新日期的数据"""
    # 获取最新日期
    latest_date = stock_data['日期'].max()
    print(f"数据集中最新的交易日期: {latest_date}")

    # 获取最新日期的数据
    latest_data = stock_data[stock_data['日期'] == latest_date]
    print(f"最新日期的数据记录数: {len(latest_data)}")

    return latest_data, latest_date

def train_model(data_file_path='股票明细.xlsx', model_dir='trained_models'):
    """训练模型并保存"""
    print_header("训练模型")

    start_time = datetime.now()
    print(f"开始训练模型: {start_time}")

    # 创建模型保存目录
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return

    # 预处理数据
    processed_data = preprocess_data(stock_data)

    # 提取特征
    features = [
        '技术强度', '连续技术强度5天数',
        '技术强度趋势', '价格趋势', '涨跌幅趋势',
        '涨跌幅'
    ]

    # 添加技术指标特征
    tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
                      '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']
    for indicator in tech_indicators:
        features.append(f'技术指标_{indicator}')

    # 特征和目标变量
    X_train = processed_data[features]
    y_train = processed_data['是否盈利']

    # 处理缺失值
    valid_indices = ~X_train.isnull().any(axis=1)
    X_train = X_train[valid_indices]
    y_train = y_train[valid_indices]

    # 标准化特征
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)

    # 训练梯度提升模型
    print("训练梯度提升模型...")
    try:
        gb_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
        gb_model.fit(X_train_scaled, y_train)

        # 计算特征重要性
        feature_importance = pd.DataFrame({
            'feature': features,
            'importance': gb_model.feature_importances_
        }).sort_values('importance', ascending=False)

        print("模型训练完成")
        print("\n特征重要性:")
        for i, row in feature_importance.iterrows():
            print(f"{row['feature']}: {row['importance']:.4f}")

        # 保存模型和特征缩放器
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_file = f"{model_dir}/gb_model_{timestamp}.joblib"
        scaler_file = f"{model_dir}/scaler_{timestamp}.joblib"
        features_file = f"{model_dir}/features_{timestamp}.joblib"

        joblib.dump(gb_model, model_file)
        joblib.dump(scaler, scaler_file)
        joblib.dump(features, features_file)

        # 保存最新模型的路径
        latest_model_info = {
            'model_file': model_file,
            'scaler_file': scaler_file,
            'features_file': features_file,
            'timestamp': timestamp
        }
        joblib.dump(latest_model_info, f"{model_dir}/latest_model_info.joblib")

        # 保存特征重要性
        feature_importance.to_excel(f"{model_dir}/feature_importance_{timestamp}.xlsx", index=False)

        # 绘制特征重要性图表
        plt.figure(figsize=(10, 6))
        plt.barh(feature_importance['feature'], feature_importance['importance'])
        plt.xlabel('重要性')
        plt.ylabel('特征')
        plt.title('特征重要性')
        plt.tight_layout()
        plt.savefig(f"{model_dir}/feature_importance_{timestamp}.png")

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        print(f"\n模型保存完成:")
        print(f"模型文件: {model_file}")
        print(f"缩放器文件: {scaler_file}")
        print(f"特征文件: {features_file}")
        print(f"训练用时: {duration:.2f} 秒")

        return True

    except Exception as e:
        print(f"训练模型失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def apply_strategy_1(predictions):
    """
    策略1：100%高胜率策略

    条件：
    1. 预测盈利概率>78%
    2. 技术强度≥70
    3. 连续技术强度5天数≥400
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.78) &  # 条件1: 预测盈利概率>78%
        (predictions['技术强度'] >= 70) &  # 条件2: 技术强度≥70
        (predictions['连续技术强度5天数'] >= 400)  # 条件3: 5天累积值≥400
    ]

    return strategy_stocks

def apply_strategy_A(predictions):
    """
    策略A：最高胜率策略

    条件：
    1. 技术强度=21-30
    2. 连续技术强度5天数=301-400
    3. 预测盈利概率>75%
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] >= 21) &  # 条件2: 技术强度≥21
        (predictions['技术强度'] <= 30) &  # 条件2: 技术强度≤30
        (predictions['连续技术强度5天数'] >= 301) &  # 条件3: 5天累积值≥301
        (predictions['连续技术强度5天数'] <= 400)  # 条件3: 5天累积值≤400
    ]

    return strategy_stocks

def apply_strategy_B(predictions):
    """
    策略B：最高收益率策略

    条件：
    1. 技术强度=100
    2. 连续技术强度5天数=401-500
    3. 预测盈利概率>75%
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] == 100) &  # 条件2: 技术强度=100
        (predictions['连续技术强度5天数'] >= 401) &  # 条件3: 5天累积值≥401
        (predictions['连续技术强度5天数'] <= 500)  # 条件3: 5天累积值≤500
    ]

    return strategy_stocks

def apply_strategy_B2(predictions):
    """
    策略B2：高收益率策略（稍微放宽条件）

    条件：
    1. 预测盈利概率>70%
    2. 技术强度>=90
    3. 连续技术强度5天数>=350
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.70) &  # 条件1: 预测盈利概率>70%
        (predictions['技术强度'] >= 90) &  # 条件2: 技术强度>=90
        (predictions['连续技术强度5天数'] >= 350)  # 条件3: 5天累积值>=350
    ]

    return strategy_stocks

def apply_strategy_C(predictions):
    """
    策略C：平衡策略（胜率和交易机会的平衡）

    条件：
    1. 技术强度=71-80
    2. 连续技术强度5天数=201-300
    3. 预测盈利概率>75%
    """
    # 基本条件筛选
    base_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] >= 71) &  # 条件2: 技术强度≥71
        (predictions['技术强度'] <= 80) &  # 条件2: 技术强度≤80
        (predictions['连续技术强度5天数'] >= 201) &  # 条件3: 5天累积值≥201
        (predictions['连续技术强度5天数'] <= 300)  # 条件3: 5天累积值≤300
    ]

    # 显示符合条件的股票数量
    print(f"策略C: 符合基本条件的股票数量: {len(base_stocks)}")

    # 返回所有符合条件的股票，不进行动态调整
    return base_stocks

def predict_with_strategy(strategy_name, strategy_func, data_file_path='股票明细.xlsx', prediction_date_str=None):
    """
    使用指定策略预测股票

    参数:
    strategy_name: 策略名称
    strategy_func: 策略函数
    data_file_path: 数据文件路径
    prediction_date_str: 预测日期字符串
    """
    strategy_descriptions = {
        'strategy_1': "策略1：100%高胜率策略",
        'strategy_A': "策略A：最高胜率策略",
        'strategy_B': "策略B：最高收益率策略",
        'strategy_C': "策略C：平衡策略（胜率和交易机会的平衡）",
        'strategy_B2': "策略B2：高收益率策略（放宽条件版）"
    }

    risk_descriptions = {
        'strategy_1': {
            'buy_risk': "低风险：该策略在历史测试中表现出100%的胜率，但交易机会较少。",
            'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
            'expected_win_rate': "100%",
            'expected_return': "约2.38%"
        },
        'strategy_A': {
            'buy_risk': "低风险：该策略在历史测试中表现出约86.77%的胜率，交易机会适中。",
            'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
            'expected_win_rate': "约86.77%",
            'expected_return': "约3.42%"
        },
        'strategy_B': {
            'buy_risk': "中低风险：该策略在历史测试中表现出约83.67%的胜率，但交易机会较少。",
            'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
            'expected_win_rate': "约83.67%",
            'expected_return': "约4.83%"
        },
        'strategy_C': {
            'buy_risk': "中低风险：该策略在历史测试中表现出约84.82%的胜率，交易机会较多。",
            'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
            'expected_win_rate': "约84.82%",
            'expected_return': "约2.59%"
        },
        'strategy_B2': {
            'buy_risk': "中风险：该策略是策略B的放宽条件版本，预期胜率约80%，交易机会较策略B更多。",
            'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
            'expected_win_rate': "约80%",
            'expected_return': "约4.0%"
        }
    }

    print_header(strategy_descriptions[strategy_name])

    # 加载模型
    model, scaler, features = load_model()
    if model is None:
        print("无法加载模型，请先训练模型")
        return

    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return

    # 预处理数据
    processed_data = preprocess_data(stock_data)

    # 确定预测日期
    if prediction_date_str is None:
        # 如果没有指定预测日期，使用最新日期的下一天
        latest_date = processed_data['日期'].max()
        next_date = latest_date + timedelta(days=1)
        next_date_str = next_date.strftime('%Y-%m-%d')
    else:
        # 使用指定的预测日期
        next_date_str = prediction_date_str
        next_date = datetime.strptime(next_date_str, '%Y-%m-%d')

    print(f"预测日期: {next_date_str}")

    # 获取指定日期的数据
    # 将日期转换为字符串进行比较，避免datetime对象比较的问题
    next_date_str_for_compare = next_date.strftime('%Y-%m-%d')
    all_dates = sorted(processed_data['日期'].unique())
    all_dates_str = [d.strftime('%Y-%m-%d') for d in all_dates]

    # 检查指定日期是否存在于数据集中
    if next_date_str_for_compare in all_dates_str:
        # 找到对应的日期索引
        date_index = all_dates_str.index(next_date_str_for_compare)
        target_date = all_dates[date_index]
        prediction_data = processed_data[processed_data['日期'] == target_date]
        print(f"使用 {next_date_str} 的数据进行预测，共 {len(prediction_data)} 条记录")
    else:
        # 如果指定日期不存在，直接返回空的DataFrame
        print(f"错误: {next_date_str} 的数据不存在，无法进行预测")
        # 创建一个空的DataFrame，保持列名一致
        prediction_data = pd.DataFrame(columns=processed_data.columns)
        # 保存空结果并返回
        result_file = f'strategy_results/{next_date_str}_{strategy_name}.xlsx'
        # 创建结果目录
        if not os.path.exists('strategy_results'):
            os.makedirs('strategy_results')
        # 保存空结果
        prediction_data.to_excel(result_file, index=False)
        print(f"\n预测结果已保存至: {result_file}")
        print(f"{strategy_descriptions[strategy_name]}推荐股票数: 0")
        print(f"错误: {next_date_str} 的数据不存在，无法进行预测")
        return None

    # 准备预测数据
    try:
        # 提取特征
        X_pred = prediction_data[features]

        # 处理预测数据中的缺失值
        valid_indices = X_pred.notna().all(axis=1)
        X_pred = X_pred[valid_indices]
        prediction_data_filtered = prediction_data.loc[valid_indices]

        if len(X_pred) == 0:
            print(f"预测数据不足，无法进行预测")
            # 创建结果目录
            if not os.path.exists('strategy_results'):
                os.makedirs('strategy_results')
            # 保存空结果
            result_file = f'strategy_results/{next_date_str}_{strategy_name}.xlsx'
            empty_df = pd.DataFrame(columns=['股票代码', '股票名称', '涨跌幅', '技术强度', '连续技术强度天数', '连续技术强度5天数', '预测盈利概率'])
            empty_df.to_excel(result_file, index=False)
            print(f"\n预测结果已保存至: {result_file}")
            print(f"{strategy_descriptions[strategy_name]}推荐股票数: 0")
            print(f"\n{strategy_descriptions[strategy_name]}没有推荐的股票")
            return None

        # 标准化预测数据
        X_pred_scaled = scaler.transform(X_pred)

        # 预测盈利概率
        pred_proba = model.predict_proba(X_pred_scaled)[:, 1]

        # 创建预测结果DataFrame
        predictions = pd.DataFrame({
            '股票代码': prediction_data_filtered['股票代码'],
            '股票名称': prediction_data_filtered['股票名称'],
            '涨跌幅': prediction_data_filtered['涨跌幅'] if '涨跌幅' in prediction_data_filtered.columns else 0,
            '技术强度': prediction_data_filtered['技术强度'],
            '连续技术强度天数': prediction_data_filtered['连续技术强度天数'],
            '连续技术强度5天数': prediction_data_filtered['连续技术强度5天数'],
            '预测盈利概率': pred_proba
        })

        # 按预测盈利概率降序排序
        predictions = predictions.sort_values('预测盈利概率', ascending=False)

        print(f"预测完成，共 {len(predictions)} 只股票")

        # 创建结果目录
        if not os.path.exists('strategy_results'):
            os.makedirs('strategy_results')

        # 应用策略
        strategy_stocks = strategy_func(predictions)

        # 保存结果
        # 创建结果目录
        if not os.path.exists('strategy_results'):
            os.makedirs('strategy_results')

        result_file = f'strategy_results/{next_date_str}_{strategy_name}.xlsx'
        strategy_stocks.to_excel(result_file, index=False)

        print(f"\n预测结果已保存至: {result_file}")
        print(f"{strategy_descriptions[strategy_name]}推荐股票数: {len(strategy_stocks)}")
        print(f"预期胜率: {risk_descriptions[strategy_name]['expected_win_rate']}")
        print(f"预期收益率: {risk_descriptions[strategy_name]['expected_return']}")
        print(f"买入风险: {risk_descriptions[strategy_name]['buy_risk']}")
        print(f"卖出风险: {risk_descriptions[strategy_name]['sell_risk']}")

        # 显示推荐股票
        if len(strategy_stocks) > 0:
            print(f"\n{strategy_descriptions[strategy_name]}推荐买入的股票:")
            print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！")
            print("【交易策略】: 买入后在第二个交易日开盘时卖出")
            print("\n股票列表:")
            for i, row in strategy_stocks.iterrows():
                print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
        else:
            print(f"\n{strategy_descriptions[strategy_name]}没有推荐的股票")

        # 返回策略股票，以便调用者可以检查是否有推荐股票
        return strategy_stocks

    except Exception as e:
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票预测工具')
    parser.add_argument('option', type=int, choices=[1, 2, 3, 4, 5, 6],
                        help='选择操作: 1=训练模型, 2=策略1, 3=策略A, 4=策略B, 5=策略C, 6=策略B2')
    parser.add_argument('--data', type=str, default='股票明细.xlsx',
                        help='数据文件路径 (默认: 股票明细.xlsx)')
    parser.add_argument('--date', type=str,
                        help='预测日期，格式为YYYY-MM-DD (默认: 从环境变量获取或使用明天的日期)')

    args = parser.parse_args()

    clear_screen()

    # 获取预测日期
    prediction_date = args.date
    if prediction_date is None:
        # 尝试从环境变量获取
        import os
        prediction_date = os.environ.get('PREDICTION_DATE')

        # 如果环境变量也没有，使用明天的日期
        if prediction_date is None:
            from datetime import datetime, timedelta
            tomorrow = datetime.now() + timedelta(days=1)
            prediction_date = tomorrow.strftime("%Y-%m-%d")

    try:
        if args.option == 1:
            result = train_model(args.data)
            if result:
                print("模型训练成功完成")
            else:
                print("模型训练失败")
        elif args.option == 2:
            # 策略1
            predict_with_strategy('strategy_1', apply_strategy_1, args.data, prediction_date)
        elif args.option == 3:
            # 策略A
            predict_with_strategy('strategy_A', apply_strategy_A, args.data, prediction_date)
        elif args.option == 4:
            # 策略B
            predict_with_strategy('strategy_B', apply_strategy_B, args.data, prediction_date)
        elif args.option == 5:
            # 策略C
            predict_with_strategy('strategy_C', apply_strategy_C, args.data, prediction_date)
        elif args.option == 6:
            # 策略B2
            predict_with_strategy('strategy_B2', apply_strategy_B2, args.data, prediction_date)
    except Exception as e:
        print(f"执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
