"""
创建性能测试版本的打包脚本
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def build_performance_test():
    """打包性能测试版本"""
    
    print("=" * 60)
    print("🚀 性能测试版本打包")
    print("=" * 60)
    
    # 版本信息
    version = "performance_test"
    build_date = datetime.now().strftime("%Y%m%d_%H%M%S")
    exe_name = f"性能诊断程序_{version}"
    
    print(f"📦 版本: {version}")
    print(f"📅 构建时间: {build_date}")
    print(f"📁 程序名称: {exe_name}")
    
    # 创建构建目录
    build_dir = f"build_{version}_{build_date}"
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    os.makedirs(build_dir)
    
    print(f"\n📁 创建构建目录: {build_dir}")
    
    # PyInstaller命令（最小化版本）
    cmd = [
        'pyinstaller',
        '--onefile',                    # 单文件模式
        '--console',                    # 保留控制台，便于查看性能日志
        '--name', exe_name,             # 程序名称
        '--distpath', build_dir,        # 输出目录
        '--workpath', f'{build_dir}/work',  # 工作目录
        '--specpath', f'{build_dir}/spec',  # spec文件目录
        '--hidden-import', 'pandas',
        '--hidden-import', 'numpy',
        '--hidden-import', 'openpyxl',
        '--hidden-import', 'psutil',
        'diagnose_performance.py'       # 诊断程序
    ]
    
    print("\n🔨 开始打包性能诊断程序...")
    print("命令:", ' '.join(cmd))
    
    try:
        # 执行打包
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        
        # 检查生成的exe文件
        exe_path = os.path.join(build_dir, f"{exe_name}.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📁 生成文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            
            # 创建测试说明
            test_info = f"""
性能诊断程序使用说明

1. 运行诊断
   - 双击 {exe_name}.exe 运行诊断程序
   - 程序会自动测试各项性能指标

2. 诊断内容
   - 系统信息检查
   - 模块导入性能
   - 文件访问性能
   - Pandas操作性能
   - 数据加载模拟

3. 性能对比
   - 在开发环境运行: python diagnose_performance.py
   - 在打包环境运行: {exe_name}.exe
   - 对比两者的执行时间

4. 问题排查
   如果打包版本明显慢于开发版本，可能原因：
   - PyInstaller解压开销
   - 临时文件访问慢
   - 模块导入慢
   - 系统资源不足

构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            test_file = os.path.join(build_dir, f"{exe_name}_使用说明.txt")
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_info)
            
            print(f"📖 使用说明: {test_file}")
            
            print(f"\n🎉 性能测试版本打包完成！")
            print(f"📁 输出目录: {build_dir}")
            print(f"🚀 可执行文件: {exe_name}.exe")
            
            print(f"\n💡 使用建议:")
            print(f"1. 先运行开发版本: python diagnose_performance.py")
            print(f"2. 再运行打包版本: {build_dir}/{exe_name}.exe")
            print(f"3. 对比两者的性能差异")
            
            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

if __name__ == "__main__":
    success = build_performance_test()
    if success:
        print("\n✅ 性能测试版本打包成功！")
    else:
        print("\n❌ 打包失败！")
    
    input("\n按回车键退出...")
