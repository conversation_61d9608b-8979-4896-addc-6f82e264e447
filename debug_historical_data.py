#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
sys.path.append('.')

from calculate_tech_strength import load_all_historical_tech_strength
import pandas as pd

def debug_historical_data():
    """调试历史数据加载"""
    
    print("=== 调试历史数据加载 ===")
    
    tech_system_path = r"E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0"
    
    print(f"技术系统路径: {tech_system_path}")
    
    # 测试历史数据加载
    print("开始加载历史数据...")
    historical_data = load_all_historical_tech_strength(tech_system_path)
    
    if historical_data.empty:
        print("❌ 历史数据加载失败！")
        
        # 手动检查目录
        import os
        if os.path.exists(tech_system_path):
            subdirs = [d for d in os.listdir(tech_system_path) if d.startswith('选股结果_')]
            print(f"找到 {len(subdirs)} 个目录")
            
            # 检查前几个目录
            for subdir in subdirs[:3]:
                print(f"检查目录: {subdir}")
                date_str = subdir.replace('选股结果_', '')
                filename = f"强势股选股结果_{date_str}.xlsx"
                file_path = os.path.join(tech_system_path, subdir, filename)
                
                print(f"  文件路径: {file_path}")
                print(f"  文件存在: {os.path.exists(file_path)}")
                
                if os.path.exists(file_path):
                    try:
                        df = pd.read_excel(file_path)
                        print(f"  文件行数: {len(df)}")
                        print(f"  文件列名: {df.columns.tolist()}")
                        
                        if '股票代码' in df.columns and '技术强度' in df.columns:
                            print(f"  前3个股票的技术强度:")
                            for i in range(min(3, len(df))):
                                row = df.iloc[i]
                                print(f"    {row['股票代码']}: {row['技术强度']}")
                        else:
                            print(f"  ❌ 缺少必要的列")
                    except Exception as e:
                        print(f"  ❌ 读取文件出错: {e}")
        else:
            print(f"❌ 技术系统路径不存在")
    else:
        print(f"✅ 历史数据加载成功")
        print(f"数据行数: {len(historical_data)}")
        print(f"数据列名: {historical_data.columns.tolist()}")
        print(f"日期范围: {historical_data['日期'].min()} 至 {historical_data['日期'].max()}")
        print(f"股票数量: {historical_data['股票代码'].nunique()}")
        
        # 检查具体股票的历史数据
        sample_stock = historical_data['股票代码'].iloc[0]
        stock_data = historical_data[historical_data['股票代码'] == sample_stock]
        
        print(f"\n股票 {sample_stock} 的历史数据:")
        for _, row in stock_data.head(10).iterrows():
            print(f"  {row['日期'].strftime('%Y-%m-%d')}: 技术强度 {row['技术强度']}")
        
        # 测试连续技术强度计算
        print(f"\n测试连续技术强度计算:")
        target_date = '2025-05-15'
        
        from calculate_tech_strength import calculate_consecutive_tech_strength_from_historical_data
        
        consecutive_3 = calculate_consecutive_tech_strength_from_historical_data(historical_data, target_date, 3)
        consecutive_5 = calculate_consecutive_tech_strength_from_historical_data(historical_data, target_date, 5)
        consecutive_10 = calculate_consecutive_tech_strength_from_historical_data(historical_data, target_date, 10)
        
        print(f"3天连续技术强度计算结果: {len(consecutive_3)} 个股票")
        print(f"5天连续技术强度计算结果: {len(consecutive_5)} 个股票")
        print(f"10天连续技术强度计算结果: {len(consecutive_10)} 个股票")
        
        if consecutive_3:
            sample_stocks = list(consecutive_3.keys())[:5]
            for stock in sample_stocks:
                c3 = consecutive_3.get(stock, 0)
                c5 = consecutive_5.get(stock, 0)
                c10 = consecutive_10.get(stock, 0)
                print(f"  股票 {stock}: 3天={c3}, 5天={c5}, 10天={c10}")

if __name__ == "__main__":
    debug_historical_data()
