import pandas as pd
import os
import glob
import re
from datetime import datetime
import numpy as np

# 定义文件路径
source_dir = r'E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0'
target_file = r'E:\机器学习\complete_excel_results\股票明细_完整.xlsx'

def extract_date_from_folder(folder_name):
    """从文件夹名称中提取日期"""
    match = re.search(r'选股结果_(\d{4}-\d{2}-\d{2})', folder_name)
    if match:
        return match.group(1)
    return None

def process_excel_file(file_path, date_str):
    """处理单个Excel文件，提取股票数据并添加特征"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 检查是否为空
        if df.empty:
            print(f"警告: {file_path} 为空")
            return None
        
        # 检查必要的列是否存在
        required_columns = ['股票代码', '股票名称', '行业', '当前价格', '涨跌幅', '技术强度']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"警告: {file_path} 缺少以下列: {missing_columns}")
            return None
        
        # 添加日期列
        df['日期'] = pd.to_datetime(date_str)
        
        # 计算技术指标特征
        if '技术指标' in df.columns:
            df['技术指标_均线多头排列'] = df['技术指标'].str.contains('均线多头排列').fillna(False).astype(int)
            df['技术指标_MACD金叉'] = df['技术指标'].str.contains('MACD金叉').fillna(False).astype(int)
            df['技术指标_RSI反弹'] = df['技术指标'].str.contains('RSI反弹').fillna(False).astype(int)
            df['技术指标_KDJ金叉'] = df['技术指标'].str.contains('KDJ金叉').fillna(False).astype(int)
            df['技术指标_布林带突破'] = df['技术指标'].str.contains('布林带突破').fillna(False).astype(int)
            df['看涨技术指标数量'] = (df['技术指标_均线多头排列'] + df['技术指标_MACD金叉'] + 
                           df['技术指标_RSI反弹'] + df['技术指标_KDJ金叉'] + 
                           df['技术指标_布林带突破'])
        else:
            # 如果没有技术指标列，添加空列
            df['技术指标'] = np.nan
            df['技术指标_均线多头排列'] = 0
            df['技术指标_MACD金叉'] = 0
            df['技术指标_RSI反弹'] = 0
            df['技术指标_KDJ金叉'] = 0
            df['技术指标_布林带突破'] = 0
            df['看涨技术指标数量'] = 0
        
        # 添加其他特征列（如果不存在）
        if '连续技术强度5天数' not in df.columns:
            df['连续技术强度5天数'] = 0
        
        if '价格趋势' not in df.columns:
            df['价格趋势'] = 0
        
        if '涨跌幅趋势' not in df.columns:
            df['涨跌幅趋势'] = 0
        
        if '技术强度趋势' not in df.columns:
            df['技术强度趋势'] = 0
        
        # 添加连续技术强度特征
        for days in [3, 5, 10]:
            col_name = f'连续技术强度{days}天数'
            if col_name not in df.columns:
                df[col_name] = 0
            
            # 添加趋势特征
            trend_col = f'{col_name}趋势'
            if trend_col not in df.columns:
                df[trend_col] = 0
            
            # 添加价格趋势特征
            price_trend_col = f'{col_name}价格趋势'
            if price_trend_col not in df.columns:
                df[price_trend_col] = 0
            
            # 添加涨跌幅趋势特征
            change_trend_col = f'{col_name}涨跌幅趋势'
            if change_trend_col not in df.columns:
                df[change_trend_col] = 0
        
        # 添加是否盈利和开盘涨跌列（这些需要后续处理）
        if '是否盈利' not in df.columns:
            df['是否盈利'] = 0
        
        if '开盘涨跌' not in df.columns:
            df['开盘涨跌'] = 0
        
        return df
    
    except Exception as e:
        print(f"处理 {file_path} 时出错: {str(e)}")
        return None

def main():
    # 读取现有的股票明细文件
    try:
        existing_df = pd.read_excel(target_file)
        print(f"成功读取现有文件: {target_file}")
        print(f"现有数据行数: {len(existing_df)}")
        print(f"现有数据日期范围: {existing_df['日期'].min()} 至 {existing_df['日期'].max()}")
        print(f"现有数据不同日期数量: {existing_df['日期'].nunique()}")
        
        # 获取现有数据的日期列表
        existing_dates = set(existing_df['日期'].dt.strftime('%Y-%m-%d').unique())
        print(f"现有数据日期列表: {existing_dates}")
    except Exception as e:
        print(f"读取现有文件时出错: {str(e)}")
        existing_df = pd.DataFrame()
        existing_dates = set()
    
    # 获取源目录中的所有日期文件夹
    date_folders = [f for f in os.listdir(source_dir) if os.path.isdir(os.path.join(source_dir, f)) and f.startswith('选股结果_')]
    
    # 提取日期并排序
    date_folders_with_dates = [(folder, extract_date_from_folder(folder)) for folder in date_folders]
    date_folders_with_dates = [(folder, date) for folder, date in date_folders_with_dates if date is not None]
    date_folders_with_dates.sort(key=lambda x: x[1])
    
    print(f"源目录中的日期文件夹数量: {len(date_folders_with_dates)}")
    
    # 处理每个日期文件夹
    all_dfs = []
    for folder, date_str in date_folders_with_dates:
        # 如果日期已经存在于现有数据中，则跳过
        if date_str in existing_dates:
            print(f"跳过已存在的日期: {date_str}")
            continue
        
        print(f"处理日期: {date_str}")
        folder_path = os.path.join(source_dir, folder)
        
        # 查找文件夹中的Excel文件
        excel_files = glob.glob(os.path.join(folder_path, '*.xlsx'))
        if not excel_files:
            print(f"警告: 在 {folder_path} 中未找到Excel文件")
            continue
        
        # 处理每个Excel文件
        for file_path in excel_files:
            df = process_excel_file(file_path, date_str)
            if df is not None:
                all_dfs.append(df)
                print(f"成功处理 {file_path}，添加 {len(df)} 行数据")
    
    # 如果没有新数据，则退出
    if not all_dfs:
        print("没有新数据需要添加")
        return
    
    # 合并所有数据
    new_df = pd.concat(all_dfs, ignore_index=True)
    print(f"新数据行数: {len(new_df)}")
    
    # 合并新数据和现有数据
    if not existing_df.empty:
        combined_df = pd.concat([existing_df, new_df], ignore_index=True)
    else:
        combined_df = new_df
    
    print(f"合并后的数据行数: {len(combined_df)}")
    
    # 保存合并后的数据
    combined_df.to_excel(target_file, index=False)
    print(f"成功保存合并后的数据到 {target_file}")

if __name__ == "__main__":
    main()
