"""
股票策略可视化界面

提供可视化的参数勾选界面，用于训练模型和选择不同的策略
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkcalendar import DateEntry
import pandas as pd
from datetime import datetime, timedelta
import threading
import subprocess
import queue

class StockStrategyApp:
    def __init__(self, root):
        self.root = root
        self.root.title("股票策略选择器")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TFrame", background="#f0f0f0")
        self.style.configure("TButton", background="#4CAF50", foreground="black", font=("Arial", 10, "bold"))
        self.style.configure("TLabel", background="#f0f0f0", font=("Arial", 10))
        self.style.configure("Header.TLabel", font=("Arial", 16, "bold"))
        self.style.configure("Subheader.TLabel", font=("Arial", 12, "bold"))

        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="20")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题
        self.title_label = ttk.Label(self.main_frame, text="股票策略选择器", style="Header.TLabel")
        self.title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20), sticky="w")

        # 创建参数框架
        self.params_frame = ttk.LabelFrame(self.main_frame, text="参数设置", padding="10")
        self.params_frame.grid(row=1, column=0, sticky="nsew", padx=(0, 10))

        # 创建数据文件选择
        self.data_file_label = ttk.Label(self.params_frame, text="数据文件:")
        self.data_file_label.grid(row=0, column=0, sticky="w", pady=(0, 5))

        self.data_file_frame = ttk.Frame(self.params_frame)
        self.data_file_frame.grid(row=0, column=1, sticky="ew", pady=(0, 5))

        self.data_file_var = tk.StringVar(value="股票明细.xlsx")
        self.data_file_entry = ttk.Entry(self.data_file_frame, textvariable=self.data_file_var, width=30)
        self.data_file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.browse_button = ttk.Button(self.data_file_frame, text="浏览...", command=self.browse_file)
        self.browse_button.pack(side=tk.RIGHT, padx=(5, 0))

        # 创建日期选择
        self.date_label = ttk.Label(self.params_frame, text="预测日期:")
        self.date_label.grid(row=1, column=0, sticky="w", pady=(0, 5))

        # 获取明天的日期
        tomorrow = datetime.now() + timedelta(days=1)

        self.date_var = tk.StringVar(value=tomorrow.strftime("%Y-%m-%d"))
        self.date_picker = DateEntry(self.params_frame, width=12, background='darkblue',
                                    foreground='white', borderwidth=2, date_pattern='yyyy-mm-dd',
                                    textvariable=self.date_var)
        self.date_picker.grid(row=1, column=1, sticky="w", pady=(0, 5))

        # 创建策略选择
        self.strategy_label = ttk.Label(self.params_frame, text="选择策略:", style="Subheader.TLabel")
        self.strategy_label.grid(row=2, column=0, columnspan=2, sticky="w", pady=(10, 5))

        # 创建策略选择框架
        self.strategies_frame = ttk.Frame(self.params_frame)
        self.strategies_frame.grid(row=3, column=0, columnspan=2, sticky="w", pady=(0, 10))

        # 创建策略选择变量
        self.train_model_var = tk.BooleanVar(value=False)
        self.strategy_1_var = tk.BooleanVar(value=False)
        self.strategy_A_var = tk.BooleanVar(value=False)
        self.strategy_B_var = tk.BooleanVar(value=False)
        self.strategy_C_var = tk.BooleanVar(value=False)

        # 创建策略选择复选框
        self.train_model_check = ttk.Checkbutton(self.strategies_frame, text="训练模型",
                                                variable=self.train_model_var, command=self.update_date_picker)
        self.train_model_check.grid(row=0, column=0, sticky="w", padx=(0, 10), pady=2)

        self.strategy_1_check = ttk.Checkbutton(self.strategies_frame, text="策略1: 100%高胜率策略",
                                               variable=self.strategy_1_var, command=self.update_date_picker)
        self.strategy_1_check.grid(row=1, column=0, sticky="w", padx=(0, 10), pady=2)

        self.strategy_A_check = ttk.Checkbutton(self.strategies_frame, text="策略A: 最高胜率策略",
                                               variable=self.strategy_A_var, command=self.update_date_picker)
        self.strategy_A_check.grid(row=2, column=0, sticky="w", padx=(0, 10), pady=2)

        self.strategy_B_check = ttk.Checkbutton(self.strategies_frame, text="策略B: 最高收益率策略",
                                               variable=self.strategy_B_var, command=self.update_date_picker)
        self.strategy_B_check.grid(row=3, column=0, sticky="w", padx=(0, 10), pady=2)

        self.strategy_C_check = ttk.Checkbutton(self.strategies_frame, text="策略C: 平衡策略",
                                               variable=self.strategy_C_var, command=self.update_date_picker)
        self.strategy_C_check.grid(row=4, column=0, sticky="w", padx=(0, 10), pady=2)

        # 创建策略说明
        self.strategy_desc_frame = ttk.LabelFrame(self.params_frame, text="策略说明", padding="10")
        self.strategy_desc_frame.grid(row=4, column=0, columnspan=2, sticky="nsew", pady=(10, 0))

        self.strategy_desc_text = tk.Text(self.strategy_desc_frame, wrap=tk.WORD, height=8, width=40)
        self.strategy_desc_text.pack(fill=tk.BOTH, expand=True)
        self.strategy_desc_text.insert(tk.END, "策略1: 100%高胜率策略\n"
                                            "- 预测盈利概率>78%\n"
                                            "- 技术强度≥70\n"
                                            "- 连续技术强度5天数≥400\n\n"
                                            "策略A: 最高胜率策略\n"
                                            "- 技术强度=21-30\n"
                                            "- 连续技术强度5天数=301-400\n"
                                            "- 预测盈利概率>75%\n\n"
                                            "策略B: 最高收益率策略\n"
                                            "- 技术强度=100\n"
                                            "- 连续技术强度5天数=401-500\n"
                                            "- 预测盈利概率>75%\n\n"
                                            "策略C: 平衡策略\n"
                                            "- 技术强度=71-80\n"
                                            "- 连续技术强度5天数=201-300\n"
                                            "- 预测盈利概率>75%")
        self.strategy_desc_text.config(state=tk.DISABLED)

        # 创建执行按钮
        self.execute_button = ttk.Button(self.params_frame, text="执行选定的策略", command=self.execute_strategies)
        self.execute_button.grid(row=5, column=0, columnspan=2, pady=(10, 0), sticky="ew")

        # 创建输出框架
        self.output_frame = ttk.LabelFrame(self.main_frame, text="输出结果", padding="10")
        self.output_frame.grid(row=1, column=1, sticky="nsew")

        # 创建输出文本框
        self.output_text = tk.Text(self.output_frame, wrap=tk.WORD, width=50)
        self.output_text.pack(fill=tk.BOTH, expand=True)

        # 创建滚动条
        self.scrollbar = ttk.Scrollbar(self.output_text, command=self.output_text.yview)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.output_text.config(yscrollcommand=self.scrollbar.set)

        # 设置网格权重
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(1, weight=1)

        # 创建状态栏
        self.status_var = tk.StringVar(value="就绪")
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 创建进度条
        self.progress = ttk.Progressbar(self.root, orient=tk.HORIZONTAL, length=100, mode='indeterminate')
        self.progress.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)

        # 创建队列用于线程通信
        self.queue = queue.Queue()
        self.root.after(100, self.process_queue)

    def browse_file(self):
        """浏览文件对话框"""
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=(("Excel files", "*.xlsx"), ("All files", "*.*"))
        )
        if filename:
            self.data_file_var.set(filename)

    def update_date_picker(self):
        """根据选择的策略启用或禁用日期选择器"""
        # 如果只选择了训练模型，禁用日期选择器
        if (self.train_model_var.get() and not (self.strategy_1_var.get() or
                                              self.strategy_A_var.get() or
                                              self.strategy_B_var.get() or
                                              self.strategy_C_var.get())):
            self.date_picker.config(state='disabled')
            self.date_label.config(foreground='gray')
        else:
            self.date_picker.config(state='normal')
            self.date_label.config(foreground='black')

    def execute_strategies(self):
        """执行选定的策略"""
        # 检查是否选择了至少一个策略
        if not (self.train_model_var.get() or self.strategy_1_var.get() or
                self.strategy_A_var.get() or self.strategy_B_var.get() or
                self.strategy_C_var.get()):
            messagebox.showwarning("警告", "请至少选择一个策略")
            return

        # 获取参数
        data_file = self.data_file_var.get()
        prediction_date = self.date_var.get()

        # 检查文件是否存在
        if not os.path.exists(data_file):
            messagebox.showerror("错误", f"数据文件不存在: {data_file}")
            return

        # 清空输出
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, f"开始执行策略...\n")
        self.output_text.insert(tk.END, f"数据文件: {data_file}\n")
        self.output_text.insert(tk.END, f"预测日期: {prediction_date}\n\n")

        # 启动进度条
        self.progress.start()
        self.status_var.set("正在执行...")

        # 创建线程执行策略
        thread = threading.Thread(target=self.run_strategies, args=(data_file, prediction_date))
        thread.daemon = True
        thread.start()

    def run_strategies(self, data_file, prediction_date):
        """在单独的线程中运行策略"""
        try:
            # 训练模型
            if self.train_model_var.get():
                self.queue.put(("status", "正在训练模型..."))
                self.queue.put(("output", "正在训练模型...\n"))

                # 直接调用stock_predictor.py进行模型训练
                cmd = ["python", "stock_predictor.py", "1", "--data", data_file]

                # 使用subprocess.Popen执行命令
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,  # 行缓冲
                    universal_newlines=True  # 文本模式
                )

                # 实时读取输出
                for line in process.stdout:
                    self.queue.put(("output", line))

                # 等待进程结束
                process.wait()

                # 读取错误输出
                stderr = process.stderr.read()

                if process.returncode == 0:
                    self.queue.put(("output", "\n模型训练完成\n"))
                else:
                    self.queue.put(("output", "\n模型训练失败\n"))
                    if stderr:
                        self.queue.put(("output", "错误信息:\n" + stderr + "\n"))

            # 执行策略1
            if self.strategy_1_var.get():
                self.queue.put(("status", "正在执行策略1..."))
                self.queue.put(("output", "\n正在执行策略1...\n"))

                # 创建一个临时脚本来执行策略1
                temp_script = f"""
import sys
import os
from datetime import datetime

# 设置预测日期
os.environ['PREDICTION_DATE'] = '{prediction_date}'

# 导入策略模块
sys.path.append('.')
from stock_predictor import main

# 执行策略1
sys.argv = ['stock_predictor.py', '2', '--data', '{data_file}']
main()
"""
                with open('temp_strategy.py', 'w') as f:
                    f.write(temp_script)

                cmd = ["python", "temp_strategy.py"]
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                stdout, stderr = process.communicate()

                # 删除临时脚本
                if os.path.exists('temp_strategy.py'):
                    os.remove('temp_strategy.py')

                if process.returncode == 0:
                    self.queue.put(("output", "策略1执行成功\n"))
                    self.queue.put(("output", stdout + "\n"))
                else:
                    self.queue.put(("output", "策略1执行失败\n"))
                    self.queue.put(("output", stderr + "\n"))

            # 执行策略A
            if self.strategy_A_var.get():
                self.queue.put(("status", "正在执行策略A..."))
                self.queue.put(("output", "\n正在执行策略A...\n"))

                # 创建一个临时脚本来执行策略A
                temp_script = f"""
import sys
import os
from datetime import datetime

# 设置预测日期
os.environ['PREDICTION_DATE'] = '{prediction_date}'

# 导入策略模块
sys.path.append('.')
from stock_predictor import main

# 执行策略A
sys.argv = ['stock_predictor.py', '3', '--data', '{data_file}']
main()
"""
                with open('temp_strategy.py', 'w') as f:
                    f.write(temp_script)

                cmd = ["python", "temp_strategy.py"]
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                stdout, stderr = process.communicate()

                # 删除临时脚本
                if os.path.exists('temp_strategy.py'):
                    os.remove('temp_strategy.py')

                if process.returncode == 0:
                    self.queue.put(("output", "策略A执行成功\n"))
                    self.queue.put(("output", stdout + "\n"))
                else:
                    self.queue.put(("output", "策略A执行失败\n"))
                    self.queue.put(("output", stderr + "\n"))

            # 执行策略B
            if self.strategy_B_var.get():
                self.queue.put(("status", "正在执行策略B..."))
                self.queue.put(("output", "\n正在执行策略B...\n"))

                # 创建一个临时脚本来执行策略B
                temp_script = f"""
import sys
import os
from datetime import datetime

# 设置预测日期
os.environ['PREDICTION_DATE'] = '{prediction_date}'

# 导入策略模块
sys.path.append('.')
from stock_predictor import main

# 执行策略B
sys.argv = ['stock_predictor.py', '4', '--data', '{data_file}']
main()
"""
                with open('temp_strategy.py', 'w') as f:
                    f.write(temp_script)

                cmd = ["python", "temp_strategy.py"]
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                stdout, stderr = process.communicate()

                # 删除临时脚本
                if os.path.exists('temp_strategy.py'):
                    os.remove('temp_strategy.py')

                if process.returncode == 0:
                    self.queue.put(("output", "策略B执行成功\n"))
                    self.queue.put(("output", stdout + "\n"))
                else:
                    self.queue.put(("output", "策略B执行失败\n"))
                    self.queue.put(("output", stderr + "\n"))

            # 执行策略C
            if self.strategy_C_var.get():
                self.queue.put(("status", "正在执行策略C..."))
                self.queue.put(("output", "\n正在执行策略C...\n"))

                # 创建一个临时脚本来执行策略C
                temp_script = f"""
import sys
import os
from datetime import datetime

# 设置预测日期
os.environ['PREDICTION_DATE'] = '{prediction_date}'

# 导入策略模块
sys.path.append('.')
from stock_predictor import main

# 执行策略C
sys.argv = ['stock_predictor.py', '5', '--data', '{data_file}']
main()
"""
                with open('temp_strategy.py', 'w') as f:
                    f.write(temp_script)

                cmd = ["python", "temp_strategy.py"]
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                stdout, stderr = process.communicate()

                # 删除临时脚本
                if os.path.exists('temp_strategy.py'):
                    os.remove('temp_strategy.py')

                if process.returncode == 0:
                    self.queue.put(("output", "策略C执行成功\n"))
                    self.queue.put(("output", stdout + "\n"))
                else:
                    self.queue.put(("output", "策略C执行失败\n"))
                    self.queue.put(("output", stderr + "\n"))

            self.queue.put(("status", "执行完成"))
            self.queue.put(("progress", "stop"))
            self.queue.put(("output", "\n所有策略执行完成\n"))

            # 打开结果目录
            self.queue.put(("output", "\n结果已保存在 strategy_results 目录中\n"))

        except Exception as e:
            self.queue.put(("status", "执行出错"))
            self.queue.put(("progress", "stop"))
            self.queue.put(("output", f"\n执行出错: {str(e)}\n"))

    def process_queue(self):
        """处理队列中的消息"""
        try:
            while True:
                message_type, message = self.queue.get_nowait()

                if message_type == "output":
                    self.output_text.insert(tk.END, message)
                    self.output_text.see(tk.END)
                elif message_type == "status":
                    self.status_var.set(message)
                elif message_type == "progress" and message == "stop":
                    self.progress.stop()

        except queue.Empty:
            pass

        self.root.after(100, self.process_queue)

if __name__ == "__main__":
    # 检查依赖
    try:
        import tkcalendar
    except ImportError:
        print("缺少依赖: tkcalendar")
        print("请运行: pip install tkcalendar")
        sys.exit(1)

    root = tk.Tk()
    app = StockStrategyApp(root)
    root.mainloop()
