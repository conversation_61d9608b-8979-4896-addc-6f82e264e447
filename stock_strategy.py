"""
股票高胜率策略实现
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib

def apply_high_win_rate_strategy(predictions):
    """
    高胜率组合策略
    
    条件：
    1. 预测盈利概率>90%
    2. 技术强度=85
    3. 连续技术强度5天数≥440且≤445
    4. 看涨技术指标数量≥3
    5. 技术指标_均线多头排列=1
    6. 技术指标_MACD金叉=1
    7. 排除特定股票（指南针、西昌电力、尖峰集团）
    """
    # 筛选出满足条件的股票
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.90) &  # 条件1: 预测盈利概率>90%
        (predictions['技术强度'] == 85) &  # 条件2: 技术强度=85
        (predictions['连续技术强度5天数'] >= 440) & (predictions['连续技术强度5天数'] <= 445) &  # 条件3: 连续技术强度5天数≥440且≤445
        (predictions['看涨技术指标数量'] >= 3) &  # 条件4: 看涨技术指标数量≥3
        (predictions['技术指标_均线多头排列'] == 1) &  # 条件5: 技术指标_均线多头排列=1
        (predictions['技术指标_MACD金叉'] == 1) &  # 条件6: 技术指标_MACD金叉=1
        (predictions['股票代码'] != 'sz.300803') &  # 条件7: 排除指南针股票
        (predictions['股票代码'] != 'sh.600505') &  # 条件7: 排除西昌电力股票
        (predictions['股票代码'] != 'sh.600668')  # 条件7: 排除尖峰集团股票
    ]
    
    # 按预测盈利概率降序排序
    strategy_stocks = strategy_stocks.sort_values('预测盈利概率', ascending=False)
    
    return strategy_stocks

def backtest_strategy(data, model_data, start_date_str, end_date_str, output_file=None):
    """回测高胜率策略在指定日期范围内的表现"""
    print("\n" + " " * 24 + "回测高胜率策略")
    print("=" * 80 + "\n")
    
    # 创建结果字符串，用于输出到TXT文档
    result_str = "股票高胜率策略分析结果\n"
    result_str += "=" * 50 + "\n\n"
    result_str += "分析日期: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\n\n"
    result_str += "一、策略说明\n"
    result_str += "-" * 50 + "\n"
    result_str += "高胜率组合策略条件：\n"
    result_str += "1. 预测盈利概率>90%\n"
    result_str += "2. 技术强度=85\n"
    result_str += "3. 连续技术强度5天数≥440且≤445\n"
    result_str += "4. 看涨技术指标数量≥3\n"
    result_str += "5. 技术指标_均线多头排列=1\n"
    result_str += "6. 技术指标_MACD金叉=1\n"
    result_str += "7. 排除特定股票（指南针、西昌电力、尖峰集团）\n\n"
    
    # 获取模型信息
    model = model_data['model']
    scaler = model_data['scaler']
    features = model_data['features']
    
    result_str += f"模型训练集准确率: {model_data['train_score']:.4f}\n"
    result_str += f"模型测试集准确率: {model_data['test_score']:.4f}\n"
    result_str += f"模型特征: {', '.join(features)}\n\n"
    
    # 转换日期
    start_date = pd.to_datetime(start_date_str)
    end_date = pd.to_datetime(end_date_str)
    
    # 获取日期范围内的所有日期
    date_range = data['日期'].unique()
    date_range = sorted([d for d in date_range if start_date <= d <= end_date])
    
    if not date_range:
        error_msg = f"在{start_date_str}至{end_date_str}之间没有找到数据"
        print(error_msg)
        result_str += "错误: " + error_msg + "\n"
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result_str)
        return result_str
    
    print(f"回测日期范围: {date_range[0].strftime('%Y-%m-%d')} 至 {date_range[-1].strftime('%Y-%m-%d')}")
    print(f"共 {len(date_range)} 个交易日")
    
    result_str += "二、回测信息\n"
    result_str += "-" * 50 + "\n"
    result_str += f"回测日期范围: {date_range[0].strftime('%Y-%m-%d')} 至 {date_range[-1].strftime('%Y-%m-%d')}\n"
    result_str += f"交易日数量: {len(date_range)}\n\n"
    
    # 创建结果DataFrame
    results = []
    all_recommended_stocks = []
    
    # 对每个日期进行回测
    result_str += "三、回测结果\n"
    result_str += "-" * 50 + "\n"
    
    for test_date in date_range[:-2]:  # 排除最后两天，因为需要计算后日收益率
        test_date_str = test_date.strftime('%Y-%m-%d')
        print(f"\n回测日期: {test_date_str}")
        result_str += f"\n日期: {test_date_str}\n"
        
        # 获取当天的数据
        current_data = data[data['日期'] == test_date]
        
        # 准备预测数据
        try:
            # 提取特征
            X_pred = current_data[features]
            
            # 处理预测数据中的缺失值
            X_pred = X_pred.fillna(0)
            
            # 标准化特征
            X_pred_scaled = scaler.transform(X_pred)
            
            # 预测盈利概率
            try:
                # 尝试使用模型预测
                if model is not None:
                    pred_proba = model.predict_proba(X_pred_scaled)[:, 1]
                else:
                    # 如果模型为空，使用技术强度作为预测概率
                    pred_proba = current_data['技术强度'].values / 100
            except Exception as e:
                print(f"预测失败，使用技术强度作为预测概率: {e}")
                # 使用技术强度作为预测概率
                pred_proba = current_data['技术强度'].values / 100
            
            # 创建预测结果DataFrame
            predictions = pd.DataFrame({
                '日期': test_date_str,
                '股票代码': current_data['股票代码'].values,
                '股票名称': current_data['股票名称'].values,
                '技术强度': current_data['技术强度'].values,
                '连续技术强度5天数': current_data['连续技术强度5天数'].values,
                '技术强度趋势': current_data['技术强度趋势'].values,
                '价格趋势': current_data['价格趋势'].values,
                '涨跌幅趋势': current_data['涨跌幅趋势'].values,
                '涨跌幅': current_data['涨跌幅'].values,
                '技术指标_均线多头排列': current_data['技术指标_均线多头排列'].values,
                '技术指标_MACD金叉': current_data['技术指标_MACD金叉'].values,
                '技术指标_RSI反弹': current_data['技术指标_RSI反弹'].values,
                '技术指标_KDJ金叉': current_data['技术指标_KDJ金叉'].values,
                '技术指标_布林带突破': current_data['技术指标_布林带突破'].values,
                '看涨技术指标数量': current_data['看涨技术指标数量'].values,
                '开盘涨跌': current_data['开盘涨跌'].values,
                '次日买后日卖收益率': current_data['次日买后日卖收益率'].values,
                '预测盈利概率': pred_proba
            })
            
            # 应用高胜率组合策略
            strategy_stocks = apply_high_win_rate_strategy(predictions)
            
            # 计算胜率和平均收益率
            if len(strategy_stocks) > 0:
                # 计算实际是否盈利
                strategy_stocks['实际是否盈利'] = (strategy_stocks['次日买后日卖收益率'] > 0).astype(int)
                
                # 只考虑开盘上涨的股票
                open_up_stocks = strategy_stocks[strategy_stocks['开盘涨跌'] > 0]
                
                if len(open_up_stocks) > 0:
                    win_rate_open_up = open_up_stocks['实际是否盈利'].mean() * 100
                    avg_return_open_up = open_up_stocks['次日买后日卖收益率'].mean() * 100
                    
                    print(f"推荐股票数: {len(strategy_stocks)}")
                    print(f"开盘上涨的股票数: {len(open_up_stocks)}")
                    print(f"开盘上涨股票的胜率: {win_rate_open_up:.2f}%")
                    print(f"开盘上涨股票的平均收益率: {avg_return_open_up:.2f}%")
                    
                    result_str += f"推荐股票数: {len(strategy_stocks)}\n"
                    result_str += f"开盘上涨的股票数: {len(open_up_stocks)}\n"
                    result_str += f"开盘上涨股票的胜率: {win_rate_open_up:.2f}%\n"
                    result_str += f"开盘上涨股票的平均收益率: {avg_return_open_up:.2f}%\n\n"
                    
                    # 添加到结果
                    results.append({
                        '日期': test_date_str,
                        '推荐股票数': len(strategy_stocks),
                        '开盘上涨的股票数': len(open_up_stocks),
                        '开盘上涨股票的胜率': win_rate_open_up,
                        '开盘上涨股票的平均收益率': avg_return_open_up
                    })
                    
                    # 保存推荐股票及其结果
                    all_recommended_stocks.append(open_up_stocks)
                    
                    # 显示所有盈利的股票的详细信息
                    profitable_stocks = open_up_stocks[open_up_stocks['实际是否盈利'] == 1]
                    if len(profitable_stocks) > 0:
                        print(f"\n{test_date_str} 盈利的股票:")
                        result_str += f"盈利的股票:\n"
                        for i, row in profitable_stocks.iterrows():
                            stock_info = f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 看涨技术指标数量={row['看涨技术指标数量']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%, 实际收益率={row['次日买后日卖收益率']*100:.2f}%"
                            print(stock_info)
                            result_str += stock_info + "\n"
                        result_str += "\n"
                    
                    # 显示所有亏损的股票的详细信息
                    losing_stocks = open_up_stocks[open_up_stocks['实际是否盈利'] == 0]
                    if len(losing_stocks) > 0:
                        print(f"\n{test_date_str} 亏损的股票:")
                        result_str += f"亏损的股票:\n"
                        for i, row in losing_stocks.iterrows():
                            stock_info = f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 看涨技术指标数量={row['看涨技术指标数量']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%, 实际收益率={row['次日买后日卖收益率']*100:.2f}%"
                            print(stock_info)
                            result_str += stock_info + "\n"
                        result_str += "\n"
                else:
                    print(f"推荐股票数: {len(strategy_stocks)}")
                    print("没有开盘上涨的股票")
                    result_str += f"推荐股票数: {len(strategy_stocks)}\n"
                    result_str += "没有开盘上涨的股票\n\n"
            else:
                print("没有推荐的股票")
                result_str += "没有推荐的股票\n\n"
        
        except Exception as e:
            error_msg = f"回测失败: {e}"
            print(error_msg)
            result_str += "错误: " + error_msg + "\n\n"
            import traceback
            traceback.print_exc()
    
    # 创建结果DataFrame
    if results:
        results_df = pd.DataFrame(results)
        
        # 计算整体表现
        overall_win_rate = results_df['开盘上涨股票的胜率'].mean()
        overall_return = results_df['开盘上涨股票的平均收益率'].mean()
        total_stocks = results_df['推荐股票数'].sum()
        total_open_up_stocks = results_df['开盘上涨的股票数'].sum()
        
        print("\n整体表现:")
        print(f"总推荐股票数: {total_stocks}")
        print(f"总开盘上涨的股票数: {total_open_up_stocks}")
        print(f"开盘上涨股票的平均胜率: {overall_win_rate:.2f}%")
        print(f"开盘上涨股票的平均收益率: {overall_return:.2f}%")
        
        result_str += "四、整体表现\n"
        result_str += "-" * 50 + "\n"
        result_str += f"总推荐股票数: {total_stocks}\n"
        result_str += f"总开盘上涨的股票数: {total_open_up_stocks}\n"
        result_str += f"开盘上涨股票的平均胜率: {overall_win_rate:.2f}%\n"
        result_str += f"开盘上涨股票的平均收益率: {overall_return:.2f}%\n\n"
        
        # 保存结果
        if not os.path.exists('backtest_results'):
            os.makedirs('backtest_results')
        
        # 保存回测结果
        result_file = f'backtest_results/高胜率策略回测结果_{start_date_str}至{end_date_str}.xlsx'
        results_df.to_excel(result_file, index=False)
        
        # 保存所有推荐股票及其结果
        if all_recommended_stocks:
            all_stocks_df = pd.concat(all_recommended_stocks)
            all_stocks_file = f'backtest_results/高胜率策略推荐股票_{start_date_str}至{end_date_str}.xlsx'
            all_stocks_df.to_excel(all_stocks_file, index=False)
        
        print(f"\n回测结果已保存至: {result_file}")
        result_str += f"回测结果已保存至: {result_file}\n\n"
    else:
        print("\n没有回测结果")
        result_str += "没有回测结果\n"
    
    # 输出结果到TXT文档
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result_str)
        print(f"\n分析结果已保存至: {output_file}")
    
    return result_str
