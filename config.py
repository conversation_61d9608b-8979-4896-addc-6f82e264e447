"""
配置文件

用于存储程序的配置信息，如数据文件夹路径等
"""

import os

# 默认数据文件夹路径
DEFAULT_DATA_DIR = r'E:\机器学习\complete_excel_results'
DEFAULT_STOCK_DATA_DIR = os.path.join(DEFAULT_DATA_DIR, 'stock_data')
DEFAULT_DAILY_DATA_DIR = os.path.join(DEFAULT_STOCK_DATA_DIR, 'daily')
DEFAULT_STOCK_DETAILS_FILE = os.path.join(DEFAULT_DATA_DIR, '股票明细_完整.xlsx')
DEFAULT_HISTORY_DATA_FILE = os.path.join(DEFAULT_STOCK_DATA_DIR, 'stock_history_data.xlsx')

# 当前使用的数据文件夹路径
DATA_DIR = DEFAULT_DATA_DIR
STOCK_DATA_DIR = DEFAULT_STOCK_DATA_DIR
DAILY_DATA_DIR = DEFAULT_DAILY_DATA_DIR
STOCK_DETAILS_FILE = DEFAULT_STOCK_DETAILS_FILE
HISTORY_DATA_FILE = DEFAULT_HISTORY_DATA_FILE

def set_data_dir(data_dir):
    """
    设置数据文件夹路径
    
    参数:
        data_dir: 数据文件夹路径
        
    返回:
        dict: 包含所有更新后路径的字典
    """
    global DATA_DIR, STOCK_DATA_DIR, DAILY_DATA_DIR, STOCK_DETAILS_FILE, HISTORY_DATA_FILE
    
    # 更新数据文件夹路径
    DATA_DIR = data_dir
    STOCK_DATA_DIR = os.path.join(DATA_DIR, 'stock_data')
    DAILY_DATA_DIR = os.path.join(STOCK_DATA_DIR, 'daily')
    STOCK_DETAILS_FILE = os.path.join(DATA_DIR, '股票明细_完整.xlsx')
    HISTORY_DATA_FILE = os.path.join(STOCK_DATA_DIR, 'stock_history_data.xlsx')
    
    # 确保目录存在
    if not os.path.exists(STOCK_DATA_DIR):
        os.makedirs(STOCK_DATA_DIR)
    if not os.path.exists(DAILY_DATA_DIR):
        os.makedirs(DAILY_DATA_DIR)
        
    # 返回更新后的路径
    return {
        'data_dir': DATA_DIR,
        'stock_data_dir': STOCK_DATA_DIR,
        'daily_data_dir': DAILY_DATA_DIR,
        'stock_details_file': STOCK_DETAILS_FILE,
        'history_data_file': HISTORY_DATA_FILE
    }

def get_data_paths():
    """
    获取当前使用的数据文件夹路径
    
    返回:
        dict: 包含所有路径的字典
    """
    return {
        'data_dir': DATA_DIR,
        'stock_data_dir': STOCK_DATA_DIR,
        'daily_data_dir': DAILY_DATA_DIR,
        'stock_details_file': STOCK_DETAILS_FILE,
        'history_data_file': HISTORY_DATA_FILE
    }
