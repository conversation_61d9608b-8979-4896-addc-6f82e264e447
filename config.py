"""
配置文件

用于存储程序的配置信息，如数据文件夹路径等
"""

import os
import sys

def get_program_dir():
    """获取程序所在目录"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        return os.path.dirname(sys.executable)
    else:
        # 如果是开发环境
        return os.path.dirname(os.path.abspath(__file__))

def detect_data_dir():
    """智能检测数据目录"""
    program_dir = get_program_dir()

    # 优先检查程序包内的数据目录
    package_data_dir = os.path.join(program_dir, 'complete_excel_results')
    if os.path.exists(package_data_dir):
        return package_data_dir

    # 检查程序同级目录的数据目录
    sibling_data_dir = os.path.join(os.path.dirname(program_dir), 'complete_excel_results')
    if os.path.exists(sibling_data_dir):
        return sibling_data_dir

    # 回退到开发环境路径
    dev_data_dir = r'E:\机器学习\complete_excel_results'
    if os.path.exists(dev_data_dir):
        return dev_data_dir

    # 如果都不存在，返回程序包内路径（即使不存在，也为后续创建做准备）
    return package_data_dir

# 智能检测数据文件夹路径
DEFAULT_DATA_DIR = detect_data_dir()
DEFAULT_STOCK_DATA_DIR = os.path.join(DEFAULT_DATA_DIR, 'stock_data')
DEFAULT_DAILY_DATA_DIR = os.path.join(DEFAULT_STOCK_DATA_DIR, 'daily')
DEFAULT_STOCK_DETAILS_FILE = os.path.join(DEFAULT_DATA_DIR, '股票明细_完整.xlsx')
DEFAULT_HISTORY_DATA_FILE = os.path.join(DEFAULT_STOCK_DATA_DIR, 'stock_history_data.xlsx')

# 调试信息：打印检测到的路径
print(f"🔍 程序目录: {get_program_dir()}")
print(f"📁 检测到的数据目录: {DEFAULT_DATA_DIR}")
print(f"📊 策略汇总文件: {os.path.join(DEFAULT_DATA_DIR, '所有策略汇总_已回测.xlsx')}")
print(f"📋 文件是否存在: {os.path.exists(os.path.join(DEFAULT_DATA_DIR, '所有策略汇总_已回测.xlsx'))}")

# 当前使用的数据文件夹路径
DATA_DIR = DEFAULT_DATA_DIR
STOCK_DATA_DIR = DEFAULT_STOCK_DATA_DIR
DAILY_DATA_DIR = DEFAULT_DAILY_DATA_DIR
STOCK_DETAILS_FILE = DEFAULT_STOCK_DETAILS_FILE
HISTORY_DATA_FILE = DEFAULT_HISTORY_DATA_FILE

def set_data_dir(data_dir):
    """
    设置数据文件夹路径
    
    参数:
        data_dir: 数据文件夹路径
        
    返回:
        dict: 包含所有更新后路径的字典
    """
    global DATA_DIR, STOCK_DATA_DIR, DAILY_DATA_DIR, STOCK_DETAILS_FILE, HISTORY_DATA_FILE
    
    # 更新数据文件夹路径
    DATA_DIR = data_dir
    STOCK_DATA_DIR = os.path.join(DATA_DIR, 'stock_data')
    DAILY_DATA_DIR = os.path.join(STOCK_DATA_DIR, 'daily')
    STOCK_DETAILS_FILE = os.path.join(DATA_DIR, '股票明细_完整.xlsx')
    HISTORY_DATA_FILE = os.path.join(STOCK_DATA_DIR, 'stock_history_data.xlsx')
    
    # 确保目录存在
    if not os.path.exists(STOCK_DATA_DIR):
        os.makedirs(STOCK_DATA_DIR)
    if not os.path.exists(DAILY_DATA_DIR):
        os.makedirs(DAILY_DATA_DIR)
        
    # 返回更新后的路径
    return {
        'data_dir': DATA_DIR,
        'stock_data_dir': STOCK_DATA_DIR,
        'daily_data_dir': DAILY_DATA_DIR,
        'stock_details_file': STOCK_DETAILS_FILE,
        'history_data_file': HISTORY_DATA_FILE
    }

def get_data_paths():
    """
    获取当前使用的数据文件夹路径
    
    返回:
        dict: 包含所有路径的字典
    """
    return {
        'data_dir': DATA_DIR,
        'stock_data_dir': STOCK_DATA_DIR,
        'daily_data_dir': DAILY_DATA_DIR,
        'stock_details_file': STOCK_DETAILS_FILE,
        'history_data_file': HISTORY_DATA_FILE
    }
