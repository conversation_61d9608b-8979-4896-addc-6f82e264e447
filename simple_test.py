"""
简化版测试脚本，尽可能接近原始代码的实现
"""

import pandas as pd
import baostock as bs
import os
import datetime
import time
import threading
import queue
import stock_data_manager as sdm

# 设置文件路径
base_dir = r'E:\机器学习\complete_excel_results'
stock_data_dir = os.path.join(base_dir, 'stock_data')
daily_data_dir = os.path.join(stock_data_dir, 'daily')
stock_details_file = os.path.join(base_dir, '股票明细_完整.xlsx')
history_data_file = os.path.join(stock_data_dir, 'stock_history_data.xlsx')

# 确保目录存在
if not os.path.exists(stock_data_dir):
    os.makedirs(stock_data_dir)
if not os.path.exists(daily_data_dir):
    os.makedirs(daily_data_dir)

def download_stock_data(stock_code, start_date, end_date):
    """下载单只股票的历史数据"""
    rs = bs.query_history_k_data_plus(
        stock_code,
        "date,code,open,high,low,close,volume,amount,pctChg",
        start_date=start_date,
        end_date=end_date,
        frequency="d",
        adjustflag="3"  # 3表示前复权
    )
    
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    
    if data_list:
        df = pd.DataFrame(data_list, columns=rs.fields)
        # 转换数据类型
        for field in ['open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']:
            if field in df.columns:
                df[field] = pd.to_numeric(df[field], errors='coerce')
        return df
    
    return None

def get_all_stock_codes():
    """获取所有股票代码"""
    try:
        # 尝试从股票明细文件中读取股票代码
        stock_df = pd.read_excel(stock_details_file)
        if '股票代码' in stock_df.columns:
            stock_codes = stock_df['股票代码'].tolist()
            return stock_codes
    except Exception as e:
        print(f"读取股票明细文件时出错: {e}")
    
    # 如果无法从文件中读取，则获取上证和深证所有股票列表
    rs = bs.query_all_stock(day=datetime.datetime.now().strftime('%Y-%m-%d'))
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    stock_list_df = pd.DataFrame(data_list, columns=rs.fields)
    stock_codes = stock_list_df['code'].tolist()
    print(f"使用默认股票列表，共 {len(stock_codes)} 只股票")
    
    return stock_codes

def download_trading_calendar(start_date, end_date):
    """下载交易日历"""
    rs = bs.query_trade_dates(start_date=start_date, end_date=end_date)
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    calendar_df = pd.DataFrame(data_list, columns=rs.fields)
    
    # 转换日期列为日期类型
    calendar_df['calendar_date'] = pd.to_datetime(calendar_df['calendar_date'])
    
    # 转换交易日标志为整数
    calendar_df['is_trading_day'] = calendar_df['is_trading_day'].apply(lambda x: 1 if x == '1' else 0)
    
    return calendar_df

def test_download_speed(test_date, num_threads=30, max_stocks=None):
    """测试下载速度"""
    print(f"开始测试日期 {test_date} 的下载速度，使用 {num_threads} 个线程")
    
    # 记录开始时间
    total_start_time = time.time()
    
    # 登录baostock
    print("登录baostock...")
    bs_login = bs.login()
    print(f"登录状态: {bs_login.error_code}, {bs_login.error_msg}")
    
    try:
        # 获取所有股票代码
        stock_codes = get_all_stock_codes()
        print(f"共找到 {len(stock_codes)} 只股票")
        
        # 限制股票数量
        if max_stocks is not None and max_stocks > 0 and max_stocks < len(stock_codes):
            stock_codes = stock_codes[:max_stocks]
            print(f"限制测试股票数量为 {max_stocks} 只")
        
        # 下载交易日历
        calendar_df = download_trading_calendar(test_date, test_date)
        print(f"交易日历下载完成，共 {len(calendar_df)} 天")
        
        # 检查是否为交易日
        is_trading_day = True
        if 'is_trading_day' in calendar_df.columns:
            day_trading_status = calendar_df[calendar_df['calendar_date'] == pd.to_datetime(test_date)]
            if not day_trading_status.empty and day_trading_status['is_trading_day'].iloc[0] == 0:
                is_trading_day = False
                print(f"日期 {test_date} 不是交易日，但仍继续测试")
        
        # 创建线程安全的队列
        result_queue = queue.Queue()
        
        # 定义线程函数
        def download_batch(batch_codes, thread_id):
            batch_results = []
            batch_success = 0
            batch_error = 0
            batch_start_time = time.time()
            
            for i, code in enumerate(batch_codes):
                try:
                    # 下载单只股票单日数据
                    stock_data = download_stock_data(code, test_date, test_date)
                    if stock_data is not None and not stock_data.empty:
                        batch_results.append(stock_data)
                        batch_success += 1
                except Exception as e:
                    batch_error += 1
            
            # 记录该批次的结束时间
            batch_end_time = time.time()
            batch_elapsed_time = batch_end_time - batch_start_time
            
            # 将结果放入队列
            result_queue.put((batch_results, batch_success, batch_error, batch_elapsed_time))
            print(f"线程{thread_id}: 完成 {len(batch_codes)} 只股票的下载，成功: {batch_success}，失败: {batch_error}，耗时: {batch_elapsed_time:.2f}秒")
        
        # 创建并启动线程
        threads = []
        stocks_per_thread = len(stock_codes) // num_threads
        
        print(f"开始下载日期 {test_date} 的股票数据，共 {len(stock_codes)} 只股票")
        print(f"将使用 {num_threads} 个线程并行下载，每个线程处理约 {stocks_per_thread} 只股票")
        
        for i in range(num_threads):
            # 计算每个线程处理的股票范围
            start_idx = i * stocks_per_thread
            # 最后一个线程处理剩余的所有股票
            end_idx = len(stock_codes) if i == num_threads - 1 else (i + 1) * stocks_per_thread
            batch_codes = stock_codes[start_idx:end_idx]
            
            thread = threading.Thread(target=download_batch, args=(batch_codes, i+1))
            thread.daemon = True
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 收集所有线程的结果
        day_stock_data = []
        success_count = 0
        error_count = 0
        thread_elapsed_times = []
        
        while not result_queue.empty():
            batch_results, batch_success, batch_error, batch_elapsed_time = result_queue.get()
            day_stock_data.extend(batch_results)
            success_count += batch_success
            error_count += batch_error
            thread_elapsed_times.append(batch_elapsed_time)
        
        # 记录总结束时间
        total_end_time = time.time()
        total_elapsed_time = total_end_time - total_start_time
        
        # 打印性能统计
        print("\n性能测试结果:")
        print(f"总耗时: {total_elapsed_time:.2f}秒 ({total_elapsed_time/60:.2f}分钟)")
        print(f"总记录数: {len(day_stock_data)}")
        print(f"成功数: {success_count}")
        print(f"失败数: {error_count}")
        if success_count > 0:
            print(f"平均每只股票耗时: {total_elapsed_time/success_count:.4f}秒 (仅计算成功的)")
        if thread_elapsed_times:
            print(f"平均线程耗时: {sum(thread_elapsed_times)/len(thread_elapsed_times):.2f}秒")
            print(f"最长线程耗时: {max(thread_elapsed_times):.2f}秒")
            print(f"最短线程耗时: {min(thread_elapsed_times):.2f}秒")
        
        # 合并并保存该日期的所有股票数据
        if day_stock_data:
            day_combined_df = pd.concat(day_stock_data, ignore_index=True)
            print(f"日期 {test_date} 的股票数据合并完成，共 {len(day_combined_df)} 条记录")
            
            # 保存到按日期存储的文件
            save_start_time = time.time()
            sdm.save_daily_data(day_combined_df, test_date)
            save_end_time = time.time()
            save_elapsed_time = save_end_time - save_start_time
            
            print(f"日期 {test_date} 的股票数据已保存，耗时: {save_elapsed_time:.2f}秒")
        else:
            print(f"日期 {test_date} 没有下载到任何股票数据")
    
    finally:
        # 登出baostock
        bs.logout()
        print("已登出baostock")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='测试股票数据下载速度')
    parser.add_argument('--date', type=str, default='2025-02-05', help='测试日期，格式为YYYY-MM-DD')
    parser.add_argument('--threads', type=int, default=30, help='线程数量')
    parser.add_argument('--max_stocks', type=int, default=None, help='最大测试股票数量')
    
    args = parser.parse_args()
    
    # 执行测试
    test_download_speed(args.date, args.threads, args.max_stocks)
