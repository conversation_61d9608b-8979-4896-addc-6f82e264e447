#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特征组合策略总收益可视化
作者: Augment AI
版本: 1.0.0

该脚本可视化不同特征组合策略在整个回测周期内的总收益。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 设置字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
except:
    print("无法设置中文字体，使用默认字体")

# 创建结果目录
results_dir = 'visualization_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data():
    """加载数据"""
    try:
        # 加载总收益数据
        total_return_df = pd.read_csv('total_return_results/top_combinations_total_return.csv')

        # 加载资金曲线数据
        equity_df = pd.read_csv('total_return_results/equity_curves.csv')
        equity_df['日期'] = pd.to_datetime(equity_df['日期'])

        return total_return_df, equity_df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None, None

def plot_total_returns(total_return_df):
    """绘制总收益率柱状图"""
    print("绘制总收益率柱状图...")

    try:
        # 创建图形
        plt.figure(figsize=(12, 8))

        # 提取数据
        strategies = [f"{row[1]['特征数量']}特征组合_{i+1}" for i, row in enumerate(total_return_df.iterrows())]
        returns = total_return_df['总收益率(%)'].values
        win_rates = total_return_df['胜率(%)'].values

        # 绘制柱状图
        bars = plt.bar(strategies, returns, color='skyblue', alpha=0.7)

        # 添加数据标签
        for bar, win_rate in zip(bars, win_rates):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{height:.2f}%\n胜率:{win_rate:.1f}%',
                    ha='center', va='bottom', fontsize=9)

        # 设置标题和标签
        plt.title('Different Feature Combination Strategies Total Return', fontsize=16)
        plt.xlabel('Strategy', fontsize=12)
        plt.ylabel('Total Return (%)', fontsize=12)
        plt.xticks(rotation=45, ha='right')
        plt.grid(axis='y', linestyle='--', alpha=0.7)

        # 调整布局
        plt.tight_layout()

        # 保存图形
        plt.savefig(f"{results_dir}/total_returns.png", dpi=300)
        print(f"总收益率柱状图已保存到 {results_dir}/total_returns.png")

        plt.close()
    except Exception as e:
        print(f"绘制总收益率柱状图时出错: {e}")

def plot_equity_curves(equity_df):
    """绘制资金曲线"""
    print("绘制资金曲线...")

    try:
        # 创建图形
        plt.figure(figsize=(14, 8))

        # 获取所有策略
        strategies = equity_df['策略'].unique()

        # 绘制每个策略的资金曲线
        for strategy in strategies:
            strategy_data = equity_df[equity_df['策略'] == strategy]
            plt.plot(strategy_data['日期'], strategy_data['收益率(%)'], label=strategy, linewidth=2)

        # 设置标题和标签
        plt.title('Equity Curves of Different Feature Combination Strategies', fontsize=16)
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Return (%)', fontsize=12)
        plt.grid(linestyle='--', alpha=0.7)

        # 添加图例
        plt.legend(loc='upper left', bbox_to_anchor=(1, 1))

        # 调整布局
        plt.tight_layout()

        # 保存图形
        plt.savefig(f"{results_dir}/equity_curves.png", dpi=300)
        print(f"资金曲线已保存到 {results_dir}/equity_curves.png")

        plt.close()
    except Exception as e:
        print(f"绘制资金曲线时出错: {e}")

def plot_feature_count_comparison(total_return_df):
    """绘制不同特征数量的表现比较"""
    print("绘制不同特征数量的表现比较...")

    try:
        # 按特征数量分组
        grouped = total_return_df.groupby('特征数量')

        # 计算每组的平均值
        avg_by_count = grouped.agg({
            '总收益率(%)': 'mean',
            '胜率(%)': 'mean',
            '平均每日收益率(%)': 'mean',
            '交易频率(%)': 'mean'
        }).reset_index()

        # 创建图形
        _, axes = plt.subplots(2, 2, figsize=(14, 10))

        # 绘制总收益率
        axes[0, 0].bar(avg_by_count['特征数量'], avg_by_count['总收益率(%)'], color='skyblue')
        axes[0, 0].set_title('Average Total Return')
        axes[0, 0].set_xlabel('Feature Count')
        axes[0, 0].set_ylabel('Total Return (%)')
        axes[0, 0].grid(axis='y', linestyle='--', alpha=0.7)

        # 绘制胜率
        axes[0, 1].bar(avg_by_count['特征数量'], avg_by_count['胜率(%)'], color='lightgreen')
        axes[0, 1].set_title('Average Win Rate')
        axes[0, 1].set_xlabel('Feature Count')
        axes[0, 1].set_ylabel('Win Rate (%)')
        axes[0, 1].grid(axis='y', linestyle='--', alpha=0.7)

        # 绘制平均每日收益率
        axes[1, 0].bar(avg_by_count['特征数量'], avg_by_count['平均每日收益率(%)'], color='salmon')
        axes[1, 0].set_title('Average Daily Return')
        axes[1, 0].set_xlabel('Feature Count')
        axes[1, 0].set_ylabel('Daily Return (%)')
        axes[1, 0].grid(axis='y', linestyle='--', alpha=0.7)

        # 绘制交易频率
        axes[1, 1].bar(avg_by_count['特征数量'], avg_by_count['交易频率(%)'], color='gold')
        axes[1, 1].set_title('Average Trading Frequency')
        axes[1, 1].set_xlabel('Feature Count')
        axes[1, 1].set_ylabel('Trading Frequency (%)')
        axes[1, 1].grid(axis='y', linestyle='--', alpha=0.7)

        # 设置总标题
        plt.suptitle('Performance Comparison of Different Feature Counts', fontsize=16)

        # 调整布局
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)

        # 保存图形
        plt.savefig(f"{results_dir}/feature_count_comparison.png", dpi=300)
        print(f"不同特征数量的表现比较已保存到 {results_dir}/feature_count_comparison.png")

        plt.close()
    except Exception as e:
        print(f"绘制不同特征数量的表现比较时出错: {e}")

def plot_top_strategies_daily_returns(equity_df):
    """绘制表现最佳的策略每日收益率"""
    print("绘制表现最佳的策略每日收益率...")

    try:
        # 计算每日收益率变化
        strategies = equity_df['策略'].unique()
        daily_returns = []

        for strategy in strategies:
            strategy_data = equity_df[equity_df['策略'] == strategy].sort_values('日期')

            # 计算每日收益率变化
            strategy_data['日收益率(%)'] = strategy_data['收益率(%)'].diff()

            # 第一天的收益率就是当天的收益率
            if not strategy_data.empty:
                strategy_data.loc[strategy_data.index[0], '日收益率(%)'] = strategy_data.iloc[0]['收益率(%)']

            daily_returns.append(strategy_data)

        # 合并数据
        daily_returns_df = pd.concat(daily_returns)

        # 选择前3个策略
        top_strategies = ['3特征_技术强度趋势_RSI反弹_KDJ金叉',
                        '4特征_技术强度趋势_连续技术强度5天数趋势_RSI反弹_KDJ金叉',
                        '2特征_技术强度趋势_KDJ金叉']

        # 筛选数据
        top_data = daily_returns_df[daily_returns_df['策略'].isin(top_strategies)]

        # 创建图形
        plt.figure(figsize=(14, 8))

        # 绘制每日收益率
        for strategy in top_strategies:
            strategy_data = top_data[top_data['策略'] == strategy]
            plt.plot(strategy_data['日期'], strategy_data['日收益率(%)'], label=strategy, marker='o', markersize=5)

        # 设置标题和标签
        plt.title('Daily Returns of Top Performing Strategies', fontsize=16)
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Daily Return (%)', fontsize=12)
        plt.grid(linestyle='--', alpha=0.7)

        # 添加图例
        plt.legend()

        # 调整布局
        plt.tight_layout()

        # 保存图形
        plt.savefig(f"{results_dir}/top_strategies_daily_returns.png", dpi=300)
        print(f"表现最佳的策略每日收益率已保存到 {results_dir}/top_strategies_daily_returns.png")

        plt.close()
    except Exception as e:
        print(f"绘制表现最佳的策略每日收益率时出错: {e}")

def generate_summary_table(total_return_df):
    """生成汇总表格"""
    print("生成汇总表格...")

    try:
        # 创建HTML表格
        html = """
        <html>
        <head>
            <style>
                table {
                    border-collapse: collapse;
                    width: 100%;
                    font-family: Arial, sans-serif;
                }
                th, td {
                    border: 1px solid #dddddd;
                    text-align: left;
                    padding: 8px;
                }
                th {
                    background-color: #f2f2f2;
                }
                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
                .highlight {
                    background-color: #e6f7ff;
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            <h2>Feature Combination Strategies Total Return Summary</h2>
            <table>
                <tr>
                    <th>Rank</th>
                    <th>Feature Combination</th>
                    <th>Feature Count</th>
                    <th>Total Return (%)</th>
                    <th>Win Rate (%)</th>
                    <th>Avg Daily Return (%)</th>
                    <th>Trading Days/Total Days</th>
                    <th>Trading Frequency (%)</th>
                    <th>Total Trades</th>
                    <th>Avg Daily Trades</th>
                </tr>
        """

        # 添加每行数据
        for i, (_, row) in enumerate(total_return_df.iterrows()):
            highlight = ' class="highlight"' if i < 3 else ''
            html += f"""
                <tr{highlight}>
                    <td>{i+1}</td>
                    <td>{row['特征组合']}</td>
                    <td>{row['特征数量']}</td>
                    <td>{row['总收益率(%)']:.2f}</td>
                    <td>{row['胜率(%)']:.2f}</td>
                    <td>{row['平均每日收益率(%)']:.2f}</td>
                    <td>{int(row['交易天数'])}/{int(row['总天数'])}</td>
                    <td>{row['交易频率(%)']:.2f}</td>
                    <td>{int(row['总交易次数'])}</td>
                    <td>{row['平均每日交易数']:.2f}</td>
                </tr>
            """

        # 关闭HTML
        html += """
            </table>
        </body>
        </html>
        """

        # 保存HTML表格
        with open(f"{results_dir}/summary_table.html", 'w', encoding='utf-8') as f:
            f.write(html)

        print(f"汇总表格已保存到 {results_dir}/summary_table.html")
    except Exception as e:
        print(f"生成汇总表格时出错: {e}")

if __name__ == "__main__":
    # 加载数据
    total_return_df, equity_df = load_data()
    if total_return_df is None or equity_df is None:
        exit(1)

    # 绘制总收益率柱状图
    plot_total_returns(total_return_df)

    # 绘制资金曲线
    plot_equity_curves(equity_df)

    # 绘制不同特征数量的表现比较
    plot_feature_count_comparison(total_return_df)

    # 绘制表现最佳的策略每日收益率
    plot_top_strategies_daily_returns(equity_df)

    # 生成汇总表格
    generate_summary_table(total_return_df)

    print("可视化完成！")
