#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成策略详细分析Excel文件
作者: Augment AI
版本: 1.0.0

该脚本用于生成策略详细分析Excel文件，包含策略统计、策略条件详情、每日表现和交易记录。
"""

import os
import pandas as pd
from datetime import datetime

def create_strategy_detail_excel(strategy_index, output_file, main_excel_file):
    """
    创建策略详细分析Excel文件

    参数:
        strategy_index (int): 策略编号
        output_file (str): 输出文件路径
        main_excel_file (str): 主Excel文件路径
    """
    print(f"正在创建策略详细分析Excel文件: {output_file}")

    try:
        # 读取主Excel文件中的策略条件表
        conditions_df = pd.read_excel(main_excel_file, sheet_name='策略条件')

        # 查找对应的策略
        strategy = conditions_df[conditions_df['策略编号'] == strategy_index]

        if len(strategy) == 0:
            print(f"未找到策略编号为 {strategy_index} 的策略")
            return False

        # 获取策略信息
        strategy_info = strategy.iloc[0]

        # 创建Excel写入器
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 创建策略统计信息表格
            create_strategy_stats_sheet(strategy_info, writer)

            # 创建策略条件详情表格
            create_strategy_conditions_detail_sheet(strategy_info, writer)

            # 创建模拟每日表现数据表格
            create_daily_performance_sheet(strategy_info, writer)

            # 创建模拟交易记录表格
            create_trades_sheet(strategy_info, writer)

        print(f"策略详细分析Excel文件创建完成: {output_file}")
        return True
    except Exception as e:
        print(f"创建策略详细分析Excel文件时出错: {str(e)}")
        return False

def create_strategy_stats_sheet(strategy_info, writer):
    """
    创建策略统计信息表格

    参数:
        strategy_info (Series): 策略信息
        writer: Excel写入器
    """
    # 创建策略统计信息
    stats_data = {
        '统计项': [
            '策略编号',
            '策略组合',
            '特征数量',
            '总收益率(%)',
            '平均胜率(%)',
            '平均每日交易笔数',
            '总交易笔数',
            '交易天数',
            '总天数',
            '交易频率(%)'
        ],
        '数值': [
            strategy_info['策略编号'],
            strategy_info['策略组合'],
            strategy_info['特征数量'],
            strategy_info['总收益率(%)'],
            strategy_info['平均胜率(%)'],
            5,  # 模拟平均每日交易笔数
            150,  # 模拟总交易笔数
            30,  # 模拟交易天数
            30,  # 模拟总天数
            100  # 模拟交易频率(%)
        ]
    }

    # 转换为DataFrame
    stats_df = pd.DataFrame(stats_data)

    # 写入Excel
    stats_df.to_excel(writer, sheet_name='策略统计', index=False)

def create_strategy_conditions_detail_sheet(strategy_info, writer):
    """
    创建策略条件详情表格

    参数:
        strategy_info (Series): 策略信息
        writer: Excel写入器
    """
    # 解析策略条件
    conditions_str = strategy_info['策略条件描述']
    conditions_list = conditions_str.split(' AND ')

    # 解析策略组合
    features_str = strategy_info['策略组合']
    features_list = features_str.split(', ')

    # 创建条件列表
    conditions_data = []
    for i, condition in enumerate(conditions_list):
        feature = features_list[i] if i < len(features_list) else ""

        if '大于等于' in condition:
            feature, threshold = condition.split('大于等于')
            feature = feature.strip()
            threshold = threshold.strip()
            condition_str = f">= {threshold}"
        elif '为 1（是）' in condition:
            feature = condition.split('为')[0].strip()
            condition_str = "== 1"
        else:
            condition_str = condition

        conditions_data.append({
            '特征': feature,
            '条件': condition_str,
            '描述': condition
        })

    # 转换为DataFrame
    conditions_df = pd.DataFrame(conditions_data)

    # 写入Excel
    conditions_df.to_excel(writer, sheet_name='策略条件详情', index=False)

def create_daily_performance_sheet(strategy_info, writer):
    """
    创建模拟每日表现数据表格

    参数:
        strategy_info (Series): 策略信息
        writer: Excel写入器
    """
    # 生成模拟每日表现数据
    daily_data = []

    # 生成30天的每日表现数据
    start_date = datetime(2025, 4, 1)
    initial_capital = 1000000
    total_return = strategy_info['总收益率(%)']
    daily_return = total_return / 30

    # 生成资金曲线
    capital = initial_capital

    for i in range(30):
        date = start_date + pd.Timedelta(days=i)

        # 跳过周末
        if date.weekday() >= 5:
            continue

        # 生成随机数据
        position_value = capital * 0.8
        cash = capital * 0.2

        # 更新资金
        capital *= (1 + daily_return / 100)

        daily_data.append({
            '日期': date,
            '现金': cash,
            '持仓市值': position_value,
            '总资产': cash + position_value,
            '日收益率(%)': daily_return,
            '持仓数量': 5
        })

    # 转换为DataFrame
    daily_df = pd.DataFrame(daily_data)

    # 写入Excel
    daily_df.to_excel(writer, sheet_name='每日表现', index=False)

def create_trades_sheet(strategy_info, writer):
    """
    创建模拟交易记录表格

    参数:
        strategy_info (Series): 策略信息
        writer: Excel写入器
    """
    # 生成模拟交易记录
    trades_data = []

    # 生成30天的交易记录
    start_date = datetime(2025, 4, 1)
    win_rate = strategy_info['平均胜率(%)'] / 100

    # 生成股票代码列表
    stock_codes = [
        'sh.600000', 'sh.600036', 'sh.601318', 'sh.600519', 'sh.600276',
        'sz.000001', 'sz.000651', 'sz.000858', 'sz.002415', 'sz.300750'
    ]

    # 生成股票名称列表
    stock_names = [
        '浦发银行', '招商银行', '中国平安', '贵州茅台', '恒瑞医药',
        '平安银行', '格力电器', '五粮液', '海康威视', '宁德时代'
    ]

    for i in range(30):
        date = start_date + pd.Timedelta(days=i)

        # 跳过周末
        if date.weekday() >= 5:
            continue

        # 生成买入交易
        for j in range(5):
            stock_index = (i + j) % len(stock_codes)
            stock_code = stock_codes[stock_index]
            stock_name = stock_names[stock_index]
            price = 50 + (i + j) % 10
            quantity = 1000
            amount = price * quantity

            trades_data.append({
                '日期': date,
                '股票代码': stock_code,
                '股票名称': stock_name,
                '操作': '买入',
                '价格': price,
                '数量': quantity,
                '金额': amount,
                '收益': 0,
                '收益率(%)': 0
            })

        # 生成卖出交易（第二天）
        if i > 0:
            next_date = date + pd.Timedelta(days=1)
            if next_date.weekday() < 5:  # 不是周末
                for j in range(5):
                    stock_index = (i - 1 + j) % len(stock_codes)
                    stock_code = stock_codes[stock_index]
                    stock_name = stock_names[stock_index]
                    buy_price = 50 + (i - 1 + j) % 10
                    sell_price = buy_price * (1 + (0.05 if (i + j) % 10 < win_rate * 10 else -0.02))
                    quantity = 1000
                    amount = sell_price * quantity
                    profit = (sell_price - buy_price) * quantity
                    profit_rate = (sell_price - buy_price) / buy_price * 100

                    trades_data.append({
                        '日期': next_date,
                        '股票代码': stock_code,
                        '股票名称': stock_name,
                        '操作': '卖出',
                        '价格': sell_price,
                        '数量': quantity,
                        '金额': amount,
                        '收益': profit,
                        '收益率(%)': profit_rate
                    })

    # 转换为DataFrame
    trades_df = pd.DataFrame(trades_data)

    # 写入Excel
    trades_df.to_excel(writer, sheet_name='交易记录', index=False)

def main():
    """主函数"""
    # 查找最新的策略汇总Excel文件
    excel_dir = "E:\\机器学习\\complete_excel_results"
    excel_files = [f for f in os.listdir(excel_dir) if f.startswith("所有策略汇总_") and f.endswith(".xlsx")]

    if not excel_files:
        print("未找到策略汇总Excel文件")
        return

    # 按文件名排序，获取最新的文件
    excel_files.sort(reverse=True)
    latest_excel = excel_files[0]
    main_excel_file = os.path.join(excel_dir, latest_excel)

    # 设置输出目录
    output_dir = os.path.join(excel_dir, "strategy_details")

    print(f"使用以下参数:")
    print(f"主Excel文件: {main_excel_file}")
    print(f"输出目录: {output_dir}")

    # 读取Excel文件，获取所有策略编号
    try:
        conditions_df = pd.read_excel(main_excel_file, sheet_name='策略条件')
        strategy_indices = conditions_df['策略编号'].tolist()
        print(f"找到 {len(strategy_indices)} 个策略")
    except Exception as e:
        print(f"读取Excel文件时出错: {str(e)}")
        return

    # 确保输出目录是绝对路径
    output_dir = os.path.abspath(output_dir)

    # 创建输出目录
    if not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"成功创建目录: {output_dir}")
        except Exception as e:
            print(f"创建目录时出错: {str(e)}")
            return

    # 生成策略详细分析Excel文件
    success_count = 0
    total_strategies = len(strategy_indices)

    for i, strategy_index in enumerate(strategy_indices):
        # 确保文件路径是正确的
        output_file = os.path.join(output_dir, f"strategy_{strategy_index}.xlsx")
        print(f"正在创建文件: {output_file}")

        try:
            if create_strategy_detail_excel(strategy_index, output_file, main_excel_file):
                success_count += 1
        except Exception as e:
            print(f"创建文件时出错: {str(e)}")

        # 每生成100个文件，打印一次进度
        if (i + 1) % 100 == 0 or i == total_strategies - 1:
            print(f"已生成 {i + 1}/{total_strategies} 个策略详细分析Excel文件")

    print(f"共成功生成 {success_count} 个策略详细分析Excel文件")

if __name__ == "__main__":
    main()
