#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def detailed_data_check():
    """详细检查生成的数据"""
    
    print("=== 详细数据检查 ===")
    
    try:
        # 读取生成的数据
        df = pd.read_excel('E:/机器学习/complete_excel_results/tech_strength/daily/tech_strength_2025-05-15.xlsx', 
                          dtype={'股票代码': str, '技术指标特征': str, '趋势组合': str})
        
        print(f"数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        print(f"列名: {df.columns.tolist()}")
        
        # 检查样本数据
        print(f"\n=== 样本数据检查 ===")
        sample = df.iloc[0]
        print(f"样本股票代码: {sample['股票代码']}")
        print(f"技术强度: {sample['技术强度']}")
        print(f"技术指标特征: {sample['技术指标特征']} (类型: {type(sample['技术指标特征'])})")
        print(f"趋势组合: {sample['趋势组合']} (类型: {type(sample['趋势组合'])})")
        
        # 重点检查连续技术强度
        print(f"\n=== 连续技术强度详细检查 ===")
        print(f"连续技术强度3天数: {sample['连续技术强度3天数']}")
        print(f"连续技术强度5天数: {sample['连续技术强度5天数']}")
        print(f"连续技术强度10天数: {sample['连续技术强度10天数']}")
        
        # 检查前10个股票的连续技术强度
        print(f"\n前10个股票的连续技术强度:")
        for i in range(min(10, len(df))):
            row = df.iloc[i]
            c3 = row['连续技术强度3天数']
            c5 = row['连续技术强度5天数']
            c10 = row['连续技术强度10天数']
            tech_strength = row['技术强度']
            
            # 检查递增关系
            order_ok = c3 <= c5 <= c10
            
            print(f"  {row['股票代码']}: 技术强度={tech_strength}, 3天={c3}, 5天={c5}, 10天={c10} {'✅' if order_ok else '❌'}")
        
        # 统计连续技术强度
        print(f"\n=== 连续技术强度统计 ===")
        print(f"3天连续技术强度:")
        print(f"  最小值: {df['连续技术强度3天数'].min()}")
        print(f"  最大值: {df['连续技术强度3天数'].max()}")
        print(f"  平均值: {df['连续技术强度3天数'].mean():.2f}")
        print(f"  中位数: {df['连续技术强度3天数'].median():.2f}")
        
        print(f"5天连续技术强度:")
        print(f"  最小值: {df['连续技术强度5天数'].min()}")
        print(f"  最大值: {df['连续技术强度5天数'].max()}")
        print(f"  平均值: {df['连续技术强度5天数'].mean():.2f}")
        print(f"  中位数: {df['连续技术强度5天数'].median():.2f}")
        
        print(f"10天连续技术强度:")
        print(f"  最小值: {df['连续技术强度10天数'].min()}")
        print(f"  最大值: {df['连续技术强度10天数'].max()}")
        print(f"  平均值: {df['连续技术强度10天数'].mean():.2f}")
        print(f"  中位数: {df['连续技术强度10天数'].median():.2f}")
        
        # 检查递增关系的正确率
        print(f"\n=== 递增关系检查 ===")
        correct_order_count = 0
        total_count = len(df)
        
        for _, row in df.iterrows():
            c3 = row['连续技术强度3天数']
            c5 = row['连续技术强度5天数']
            c10 = row['连续技术强度10天数']
            
            if c3 <= c5 <= c10:
                correct_order_count += 1
        
        print(f"递增关系正确的股票数: {correct_order_count}/{total_count}")
        print(f"递增关系正确率: {correct_order_count/total_count*100:.2f}%")
        
        # 检查是否有异常值
        print(f"\n=== 异常值检查 ===")
        
        # 检查相同值的情况
        same_3_5 = (df['连续技术强度3天数'] == df['连续技术强度5天数']).sum()
        same_5_10 = (df['连续技术强度5天数'] == df['连续技术强度10天数']).sum()
        same_3_5_10 = ((df['连续技术强度3天数'] == df['连续技术强度5天数']) & 
                       (df['连续技术强度5天数'] == df['连续技术强度10天数'])).sum()
        
        print(f"3天=5天的股票数: {same_3_5}")
        print(f"5天=10天的股票数: {same_5_10}")
        print(f"3天=5天=10天的股票数: {same_3_5_10}")
        
        # 检查0值的情况
        zero_3 = (df['连续技术强度3天数'] == 0).sum()
        zero_5 = (df['连续技术强度5天数'] == 0).sum()
        zero_10 = (df['连续技术强度10天数'] == 0).sum()
        
        print(f"3天连续技术强度为0的股票数: {zero_3}")
        print(f"5天连续技术强度为0的股票数: {zero_5}")
        print(f"10天连续技术强度为0的股票数: {zero_10}")
        
        # 检查技术强度为-100的股票
        weak_stocks = (df['技术强度'] == -100).sum()
        print(f"技术强度为-100的股票数: {weak_stocks}")
        
        # 检查字段类型
        print(f"\n=== 字段类型检查 ===")
        print(f"技术指标特征类型: {df['技术指标特征'].dtype}")
        print(f"趋势组合类型: {df['趋势组合'].dtype}")
        
        if '买入日开盘涨跌幅' in df.columns:
            print(f"买入日开盘涨跌幅类型: {df['买入日开盘涨跌幅'].dtype}")
        if '日内股票标记' in df.columns:
            print(f"日内股票标记类型: {df['日内股票标记'].dtype}")
        if '卖出日开盘涨跌幅' in df.columns:
            print(f"卖出日开盘涨跌幅类型: {df['卖出日开盘涨跌幅'].dtype}")
        
        # 检查数据多样性
        print(f"\n=== 数据多样性检查 ===")
        print(f"技术指标特征唯一值数量: {df['技术指标特征'].nunique()}")
        print(f"趋势组合唯一值数量: {df['趋势组合'].nunique()}")
        
        # 显示一些具体的唯一值
        print(f"技术指标特征前10个值: {df['技术指标特征'].value_counts().head(10).index.tolist()}")
        print(f"趋势组合前10个值: {df['趋势组合'].value_counts().head(10).index.tolist()}")
        
        # 与原始数据对比
        print(f"\n=== 与原始数据对比 ===")
        try:
            original_df = pd.read_excel('E:/机器学习/complete_excel_results/股票明细_完整.xlsx')
            original_sample = original_df[original_df['日期'] == '2025-05-15'].iloc[0]
            
            print(f"原始数据样本:")
            print(f"  技术指标特征: {original_sample['技术指标特征']} (类型: {type(original_sample['技术指标特征'])})")
            print(f"  趋势组合: {original_sample['趋势组合']} (类型: {type(original_sample['趋势组合'])})")
            print(f"  连续技术强度3天数: {original_sample['连续技术强度3天数']}")
            print(f"  连续技术强度5天数: {original_sample['连续技术强度5天数']}")
            print(f"  连续技术强度10天数: {original_sample['连续技术强度10天数']}")
            
            print(f"\n生成数据样本:")
            print(f"  技术指标特征: {sample['技术指标特征']} (类型: {type(sample['技术指标特征'])})")
            print(f"  趋势组合: {sample['趋势组合']} (类型: {type(sample['趋势组合'])})")
            print(f"  连续技术强度3天数: {sample['连续技术强度3天数']}")
            print(f"  连续技术强度5天数: {sample['连续技术强度5天数']}")
            print(f"  连续技术强度10天数: {sample['连续技术强度10天数']}")
            
        except Exception as e:
            print(f"读取原始数据时出错: {e}")
        
        print(f"\n✅ 详细数据检查完成")
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    detailed_data_check()
