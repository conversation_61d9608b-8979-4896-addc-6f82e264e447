"""
创建策略Excel文件

这个脚本用于创建策略Excel文件，包含所有必要的sheet和列。
即使没有筛选出数据，也会创建一个有效的Excel文件，确保回测程序能够继续执行下一个组合。
"""

import os
import pandas as pd
import argparse
from datetime import datetime

def create_strategy_excel(strategy_id, strategy_desc, output_dir=None):
    """
    创建策略Excel文件
    
    参数:
        strategy_id (int): 策略ID
        strategy_desc (str): 策略描述
        output_dir (str): 输出目录，默认为E:\机器学习\complete_excel_results\new_strategy_details
    """
    # 设置默认输出目录
    if output_dir is None:
        output_dir = r'E:\机器学习\complete_excel_results\new_strategy_details'
    
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 创建文件路径
    file_path = os.path.join(output_dir, f"strategy_{strategy_id}.xlsx")
    
    # 创建一个Excel写入器
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        # 1. 策略汇总参数
        strategy_summary = pd.DataFrame({
            '策略编号': [strategy_id],
            '策略组合': [f'策略{strategy_id}'],
            '特征数量': [len(strategy_desc.split('AND'))],
            '平均收益率(%)': [0],
            '平均胜率(%)': [0],
            '平均每日交易笔数': [0],
            '总交易笔数': [0],
            '交易天数': [0],
            '总天数': [30],  # 假设总天数为30
            '交易频率(%)': [0],
            '初始资金(元)': [10000],
            '最终资金(元)': [10000],
            '盈利(元)': [0],
            '累计收益率(%)': [0],
            '年化收益率(%)': [0],
        })
        strategy_summary.to_excel(writer, sheet_name='策略汇总参数', index=False)

        # 2. 策略参数
        strategy_params = pd.DataFrame({
            '策略编号': [strategy_id],
            '策略条件描述': [strategy_desc],
        })
        strategy_params.to_excel(writer, sheet_name='策略参数', index=False)
        
        # 3. 空的股票选择结果 - 添加一行假数据，确保文件能被正确读取
        empty_df = pd.DataFrame({
            '股票代码': ['000000'],
            '股票名称': ['空数据'],
            '选股日期': [pd.Timestamp('2025-01-01')],
            '技术强度': [0],
            '涨跌幅': [0],
            '买入日期': [pd.Timestamp('2025-01-02')],
            '买入日涨跌幅': [0],
            '卖出日期': [pd.Timestamp('2025-01-03')],
            '卖出日股票涨跌幅': [0],
            '是否盈利': [False],
            '策略编号': [strategy_id]
        })
        empty_df.to_excel(writer, sheet_name='选股明细', index=False)
        
        # 4. 空的每日收益明细 - 添加一行假数据，确保文件能被正确读取
        empty_daily_df = pd.DataFrame({
            '日期': [pd.Timestamp('2025-01-01')],
            '日平均涨幅(%)': [0],
            '当日胜率(%)': [0],
            '日收益率': [0],
            '日收益金额': [0],
            '累计资金': [10000],
            '交易股票数量': [0],
            '策略编号': [strategy_id]
        })
        empty_daily_df.to_excel(writer, sheet_name='每日收益明细', index=False)
        
        # 5. 说明
        info_df = pd.DataFrame({
            '说明': [
                f'策略编号: {strategy_id}',
                f'创建时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                f'策略条件描述: {strategy_desc}',
                '该策略没有筛选出符合条件的股票',
                '这是一个空的Excel文件，用于确保程序能够继续执行下一个组合'
            ]
        })
        info_df.to_excel(writer, sheet_name='说明', index=False)
    
    print(f"已创建策略Excel文件: {file_path}")
    
    # 验证文件是否成功保存
    if os.path.exists(file_path):
        file_size = os.path.getsize(file_path)
        print(f"文件大小: {file_size} 字节")
        if file_size > 0:
            print(f"文件创建成功")
            return file_path
        else:
            print(f"警告: 文件大小为0")
            return None
    else:
        print(f"错误: 文件不存在")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='创建策略Excel文件')
    parser.add_argument('--id', type=int, required=True, help='策略ID')
    parser.add_argument('--desc', type=str, default=None, help='策略描述')
    parser.add_argument('--output_dir', type=str, default=None, help='输出目录')
    
    args = parser.parse_args()
    
    # 如果没有提供策略描述，使用默认描述
    if args.desc is None:
        args.desc = f"技术强度 等于 28 AND 连续技术强度5天数 大于等于 355 AND 连续技术强度10天数 大于等于 579 AND 技术指标特征 为 111111（满足: 均线多头排列, 成交量放大, MACD金叉, RSI反弹, KDJ金叉, 布林带突破） AND 趋势组合 为 111111（满足: 3天技术强度上升, 3天价格上升, 5天技术强度上升, 5天价格上升, 10天技术强度上升, 10天价格上升） AND 日内股票标记 为 664（开盘上涨, 日内最高点强势） AND 成交量是前一日几倍 大于等于 2.5"
    
    create_strategy_excel(args.id, args.desc, args.output_dir)

if __name__ == "__main__":
    main()
