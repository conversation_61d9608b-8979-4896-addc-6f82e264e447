#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票明细Excel分析主程序
作者: Augment AI
版本: 1.0.0

该脚本是股票明细Excel分析程序的主程序，用于从股票明细Excel文件中读取数据，
对所有可能的特征组合和条件组合进行回测，并生成与complete_excel_results目录中相同结构的Excel文件。
"""

import os
import pandas as pd
import itertools
from datetime import datetime
import analyze_stock_excel as ase

def create_main_excel(results, output_file):
    """
    创建主Excel文件

    参数:
        results (list): 策略结果列表
        output_file (str): 输出文件路径
    """
    print(f"正在创建主Excel文件: {output_file}")

    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 创建策略汇总表格
        create_strategy_summary_sheet(results, writer)

        # 创建策略条件表格
        create_strategy_conditions_sheet(results, writer)

        # 创建按特征数量分组的统计表格
        create_feature_count_stats_sheet(results, writer)

    print(f"主Excel文件创建完成: {output_file}")

def create_strategy_summary_sheet(results, writer):
    """
    创建策略汇总表格

    参数:
        results (list): 策略结果列表
        writer: Excel写入器
    """
    # 创建汇总数据
    summary_data = []

    for result in results:
        feature_str = ', '.join([cond['feature'] for cond in result['strategy_conditions']])
        summary_data.append({
            '策略编号': result['strategy_index'],
            '策略组合': feature_str,
            '特征数量': len(result['strategy_conditions']),
            '总收益率(%)': result['backtest_result']['summary']['总收益率(%)'],
            '平均收益率(%)': result['backtest_result']['summary']['年化收益率(%)'] / 365,
            '平均胜率(%)': result['backtest_result']['summary']['胜率(%)'],
            '平均每日交易笔数': result['backtest_result']['summary']['平均每日交易笔数'],
            '总交易笔数': result['backtest_result']['summary']['总交易笔数'],
            '交易天数': result['backtest_result']['summary']['交易天数'],
            '总天数': result['backtest_result']['summary']['总天数'],
            '交易频率(%)': result['backtest_result']['summary']['交易频率(%)']
        })

    # 转换为DataFrame
    summary_df = pd.DataFrame(summary_data)

    # 写入Excel
    summary_df.to_excel(writer, sheet_name='策略汇总', index=False)

def create_strategy_conditions_sheet(results, writer):
    """
    创建策略条件表格

    参数:
        results (list): 策略结果列表
        writer: Excel写入器
    """
    # 创建策略条件数据
    conditions_data = []

    for result in results:
        feature_str = ', '.join([cond['feature'] for cond in result['strategy_conditions']])

        # 生成策略条件描述
        conditions_str = ' AND '.join([cond['description'] for cond in result['strategy_conditions']])

        # 生成策略代码
        code_parts = []
        for cond in result['strategy_conditions']:
            code_parts.append(f"df['{cond['feature']}'] {cond['condition']}")
        code_str = 'df[' + ' & '.join(code_parts) + ']'

        conditions_data.append({
            '策略编号': result['strategy_index'],
            '策略组合': feature_str,
            '特征数量': len(result['strategy_conditions']),
            '总收益率(%)': result['backtest_result']['summary']['总收益率(%)'],
            '平均胜率(%)': result['backtest_result']['summary']['胜率(%)'],
            '策略条件描述': conditions_str,
            '策略代码': code_str,
            '详细分析文件': f"strategy_{result['strategy_index']}.xlsx"
        })

    # 转换为DataFrame
    conditions_df = pd.DataFrame(conditions_data)

    # 写入Excel
    conditions_df.to_excel(writer, sheet_name='策略条件', index=False)

def create_feature_count_stats_sheet(results, writer):
    """
    创建按特征数量分组的统计表格

    参数:
        results (list): 策略结果列表
        writer: Excel写入器
    """
    # 创建汇总数据
    summary_data = []

    for result in results:
        summary_data.append({
            '特征数量': len(result['strategy_conditions']),
            '总收益率(%)': result['backtest_result']['summary']['总收益率(%)'],
            '平均收益率(%)': result['backtest_result']['summary']['年化收益率(%)'] / 365,
            '平均胜率(%)': result['backtest_result']['summary']['胜率(%)'],
            '平均每日交易笔数': result['backtest_result']['summary']['平均每日交易笔数'],
            '总交易笔数': result['backtest_result']['summary']['总交易笔数']
        })

    # 转换为DataFrame
    summary_df = pd.DataFrame(summary_data)

    # 按特征数量分组
    grouped = summary_df.groupby('特征数量').agg({
        '总收益率(%)': ['mean', 'std', 'max', 'min'],
        '平均收益率(%)': ['mean', 'std'],
        '平均胜率(%)': ['mean', 'std'],
        '平均每日交易笔数': 'mean',
        '总交易笔数': 'mean'
    })

    # 写入Excel
    grouped.to_excel(writer, sheet_name='特征数量统计')

def create_strategy_detail_excel(result, output_file):
    """
    创建策略详细分析Excel文件

    参数:
        result (dict): 策略结果
        output_file (str): 输出文件路径
    """
    print(f"正在创建策略详细分析Excel文件: {output_file}")

    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 创建策略统计信息表格
        create_strategy_stats_sheet(result, writer)

        # 创建策略条件详情表格
        create_strategy_conditions_detail_sheet(result, writer)

        # 创建每日表现数据表格
        create_daily_performance_sheet(result, writer)

    print(f"策略详细分析Excel文件创建完成: {output_file}")

def create_strategy_stats_sheet(result, writer):
    """
    创建策略统计信息表格

    参数:
        result (dict): 策略结果
        writer: Excel写入器
    """
    # 创建策略统计信息
    stats_data = {
        '统计项': [
            '策略编号',
            '特征组合',
            '特征数量',
            '总收益率(%)',
            '平均收益率(%)',
            '平均胜率(%)',
            '平均每日交易笔数',
            '总交易笔数',
            '交易天数',
            '总天数',
            '交易频率(%)'
        ],
        '数值': [
            result['strategy_index'],
            ', '.join([cond['feature'] for cond in result['strategy_conditions']]),
            len(result['strategy_conditions']),
            result['backtest_result']['summary']['总收益率(%)'],
            result['backtest_result']['summary']['年化收益率(%)'] / 365,
            result['backtest_result']['summary']['胜率(%)'],
            result['backtest_result']['summary']['平均每日交易笔数'],
            result['backtest_result']['summary']['总交易笔数'],
            result['backtest_result']['summary']['交易天数'],
            result['backtest_result']['summary']['总天数'],
            result['backtest_result']['summary']['交易频率(%)']
        ]
    }

    # 转换为DataFrame
    stats_df = pd.DataFrame(stats_data)

    # 写入Excel
    stats_df.to_excel(writer, sheet_name='策略统计', index=False)

def create_strategy_conditions_detail_sheet(result, writer):
    """
    创建策略条件详情表格

    参数:
        result (dict): 策略结果
        writer: Excel写入器
    """
    # 创建策略条件数据
    conditions_data = {
        '特征': [cond['feature'] for cond in result['strategy_conditions']],
        '条件': [cond['condition'] for cond in result['strategy_conditions']],
        '描述': [cond['description'] for cond in result['strategy_conditions']]
    }

    # 转换为DataFrame
    conditions_df = pd.DataFrame(conditions_data)

    # 写入Excel
    conditions_df.to_excel(writer, sheet_name='策略条件详情', index=False)

def create_daily_performance_sheet(result, writer):
    """
    创建每日表现数据表格

    参数:
        result (dict): 策略结果
        writer: Excel写入器
    """
    # 获取每日表现数据
    daily_performance = result['backtest_result']['daily_performance']

    # 转换为DataFrame
    daily_df = pd.DataFrame(daily_performance)

    # 写入Excel
    daily_df.to_excel(writer, sheet_name='每日表现', index=False)

    # 创建交易记录表格
    trades = result['backtest_result']['trades']
    if trades:
        trades_df = pd.DataFrame(trades)
        trades_df.to_excel(writer, sheet_name='交易记录', index=False)

def main():
    """主函数"""
    # 设置参数
    excel_file = input("请输入股票明细Excel文件路径 (默认: E:\\机器学习\\股票明细.xlsx): ") or "E:\\机器学习\\股票明细.xlsx"
    output_dir = input("请输入输出目录 (默认: complete_excel_results): ") or "complete_excel_results"
    start_date = input("请输入回测开始日期，格式: YYYY-MM-DD (默认: 2025-04-01): ") or "2025-04-01"
    end_date = input("请输入回测结束日期，格式: YYYY-MM-DD (默认: 2025-04-30): ") or "2025-04-30"

    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 创建策略详细分析目录
    strategy_details_dir = os.path.join(output_dir, 'strategy_details')
    if not os.path.exists(strategy_details_dir):
        os.makedirs(strategy_details_dir)

    # 读取股票明细Excel文件
    stock_data = ase.read_stock_excel(excel_file)

    if stock_data is None or len(stock_data) == 0:
        print("没有找到有效的股票数据，程序退出")
        return

    # 特征列表
    features = [
        '技术强度',
        '连续技术强度5天数',
        '连续技术强度3天数',
        '连续技术强度10天数',
        '看涨技术指标数量',
        '涨跌幅趋势',
        '技术强度趋势',
        '连续技术强度5天数趋势',
        '技术指标_均线多头排列',
        '技术指标_MACD金叉',
        '技术指标_RSI反弹',
        '技术指标_KDJ金叉',
        '技术指标_布林带突破',
        '开盘涨跌'
    ]

    # 生成所有特征组合
    print("生成从2到5个特征的所有组合...")

    all_combinations = []
    for r in range(2, 6):
        combinations = list(itertools.combinations(features, r))
        print(f"{r}特征组合数量: {len(combinations)}")
        all_combinations.extend(combinations)

    print(f"总组合数量: {len(all_combinations)}")

    # 生成所有策略
    all_results = []
    strategy_index = 1

    # 创建进度条
    total_combinations = len(all_combinations)

    for i, feature_combination in enumerate(all_combinations):
        print(f"正在处理特征组合: {feature_combination} ({i+1}/{total_combinations})")

        # 生成所有可能的条件组合
        all_condition_combinations = ase.generate_all_condition_combinations(feature_combination)

        if all_condition_combinations:
            # 生成每个条件组合的策略结果
            for condition_combination in all_condition_combinations:
                # 回测策略
                backtest_result = ase.backtest_strategy(stock_data, condition_combination, start_date, end_date)

                if backtest_result and backtest_result['summary']:
                    # 添加到结果列表
                    all_results.append({
                        'strategy_index': strategy_index,
                        'strategy_conditions': condition_combination,
                        'backtest_result': backtest_result
                    })

                    strategy_index += 1

                # 限制策略数量，避免生成过多
                if strategy_index > 10000:
                    print(f"已达到最大策略数量限制 (10000)，停止生成")
                    break

        # 限制策略数量，避免生成过多
        if strategy_index > 10000:
            break

        # 每处理100个特征组合，保存一次中间结果
        if (i + 1) % 100 == 0 or i == total_combinations - 1:
            # 按总收益率排序
            sorted_results = sorted(all_results, key=lambda x: x['backtest_result']['summary']['总收益率(%)'], reverse=True)

            # 重新分配策略编号，确保按总收益率排序
            for j, result in enumerate(sorted_results, 1):
                result['strategy_index'] = j

            # 创建中间Excel文件
            interim_excel_file = os.path.join(output_dir, f"中间结果_{i+1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
            create_main_excel(sorted_results[:1000], interim_excel_file)  # 只保存前1000个结果，避免文件过大

            print(f"已处理 {i+1}/{total_combinations} 个特征组合，中间结果已保存到 {interim_excel_file}")

    # 按总收益率排序
    all_results.sort(key=lambda x: x['backtest_result']['summary']['总收益率(%)'], reverse=True)

    # 重新分配策略编号，确保按总收益率排序
    for i, result in enumerate(all_results, 1):
        result['strategy_index'] = i

    print(f"共生成 {len(all_results)} 个策略")

    # 创建主Excel文件
    main_excel_file = os.path.join(output_dir, f"所有策略汇总_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
    create_main_excel(all_results, main_excel_file)

    print(f"主Excel文件已保存到: {main_excel_file}")

    # 创建前1000个策略的详细分析Excel文件
    print(f"创建前1000个策略的详细分析Excel文件...")

    # 只处理前1000个策略
    top_results = all_results[:1000]

    for i, result in enumerate(top_results):
        strategy_excel_file = os.path.join(strategy_details_dir, f"strategy_{result['strategy_index']}.xlsx")
        create_strategy_detail_excel(result, strategy_excel_file)

        if (i + 1) % 100 == 0:
            print(f"已创建 {i+1}/{len(top_results)} 个策略的详细分析Excel文件")

    print(f"已创建前1000个策略的详细分析Excel文件")
    print("所有Excel文件创建完成")

if __name__ == "__main__":
    main()
