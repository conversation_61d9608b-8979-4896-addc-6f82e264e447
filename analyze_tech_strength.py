import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 读取数据
print("读取股票数据...")
data = pd.read_excel('股票明细.xlsx')
print(f"数据形状: {data.shape}")

# 检查数据日期范围
data['日期'] = pd.to_datetime(data['日期'])
print("\n数据日期范围:")
print(f"{data['日期'].min()} 至 {data['日期'].max()}")

# 计算每只股票在每个日期的次日涨跌幅和后日涨跌幅
def calculate_returns(group):
    """计算每只股票的次日涨跌幅和后日涨跌幅"""
    group = group.sort_values('日期')
    
    # 计算次日涨跌幅 (T+1)
    group['次日涨跌幅'] = group['当前价格'].shift(-1) / group['当前价格'] - 1
    
    # 计算后日涨跌幅 (T+2)
    group['后日涨跌幅'] = group['当前价格'].shift(-2) / group['当前价格'].shift(-1) - 1
    
    # 计算次日买入后日卖出的收益率
    group['次日买入后日卖出收益率'] = group['后日涨跌幅']
    
    return group

# 按股票代码分组，计算涨跌幅
print("\n计算次日和后日涨跌幅...")
data_with_returns = data.groupby('股票代码', group_keys=False).apply(calculate_returns)

# 删除没有后日数据的记录
data_with_returns = data_with_returns.dropna(subset=['次日买入后日卖出收益率'])

# 分析次日买入后日卖出收益率的分布
print("\n次日买入后日卖出收益率的分布统计:")
print(data_with_returns['次日买入后日卖出收益率'].describe([0.01, 0.05, 0.1, 0.9, 0.95, 0.99]))

# 定义目标变量：次日买入后日卖出是否盈利
data_with_returns['目标_次日买入后日卖出盈利'] = (data_with_returns['次日买入后日卖出收益率'] > 0).astype(int)

# 定义目标变量：次日买入后日卖出是否显著盈利 (收益率超过90%分位数)
significant_profit_threshold = data_with_returns['次日买入后日卖出收益率'].quantile(0.9)
data_with_returns['目标_次日买入后日卖出显著盈利'] = (data_with_returns['次日买入后日卖出收益率'] >= significant_profit_threshold).astype(int)
print(f"\n次日买入后日卖出显著盈利阈值 (90%分位数): {significant_profit_threshold:.4f}")

# 显示目标变量的分布
print("\n目标变量分布:")
for col in ['目标_次日买入后日卖出盈利', '目标_次日买入后日卖出显著盈利']:
    print(f"{col}:")
    print(data_with_returns[col].value_counts())
    print(f"比例: {data_with_returns[col].mean():.4f}")
    print()

# 分析技术强度与次日买入后日卖出收益率的关系
print("\n分析技术强度与次日买入后日卖出收益率的关系...")

# 技术强度的分布
print("\n技术强度的分布:")
print(data_with_returns['技术强度'].describe())
print("\n技术强度的值计数:")
print(data_with_returns['技术强度'].value_counts().sort_index())

# 按技术强度分组，计算次日买入后日卖出的平均收益率和成功率
tech_strength_groups = data_with_returns.groupby('技术强度')
tech_strength_performance = tech_strength_groups.agg({
    '次日买入后日卖出收益率': 'mean',
    '目标_次日买入后日卖出盈利': 'mean',
    '目标_次日买入后日卖出显著盈利': 'mean'
}).reset_index()

# 添加样本数量
tech_strength_performance['样本数量'] = tech_strength_groups.size().values

# 按技术强度排序
tech_strength_performance = tech_strength_performance.sort_values('技术强度')

print("\n按技术强度分组的表现:")
print(tech_strength_performance)

# 绘制技术强度与次日买入后日卖出收益率的关系图
plt.figure(figsize=(12, 8))

# 收益率图
plt.subplot(2, 1, 1)
plt.bar(tech_strength_performance['技术强度'], tech_strength_performance['次日买入后日卖出收益率'] * 100)
plt.axhline(y=0, color='r', linestyle='-')
plt.title('技术强度与次日买入后日卖出平均收益率(%)')
plt.xlabel('技术强度')
plt.ylabel('平均收益率(%)')

# 成功率图
plt.subplot(2, 1, 2)
plt.bar(tech_strength_performance['技术强度'], tech_strength_performance['目标_次日买入后日卖出盈利'] * 100)
plt.axhline(y=data_with_returns['目标_次日买入后日卖出盈利'].mean() * 100, color='r', linestyle='-', label='平均成功率')
plt.title('技术强度与次日买入后日卖出成功率(%)')
plt.xlabel('技术强度')
plt.ylabel('成功率(%)')
plt.legend()

plt.tight_layout()
plt.savefig('技术强度分析.png')
print("已保存分析图表到 '技术强度分析.png'")

# 找出最佳技术强度阈值
best_strength = None
best_success_rate = 0
best_sample_size = 0
best_return = 0

for strength in sorted(data_with_returns['技术强度'].unique()):
    # 筛选技术强度大于等于当前值的股票
    filtered = data_with_returns[data_with_returns['技术强度'] >= strength]
    
    if len(filtered) >= 100:  # 确保样本量足够
        success_rate = filtered['目标_次日买入后日卖出盈利'].mean()
        avg_return = filtered['次日买入后日卖出收益率'].mean() * 100
        
        print(f"技术强度 >= {strength}: 样本量={len(filtered)}, 成功率={success_rate:.4f}, 平均收益率={avg_return:.2f}%")
        
        if success_rate > best_success_rate:
            best_success_rate = success_rate
            best_strength = strength
            best_sample_size = len(filtered)
            best_return = avg_return

print(f"\n最佳技术强度阈值: >= {best_strength}")
print(f"样本量: {best_sample_size}")
print(f"成功率: {best_success_rate:.4f}")
print(f"平均收益率: {best_return:.2f}%")
print(f"相比基准提升: {best_success_rate / data_with_returns['目标_次日买入后日卖出盈利'].mean():.2f}倍")

# 验证最佳技术强度在历史数据上的表现
print("\n验证最佳技术强度在历史数据上的表现:")

# 筛选技术强度大于等于最佳阈值的股票
best_filtered = data_with_returns[data_with_returns['技术强度'] >= best_strength]

# 按日期分组，计算每天满足条件的股票在次日买入后日卖出的平均收益率
daily_performance = []

for date in sorted(data_with_returns['日期'].unique())[:-2]:  # 排除最后两天，因为没有完整的后日数据
    # 获取当天满足条件的股票
    day_data = best_filtered[best_filtered['日期'] == date]
    
    if len(day_data) > 0:
        # 计算这些股票次日买入后日卖出的平均收益率
        avg_return = day_data['次日买入后日卖出收益率'].mean() * 100
        success_rate = day_data['目标_次日买入后日卖出盈利'].mean()
        
        daily_performance.append({
            '日期': date,
            '满足条件的股票数': len(day_data),
            '次日买入后日卖出平均收益率': avg_return,
            '成功率': success_rate
        })

if daily_performance:
    daily_df = pd.DataFrame(daily_performance)
    print(daily_df)
    
    # 计算总体表现
    print("\n总体表现:")
    print(f"平均每天满足条件的股票数: {daily_df['满足条件的股票数'].mean():.2f}")
    print(f"次日买入后日卖出平均收益率: {daily_df['次日买入后日卖出平均收益率'].mean():.2f}%")
    print(f"平均成功率: {daily_df['成功率'].mean():.4f}")
    
    # 保存每日表现数据
    daily_df.to_excel(f"技术强度{best_strength}_每日表现.xlsx", index=False)
    print(f"已将每日表现数据保存到 '技术强度{best_strength}_每日表现.xlsx'")
else:
    print("没有足够的历史数据来验证规则表现")

# 保存满足条件的最新股票列表
recent_date = data_with_returns['日期'].max()
recent_signals = best_filtered[best_filtered['日期'] == recent_date][['股票代码', '股票名称', '当前价格', '涨跌幅', '技术强度', '技术指标']]

if len(recent_signals) > 0:
    print(f"\n最近日期 {recent_date.date()} 满足条件的股票:")
    print(recent_signals.head(10))
    
    # 保存推荐股票
    filename = f"技术强度{best_strength}_推荐股票.xlsx"
    recent_signals.to_excel(filename, index=False)
    print(f"已将推荐股票保存到 '{filename}'")
else:
    print(f"\n最近日期 {recent_date.date()} 没有满足条件的股票")

# 分析不同技术强度区间的表现
print("\n分析不同技术强度区间的表现:")

# 创建技术强度区间
strength_bins = [0, 20, 40, 60, 80, 100]
strength_labels = ['0-20', '21-40', '41-60', '61-80', '81-100']

data_with_returns['技术强度区间'] = pd.cut(data_with_returns['技术强度'], bins=strength_bins, labels=strength_labels)

# 按技术强度区间分组，计算次日买入后日卖出的平均收益率和成功率
strength_interval_performance = data_with_returns.groupby('技术强度区间').agg({
    '次日买入后日卖出收益率': 'mean',
    '目标_次日买入后日卖出盈利': 'mean',
    '目标_次日买入后日卖出显著盈利': 'mean'
}).reset_index()

# 添加样本数量
strength_interval_performance['样本数量'] = data_with_returns.groupby('技术强度区间').size().values

print("\n按技术强度区间分组的表现:")
print(strength_interval_performance)

# 保存结果
strength_interval_performance.to_excel("技术强度区间分析.xlsx", index=False)
print("已将技术强度区间分析保存到 '技术强度区间分析.xlsx'")
