"""
创建空的Excel文件，用于处理没有筛选出数据的情况

当回测程序中某个组合没有筛选出数据时，程序会跳过这个组合，不会创建相应的Excel文件。
这导致程序无法继续执行下一个组合。本程序用于创建一个空的Excel文件，确保程序能够继续执行。
"""

import os
import pandas as pd
import argparse
from datetime import datetime

def create_empty_excel(output_dir, strategy_name):
    """
    创建空的Excel文件
    
    参数:
        output_dir (str): 输出目录
        strategy_name (str): 策略名称
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 创建文件名
    file_name = f"{strategy_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    output_file = os.path.join(output_dir, file_name)
    
    # 创建空的DataFrame
    empty_df = pd.DataFrame(columns=[
        '股票代码', '股票名称', '日期', '收盘价', '涨跌幅', 
        '技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数',
        '技术指标特征', '趋势组合', '成交量是前一日几倍', 
        '买入日开盘涨跌幅', '卖出日开盘涨跌幅', '日内股票标记'
    ])
    
    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 写入空的DataFrame
        empty_df.to_excel(writer, sheet_name='筛选结果', index=False)
        
        # 创建说明表格
        info_df = pd.DataFrame({
            '说明': [
                f'策略名称: {strategy_name}',
                f'创建时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                '该策略没有筛选出符合条件的股票',
                '这是一个空的Excel文件，用于确保程序能够继续执行下一个组合'
            ]
        })
        info_df.to_excel(writer, sheet_name='说明', index=False)
    
    print(f"已创建空的Excel文件: {output_file}")
    return output_file

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='创建空的Excel文件')
    parser.add_argument('--output_dir', type=str, default='strategy_results',
                        help='输出目录 (默认: strategy_results)')
    parser.add_argument('--strategy_name', type=str, default='empty_strategy',
                        help='策略名称 (默认: empty_strategy)')
    
    args = parser.parse_args()
    
    create_empty_excel(args.output_dir, args.strategy_name)

if __name__ == "__main__":
    main()
