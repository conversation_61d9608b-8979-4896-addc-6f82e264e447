"""
预处理并预测股票走势

先预处理数据，计算所需特征，然后使用已训练好的模型预测股票走势
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib

def clear_screen():
    """清除屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """打印界面标题"""
    print("=" * 80)
    print("                        预处理并预测股票走势")
    print("=" * 80)
    print()

def load_model(model_dir='trained_models'):
    """加载已训练好的模型"""
    print(f"加载模型...")
    try:
        # 加载最新模型信息
        latest_model_info = joblib.load(f"{model_dir}/latest_model_info.joblib")
        
        # 加载模型、缩放器和特征
        model = joblib.load(latest_model_info['model_file'])
        scaler = joblib.load(latest_model_info['scaler_file'])
        features = joblib.load(latest_model_info['features_file'])
        
        print(f"成功加载模型 (训练时间: {latest_model_info['timestamp']})")
        print(f"模型使用的特征: {features}")
        
        return model, scaler, features
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None, None, None

def load_data(file_path='股票明细.xlsx'):
    """加载股票数据"""
    print(f"加载股票数据: {file_path}")
    try:
        stock_data = pd.read_excel(file_path)
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
        return stock_data
    except Exception as e:
        print(f"加载股票数据失败: {e}")
        return None

def preprocess_data(stock_data):
    """预处理股票数据，计算必要的特征"""
    print("预处理数据...")
    
    # 转换日期格式
    if isinstance(stock_data['日期'].iloc[0], str):
        stock_data['日期'] = pd.to_datetime(stock_data['日期'])
    
    # 按股票代码和日期排序
    stock_data = stock_data.sort_values(['股票代码', '日期'])
    
    # 按股票代码分组处理
    for code, group in stock_data.groupby('股票代码'):
        # 确保数据按日期排序
        group = group.sort_values('日期')
        
        # 计算连续技术强度天数（连续多少天为100）
        consecutive_days = []
        current_count = 0
        
        for strength in group['技术强度'].values:
            if strength == 100:
                current_count += 1
            else:
                current_count = 0
            consecutive_days.append(current_count)
        
        # 更新原始数据
        stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days
        
        # 计算技术强度累积值（5天）
        cumulative_strength = group['技术强度'].copy()
        for i in range(1, 5):
            cumulative_strength += group['技术强度'].shift(i).fillna(0)
        
        # 更新原始数据
        stock_data.loc[group.index, '连续技术强度5天数'] = cumulative_strength
        
        # 计算趋势特征
        stock_data.loc[group.index, '技术强度趋势'] = (
            (group['技术强度'] > group['技术强度'].shift(1)) & 
            (group['技术强度'].shift(1) > group['技术强度'].shift(2))
        ).astype(int)
        
        stock_data.loc[group.index, '价格趋势'] = (
            (group['当前价格'] > group['当前价格'].shift(1)) & 
            (group['当前价格'].shift(1) > group['当前价格'].shift(2))
        ).astype(int)
        
        if '涨跌幅' in group.columns:
            stock_data.loc[group.index, '涨跌幅趋势'] = (
                (group['涨跌幅'] > group['涨跌幅'].shift(1)) & 
                (group['涨跌幅'].shift(1) > group['涨跌幅'].shift(2))
            ).astype(int)
        else:
            stock_data.loc[group.index, '涨跌幅趋势'] = 0
    
    # 处理技术指标特征
    if '技术指标' in stock_data.columns:
        # 提取常见的技术指标关键词
        tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破', 
                          '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']
        
        # 为每个技术指标创建一个新列
        for indicator in tech_indicators:
            col_name = f'技术指标_{indicator}'
            # 检查技术指标文本中是否包含该关键词
            if '技术指标' in stock_data.columns:
                stock_data[col_name] = stock_data['技术指标'].str.contains(indicator, na=False).astype(int)
            else:
                stock_data[col_name] = 0
    else:
        # 如果没有技术指标列，创建空的技术指标特征
        tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破', 
                          '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']
        for indicator in tech_indicators:
            stock_data[f'技术指标_{indicator}'] = 0
    
    print("预处理完成")
    return stock_data

def get_latest_data(stock_data):
    """获取最新日期的数据"""
    # 获取最新日期
    latest_date = stock_data['日期'].max()
    print(f"数据集中最新的交易日期: {latest_date}")
    
    # 获取最新日期的数据
    latest_data = stock_data[stock_data['日期'] == latest_date]
    print(f"最新日期的数据记录数: {len(latest_data)}")
    
    return latest_data, latest_date

def apply_strategy_1(predictions):
    """
    策略1：100%高胜率策略
    
    条件：
    1. 预测盈利概率>78%
    2. 技术强度≥70
    3. 连续技术强度5天数≥400
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.78) &  # 条件1: 预测盈利概率>78%
        (predictions['技术强度'] >= 70) &  # 条件2: 技术强度≥70
        (predictions['连续技术强度5天数'] >= 400)  # 条件3: 5天累积值≥400
    ]
    
    return strategy_stocks

def apply_strategy_A(predictions):
    """
    策略A：最高胜率策略
    
    条件：
    1. 技术强度=21-30
    2. 连续技术强度5天数=301-400
    3. 预测盈利概率>75%
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] >= 21) &  # 条件2: 技术强度≥21
        (predictions['技术强度'] <= 30) &  # 条件2: 技术强度≤30
        (predictions['连续技术强度5天数'] >= 301) &  # 条件3: 5天累积值≥301
        (predictions['连续技术强度5天数'] <= 400)  # 条件3: 5天累积值≤400
    ]
    
    return strategy_stocks

def apply_strategy_B(predictions):
    """
    策略B：最高收益率策略
    
    条件：
    1. 技术强度=100
    2. 连续技术强度5天数=401-500
    3. 预测盈利概率>75%
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] == 100) &  # 条件2: 技术强度=100
        (predictions['连续技术强度5天数'] >= 401) &  # 条件3: 5天累积值≥401
        (predictions['连续技术强度5天数'] <= 500)  # 条件3: 5天累积值≤500
    ]
    
    return strategy_stocks

def apply_strategy_C(predictions):
    """
    策略C：平衡策略（胜率和交易机会的平衡）
    
    条件：
    1. 技术强度=71-80
    2. 连续技术强度5天数=201-300
    3. 预测盈利概率>75%
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] >= 71) &  # 条件2: 技术强度≥71
        (predictions['技术强度'] <= 80) &  # 条件2: 技术强度≤80
        (predictions['连续技术强度5天数'] >= 201) &  # 条件3: 5天累积值≥201
        (predictions['连续技术强度5天数'] <= 300)  # 条件3: 5天累积值≤300
    ]
    
    return strategy_stocks

def preprocess_predict():
    """预处理并预测股票走势"""
    # 加载模型
    model, scaler, features = load_model()
    if model is None:
        print("无法加载模型，请先训练模型")
        return
    
    # 加载数据
    stock_data = load_data()
    if stock_data is None:
        return
    
    # 预处理数据
    processed_data = preprocess_data(stock_data)
    
    # 获取最新数据
    latest_data, latest_date = get_latest_data(processed_data)
    
    # 计算下一个交易日（简单地加一天，实际应考虑周末和节假日）
    next_date = latest_date + timedelta(days=1)
    next_date_str = next_date.strftime('%Y-%m-%d')
    print(f"预测下一个交易日: {next_date_str}")
    
    # 准备预测数据
    try:
        # 提取特征
        X_pred = latest_data[features]
        
        # 处理预测数据中的缺失值
        valid_pred_indices = ~X_pred.isnull().any(axis=1)
        X_pred = X_pred[valid_pred_indices]
        latest_data_filtered = latest_data.loc[valid_pred_indices.index[valid_pred_indices]]
        
        if len(X_pred) == 0:
            print(f"预测数据不足，无法进行预测")
            return
        
        # 标准化预测数据
        X_pred_scaled = scaler.transform(X_pred)
        
        # 预测盈利概率
        pred_proba = model.predict_proba(X_pred_scaled)[:, 1]
        
        # 创建预测结果DataFrame
        predictions = pd.DataFrame({
            '股票代码': latest_data_filtered['股票代码'],
            '股票名称': latest_data_filtered['股票名称'],
            '涨跌幅': latest_data_filtered['涨跌幅'] if '涨跌幅' in latest_data_filtered.columns else 0,
            '技术强度': latest_data_filtered['技术强度'],
            '连续技术强度天数': latest_data_filtered['连续技术强度天数'],
            '连续技术强度5天数': latest_data_filtered['连续技术强度5天数'],
            '预测盈利概率': pred_proba
        })
        
        # 按预测盈利概率降序排序
        predictions = predictions.sort_values('预测盈利概率', ascending=False)
        
        print(f"预测完成，共 {len(predictions)} 只股票")
        
        # 创建结果目录
        if not os.path.exists('strategy_results'):
            os.makedirs('strategy_results')
        
        # 应用各种策略
        strategy_1_stocks = apply_strategy_1(predictions)
        strategy_A_stocks = apply_strategy_A(predictions)
        strategy_B_stocks = apply_strategy_B(predictions)
        strategy_C_stocks = apply_strategy_C(predictions)
        
        # 保存结果
        result_file = f'strategy_results/{next_date_str}_all_strategies.xlsx'
        with pd.ExcelWriter(result_file) as writer:
            predictions.to_excel(writer, sheet_name='所有股票', index=False)
            strategy_1_stocks.to_excel(writer, sheet_name='策略1', index=False)
            strategy_A_stocks.to_excel(writer, sheet_name='策略A', index=False)
            strategy_B_stocks.to_excel(writer, sheet_name='策略B', index=False)
            strategy_C_stocks.to_excel(writer, sheet_name='策略C', index=False)
        
        print(f"\n预测结果已保存至: {result_file}")
        print(f"策略1推荐股票数: {len(strategy_1_stocks)}")
        print(f"策略A推荐股票数: {len(strategy_A_stocks)}")
        print(f"策略B推荐股票数: {len(strategy_B_stocks)}")
        print(f"策略C推荐股票数: {len(strategy_C_stocks)}")
        
        # 显示策略1的推荐股票
        if len(strategy_1_stocks) > 0:
            print("\n策略1推荐买入的股票:")
            print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！")
            print("【交易策略】: 买入后在第二个交易日开盘时卖出")
            print("\n股票列表:")
            for i, row in strategy_1_stocks.iterrows():
                print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
        else:
            print("\n策略1没有推荐的股票")
        
        # 显示策略A的推荐股票
        if len(strategy_A_stocks) > 0:
            print("\n策略A推荐买入的股票:")
            print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！")
            print("【交易策略】: 买入后在第二个交易日开盘时卖出")
            print("\n股票列表:")
            for i, row in strategy_A_stocks.iterrows():
                print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
        else:
            print("\n策略A没有推荐的股票")
        
        # 显示策略B的推荐股票
        if len(strategy_B_stocks) > 0:
            print("\n策略B推荐买入的股票:")
            print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！")
            print("【交易策略】: 买入后在第二个交易日开盘时卖出")
            print("\n股票列表:")
            for i, row in strategy_B_stocks.iterrows():
                print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
        else:
            print("\n策略B没有推荐的股票")
        
        # 显示策略C的推荐股票
        if len(strategy_C_stocks) > 0:
            print("\n策略C推荐买入的股票:")
            print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！")
            print("【交易策略】: 买入后在第二个交易日开盘时卖出")
            print("\n股票列表:")
            for i, row in strategy_C_stocks.iterrows():
                print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
        else:
            print("\n策略C没有推荐的股票")
    
    except Exception as e:
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    clear_screen()
    print_header()
    preprocess_predict()
    input("\n按Enter键退出...")
