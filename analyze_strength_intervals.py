import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from datetime import datetime

def analyze_strength_intervals():
    """
    分析不同技术强度区间的股票表现
    """
    print("开始分析不同技术强度区间的股票表现...")

    # 创建结果目录
    if not os.path.exists('strength_interval_results'):
        os.makedirs('strength_interval_results')

    # 加载股票数据
    print("\n加载股票数据...")
    try:
        stock_data = pd.read_excel('股票明细.xlsx')
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")

        # 转换日期格式
        if isinstance(stock_data['日期'].iloc[0], str):
            stock_data['日期'] = pd.to_datetime(stock_data['日期'])

        # 获取所有日期并排序
        all_dates = sorted(stock_data['日期'].unique())
        print(f"数据集中的日期范围: {all_dates[0]} 到 {all_dates[-1]}")

        # 按股票代码和日期排序
        stock_data = stock_data.sort_values(['股票代码', '日期'])

        # 数据预处理
        print("\n数据预处理...")

        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 计算连续技术强度天数（连续多少天为100）
            consecutive_days = []
            current_count = 0

            for strength in group['技术强度'].values:
                if strength == 100:
                    current_count += 1
                else:
                    current_count = 0
                consecutive_days.append(current_count)

            # 更新原始数据
            stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days

            # 计算技术强度累积值（5天）
            cumulative_strength = group['技术强度'].copy()
            for i in range(1, 5):
                cumulative_strength += group['技术强度'].shift(i).fillna(0)

            # 更新原始数据
            stock_data.loc[group.index, '连续技术强度5天数'] = cumulative_strength

            # 计算两日收益率（买入后第二天卖出）
            two_day_later_price = group['当前价格'].shift(-2)
            two_day_return = (two_day_later_price / group['当前价格'] - 1) * 100

            # 更新原始数据
            stock_data.loc[group.index, '两日收益率'] = two_day_return

            # 计算是否盈利（两日收益率>0）
            is_profit = (two_day_return > 0).astype(int)
            stock_data.loc[group.index, '是否盈利'] = is_profit

            # 计算次日涨跌方向（用于判断开盘时是否上涨）
            stock_data.loc[group.index, '次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)

        # 删除没有完整数据的记录
        stock_data = stock_data.dropna(subset=['两日收益率', '是否盈利', '次日涨跌方向'])

        print(f"处理后的数据集大小: {len(stock_data)} 条记录")

        # 定义技术强度区间
        strength_intervals = [
            (0, 10), (11, 20), (21, 30), (31, 40), (41, 50),
            (51, 60), (61, 70), (71, 80), (81, 90), (91, 99), (100, 100)
        ]

        # 定义连续技术强度5天数区间
        cumulative_intervals = [
            (0, 100), (101, 200), (201, 300), (301, 400), (401, 500)
        ]

        # 创建结果DataFrame
        results = pd.DataFrame(columns=[
            '技术强度区间', '股票数量', '胜率', '平均收益率',
            '买入时上涨股票数量', '买入时上涨股票胜率', '买入时上涨股票平均收益率'
        ])

        # 分析每个技术强度区间
        print("\n分析每个技术强度区间...")
        for low, high in strength_intervals:
            # 筛选该区间的股票
            interval_stocks = stock_data[(stock_data['技术强度'] >= low) & (stock_data['技术强度'] <= high)]

            # 计算该区间的统计数据
            total_count = len(interval_stocks)
            if total_count == 0:
                continue

            win_count = sum(interval_stocks['是否盈利'])
            win_rate = win_count / total_count
            avg_return = interval_stocks['两日收益率'].mean()

            # 筛选买入时上涨的股票
            up_stocks = interval_stocks[interval_stocks['次日涨跌方向'] == 1]
            up_count = len(up_stocks)

            if up_count > 0:
                up_win_count = sum(up_stocks['是否盈利'])
                up_win_rate = up_win_count / up_count
                up_avg_return = up_stocks['两日收益率'].mean()
            else:
                up_win_rate = np.nan
                up_avg_return = np.nan

            # 添加到结果
            new_row = pd.DataFrame({
                '技术强度区间': [f"{low}-{high}"],
                '股票数量': [total_count],
                '胜率': [win_rate],
                '平均收益率': [avg_return],
                '买入时上涨股票数量': [up_count],
                '买入时上涨股票胜率': [up_win_rate],
                '买入时上涨股票平均收益率': [up_avg_return]
            })
            results = pd.concat([results, new_row], ignore_index=True)

            print(f"技术强度区间 {low}-{high}: 股票数量={total_count}, 胜率={win_rate:.4f}, 平均收益率={avg_return:.4f}%")
            if up_count > 0:
                print(f"  买入时上涨: 股票数量={up_count}, 胜率={up_win_rate:.4f}, 平均收益率={up_avg_return:.4f}%")

        # 保存技术强度区间分析结果
        results.to_excel('strength_interval_results/技术强度区间分析.xlsx', index=False)

        # 创建累积技术强度结果DataFrame
        cumulative_results = pd.DataFrame(columns=[
            '连续技术强度5天数区间', '股票数量', '胜率', '平均收益率',
            '买入时上涨股票数量', '买入时上涨股票胜率', '买入时上涨股票平均收益率'
        ])

        # 分析每个连续技术强度5天数区间
        print("\n分析每个连续技术强度5天数区间...")
        for low, high in cumulative_intervals:
            # 筛选该区间的股票
            interval_stocks = stock_data[(stock_data['连续技术强度5天数'] >= low) & (stock_data['连续技术强度5天数'] <= high)]

            # 计算该区间的统计数据
            total_count = len(interval_stocks)
            if total_count == 0:
                continue

            win_count = sum(interval_stocks['是否盈利'])
            win_rate = win_count / total_count
            avg_return = interval_stocks['两日收益率'].mean()

            # 筛选买入时上涨的股票
            up_stocks = interval_stocks[interval_stocks['次日涨跌方向'] == 1]
            up_count = len(up_stocks)

            if up_count > 0:
                up_win_count = sum(up_stocks['是否盈利'])
                up_win_rate = up_win_count / up_count
                up_avg_return = up_stocks['两日收益率'].mean()
            else:
                up_win_rate = np.nan
                up_avg_return = np.nan

            # 添加到结果
            new_row = pd.DataFrame({
                '连续技术强度5天数区间': [f"{low}-{high}"],
                '股票数量': [total_count],
                '胜率': [win_rate],
                '平均收益率': [avg_return],
                '买入时上涨股票数量': [up_count],
                '买入时上涨股票胜率': [up_win_rate],
                '买入时上涨股票平均收益率': [up_avg_return]
            })
            cumulative_results = pd.concat([cumulative_results, new_row], ignore_index=True)

            print(f"连续技术强度5天数区间 {low}-{high}: 股票数量={total_count}, 胜率={win_rate:.4f}, 平均收益率={avg_return:.4f}%")
            if up_count > 0:
                print(f"  买入时上涨: 股票数量={up_count}, 胜率={up_win_rate:.4f}, 平均收益率={up_avg_return:.4f}%")

        # 保存连续技术强度5天数区间分析结果
        cumulative_results.to_excel('strength_interval_results/连续技术强度5天数区间分析.xlsx', index=False)

        # 分析技术强度和连续技术强度5天数的组合
        print("\n分析技术强度和连续技术强度5天数的组合...")

        # 创建组合结果DataFrame
        combination_results = pd.DataFrame(columns=[
            '技术强度区间', '连续技术强度5天数区间', '股票数量', '胜率', '平均收益率',
            '买入时上涨股票数量', '买入时上涨股票胜率', '买入时上涨股票平均收益率'
        ])

        # 分析每个组合
        for strength_interval in strength_intervals:
            for cumulative_interval in cumulative_intervals:
                s_low, s_high = strength_interval
                c_low, c_high = cumulative_interval

                # 筛选该组合的股票
                combo_stocks = stock_data[
                    (stock_data['技术强度'] >= s_low) & (stock_data['技术强度'] <= s_high) &
                    (stock_data['连续技术强度5天数'] >= c_low) & (stock_data['连续技术强度5天数'] <= c_high)
                ]

                # 计算该组合的统计数据
                total_count = len(combo_stocks)
                if total_count < 10:  # 忽略样本量太小的组合
                    continue

                win_count = sum(combo_stocks['是否盈利'])
                win_rate = win_count / total_count
                avg_return = combo_stocks['两日收益率'].mean()

                # 筛选买入时上涨的股票
                up_stocks = combo_stocks[combo_stocks['次日涨跌方向'] == 1]
                up_count = len(up_stocks)

                if up_count > 0:
                    up_win_count = sum(up_stocks['是否盈利'])
                    up_win_rate = up_win_count / up_count
                    up_avg_return = up_stocks['两日收益率'].mean()
                else:
                    up_win_rate = np.nan
                    up_avg_return = np.nan

                # 添加到结果
                new_row = pd.DataFrame({
                    '技术强度区间': [f"{s_low}-{s_high}"],
                    '连续技术强度5天数区间': [f"{c_low}-{c_high}"],
                    '股票数量': [total_count],
                    '胜率': [win_rate],
                    '平均收益率': [avg_return],
                    '买入时上涨股票数量': [up_count],
                    '买入时上涨股票胜率': [up_win_rate],
                    '买入时上涨股票平均收益率': [up_avg_return]
                })
                combination_results = pd.concat([combination_results, new_row], ignore_index=True)

        # 按买入时上涨股票胜率降序排序
        combination_results = combination_results.sort_values('买入时上涨股票胜率', ascending=False)

        # 保存组合分析结果
        combination_results.to_excel('strength_interval_results/组合分析.xlsx', index=False)

        # 输出胜率最高的组合
        print("\n胜率最高的组合:")
        top_combinations = combination_results.head(10)
        for i, row in top_combinations.iterrows():
            print(f"技术强度区间 {row['技术强度区间']}, 连续技术强度5天数区间 {row['连续技术强度5天数区间']}")
            print(f"  股票数量={row['股票数量']}, 胜率={row['胜率']:.4f}, 平均收益率={row['平均收益率']:.4f}%")
            print(f"  买入时上涨: 股票数量={row['买入时上涨股票数量']}, 胜率={row['买入时上涨股票胜率']:.4f}, 平均收益率={row['买入时上涨股票平均收益率']:.4f}%")

        # 绘制技术强度区间胜率图表
        plt.figure(figsize=(12, 6))
        plt.bar(results['技术强度区间'], results['胜率'], color='blue', alpha=0.6, label='所有股票')
        plt.bar(results['技术强度区间'], results['买入时上涨股票胜率'], color='green', alpha=0.6, label='买入时上涨股票')
        plt.title('不同技术强度区间的胜率比较')
        plt.xlabel('技术强度区间')
        plt.ylabel('胜率')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('strength_interval_results/技术强度区间胜率.png')

        # 绘制技术强度区间平均收益率图表
        plt.figure(figsize=(12, 6))
        plt.bar(results['技术强度区间'], results['平均收益率'], color='blue', alpha=0.6, label='所有股票')
        plt.bar(results['技术强度区间'], results['买入时上涨股票平均收益率'], color='green', alpha=0.6, label='买入时上涨股票')
        plt.title('不同技术强度区间的平均收益率比较')
        plt.xlabel('技术强度区间')
        plt.ylabel('平均收益率 (%)')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('strength_interval_results/技术强度区间平均收益率.png')

        print("\n分析完成！结果已保存至 strength_interval_results 目录")

        return results, cumulative_results, combination_results

    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

if __name__ == "__main__":
    analyze_strength_intervals()
