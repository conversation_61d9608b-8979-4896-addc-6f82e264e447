"""
检查数据集中的日期情况
"""

import pandas as pd
from datetime import datetime

# 加载数据
print("加载数据...")
df = pd.read_excel('股票明细.xlsx')
print(f"数据集总记录数: {len(df)}")

# 检查列名
print("\n数据集列名:")
for col in df.columns:
    print(f"- {col}")

# 检查日期列
print("\n日期列信息:")
print(f"日期列类型: {df['日期'].dtype}")
print(f"日期列唯一值数量: {df['日期'].nunique()}")

# 获取所有唯一日期并排序
dates = sorted(df['日期'].unique())
print(f"\n最早日期: {dates[0]}")
print(f"最晚日期: {dates[-1]}")
print(f"最近10个交易日: {[d.strftime('%Y-%m-%d') for d in dates[-10:]]}")

# 检查特定日期的记录数
print("\n特定日期的记录数:")
for date_str in ['2025-05-06', '2025-05-07', '2025-05-08', '2025-05-09']:
    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
    count = len(df[df['日期'] == date_obj])
    print(f"{date_str}: {count} 条记录")

# 检查这些日期的技术强度=71的股票数量
print("\n技术强度=71的股票数量:")
for date_str in ['2025-05-06', '2025-05-07', '2025-05-08', '2025-05-09']:
    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
    count = len(df[(df['日期'] == date_obj) & (df['技术强度'] == 71)])
    print(f"{date_str}: {count} 只股票")
