"""
股票交易策略实现模块

包含以下策略：
- 策略1：100%高胜率策略
- 策略A：最高胜率策略
- 策略B：最高收益率策略
- 策略C：平衡策略（胜率和交易机会的平衡）
"""

import pandas as pd
import numpy as np
import os
import joblib
from datetime import datetime, timedelta
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

# 创建结果目录
if not os.path.exists('strategy_results'):
    os.makedirs('strategy_results')

def preprocess_data(stock_data):
    """
    预处理股票数据，计算必要的特征
    
    参数:
    stock_data: 股票数据DataFrame
    
    返回:
    处理后的股票数据
    """
    print("数据预处理...")
    
    # 转换日期格式
    if isinstance(stock_data['日期'].iloc[0], str):
        stock_data['日期'] = pd.to_datetime(stock_data['日期'])
    
    # 按股票代码和日期排序
    stock_data = stock_data.sort_values(['股票代码', '日期'])
    
    # 按股票代码分组处理
    for code, group in stock_data.groupby('股票代码'):
        # 确保数据按日期排序
        group = group.sort_values('日期')
        
        # 计算连续技术强度天数（连续多少天为100）
        consecutive_days = []
        current_count = 0
        
        for strength in group['技术强度'].values:
            if strength == 100:
                current_count += 1
            else:
                current_count = 0
            consecutive_days.append(current_count)
        
        # 更新原始数据
        stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days
        
        # 计算技术强度累积值（5天）
        cumulative_strength = group['技术强度'].copy()
        for i in range(1, 5):
            cumulative_strength += group['技术强度'].shift(i).fillna(0)
        
        # 更新原始数据
        stock_data.loc[group.index, '连续技术强度5天数'] = cumulative_strength
        
        # 计算趋势特征
        stock_data.loc[group.index, '技术强度趋势'] = (
            (group['技术强度'] > group['技术强度'].shift(1)) & 
            (group['技术强度'].shift(1) > group['技术强度'].shift(2))
        ).astype(int)
        
        stock_data.loc[group.index, '价格趋势'] = (
            (group['当前价格'] > group['当前价格'].shift(1)) & 
            (group['当前价格'].shift(1) > group['当前价格'].shift(2))
        ).astype(int)
        
        if '涨跌幅' in group.columns:
            stock_data.loc[group.index, '涨跌幅趋势'] = (
                (group['涨跌幅'] > group['涨跌幅'].shift(1)) & 
                (group['涨跌幅'].shift(1) > group['涨跌幅'].shift(2))
            ).astype(int)
        
        # 计算两日收益率（买入后第二天卖出）
        two_day_later_price = group['当前价格'].shift(-2)
        two_day_return = (two_day_later_price / group['当前价格'] - 1) * 100
        
        # 更新原始数据
        stock_data.loc[group.index, '两日收益率'] = two_day_return
        
        # 计算是否盈利（两日收益率>0）
        is_profit = (two_day_return > 0).astype(int)
        stock_data.loc[group.index, '是否盈利'] = is_profit
        
        # 计算次日涨跌方向（用于判断开盘时是否上涨）
        stock_data.loc[group.index, '次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)
    
    # 处理技术指标特征
    if '技术指标' in stock_data.columns:
        # 提取常见的技术指标关键词
        tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破', 
                          '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']
        
        # 为每个技术指标创建一个新列
        for indicator in tech_indicators:
            col_name = f'技术指标_{indicator}'
            # 检查技术指标文本中是否包含该关键词
            stock_data[col_name] = stock_data['技术指标'].str.contains(indicator, na=False).astype(int)
        
        # 计算看涨技术指标数量
        bullish_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破']
        stock_data['看涨技术指标数量'] = 0
        for indicator in bullish_indicators:
            stock_data['看涨技术指标数量'] += stock_data[f'技术指标_{indicator}']
    
    # 删除没有完整数据的记录
    stock_data = stock_data.dropna(subset=['两日收益率', '是否盈利', '次日涨跌方向'])
    
    print(f"处理后的数据集大小: {len(stock_data)} 条记录")
    
    return stock_data

def train_model(stock_data, prediction_date=None):
    """
    训练机器学习模型
    
    参数:
    stock_data: 预处理后的股票数据
    prediction_date: 预测日期，如果提供，则只使用该日期之前的数据训练
    
    返回:
    训练好的模型、特征缩放器和特征列表
    """
    print("训练机器学习模型...")
    
    # 提取特征
    features = [
        '技术强度', '连续技术强度5天数',
        '技术强度趋势', '价格趋势', '涨跌幅趋势',
        '涨跌幅'
    ]
    
    # 添加技术指标特征
    if '技术指标_均线多头排列' in stock_data.columns:
        tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破', 
                          '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']
        for indicator in tech_indicators:
            features.append(f'技术指标_{indicator}')
    
    # 如果提供了预测日期，则只使用该日期之前的数据训练
    if prediction_date is not None:
        train_data = stock_data[stock_data['日期'] < prediction_date]
    else:
        train_data = stock_data
    
    # 特征和目标变量
    X_train = train_data[features]
    y_train = train_data['是否盈利']
    
    # 处理缺失值
    valid_indices = ~X_train.isnull().any(axis=1)
    X_train = X_train[valid_indices]
    y_train = y_train[valid_indices]
    
    # 标准化特征
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    
    # 训练梯度提升模型
    gb_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
    gb_model.fit(X_train_scaled, y_train)
    
    print(f"模型训练完成，使用 {len(X_train)} 条记录")
    
    return gb_model, scaler, features

def predict_stocks(stock_data, model, scaler, features, prediction_date):
    """
    预测股票盈利概率
    
    参数:
    stock_data: 预处理后的股票数据
    model: 训练好的模型
    scaler: 特征缩放器
    features: 特征列表
    prediction_date: 预测日期
    
    返回:
    预测结果DataFrame
    """
    print(f"预测 {prediction_date} 的股票盈利概率...")
    
    # 获取预测日期的数据
    prediction_date_data = stock_data[stock_data['日期'] == prediction_date]
    
    # 准备预测数据
    X_pred = prediction_date_data[features]
    
    # 处理预测数据中的缺失值
    valid_pred_indices = ~X_pred.isnull().any(axis=1)
    X_pred = X_pred[valid_pred_indices]
    prediction_date_data_filtered = prediction_date_data.loc[valid_pred_indices.index[valid_pred_indices]]
    
    if len(X_pred) == 0:
        print(f"预测数据不足，无法进行预测")
        return None
    
    # 标准化预测数据
    X_pred_scaled = scaler.transform(X_pred)
    
    # 预测盈利概率
    pred_proba = model.predict_proba(X_pred_scaled)[:, 1]
    
    # 创建预测结果DataFrame
    predictions = pd.DataFrame({
        '股票代码': prediction_date_data_filtered['股票代码'],
        '股票名称': prediction_date_data_filtered['股票名称'],
        '涨跌幅': prediction_date_data_filtered['涨跌幅'],
        '技术强度': prediction_date_data_filtered['技术强度'],
        '连续技术强度天数': prediction_date_data_filtered['连续技术强度天数'],
        '连续技术强度5天数': prediction_date_data_filtered['连续技术强度5天数'],
        '看涨技术指标数量': prediction_date_data_filtered['看涨技术指标数量'] if '看涨技术指标数量' in prediction_date_data_filtered.columns else 0,
        '预测盈利概率': pred_proba
    })
    
    # 按预测盈利概率降序排序
    predictions = predictions.sort_values('预测盈利概率', ascending=False)
    
    print(f"预测完成，共 {len(predictions)} 只股票")
    
    return predictions

def strategy_1(predictions):
    """
    策略1：100%高胜率策略
    
    条件：
    1. 预测盈利概率>78%
    2. 技术强度≥70
    3. 连续技术强度5天数≥400
    4. 只买入开盘时上涨的股票（需要在实际交易时执行）
    
    参数:
    predictions: 预测结果DataFrame
    
    返回:
    符合策略1条件的股票DataFrame
    """
    strategy_1_stocks = predictions[
        (predictions['预测盈利概率'] > 0.78) &  # 条件1: 预测盈利概率>78%
        (predictions['技术强度'] >= 70) &  # 条件2: 技术强度≥70
        (predictions['连续技术强度5天数'] >= 400)  # 条件3: 5天累积值≥400
    ]
    
    return strategy_1_stocks

def strategy_A(predictions):
    """
    策略A：最高胜率策略
    
    条件：
    1. 技术强度=21-30
    2. 连续技术强度5天数=301-400
    3. 预测盈利概率>75%
    4. 只买入开盘时上涨的股票（需要在实际交易时执行）
    
    参数:
    predictions: 预测结果DataFrame
    
    返回:
    符合策略A条件的股票DataFrame
    """
    strategy_A_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] >= 21) &  # 条件2: 技术强度≥21
        (predictions['技术强度'] <= 30) &  # 条件2: 技术强度≤30
        (predictions['连续技术强度5天数'] >= 301) &  # 条件3: 5天累积值≥301
        (predictions['连续技术强度5天数'] <= 400)  # 条件3: 5天累积值≤400
    ]
    
    return strategy_A_stocks

def strategy_B(predictions):
    """
    策略B：最高收益率策略
    
    条件：
    1. 技术强度=100
    2. 连续技术强度5天数=401-500
    3. 预测盈利概率>75%
    4. 只买入开盘时上涨的股票（需要在实际交易时执行）
    
    参数:
    predictions: 预测结果DataFrame
    
    返回:
    符合策略B条件的股票DataFrame
    """
    strategy_B_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] == 100) &  # 条件2: 技术强度=100
        (predictions['连续技术强度5天数'] >= 401) &  # 条件3: 5天累积值≥401
        (predictions['连续技术强度5天数'] <= 500)  # 条件3: 5天累积值≤500
    ]
    
    return strategy_B_stocks

def strategy_C(predictions):
    """
    策略C：平衡策略（胜率和交易机会的平衡）
    
    条件：
    1. 技术强度=71-80
    2. 连续技术强度5天数=201-300
    3. 预测盈利概率>75%
    4. 只买入开盘时上涨的股票（需要在实际交易时执行）
    
    参数:
    predictions: 预测结果DataFrame
    
    返回:
    符合策略C条件的股票DataFrame
    """
    strategy_C_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] >= 71) &  # 条件2: 技术强度≥71
        (predictions['技术强度'] <= 80) &  # 条件2: 技术强度≤80
        (predictions['连续技术强度5天数'] >= 201) &  # 条件3: 5天累积值≥201
        (predictions['连续技术强度5天数'] <= 300)  # 条件3: 5天累积值≤300
    ]
    
    return strategy_C_stocks
