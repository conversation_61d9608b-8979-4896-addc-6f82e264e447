import pandas as pd
import os
import subprocess
import time
import argparse

# 设置文件路径
base_dir = r'E:\机器学习\complete_excel_results'
summary_file = os.path.join(base_dir, '所有策略汇总.xlsx')

def get_strategy_count():
    """获取策略总数"""
    try:
        summary_df = pd.read_excel(summary_file)
        return len(summary_df)
    except Exception as e:
        print(f"读取策略汇总文件时出错: {e}")
        return 0

def run_batch(start_id, end_id, batch_size=100):
    """运行一批策略回测"""
    print(f"开始处理策略 {start_id} 到 {end_id}")

    # 计算需要处理的批次数
    total_strategies = end_id - start_id + 1
    num_batches = (total_strategies + batch_size - 1) // batch_size

    for batch in range(num_batches):
        batch_start = start_id + batch * batch_size
        batch_end = min(batch_start + batch_size - 1, end_id)

        print(f"\n{'='*50}")
        print(f"处理批次 {batch+1}/{num_batches}: 策略 {batch_start} 到 {batch_end}")
        print(f"{'='*50}\n")

        # 构建命令
        cmd = f"python backtest_with_baostock.py --start {batch_start} --end {batch_end}"

        # 执行命令
        try:
            subprocess.run(cmd, shell=True, check=True)
        except subprocess.CalledProcessError as e:
            print(f"批次 {batch+1} 执行失败: {e}")
        except KeyboardInterrupt:
            print("\n用户中断，停止处理")
            return False

        # 批次之间暂停一下，避免API调用过于频繁
        if batch < num_batches - 1:
            print(f"批次 {batch+1} 完成，暂停10秒后继续...")
            time.sleep(10)

    return True

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='批量回测股票策略')
    parser.add_argument('--start', type=int, help='起始策略ID')
    parser.add_argument('--end', type=int, help='结束策略ID')
    parser.add_argument('--batch-size', type=int, default=100, help='每批处理的策略数量 (默认: 100)')
    args = parser.parse_args()

    # 获取策略总数
    total_strategies = get_strategy_count()
    if total_strategies == 0:
        print("无法获取策略总数，请检查策略汇总文件")
        return

    print(f"总共有 {total_strategies} 个策略")

    # 确定要处理的策略范围
    start_id = args.start if args.start is not None else 1
    end_id = args.end if args.end is not None else total_strategies

    # 验证范围
    if start_id < 1:
        start_id = 1
    if end_id > total_strategies:
        end_id = total_strategies
    if start_id > end_id:
        print(f"起始ID ({start_id}) 大于结束ID ({end_id})，无法处理")
        return

    print(f"将处理策略 {start_id} 到 {end_id}，共 {end_id - start_id + 1} 个策略")
    print(f"每批处理 {args.batch_size} 个策略")

    # 直接执行，不需要确认
    print("开始执行回测...")

    # 开始处理
    start_time = time.time()
    success = run_batch(start_id, end_id, args.batch_size)
    end_time = time.time()

    if success:
        elapsed_time = end_time - start_time
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        print(f"\n处理完成！总耗时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    else:
        print("\n处理被中断")

if __name__ == "__main__":
    main()
