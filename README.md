# 机器学习股票交易策略挖掘系统

## 系统简介

本系统使用机器学习算法深度挖掘股票交易策略，通过分析历史数据，寻找具有高胜率和高收益率的交易规则。系统包含两个版本：

1. **基础版**：使用常规机器学习算法挖掘交易策略
2. **高级版**：使用更先进的机器学习算法和特征工程技术挖掘交易策略

## 功能特点

### 基础版功能

- 数据加载和预处理
- 特征工程
- 多种机器学习模型训练（决策树、随机森林、XGBoost等）
- 特征重要性分析
- 交易规则提取
- 组合策略生成
- 策略回测

### 高级版功能

- 高级特征工程
- 特征选择
- 高级机器学习模型（集成学习、神经网络等）
- 高级交易规则提取
- 高级组合策略生成
- 基于模型预测概率的策略
- 高级策略回测

## 使用方法

### 数据准备

将股票数据保存为Excel文件（股票明细.xlsx），确保包含以下字段：

- 日期
- 股票代码
- 股票名称
- 技术强度
- 看涨技术指标数量
- 看跌技术指标数量
- 涨跌幅趋势
- 连续技术强度天数
- 涨跌幅（次日涨跌幅）

### 运行系统

#### 基础版

1. 双击运行 `run_ml_strategy.bat` 进行策略挖掘
2. 双击运行 `generate_ml_recommendations.bat` 生成股票推荐

#### 高级版

1. 双击运行 `run_advanced_ml_strategy.bat` 进行高级策略挖掘
2. 双击运行 `generate_advanced_ml_recommendations.bat` 生成高级股票推荐

### 查看结果

策略挖掘结果保存在以下目录：

- 基础版：`ml_strategy_results`
- 高级版：`advanced_ml_results`

推荐股票保存为CSV文件：

- 基础版：`recommended_stocks_YYYYMMDD.csv`
- 高级版：`advanced_recommended_stocks_YYYYMMDD.csv`

## 系统要求

- Python 3.7+
- 依赖库：pandas, numpy, scikit-learn, xgboost, lightgbm, matplotlib, seaborn

## 注意事项

- 系统需要足够的历史数据才能挖掘出有效的交易策略
- 过去的表现不代表未来的收益，请谨慎使用
- 建议结合其他分析方法和风险控制措施使用本系统

## 文件说明

- `ml_strategy_mining.py`：基础版机器学习策略挖掘系统
- `run_ml_strategy_mining.py`：基础版运行脚本
- `run_ml_strategy.bat`：基础版批处理文件
- `generate_ml_recommendations.bat`：基础版推荐生成批处理文件
- `advanced_ml_strategy.py`：高级版机器学习策略挖掘系统
- `run_advanced_ml_strategy.py`：高级版运行脚本
- `run_advanced_ml_strategy.bat`：高级版批处理文件
- `generate_advanced_ml_recommendations.bat`：高级版推荐生成批处理文件
- `README.md`：说明文档

## 开发者

Augment AI
