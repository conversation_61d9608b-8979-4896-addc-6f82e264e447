"""
完整的股票回测系统打包脚本
包含所有必要的文件、依赖和环境配置
"""

import os
import sys
import shutil
import subprocess
import json
from pathlib import Path

def create_complete_package():
    """创建完整的可执行程序包"""
    
    print("🚀 开始创建完整的股票回测系统可执行程序包...")
    print("=" * 60)
    
    # 1. 检查当前环境
    print("1️⃣ 检查当前环境...")
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    print(f"Python版本: {sys.version}")
    
    # 2. 检查必要文件
    print("\n2️⃣ 检查必要文件...")
    required_files = [
        'backtest_gui.py',           # 主GUI程序
        'backtest_local.py',         # 核心回测逻辑
        'config.py',                 # 配置模块
        'stock_data_manager.py',     # 数据管理模块
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ 缺少必要文件: {missing_files}")
        return False
    
    # 3. 检查和安装依赖
    print("\n3️⃣ 检查和安装依赖...")
    
    # 创建完整的requirements.txt
    complete_requirements = [
        'pandas>=1.5.0',
        'numpy>=1.20.0',
        'openpyxl>=3.0.0',
        'xlsxwriter>=3.0.0',
        'scikit-learn>=1.0.0',
        'xgboost>=1.5.0',
        'lightgbm>=3.3.0',
        'matplotlib>=3.5.0',
        'seaborn>=0.11.0',
        'joblib>=1.1.0',
        'pyinstaller>=5.0.0',
        'tkinter',  # 通常内置
        'datetime',  # 内置
        'threading',  # 内置
        'subprocess',  # 内置
        'json',  # 内置
        'os',  # 内置
        'sys',  # 内置
        'time',  # 内置
        'glob',  # 内置
        'argparse',  # 内置
        'traceback',  # 内置
        'warnings',  # 内置
        'collections',  # 内置
        'itertools',  # 内置
        'functools',  # 内置
        'pathlib',  # 内置
    ]
    
    # 写入完整的requirements.txt
    with open('requirements_complete.txt', 'w', encoding='utf-8') as f:
        for req in complete_requirements:
            if not req in ['tkinter', 'datetime', 'threading', 'subprocess', 'json', 'os', 'sys', 'time', 'glob', 'argparse', 'traceback', 'warnings', 'collections', 'itertools', 'functools', 'pathlib']:
                f.write(req + '\n')
    
    print("  📝 已创建完整的requirements_complete.txt")
    
    # 安装依赖
    try:
        print("  📦 安装依赖包...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements_complete.txt'], 
                      check=True, capture_output=True, text=True)
        print("  ✅ 依赖包安装完成")
    except subprocess.CalledProcessError as e:
        print(f"  ⚠️ 依赖包安装警告: {e}")
        print("  继续执行打包...")
    
    # 4. 创建PyInstaller配置
    print("\n4️⃣ 创建PyInstaller配置...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集数据文件
datas = []

# 收集隐藏导入
hiddenimports = [
    'pandas',
    'numpy',
    'openpyxl',
    'xlsxwriter',
    'sklearn',
    'sklearn.ensemble',
    'sklearn.tree',
    'sklearn.linear_model',
    'sklearn.metrics',
    'sklearn.model_selection',
    'sklearn.preprocessing',
    'xgboost',
    'lightgbm',
    'matplotlib',
    'matplotlib.pyplot',
    'seaborn',
    'joblib',
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'threading',
    'subprocess',
    'json',
    'datetime',
    'time',
    'glob',
    'argparse',
    'traceback',
    'warnings',
    'collections',
    'itertools',
    'functools',
    'pathlib',
    'config',
    'stock_data_manager',
    'backtest_local',
]

# 收集所有sklearn子模块
try:
    hiddenimports.extend(collect_submodules('sklearn'))
except:
    pass

# 收集所有pandas子模块
try:
    hiddenimports.extend(collect_submodules('pandas'))
except:
    pass

# 收集所有numpy子模块
try:
    hiddenimports.extend(collect_submodules('numpy'))
except:
    pass

block_cipher = None

a = Analysis(
    ['backtest_gui.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='股票回测系统_完整版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('股票回测系统_完整版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("  ✅ PyInstaller配置文件已创建")
    
    # 5. 创建启动脚本
    print("\n5️⃣ 创建启动脚本...")
    
    # 创建批处理启动脚本（使用ASCII字符避免编码问题）
    bat_content = '''@echo off
chcp 65001 > nul
echo [启动] 股票回测系统...
echo.

REM 设置环境变量
set PYTHONIOENCODING=utf-8
set PYTHONPATH=%~dp0

REM 检查数据目录
if not exist "complete_excel_results" (
    echo [警告] 未找到数据目录 complete_excel_results
    echo 请确保数据目录与程序在同一文件夹中
    echo.
)

REM 启动程序
echo 正在启动程序...
start "" "%~dp0股票回测系统_完整版.exe"

echo [完成] 程序已启动
echo 如果程序无法正常运行，请检查：
echo 1. 数据目录 complete_excel_results 是否存在
echo 2. 是否有足够的磁盘空间
echo 3. 是否有管理员权限
echo.
pause
'''

    with open('启动股票回测系统.bat', 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    print("  ✅ 启动脚本已创建")
    
    return True

def build_executable():
    """使用PyInstaller构建可执行文件"""
    
    print("\n6️⃣ 使用PyInstaller构建可执行文件...")
    
    try:
        # 清理之前的构建
        if os.path.exists('build'):
            shutil.rmtree('build')
            print("  🗑️ 已清理旧的build目录")
        
        if os.path.exists('dist'):
            shutil.rmtree('dist')
            print("  🗑️ 已清理旧的dist目录")
        
        # 执行PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            '股票回测系统_完整版.spec'
        ]
        
        print(f"  🔨 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("  ✅ PyInstaller构建成功")
            
            # 检查生成的文件
            exe_path = os.path.join('dist', '股票回测系统_完整版.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"  📦 生成的可执行文件: {exe_path}")
                print(f"  📏 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("  ❌ 未找到生成的可执行文件")
                return False
        else:
            print(f"  ❌ PyInstaller构建失败")
            print(f"  错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ 构建过程中出错: {e}")
        return False

def create_package_structure():
    """创建完整的程序包结构"""
    
    print("\n7️⃣ 创建完整的程序包结构...")
    
    # 创建发布目录
    release_dir = f"股票回测系统_完整版_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    
    os.makedirs(release_dir)
    print(f"  📁 创建发布目录: {release_dir}")
    
    try:
        # 复制可执行文件
        exe_source = os.path.join('dist', '股票回测系统_完整版.exe')
        exe_dest = os.path.join(release_dir, '股票回测系统_完整版.exe')
        if os.path.exists(exe_source):
            shutil.copy2(exe_source, exe_dest)
            print(f"  ✅ 复制可执行文件")
        
        # 复制启动脚本
        bat_source = '启动股票回测系统.bat'
        bat_dest = os.path.join(release_dir, '启动股票回测系统.bat')
        if os.path.exists(bat_source):
            shutil.copy2(bat_source, bat_dest)
            print(f"  ✅ 复制启动脚本")
        
        # 创建说明文件
        readme_content = '''# 股票回测系统 - 完整版

## 📋 系统说明

这是一个完整的股票回测系统，包含以下功能：
- 策略回测分析
- 累积涨幅计算
- 智能数据预加载
- 图形化用户界面

## 🚀 使用方法

### 方法1：双击启动脚本（推荐）
1. 双击 `启动股票回测系统.bat`
2. 等待程序启动

### 方法2：直接运行程序
1. 双击 `股票回测系统_完整版.exe`

## 📁 数据目录要求

程序需要以下数据目录结构：
```
complete_excel_results/
├── stock_data/              # 历史股票数据
├── tech_strength/           # 技术强度数据
│   └── daily/              # 按日期存储的技术强度文件
├── 所有策略汇总.xlsx        # 策略汇总文件
└── 股票明细_完整.xlsx       # 股票明细文件
```

## ⚙️ 系统要求

- Windows 7/8/10/11 (64位)
- 至少 4GB 内存
- 至少 2GB 可用磁盘空间

## 🔧 故障排除

### 程序无法启动
1. 检查是否有杀毒软件拦截
2. 尝试以管理员身份运行
3. 检查系统是否缺少 Visual C++ 运行库

### 数据加载失败
1. 确保数据目录 `complete_excel_results` 存在
2. 检查数据文件是否完整
3. 确保有足够的磁盘空间

### 性能问题
1. 关闭其他占用内存的程序
2. 确保有足够的可用内存
3. 使用SSD硬盘可提升性能

## 📞 技术支持

如遇到问题，请检查：
1. 系统日志（程序界面中的日志区域）
2. 数据目录是否正确
3. 系统资源是否充足

## 📝 版本信息

- 版本: 完整版
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 包含功能: 完整回测系统 + 智能累积涨幅生成
'''
        
        readme_path = os.path.join(release_dir, 'README.md')
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"  ✅ 创建说明文件")
        
        # 复制实际的数据文件
        source_data_dir = 'complete_excel_results'
        dest_data_dir = os.path.join(release_dir, 'complete_excel_results')

        if os.path.exists(source_data_dir):
            print(f"  📁 复制数据目录...")

            # 复制关键文件
            key_files = [
                '所有策略汇总.xlsx',
                '所有策略汇总_已回测.xlsx',
                '股票明细_完整.xlsx'
            ]

            os.makedirs(dest_data_dir, exist_ok=True)

            for key_file in key_files:
                source_file = os.path.join(source_data_dir, key_file)
                dest_file = os.path.join(dest_data_dir, key_file)
                if os.path.exists(source_file):
                    shutil.copy2(source_file, dest_file)
                    print(f"    ✅ 复制关键文件: {key_file}")
                else:
                    print(f"    ⚠️ 未找到关键文件: {key_file}")

            # 复制tech_strength目录结构
            source_tech_dir = os.path.join(source_data_dir, 'tech_strength')
            dest_tech_dir = os.path.join(dest_data_dir, 'tech_strength')

            if os.path.exists(source_tech_dir):
                print(f"    📊 复制技术强度数据...")
                shutil.copytree(source_tech_dir, dest_tech_dir, dirs_exist_ok=True)

                # 统计复制的文件数量
                daily_dir = os.path.join(dest_tech_dir, 'daily')
                if os.path.exists(daily_dir):
                    daily_files = [f for f in os.listdir(daily_dir) if f.endswith('.xlsx')]
                    print(f"    ✅ 复制技术强度文件: {len(daily_files)} 个")

            # 创建stock_data目录（可能为空，但保持结构）
            os.makedirs(os.path.join(dest_data_dir, 'stock_data'), exist_ok=True)
            os.makedirs(os.path.join(dest_data_dir, 'stock_data', 'daily'), exist_ok=True)

        else:
            print(f"  ⚠️ 源数据目录不存在，创建示例结构")
            # 创建示例数据目录结构（空目录）
            example_data_dir = os.path.join(release_dir, 'complete_excel_results_示例结构')
            os.makedirs(os.path.join(example_data_dir, 'stock_data'), exist_ok=True)
            os.makedirs(os.path.join(example_data_dir, 'tech_strength', 'daily'), exist_ok=True)
        
        # 创建目录说明文件
        structure_info = '''# 数据目录结构说明

请将您的实际数据文件放置在以下目录结构中：

complete_excel_results/
├── stock_data/                          # 历史股票数据目录
├── tech_strength/                       # 技术强度数据目录
│   └── daily/                          # 按日期存储的技术强度文件
│       ├── tech_strength_strong_2025-03-07_smart.xlsx
│       ├── tech_strength_strong_2025-03-08_smart.xlsx
│       └── ...
├── 所有策略汇总.xlsx                    # 策略汇总文件
└── 股票明细_完整.xlsx                   # 股票明细文件

注意：
1. 请删除此示例目录，使用您的实际数据目录
2. 确保目录名称和文件名称完全匹配
3. 所有Excel文件应为.xlsx格式
'''
        
        structure_path = os.path.join(example_data_dir, '目录结构说明.txt')
        with open(structure_path, 'w', encoding='utf-8') as f:
            f.write(structure_info)
        
        print(f"  ✅ 创建示例数据目录结构")
        
        print(f"\n🎉 程序包创建完成！")
        print(f"📦 发布目录: {release_dir}")
        print(f"📏 目录大小: {get_dir_size(release_dir):.1f} MB")
        
        return release_dir
        
    except Exception as e:
        print(f"  ❌ 创建程序包时出错: {e}")
        return None

def get_dir_size(path):
    """获取目录大小（MB）"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size / (1024 * 1024)

if __name__ == "__main__":
    from datetime import datetime
    
    try:
        # 创建完整程序包
        if create_complete_package():
            if build_executable():
                release_dir = create_package_structure()
                if release_dir:
                    print(f"\n🎊 恭喜！股票回测系统完整版打包成功！")
                    print(f"📁 程序包位置: {os.path.abspath(release_dir)}")
                    print(f"🚀 请将整个文件夹分发给用户使用")
                else:
                    print(f"\n❌ 程序包创建失败")
            else:
                print(f"\n❌ 可执行文件构建失败")
        else:
            print(f"\n❌ 环境检查失败")
            
    except Exception as e:
        print(f"\n💥 打包过程中发生错误: {e}")
        import traceback
        print(traceback.format_exc())
    
    input("\n按回车键退出...")
