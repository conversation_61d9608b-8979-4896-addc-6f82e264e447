"""
运行技术指标组合策略
"""

import os
import argparse
from datetime import datetime, timedelta
import technical_indicator_strategy as tis

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='技术指标组合策略')
    
    # 添加参数
    parser.add_argument('--data_file', type=str, default='股票明细.xlsx', help='数据文件路径')
    parser.add_argument('--backtest', action='store_true', help='是否进行回测')
    parser.add_argument('--recommend', action='store_true', help='是否生成推荐')
    parser.add_argument('--start_date', type=str, help='回测开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end_date', type=str, help='回测结束日期 (YYYY-MM-DD)')
    parser.add_argument('--date', type=str, help='推荐日期 (YYYY-MM-DD)')
    parser.add_argument('--output', type=str, help='输出文件路径')
    
    return parser.parse_args()

def backtest_strategy(data_file, start_date, end_date, output_file):
    """回测策略"""
    # 加载数据
    data = tis.load_data(data_file)
    
    if data is None:
        print("数据加载失败，程序退出")
        return
    
    # 预处理数据
    processed_data = tis.preprocess_data(data)
    
    # 回测策略
    tis.backtest_technical_indicator_strategy(processed_data, start_date, end_date, output_file)

def recommend_stocks(data_file, date, output_file):
    """生成股票推荐"""
    # 加载数据
    data = tis.load_data(data_file)
    
    if data is None:
        print("数据加载失败，程序退出")
        return
    
    # 预处理数据
    processed_data = tis.preprocess_data(data)
    
    # 生成推荐
    tis.recommend_stocks(processed_data, date, output_file)

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 回测策略
    if args.backtest:
        if not args.start_date or not args.end_date:
            print("回测需要指定开始日期和结束日期")
            return
        
        output_file = args.output if args.output else f"技术指标组合策略回测结果.txt"
        backtest_strategy(args.data_file, args.start_date, args.end_date, output_file)
    
    # 生成推荐
    elif args.recommend:
        if not args.date:
            # 默认使用当前日期
            args.date = datetime.now().strftime('%Y-%m-%d')
        
        output_file = args.output if args.output else f"技术指标组合策略推荐股票.xlsx"
        recommend_stocks(args.data_file, args.date, output_file)
    
    # 如果没有指定操作，显示帮助信息
    else:
        print("请指定操作：--backtest 或 --recommend")

if __name__ == "__main__":
    main()
