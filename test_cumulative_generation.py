"""
测试累积涨幅生成功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入回测模块
import backtest_local

def test_cumulative_generation():
    """测试累积涨幅生成功能"""
    print("🔍 测试累积涨幅生成功能")
    print("=" * 50)
    
    try:
        # 调用预加载数据函数
        print("📊 开始预加载数据...")
        backtest_local.preload_data()
        
        print("✅ 预加载完成！")
        
        # 检查是否有技术强度文件被更新
        tech_strength_dir = os.path.join(backtest_local.base_dir, 'tech_strength', 'daily')
        if os.path.exists(tech_strength_dir):
            files = os.listdir(tech_strength_dir)
            excel_files = [f for f in files if f.endswith('.xlsx')]
            print(f"📁 技术强度目录包含 {len(excel_files)} 个Excel文件")
            
            # 检查第一个文件是否包含累积涨幅列
            if excel_files:
                import pandas as pd
                first_file = os.path.join(tech_strength_dir, excel_files[0])
                try:
                    df = pd.read_excel(first_file)
                    cumulative_columns = [
                        '买入后连续2个交易日累计涨幅',
                        '买入后连续3个交易日累计涨幅',
                        '买入日起2日累计涨幅(含买入日)',
                        '买入日起3日累计涨幅(含买入日)',
                        '累积涨幅已生成'
                    ]
                    
                    existing_columns = [col for col in cumulative_columns if col in df.columns]
                    missing_columns = [col for col in cumulative_columns if col not in df.columns]
                    
                    print(f"📋 检查文件: {excel_files[0]}")
                    print(f"✅ 已存在的累积涨幅列: {existing_columns}")
                    print(f"❌ 缺失的累积涨幅列: {missing_columns}")
                    
                    if '累积涨幅已生成' in df.columns:
                        flag_values = df['累积涨幅已生成'].unique()
                        print(f"🏷️ 累积涨幅标记值: {flag_values}")
                    
                    if existing_columns:
                        print("🎯 累积涨幅生成功能正常工作！")
                    else:
                        print("⚠️ 累积涨幅列未生成，可能需要检查生成逻辑")
                        
                except Exception as e:
                    print(f"❌ 读取文件失败: {e}")
        else:
            print("❌ 技术强度目录不存在")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    test_cumulative_generation()
    input("\n按回车键退出...")
