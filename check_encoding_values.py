# 检查编码特征的实际值数量

# 生成技术指标特征的全部有意义组合（1=满足，2=不满足）
def generate_tech_indicator_values():
    values = []
    # 单个指标满足
    for i in range(6):
        value = ['2'] * 6
        value[i] = '1'
        values.append(''.join(value))
    
    # 两个指标满足的常见组合
    common_pairs = [(0,1), (0,2), (0,4), (1,2), (2,4), (3,4)]  # 常见的技术指标组合
    for i, j in common_pairs:
        value = ['2'] * 6
        value[i] = '1'
        value[j] = '1'
        values.append(''.join(value))
    
    # 三个指标满足的强势组合
    strong_triplets = [(0,1,2), (0,2,4), (1,2,4), (0,1,4)]
    for i, j, k in strong_triplets:
        value = ['2'] * 6
        value[i] = '1'
        value[j] = '1'
        value[k] = '1'
        values.append(''.join(value))
    
    # 四个及以上指标满足
    values.extend(['111122', '111112', '111121', '111111'])
    
    return values

# 生成趋势组合的全部有意义组合（1=上升，2=下降或不变）
def generate_trend_combo_values():
    values = []
    # 单个趋势上升
    for i in range(6):
        value = ['2'] * 6
        value[i] = '1'
        values.append(''.join(value))
    
    # 短期趋势组合（3天相关）
    values.extend(['112222', '121222', '112122'])
    
    # 中期趋势组合（5天相关）
    values.extend(['111222', '111122', '112112'])
    
    # 长期趋势组合（10天相关）
    values.extend(['111112', '111121', '112111'])
    
    # 全面上升
    values.append('111111')
    
    return values

# 生成日内股票标记的全部有意义组合（1=其他情况，2-8=不同强度）
def generate_stock_mark_values():
    values = []
    # 强势走势
    strong_patterns = ['888', '886', '868', '866', '688', '686', '668', '666']
    values.extend(strong_patterns)
    
    # 中等走势
    medium_patterns = ['664', '646', '644', '466', '464', '446', '444']
    values.extend(medium_patterns)
    
    # 弱势走势
    weak_patterns = ['222', '224', '242', '244', '422', '424', '442']
    values.extend(weak_patterns)
    
    # 其他情况
    values.append('111')
    
    return values

# 检查实际数量
tech_indicator_values = generate_tech_indicator_values()
trend_combo_values = generate_trend_combo_values()
stock_mark_values = generate_stock_mark_values()

print("编码特征的实际值数量:")
print(f"技术指标特征: {len(tech_indicator_values)} 个值")
print("技术指标特征值列表:")
for i, value in enumerate(tech_indicator_values, 1):
    print(f"  {i:2d}. {value}")

print(f"\n趋势组合: {len(trend_combo_values)} 个值")
print("趋势组合值列表:")
for i, value in enumerate(trend_combo_values, 1):
    print(f"  {i:2d}. {value}")

print(f"\n日内股票标记: {len(stock_mark_values)} 个值")
print("日内股票标记值列表:")
for i, value in enumerate(stock_mark_values, 1):
    print(f"  {i:2d}. {value}")

# 重新计算组合数量
print(f"\n重新计算组合数量:")
print(f"连续技术强度3天数: 4 个值")
print(f"连续技术强度5天数: 4 个值")
print(f"连续技术强度10天数: 3 个值")
print(f"成交量是前一日几倍: 7 个值")
print(f"技术指标特征: {len(tech_indicator_values)} 个值")
print(f"趋势组合: {len(trend_combo_values)} 个值")
print(f"日内股票标记: {len(stock_mark_values)} 个值")

# 计算7个特征组合的数量（技术强度 + 其他6个特征）
print(f"\n7个特征组合的数量计算:")
total_7_features = (
    4 * 4 * 3 * 7 * len(tech_indicator_values) * len(trend_combo_values) +  # 排除日内股票标记
    4 * 4 * 3 * 7 * len(tech_indicator_values) * len(stock_mark_values) +   # 排除趋势组合
    4 * 4 * 3 * 7 * len(trend_combo_values) * len(stock_mark_values) +      # 排除技术指标特征
    4 * 4 * 3 * len(tech_indicator_values) * len(trend_combo_values) * len(stock_mark_values) +  # 排除成交量
    4 * 4 * 7 * len(tech_indicator_values) * len(trend_combo_values) * len(stock_mark_values) +  # 排除连续技术强度10天数
    4 * 3 * 7 * len(tech_indicator_values) * len(trend_combo_values) * len(stock_mark_values) +  # 排除连续技术强度5天数
    4 * 3 * 7 * len(tech_indicator_values) * len(trend_combo_values) * len(stock_mark_values)    # 排除连续技术强度3天数
)

print(f"单个技术强度的7个特征组合总数: {total_7_features:,}")
print(f"所有技术强度(6个)的7个特征组合总数: {total_7_features * 6:,}")
