#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的回测程序是否能正确使用按日期存储的数据文件
"""

import pandas as pd
import os
import sys
import datetime

# 添加当前目录到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

def test_daily_data_functions():
    """测试按日期加载数据的函数"""
    print("=== 测试按日期加载数据的函数 ===")
    
    # 导入修改后的函数
    try:
        from backtest_local import setup_daily_data_index, load_daily_stock_data
        print("成功导入按日期加载数据的函数")
    except ImportError as e:
        print(f"导入函数失败: {e}")
        return False
    
    # 测试设置数据索引
    print("\n1. 测试设置数据索引...")
    try:
        date_to_file_map, available_dates = setup_daily_data_index()
        
        if date_to_file_map is None:
            print("没有找到按日期存储的数据文件，这是正常的")
            return True
        
        print(f"找到 {len(available_dates)} 个可用日期")
        print(f"日期范围: {available_dates[0].strftime('%Y-%m-%d')} 到 {available_dates[-1].strftime('%Y-%m-%d')}")
        
        # 测试加载一个日期的数据
        print("\n2. 测试加载单个日期的数据...")
        test_date = available_dates[0]
        daily_data = load_daily_stock_data(test_date, date_to_file_map)
        
        if not daily_data.empty:
            print(f"成功加载 {test_date.strftime('%Y-%m-%d')} 的数据，共 {len(daily_data)} 条记录")
            print(f"列名: {list(daily_data.columns)}")
            
            # 显示前几行数据
            if len(daily_data) > 0:
                print("前3行数据:")
                display_columns = ['股票代码', '股票名称', '技术强度', '日期']
                available_columns = [col for col in display_columns if col in daily_data.columns]
                if available_columns:
                    print(daily_data[available_columns].head(3).to_string(index=False))
        else:
            print(f"加载 {test_date.strftime('%Y-%m-%d')} 的数据为空")
        
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        return False

def test_backtest_integration():
    """测试回测程序的集成"""
    print("\n=== 测试回测程序集成 ===")
    
    try:
        # 导入回测相关的变量和函数
        import backtest_local
        
        # 检查是否正确设置了按日期加载的变量
        if hasattr(backtest_local, 'use_daily_files'):
            print(f"use_daily_files 变量存在: {backtest_local.use_daily_files}")
        else:
            print("use_daily_files 变量不存在，可能需要运行完整的初始化")
        
        if hasattr(backtest_local, 'date_to_file_map'):
            print(f"date_to_file_map 变量存在: {backtest_local.date_to_file_map is not None}")
        else:
            print("date_to_file_map 变量不存在，可能需要运行完整的初始化")
        
        if hasattr(backtest_local, 'available_dates'):
            print(f"available_dates 变量存在: {backtest_local.available_dates is not None}")
        else:
            print("available_dates 变量不存在，可能需要运行完整的初始化")
        
        print("回测程序集成测试完成")
        return True
        
    except Exception as e:
        print(f"集成测试过程中出错: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试修改后的回测程序...")
    
    # 测试按日期加载数据的函数
    success1 = test_daily_data_functions()
    
    # 测试回测程序集成
    success2 = test_backtest_integration()
    
    if success1 and success2:
        print("\n✓ 所有测试通过")
        print("\n修改说明:")
        print("1. 程序现在支持从按日期存储的文件中读取数据")
        print("2. 如果找到按日期存储的文件，将使用按需加载模式")
        print("3. 如果没有找到按日期存储的文件，将回退到原始的单文件模式")
        print("4. 这样可以避免Excel 100万行限制的问题")
    else:
        print("\n✗ 部分测试失败")
    
    print("\n测试完成")

if __name__ == "__main__":
    main()
