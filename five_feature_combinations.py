#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
5特征组合分析系统
作者: Augment AI
版本: 1.0.0
"""

import pandas as pd
import numpy as np
import os
import itertools
import time
from datetime import datetime
from tqdm import tqdm
import pickle

# 创建结果目录
results_dir = 'five_feature_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
        
        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")
        
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def evaluate_feature_combination(data, feature_combination, start_date, end_date):
    """评估特征组合的表现"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    # 初始化结果
    results = {
        'feature_combination': feature_combination,
        'feature_count': len(feature_combination),
        'total_trades': 0,
        'win_count': 0,
        'total_return': 0,
        'daily_returns': [],
        'daily_win_rates': [],
        'daily_trade_counts': []
    }
    
    # 对每个交易日进行评估
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 获取当日数据
        daily_data = date_range_data[date_range_data['日期'] == current_date]
        
        # 应用特征组合条件
        selected = daily_data.copy()
        for feature in feature_combination:
            if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', 
                         '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势', 
                         '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势', 
                         '连续技术强度10天数趋势', '连续技术强度3天数价格趋势', 
                         '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势', 
                         '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势', 
                         '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
                # 二元特征，使用==1条件
                selected = selected[selected[feature] == 1]
            elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                # 连续值特征，使用>=中位数条件
                median_value = np.median(daily_data[feature])
                selected = selected[selected[feature] >= median_value]
            elif feature == '看涨技术指标数量':
                # 看涨技术指标数量，使用>=3条件
                selected = selected[selected[feature] >= 3]
        
        # 如果有推荐的股票，计算表现
        if len(selected) > 0:
            # 获取推荐股票的代码
            recommended_codes = selected['股票代码'].tolist()
            
            # 获取次日这些股票的数据
            next_day_data = date_range_data[
                (date_range_data['日期'] == next_date) & 
                (date_range_data['股票代码'].isin(recommended_codes))
            ]
            
            # 如果次日有这些股票的数据
            if len(next_day_data) > 0:
                # 计算平均涨跌幅
                avg_return = next_day_data['涨跌幅'].mean()
                
                # 计算盈利股票数量
                win_stocks = len(next_day_data[next_day_data['涨跌幅'] > 0])
                win_rate = win_stocks / len(next_day_data) * 100
                
                # 更新结果
                results['total_trades'] += len(next_day_data)
                results['win_count'] += win_stocks
                results['total_return'] += avg_return
                results['daily_returns'].append(avg_return)
                results['daily_win_rates'].append(win_rate)
                results['daily_trade_counts'].append(len(next_day_data))
    
    # 计算最终统计结果
    if results['total_trades'] > 0:
        results['win_rate'] = results['win_count'] / results['total_trades'] * 100
        results['avg_return'] = results['total_return'] / len(results['daily_returns'])
        results['avg_daily_trades'] = sum(results['daily_trade_counts']) / len(results['daily_trade_counts'])
        results['std_return'] = np.std(results['daily_returns'])
        results['max_return'] = max(results['daily_returns'])
        results['min_return'] = min(results['daily_returns'])
        results['sharpe_ratio'] = results['avg_return'] / results['std_return'] if results['std_return'] > 0 else 0
    else:
        results['win_rate'] = 0
        results['avg_return'] = 0
        results['avg_daily_trades'] = 0
        results['std_return'] = 0
        results['max_return'] = 0
        results['min_return'] = 0
        results['sharpe_ratio'] = 0
    
    return results

def explore_five_feature_combinations(data, features, start_date, end_date, save_interim=True, interim_frequency=10):
    """探索5特征组合"""
    print("正在探索5特征组合...")
    
    # 生成5特征组合
    combinations = list(itertools.combinations(features, 5))
    print(f"5特征组合数量: {len(combinations)}")
    
    # 初始化结果
    all_results = []
    
    # 对每个组合进行评估
    for i, combination in enumerate(tqdm(combinations)):
        result = evaluate_feature_combination(data, combination, start_date, end_date)
        
        # 只保留有交易的结果
        if result['total_trades'] > 0:
            all_results.append(result)
        
        # 定期保存中间结果
        if save_interim and (i + 1) % interim_frequency == 0:
            # 按总收益率排序
            sorted_results = sorted(all_results, key=lambda x: x['avg_return'], reverse=True)
            
            # 保存中间结果
            interim_file = f"{results_dir}/interim_results_{i+1}.pkl"
            with open(interim_file, 'wb') as f:
                pickle.dump(sorted_results, f)
            
            print(f"已评估 {i+1}/{len(combinations)} 个组合，中间结果已保存到 {interim_file}")
    
    # 按总收益率排序
    all_results = sorted(all_results, key=lambda x: x['avg_return'], reverse=True)
    
    # 保存最终结果
    final_file = f"{results_dir}/five_feature_results.pkl"
    with open(final_file, 'wb') as f:
        pickle.dump(all_results, f)
    
    print(f"5特征组合评估完成，结果已保存到 {final_file}")
    
    return all_results

def analyze_results(results):
    """分析5特征组合结果"""
    print("分析5特征组合结果...")
    
    # 计算统计量
    avg_return_mean = np.mean([r['avg_return'] for r in results])
    avg_return_std = np.std([r['avg_return'] for r in results])
    win_rate_mean = np.mean([r['win_rate'] for r in results])
    win_rate_std = np.std([r['win_rate'] for r in results])
    sharpe_ratio_mean = np.mean([r['sharpe_ratio'] for r in results])
    sharpe_ratio_std = np.std([r['sharpe_ratio'] for r in results])
    
    # 打印统计结果
    print("\n5特征组合统计结果:")
    print(f"  组合数量: {len(results)}")
    print(f"  平均收益率: {avg_return_mean:.4f} ± {avg_return_std:.4f}")
    print(f"  平均胜率: {win_rate_mean:.2f}% ± {win_rate_std:.2f}%")
    print(f"  平均夏普比率: {sharpe_ratio_mean:.4f} ± {sharpe_ratio_std:.4f}")
    
    # 打印前10名组合
    print("\n  前10名组合:")
    for i, result in enumerate(results[:10]):
        feature_str = ', '.join(result['feature_combination'])
        print(f"    {i+1}. {feature_str}")
        print(f"       平均收益率: {result['avg_return']:.4f}, 胜率: {result['win_rate']:.2f}%, 交易次数: {result['total_trades']}")
    
    # 保存统计结果
    stats = {
        'combination_count': len(results),
        'avg_return_mean': avg_return_mean,
        'avg_return_std': avg_return_std,
        'win_rate_mean': win_rate_mean,
        'win_rate_std': win_rate_std,
        'sharpe_ratio_mean': sharpe_ratio_mean,
        'sharpe_ratio_std': sharpe_ratio_std,
        'top_10_combinations': results[:10]
    }
    
    stats_file = f"{results_dir}/five_feature_statistics.pkl"
    with open(stats_file, 'wb') as f:
        pickle.dump(stats, f)
    
    print(f"5特征组合统计结果已保存到 {stats_file}")
    
    # 保存CSV格式的结果
    summary_data = []
    for i, result in enumerate(results):
        feature_str = ', '.join(result['feature_combination'])
        summary_data.append({
            '排名': i + 1,
            '特征组合': feature_str,
            '平均收益率': result['avg_return'],
            '胜率': result['win_rate'],
            '夏普比率': result['sharpe_ratio'],
            '交易次数': result['total_trades'],
            '平均每日交易': result['avg_daily_trades']
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_file = f"{results_dir}/five_feature_summary.csv"
    summary_df.to_csv(summary_file, index=False)
    
    print(f"5特征组合汇总报告已保存到 {summary_file}")
    
    return stats

def generate_recommendations(data, top_combinations, date=None):
    """根据最佳5特征组合生成股票推荐"""
    print("生成股票推荐...")
    
    if date is None:
        date = data['日期'].max()
    else:
        date = pd.to_datetime(date)
    
    print(f"推荐日期: {date.strftime('%Y-%m-%d')}")
    
    # 获取当日数据
    daily_data = data[data['日期'] == date]
    
    # 对每个最佳组合生成推荐
    recommendations = {}
    for i, result in enumerate(top_combinations[:3]):  # 只使用前3个最佳组合
        combination = result['feature_combination']
        
        # 应用特征组合条件
        selected = daily_data.copy()
        for feature in combination:
            if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', 
                         '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势', 
                         '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势', 
                         '连续技术强度10天数趋势', '连续技术强度3天数价格趋势', 
                         '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势', 
                         '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势', 
                         '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
                # 二元特征，使用==1条件
                selected = selected[selected[feature] == 1]
            elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                # 连续值特征，使用>=中位数条件
                median_value = np.median(daily_data[feature])
                selected = selected[selected[feature] >= median_value]
            elif feature == '看涨技术指标数量':
                # 看涨技术指标数量，使用>=3条件
                selected = selected[selected[feature] >= 3]
        
        # 组合名称
        combination_name = f"五特征组合{i+1}"
        
        # 保存推荐结果
        recommendations[combination_name] = selected
        
        # 保存到文件
        if len(selected) > 0:
            output_file = f"{results_dir}/{combination_name}_{date.strftime('%Y%m%d')}.csv"
            selected.to_csv(output_file, index=False)
            print(f"{combination_name}: 推荐 {len(selected)} 只股票，已保存到 {output_file}")
            
            # 打印前10只股票
            print(f"\n{combination_name} 推荐股票列表 (前10只):")
            for j, (_, stock) in enumerate(selected.iterrows()):
                if j < 10:
                    print(f"{j+1}. {stock['股票代码']} {stock['股票名称']}, 技术强度: {stock['技术强度']}, 连续技术强度5天数: {stock['连续技术强度5天数']}, 看涨技术指标数量: {stock['看涨技术指标数量']}")
            
            if len(selected) > 10:
                print(f"... 共 {len(selected)} 只股票")
        else:
            print(f"{combination_name}: 无推荐股票")
    
    return recommendations

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)
    
    # 获取最新日期
    latest_date = df['日期'].max()
    print(f"最新日期: {latest_date.strftime('%Y-%m-%d')}")
    
    # 定义要探索的特征
    features = [
        '技术强度',
        '连续技术强度5天数',
        '看涨技术指标数量',
        '技术强度趋势',
        '连续技术强度5天数趋势',
        '技术指标_RSI反弹',
        '技术指标_KDJ金叉',
        '涨跌幅趋势'
    ]
    
    # 探索5特征组合
    results = explore_five_feature_combinations(
        df, features, '2025-04-01', '2025-04-30', 
        save_interim=True, interim_frequency=10
    )
    
    # 分析结果
    stats = analyze_results(results)
    
    # 生成股票推荐
    recommendations = generate_recommendations(df, results, latest_date)
