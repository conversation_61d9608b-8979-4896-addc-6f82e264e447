import pandas as pd
import numpy as np
import requests
import json
from datetime import datetime, timedelta
import os
import time
import random
from bs4 import BeautifulSoup
import re
import jieba
import jieba.analyse
from collections import Counter

class SentimentAnalyzer:
    """
    情感分析模块，用于分析新闻和社交媒体数据，捕捉市场情绪变化
    """
    
    def __init__(self):
        """初始化情感分析模块"""
        # 创建数据目录
        if not os.path.exists('sentiment_data'):
            os.makedirs('sentiment_data')
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        # 加载情感词典
        self.load_sentiment_dict()
    
    def load_sentiment_dict(self):
        """加载情感词典"""
        # 情感词典文件路径
        dict_file = 'sentiment_data/sentiment_dict.json'
        
        # 如果文件存在，直接加载
        if os.path.exists(dict_file):
            with open(dict_file, 'r', encoding='utf-8') as f:
                self.sentiment_dict = json.load(f)
            print(f"已加载情感词典，共 {len(self.sentiment_dict)} 个词")
            return
        
        # 如果文件不存在，创建简单的情感词典
        print("创建简单的情感词典...")
        
        # 正面词汇
        positive_words = [
            '上涨', '增长', '利好', '突破', '反弹', '强势', '机会', '看好', '牛市', '利润',
            '增持', '扩张', '创新', '领先', '优质', '稳健', '回暖', '复苏', '繁荣', '向好',
            '提升', '改善', '超预期', '高增长', '高回报', '高收益', '高盈利', '高分红', '高股息',
            '龙头', '蓝筹', '白马', '绩优', '成长', '价值', '潜力', '机遇', '红利', '热点'
        ]
        
        # 负面词汇
        negative_words = [
            '下跌', '下滑', '利空', '跌破', '回调', '弱势', '风险', '看空', '熊市', '亏损',
            '减持', '收缩', '停滞', '落后', '劣质', '不稳', '降温', '衰退', '萧条', '恶化',
            '下降', '恶化', '低于预期', '低增长', '低回报', '低收益', '低盈利', '低分红', '低股息',
            '垃圾', '壳股', '题材', '亏损', '退市', '风险', '危机', '泡沫', '暴跌', '崩盘'
        ]
        
        # 创建情感词典
        self.sentiment_dict = {}
        
        # 添加正面词汇
        for word in positive_words:
            self.sentiment_dict[word] = 1
        
        # 添加负面词汇
        for word in negative_words:
            self.sentiment_dict[word] = -1
        
        # 保存情感词典
        with open(dict_file, 'w', encoding='utf-8') as f:
            json.dump(self.sentiment_dict, f, ensure_ascii=False, indent=4)
        
        print(f"已创建并保存情感词典，共 {len(self.sentiment_dict)} 个词")
    
    def get_market_sentiment(self):
        """
        获取市场整体情感
        返回市场情感DataFrame
        """
        print("获取市场整体情感...")
        
        # 市场情感文件路径
        sentiment_file = 'sentiment_data/market_sentiment.xlsx'
        
        # 如果今天已经获取过数据，直接返回
        if os.path.exists(sentiment_file) and self._is_file_updated_today(sentiment_file):
            print(f"今日已获取市场情感数据，直接读取 {sentiment_file}")
            return pd.read_excel(sentiment_file)
        
        try:
            # 获取财经新闻
            news_data = self._get_financial_news()
            
            # 获取股票论坛帖子
            forum_data = self._get_stock_forum_posts()
            
            # 获取分析师评论
            analyst_data = self._get_analyst_comments()
            
            # 分析新闻情感
            news_sentiment = self._analyze_text_sentiment(news_data)
            
            # 分析论坛帖子情感
            forum_sentiment = self._analyze_text_sentiment(forum_data)
            
            # 分析分析师评论情感
            analyst_sentiment = self._analyze_text_sentiment(analyst_data)
            
            # 计算综合情感得分
            overall_sentiment = news_sentiment * 0.5 + forum_sentiment * 0.3 + analyst_sentiment * 0.2
            
            # 创建市场情感DataFrame
            sentiment_data = pd.DataFrame({
                '日期': [datetime.now().strftime('%Y-%m-%d')],
                '新闻情感得分': [news_sentiment],
                '论坛情感得分': [forum_sentiment],
                '分析师情感得分': [analyst_sentiment],
                '综合情感得分': [overall_sentiment],
                '情感状态': [self._sentiment_score_to_state(overall_sentiment)]
            })
            
            # 保存市场情感数据
            sentiment_data.to_excel(sentiment_file, index=False)
            print(f"已将市场情感数据保存至 {sentiment_file}")
            
            return sentiment_data
        except Exception as e:
            print(f"获取市场情感数据失败: {e}")
            
            # 如果获取失败但文件存在，返回最近的数据
            if os.path.exists(sentiment_file):
                print(f"返回最近的市场情感数据")
                return pd.read_excel(sentiment_file)
            
            # 如果文件不存在，返回空DataFrame
            return pd.DataFrame()
    
    def get_stock_sentiment(self, stock_codes):
        """
        获取特定股票的情感
        参数:
            stock_codes: 股票代码列表
        返回股票情感DataFrame
        """
        print("获取股票情感...")
        
        # 股票情感文件路径
        stock_sentiment_file = 'sentiment_data/stock_sentiment.xlsx'
        
        # 如果今天已经获取过数据，直接返回
        if os.path.exists(stock_sentiment_file) and self._is_file_updated_today(stock_sentiment_file):
            print(f"今日已获取股票情感数据，直接读取 {stock_sentiment_file}")
            stock_sentiment = pd.read_excel(stock_sentiment_file)
            
            # 检查是否包含所有需要的股票
            existing_codes = set(stock_sentiment['股票代码'].unique())
            missing_codes = [code for code in stock_codes if code not in existing_codes]
            
            if not missing_codes:
                return stock_sentiment
            
            print(f"需要获取 {len(missing_codes)} 只新股票的情感数据")
            stock_codes = missing_codes
        
        try:
            stock_sentiment_data = pd.DataFrame()
            
            for code in stock_codes:
                # 获取股票相关新闻
                stock_news = self._get_stock_news(code)
                
                # 获取股票相关帖子
                stock_posts = self._get_stock_posts(code)
                
                # 获取股票相关分析师评论
                stock_analyst = self._get_stock_analyst_comments(code)
                
                # 分析股票新闻情感
                news_sentiment = self._analyze_text_sentiment(stock_news)
                
                # 分析股票帖子情感
                posts_sentiment = self._analyze_text_sentiment(stock_posts)
                
                # 分析股票分析师评论情感
                analyst_sentiment = self._analyze_text_sentiment(stock_analyst)
                
                # 计算股票综合情感得分
                overall_sentiment = news_sentiment * 0.5 + posts_sentiment * 0.3 + analyst_sentiment * 0.2
                
                # 添加到股票情感DataFrame
                stock_sentiment_data = pd.concat([stock_sentiment_data, pd.DataFrame({
                    '日期': [datetime.now().strftime('%Y-%m-%d')],
                    '股票代码': [code],
                    '新闻情感得分': [news_sentiment],
                    '论坛情感得分': [posts_sentiment],
                    '分析师情感得分': [analyst_sentiment],
                    '综合情感得分': [overall_sentiment],
                    '情感状态': [self._sentiment_score_to_state(overall_sentiment)]
                })], ignore_index=True)
                
                # 随机暂停1-3秒，避免请求过于频繁
                time.sleep(random.uniform(1, 3))
            
            # 如果文件存在，合并新数据和旧数据
            if os.path.exists(stock_sentiment_file):
                old_data = pd.read_excel(stock_sentiment_file)
                
                # 删除旧数据中与新数据重复的记录
                old_data = old_data[~old_data['股票代码'].isin(stock_sentiment_data['股票代码'])]
                
                # 合并新旧数据
                stock_sentiment_data = pd.concat([old_data, stock_sentiment_data], ignore_index=True)
            
            # 保存股票情感数据
            stock_sentiment_data.to_excel(stock_sentiment_file, index=False)
            print(f"已将股票情感数据保存至 {stock_sentiment_file}")
            
            return stock_sentiment_data
        except Exception as e:
            print(f"获取股票情感数据失败: {e}")
            
            # 如果获取失败但文件存在，返回最近的数据
            if os.path.exists(stock_sentiment_file):
                print(f"返回最近的股票情感数据")
                return pd.read_excel(stock_sentiment_file)
            
            # 如果文件不存在，返回空DataFrame
            return pd.DataFrame()
    
    def _is_file_updated_today(self, file_path):
        """
        检查文件是否在今天更新过
        参数:
            file_path: 文件路径
        返回布尔值
        """
        if not os.path.exists(file_path):
            return False
        
        file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
        today = datetime.now().date()
        
        return file_time.date() == today
    
    def _get_financial_news(self):
        """
        获取财经新闻
        返回新闻文本列表
        """
        # 模拟获取财经新闻
        # 实际应用中，可以从财经网站爬取新闻
        return [
            "央行降准0.5个百分点，释放长期资金约1万亿元，市场流动性充裕",
            "国家统计局公布最新数据，GDP同比增长5.2%，经济稳步复苏",
            "证监会发布新规，加强对上市公司监管，提高信息披露质量",
            "多家券商发布研报，看好下半年A股市场表现，建议关注科技和消费板块",
            "外资连续三个月净流入A股市场，显示国际投资者对中国经济的信心增强",
            "央行行长表示，将继续实施稳健的货币政策，保持流动性合理充裕",
            "财政部发布数据，前5个月财政收入同比增长8.5%，财政状况良好",
            "多家上市公司发布业绩预告，科技和医药板块表现亮眼",
            "国务院常务会议强调，要加大对实体经济的支持力度，促进经济稳定增长",
            "沪深两市成交额连续三日突破1万亿元，市场活跃度提升"
        ]
    
    def _get_stock_forum_posts(self):
        """
        获取股票论坛帖子
        返回帖子文本列表
        """
        # 模拟获取股票论坛帖子
        # 实际应用中，可以从股票论坛爬取帖子
        return [
            "大盘已经企稳，接下来有望继续上涨，建议关注科技板块",
            "今天大盘大跌，是不是要开始调整了？感觉有点担心",
            "刚买了一只银行股，感觉估值很低，未来有上涨空间",
            "最近新能源汽车板块很火，有没有推荐的股票？",
            "医药板块已经调整很久了，现在是不是可以布局了？",
            "今天券商股集体大涨，是不是有什么利好消息？",
            "大家对下周行情怎么看？我感觉可能会继续震荡",
            "最近外资一直在买入A股，这是个很好的信号",
            "今天我的股票全部飘红，心情非常好，牛市要来了",
            "最近市场波动很大，建议大家控制仓位，不要满仓"
        ]
    
    def _get_analyst_comments(self):
        """
        获取分析师评论
        返回评论文本列表
        """
        # 模拟获取分析师评论
        # 实际应用中，可以从研报或财经媒体获取分析师评论
        return [
            "我们认为当前市场估值处于历史低位，具有较高的投资价值，建议逢低布局",
            "随着政策面的持续发力，市场有望迎来一波上涨行情，重点关注科技和消费板块",
            "当前市场仍处于震荡阶段，建议投资者保持谨慎，控制仓位",
            "下半年经济有望继续复苏，A股市场有望走出一波结构性行情",
            "近期外部不确定性因素增加，市场可能面临一定压力，建议关注防御性板块",
            "随着业绩的逐步兑现，高景气度行业有望获得资金青睐，推荐关注新能源、半导体等领域",
            "当前市场情绪较为谨慎，但部分板块已经具备配置价值，可以适当布局",
            "我们预计下半年货币政策将保持稳健，流动性环境有利于权益市场",
            "近期北向资金持续流入，外资对A股的配置意愿增强，这是市场的积极信号",
            "当前A股估值处于全球主要市场的低位，长期投资价值凸显"
        ]
    
    def _get_stock_news(self, stock_code):
        """
        获取特定股票的相关新闻
        参数:
            stock_code: 股票代码
        返回新闻文本列表
        """
        # 模拟获取股票相关新闻
        # 实际应用中，可以从财经网站搜索特定股票的新闻
        stock_name = f"股票{stock_code[-4:]}"
        
        return [
            f"{stock_name}发布2023年年报，净利润同比增长15.2%，超出市场预期",
            f"{stock_name}宣布新的战略合作，有望拓展新的业务领域",
            f"{stock_name}获得多家券商推荐，目标价上调10%",
            f"机构调研：{stock_name}未来发展前景广阔，维持买入评级",
            f"{stock_name}技术面突破重要压力位，后市有望继续上涨"
        ]
    
    def _get_stock_posts(self, stock_code):
        """
        获取特定股票的相关帖子
        参数:
            stock_code: 股票代码
        返回帖子文本列表
        """
        # 模拟获取股票相关帖子
        # 实际应用中，可以从股票论坛搜索特定股票的帖子
        stock_name = f"股票{stock_code[-4:]}"
        
        return [
            f"刚买了{stock_name}，感觉未来有上涨空间，大家怎么看？",
            f"{stock_name}今天大涨5%，是不是有什么利好消息？",
            f"对{stock_name}的业绩很看好，准备长期持有",
            f"{stock_name}最近走势不错，突破了前期高点",
            f"有没有人关注{stock_name}？最近有什么消息吗？"
        ]
    
    def _get_stock_analyst_comments(self, stock_code):
        """
        获取特定股票的分析师评论
        参数:
            stock_code: 股票代码
        返回评论文本列表
        """
        # 模拟获取股票相关分析师评论
        # 实际应用中，可以从研报或财经媒体获取特定股票的分析师评论
        stock_name = f"股票{stock_code[-4:]}"
        
        return [
            f"我们维持对{stock_name}的买入评级，目标价上调至XX元",
            f"{stock_name}所在行业景气度持续提升，公司有望受益",
            f"考虑到{stock_name}的业绩增长和估值水平，我们认为当前股价具有吸引力",
            f"{stock_name}的技术指标显示出积极信号，短期内有望突破前期高点",
            f"我们预计{stock_name}今年净利润将同比增长20%左右，维持增持评级"
        ]
    
    def _analyze_text_sentiment(self, texts):
        """
        分析文本情感
        参数:
            texts: 文本列表
        返回情感得分（-1到1之间，负值表示负面情感，正值表示正面情感）
        """
        if not texts:
            return 0
        
        # 合并所有文本
        all_text = ' '.join(texts)
        
        # 分词
        words = jieba.lcut(all_text)
        
        # 计算情感得分
        sentiment_score = 0
        word_count = 0
        
        for word in words:
            if word in self.sentiment_dict:
                sentiment_score += self.sentiment_dict[word]
                word_count += 1
        
        # 如果没有情感词，返回中性得分0
        if word_count == 0:
            return 0
        
        # 归一化情感得分到-1到1之间
        normalized_score = sentiment_score / word_count
        
        return normalized_score
    
    def _sentiment_score_to_state(self, score):
        """
        将情感得分转换为情感状态
        参数:
            score: 情感得分
        返回情感状态（非常负面、负面、中性、正面、非常正面）
        """
        if score < -0.6:
            return '非常负面'
        elif score < -0.2:
            return '负面'
        elif score < 0.2:
            return '中性'
        elif score < 0.6:
            return '正面'
        else:
            return '非常正面'

if __name__ == "__main__":
    # 测试情感分析模块
    analyzer = SentimentAnalyzer()
    
    # 获取市场整体情感
    market_sentiment = analyzer.get_market_sentiment()
    print("\n市场整体情感:")
    print(market_sentiment)
    
    # 获取特定股票的情感
    stock_codes = ['sh.600000', 'sh.600036', 'sz.000001']
    stock_sentiment = analyzer.get_stock_sentiment(stock_codes)
    print("\n股票情感:")
    print(stock_sentiment)
