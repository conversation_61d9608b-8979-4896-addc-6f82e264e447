"""
错误处理模块 - 用于捕获和显示程序运行时的错误
"""

import sys
import traceback
import tkinter as tk
from tkinter import messagebox

def show_error_dialog(error_type, error_value, tb):
    """显示错误对话框"""
    error_msg = f"程序运行出错:\n\n{error_type.__name__}: {error_value}\n\n"
    error_msg += "详细错误信息:\n"
    error_msg += "".join(traceback.format_tb(tb))
    
    # 创建一个简单的Tkinter窗口来显示错误
    try:
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        messagebox.showerror("程序错误", error_msg)
        root.destroy()
    except:
        # 如果Tkinter也出错，则使用控制台输出
        print(error_msg, file=sys.stderr)

def install_error_handler():
    """安装全局错误处理器"""
    def global_exception_handler(error_type, error_value, tb):
        show_error_dialog(error_type, error_value, tb)
        # 调用原始的异常处理器
        sys.__excepthook__(error_type, error_value, tb)
    
    # 设置全局异常处理器
    sys.excepthook = global_exception_handler
