@echo off
echo 技术指标组合策略
echo ====================================
echo 1. 回测技术指标组合策略
echo 2. 生成今日股票推荐
echo 3. 生成指定日期股票推荐
echo ====================================
set /p choice=请选择操作 (1-3): 

if "%choice%"=="1" (
    echo 回测技术指标组合策略
    set /p start_date=请输入回测开始日期 (YYYY-MM-DD): 
    set /p end_date=请输入回测结束日期 (YYYY-MM-DD): 
    python run_technical_indicator.py --backtest --start_date %start_date% --end_date %end_date% --output 技术指标组合策略回测结果.txt
) else if "%choice%"=="2" (
    echo 生成今日股票推荐
    python run_technical_indicator.py --recommend --output 技术指标组合策略推荐股票.xlsx
) else if "%choice%"=="3" (
    echo 生成指定日期股票推荐
    set /p date=请输入日期 (YYYY-MM-DD): 
    python run_technical_indicator.py --recommend --date %date% --output 技术指标组合策略推荐股票.xlsx
) else (
    echo 无效的选择
)

pause
