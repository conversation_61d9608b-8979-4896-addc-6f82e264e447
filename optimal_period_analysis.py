import pandas as pd
import numpy as np
import os
import joblib
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import time

def analyze_optimal_period():
    """
    分析技术强度累积的最优周期，测试从1天到20天不同的累积周期
    """
    print("开始分析技术强度累积的最优周期...")
    
    # 创建结果目录
    if not os.path.exists('optimal_period_results'):
        os.makedirs('optimal_period_results')
    
    # 加载股票数据
    print("\n加载股票数据...")
    try:
        stock_data = pd.read_excel('股票明细.xlsx')
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
        
        # 转换日期格式
        if isinstance(stock_data['日期'].iloc[0], str):
            stock_data['日期'] = pd.to_datetime(stock_data['日期'])
        
        # 获取所有日期并排序
        all_dates = sorted(stock_data['日期'].unique())
        print(f"数据集中的日期范围: {all_dates[0]} 到 {all_dates[-1]}")
        
        # 按股票代码和日期排序
        stock_data = stock_data.sort_values(['股票代码', '日期'])
        
        # 数据预处理
        print("\n数据预处理...")
        
        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')
            
            # 计算连续技术强度天数（连续多少天为100）
            consecutive_days = []
            current_count = 0
            
            for strength in group['技术强度'].values:
                if strength == 100:
                    current_count += 1
                else:
                    current_count = 0
                consecutive_days.append(current_count)
            
            # 更新原始数据
            stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days
            
            # 计算技术强度累积值（从1天到20天）
            for period in range(1, 21):
                # 计算连续N天技术强度求和
                cumulative_strength = group['技术强度'].copy()
                for i in range(1, period):
                    cumulative_strength += group['技术强度'].shift(i).fillna(0)
                
                # 更新原始数据
                stock_data.loc[group.index, f'连续技术强度{period}天数'] = cumulative_strength
            
            # 计算次日涨跌方向和收益率
            group['次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)
            group['次日收益率'] = (group['当前价格'].shift(-1) / group['当前价格'] - 1) * 100
            
            # 计算后日涨跌方向和收益率（买入后的第二天，即卖出日）
            group['后日涨跌方向'] = (group['当前价格'].shift(-2) > group['当前价格'].shift(-1)).astype(int)
            group['后日收益率'] = (group['当前价格'].shift(-2) / group['当前价格'].shift(-1) - 1) * 100
            
            # 计算两日收益率（买入后持有两天的总收益）
            group['两日收益率'] = (group['当前价格'].shift(-2) / group['当前价格'] - 1) * 100
            
            # 计算是否盈利（两日收益率为正）
            group['是否盈利'] = (group['两日收益率'] > 0).astype(int)
            
            # 更新原始数据
            stock_data.loc[group.index, '次日涨跌方向'] = group['次日涨跌方向']
            stock_data.loc[group.index, '次日收益率'] = group['次日收益率']
            stock_data.loc[group.index, '后日涨跌方向'] = group['后日涨跌方向']
            stock_data.loc[group.index, '后日收益率'] = group['后日收益率']
            stock_data.loc[group.index, '两日收益率'] = group['两日收益率']
            stock_data.loc[group.index, '是否盈利'] = group['是否盈利']
        
        # 删除没有完整数据的记录
        stock_data = stock_data.dropna(subset=['次日涨跌方向', '次日收益率', '后日涨跌方向', '后日收益率', '两日收益率', '是否盈利'])
        
        print(f"处理后的数据集大小: {len(stock_data)} 条记录")
        
        # 创建结果DataFrame
        results = pd.DataFrame(columns=['周期', '准确率', '精确率', '召回率', 'F1分数', 'AUC', '特征重要性', '训练时间'])
        
        # 基础特征（不包括技术强度累积值）
        base_features = ['技术强度', '涨跌幅', '当前价格']
        
        # 对每个周期进行评估
        for period in range(1, 21):
            print(f"\n评估周期 {period}...")
            start_time = time.time()
            
            # 特征集合：基础特征 + 当前周期的技术强度累积值
            features = base_features + [f'连续技术强度{period}天数']
            
            # 准备训练数据
            X = stock_data[features]
            y = stock_data['是否盈利']
            
            # 处理缺失值
            valid_indices = ~X.isnull().any(axis=1)
            X = X[valid_indices]
            y = y[valid_indices]
            
            print(f"有效数据集大小: {len(X)} 条记录")
            
            # 标准化特征
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # 划分训练集和测试集
            X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)
            
            # 训练随机森林模型
            rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
            rf_model.fit(X_train, y_train)
            
            # 模型评估
            rf_pred = rf_model.predict(X_test)
            rf_prob = rf_model.predict_proba(X_test)[:, 1]
            
            accuracy = accuracy_score(y_test, rf_pred)
            precision = precision_score(y_test, rf_pred)
            recall = recall_score(y_test, rf_pred)
            f1 = f1_score(y_test, rf_pred)
            auc = roc_auc_score(y_test, rf_prob)
            
            # 特征重要性
            importances = rf_model.feature_importances_
            period_importance = importances[features.index(f'连续技术强度{period}天数')]
            
            # 计算训练时间
            train_time = time.time() - start_time
            
            # 添加到结果
            new_row = pd.DataFrame({
                '周期': [period],
                '准确率': [accuracy],
                '精确率': [precision],
                '召回率': [recall],
                'F1分数': [f1],
                'AUC': [auc],
                '特征重要性': [period_importance],
                '训练时间': [train_time]
            })
            results = pd.concat([results, new_row], ignore_index=True)
            
            print(f"周期 {period} 评估结果:")
            print(f"准确率: {accuracy:.4f}")
            print(f"精确率: {precision:.4f}")
            print(f"召回率: {recall:.4f}")
            print(f"F1分数: {f1:.4f}")
            print(f"AUC: {auc:.4f}")
            print(f"特征重要性: {period_importance:.4f}")
            print(f"训练时间: {train_time:.2f}秒")
        
        # 保存结果
        results.to_excel('optimal_period_results/period_evaluation.xlsx', index=False)
        
        # 绘制准确率图表
        plt.figure(figsize=(12, 6))
        plt.plot(results['周期'], results['准确率'], marker='o', linestyle='-', linewidth=2)
        plt.title('不同周期的准确率')
        plt.xlabel('技术强度累积周期（天）')
        plt.ylabel('准确率')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xticks(results['周期'])
        plt.savefig('optimal_period_results/accuracy_by_period.png')
        
        # 绘制特征重要性图表
        plt.figure(figsize=(12, 6))
        plt.plot(results['周期'], results['特征重要性'], marker='o', linestyle='-', linewidth=2, color='green')
        plt.title('不同周期的特征重要性')
        plt.xlabel('技术强度累积周期（天）')
        plt.ylabel('特征重要性')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xticks(results['周期'])
        plt.savefig('optimal_period_results/importance_by_period.png')
        
        # 绘制F1分数图表
        plt.figure(figsize=(12, 6))
        plt.plot(results['周期'], results['F1分数'], marker='o', linestyle='-', linewidth=2, color='red')
        plt.title('不同周期的F1分数')
        plt.xlabel('技术强度累积周期（天）')
        plt.ylabel('F1分数')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xticks(results['周期'])
        plt.savefig('optimal_period_results/f1_by_period.png')
        
        # 找出最优周期
        best_accuracy_period = results.loc[results['准确率'].idxmax(), '周期']
        best_f1_period = results.loc[results['F1分数'].idxmax(), '周期']
        best_importance_period = results.loc[results['特征重要性'].idxmax(), '周期']
        
        print("\n最优周期分析结果:")
        print(f"准确率最高的周期: {best_accuracy_period}天")
        print(f"F1分数最高的周期: {best_f1_period}天")
        print(f"特征重要性最高的周期: {best_importance_period}天")
        
        # 综合评分（准确率 * 0.4 + F1分数 * 0.4 + 特征重要性 * 0.2）
        results['综合评分'] = results['准确率'] * 0.4 + results['F1分数'] * 0.4 + results['特征重要性'] * 0.2
        best_overall_period = results.loc[results['综合评分'].idxmax(), '周期']
        
        print(f"综合评分最高的周期: {best_overall_period}天")
        
        # 保存更新后的结果
        results.to_excel('optimal_period_results/period_evaluation_with_score.xlsx', index=False)
        
        # 绘制综合评分图表
        plt.figure(figsize=(12, 6))
        plt.plot(results['周期'], results['综合评分'], marker='o', linestyle='-', linewidth=2, color='purple')
        plt.title('不同周期的综合评分')
        plt.xlabel('技术强度累积周期（天）')
        plt.ylabel('综合评分')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xticks(results['周期'])
        plt.savefig('optimal_period_results/overall_score_by_period.png')
        
        print("\n分析完成！结果已保存至 optimal_period_results 目录")
        
        return results
    
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    analyze_optimal_period()
