"""
akshare包装器 - 提供更健壮的akshare API调用
"""

import pandas as pd
import time

# 尝试导入akshare
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    print("警告: 未能导入akshare库")

# 定义重试次数和等待时间
MAX_RETRIES = 3
RETRY_WAIT_TIME = 2  # 秒

def safe_api_call(func, *args, **kwargs):
    """
    安全地调用akshare API，包含错误处理和重试机制

    参数:
        func: 要调用的akshare函数
        *args, **kwargs: 传递给函数的参数

    返回:
        DataFrame: API调用结果，如果失败则返回空DataFrame
    """
    if not AKSHARE_AVAILABLE:
        print("错误: akshare库不可用")
        return pd.DataFrame()

    # 重试机制
    for attempt in range(MAX_RETRIES):
        try:
            # 直接调用API
            result = func(*args, **kwargs)

            # 检查结果
            if result is None:
                print(f"警告: API调用返回None (尝试 {attempt+1}/{MAX_RETRIES})")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_WAIT_TIME)
                    continue
                return pd.DataFrame()

            if isinstance(result, pd.DataFrame) and result.empty:
                print(f"警告: API调用返回空DataFrame (尝试 {attempt+1}/{MAX_RETRIES})")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_WAIT_TIME)
                    continue
                return pd.DataFrame()

            return result
        except Exception as e:
            print(f"API调用出错 (尝试 {attempt+1}/{MAX_RETRIES}): {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_WAIT_TIME)
                continue
            return pd.DataFrame()

    # 所有重试都失败
    return pd.DataFrame()

def get_stock_codes():
    """
    获取所有A股股票代码

    返回:
        list: 股票代码列表
    """
    # 尝试使用stock_info_a_code_name接口
    df = safe_api_call(ak.stock_info_a_code_name)
    if not df.empty and 'code' in df.columns:
        # 处理股票代码格式
        stock_codes = []
        for code in df['code'].tolist():
            # 如果代码带有前缀（如sh或sz），去掉前缀
            if code.startswith('sh') or code.startswith('sz'):
                code = code[2:]
            stock_codes.append(code)

        if len(stock_codes) > 100:  # 确保获取到足够多的股票代码
            print(f"使用stock_info_a_code_name接口获取到 {len(stock_codes)} 只股票")
            return stock_codes
        else:
            print(f"使用stock_info_a_code_name接口只获取到 {len(stock_codes)} 只股票，尝试其他方法")

    # 如果第一个接口失败，尝试使用stock_zh_a_spot_em接口
    df = safe_api_call(ak.stock_zh_a_spot_em)
    if not df.empty:
        # 检查列名
        code_column = '代码' if '代码' in df.columns else None
        if code_column:
            stock_codes = df[code_column].tolist()
            if len(stock_codes) > 100:  # 确保获取到足够多的股票代码
                print(f"使用stock_zh_a_spot_em接口获取到 {len(stock_codes)} 只股票")
                return stock_codes
            else:
                print(f"使用stock_zh_a_spot_em接口只获取到 {len(stock_codes)} 只股票，尝试其他方法")

    # 如果所有接口都失败，返回扩展的默认股票代码列表
    default_codes = [
        "600000", "600036", "601398", "000001", "000002",  # 大型银行
        "600519", "000858", "600887", "600309", "603288",  # 消费龙头
        "601318", "601601", "601628", "601336", "601688",  # 保险证券
        "600030", "600837", "601688", "601211", "601066",  # 券商
        "600276", "600196", "603259", "603160", "300015",  # 医药
        "000651", "000333", "000625", "000100", "000063",  # 家电科技
        "601857", "600028", "601988", "601288", "601328",  # 能源金融
        "600050", "600104", "600585", "600019", "600010",  # 工业
        "300059", "300033", "002230", "002415", "300760",  # 创业板
        "688981", "688111", "688012", "688599", "688561"   # 科创板
    ]
    print(f"所有方法都失败，使用扩展的默认股票代码: {len(default_codes)} 只")
    return default_codes

def get_trading_calendar(start_date, end_date):
    """
    获取交易日历

    参数:
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD

    返回:
        DataFrame: 交易日历，包含calendar_date和is_trading_day列
    """
    # 尝试使用tool_trade_date_hist_sina接口
    df = safe_api_call(ak.tool_trade_date_hist_sina)
    if not df.empty and 'trade_date' in df.columns:
        # 转换日期列为日期类型
        df['trade_date'] = pd.to_datetime(df['trade_date'])

        # 筛选指定日期范围内的交易日
        start_date_dt = pd.to_datetime(start_date)
        end_date_dt = pd.to_datetime(end_date)
        df = df[
            (df['trade_date'] >= start_date_dt) &
            (df['trade_date'] <= end_date_dt)
        ]

        # 重命名列，使其与原来的结构一致
        df = df.rename(columns={
            'trade_date': 'calendar_date',
        })

        # 添加is_trading_day列，所有日期都是交易日
        df['is_trading_day'] = 1

        print(f"交易日历下载完成，共 {len(df)} 个交易日")
        return df

    # 如果接口失败，生成日期范围
    print("交易日历接口失败，生成日期范围")

    # 转换为日期对象
    start_date_obj = pd.to_datetime(start_date)
    end_date_obj = pd.to_datetime(end_date)

    # 生成日期范围
    date_range = pd.date_range(start=start_date_obj, end=end_date_obj)

    # 创建交易日历DataFrame
    calendar_df = pd.DataFrame()
    calendar_df['calendar_date'] = date_range
    calendar_df['is_trading_day'] = 1

    # 标记周末为非交易日
    calendar_df['is_trading_day'] = calendar_df['calendar_date'].apply(
        lambda x: 0 if x.weekday() >= 5 else 1
    )

    print(f"生成了 {len(calendar_df)} 天的日期范围，其中 {calendar_df['is_trading_day'].sum()} 天为交易日")
    return calendar_df

def get_stock_data_for_date(date_str, stock_code=None):
    """
    获取指定日期的股票数据

    参数:
        date_str: 日期字符串，格式为YYYY-MM-DD
        stock_code: 可选，指定股票代码。如果为None，则获取所有股票的数据

    返回:
        DataFrame: 股票数据
    """
    # 如果指定了股票代码，使用单只股票的下载方法
    if stock_code is not None:
        # 根据股票代码判断是上证还是深证
        if stock_code.startswith('6'):
            stock_code_full = f"sh{stock_code}"
        else:
            stock_code_full = f"sz{stock_code}"

        # 获取单只股票的历史数据（仅指定日期）
        df = safe_api_call(
            ak.stock_zh_a_hist,
            symbol=stock_code_full,
            period="daily",
            start_date=date_str,
            end_date=date_str,
            adjust="qfq"
        )

        if not df.empty:
            # 添加股票代码列
            df['code'] = stock_code

            # 重命名列，使其与我们的数据结构一致
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '最高': 'high',
                '最低': 'low',
                '收盘': 'close',
                '成交量': 'volume',
                '成交额': 'amount',
                '涨跌幅': 'pctChg'
            })

            # 选择需要的列
            columns = ['date', 'code', 'open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']
            available_columns = [col for col in columns if col in df.columns]
            result_df = df[available_columns]

            print(f"使用stock_zh_a_hist接口获取到股票 {stock_code} 的数据")
            return result_df

    # 如果没有指定股票代码，使用批量下载方法
    # 首先获取所有股票代码
    print("获取所有股票代码...")
    stock_codes = get_stock_codes()
    if not stock_codes:
        print("无法获取股票代码列表")
        return pd.DataFrame()

    print(f"获取到 {len(stock_codes)} 只股票的代码")

    # 使用多批次下载，每批次最多50只股票
    batch_size = 50
    all_data = []
    total_batches = (len(stock_codes) + batch_size - 1) // batch_size

    print(f"将分 {total_batches} 批下载数据，每批 {batch_size} 只股票")

    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(stock_codes))
        batch_codes = stock_codes[start_idx:end_idx]

        print(f"正在下载第 {batch_idx+1}/{total_batches} 批，共 {len(batch_codes)} 只股票")

        batch_data = []
        for code in batch_codes:
            try:
                # 根据股票代码判断是上证还是深证
                if code.startswith('6'):
                    stock_code_full = f"sh{code}"
                else:
                    stock_code_full = f"sz{code}"

                # 获取单只股票的历史数据
                stock_df = safe_api_call(
                    ak.stock_zh_a_hist,
                    symbol=stock_code_full,
                    period="daily",
                    start_date=date_str,
                    end_date=date_str,
                    adjust="qfq"
                )

                if not stock_df.empty:
                    # 添加股票代码列
                    stock_df['code'] = code

                    # 重命名列，使其与我们的数据结构一致
                    stock_df = stock_df.rename(columns={
                        '日期': 'date',
                        '开盘': 'open',
                        '最高': 'high',
                        '最低': 'low',
                        '收盘': 'close',
                        '成交量': 'volume',
                        '成交额': 'amount',
                        '涨跌幅': 'pctChg'
                    })

                    batch_data.append(stock_df)
            except Exception as e:
                print(f"获取股票 {code} 数据时出错: {e}")
                continue

        if batch_data:
            # 合并批次数据
            batch_df = pd.concat(batch_data, ignore_index=True)
            all_data.append(batch_df)
            print(f"第 {batch_idx+1} 批数据下载完成，获取到 {len(batch_df)} 条记录")
        else:
            print(f"第 {batch_idx+1} 批数据下载失败，未获取到任何数据")

    if all_data:
        # 合并所有批次的数据
        result_df = pd.concat(all_data, ignore_index=True)

        # 确保必要的列存在
        required_columns = ['date', 'code', 'open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']
        for col in required_columns:
            if col not in result_df.columns:
                print(f"警告: 列 '{col}' 不存在，将创建空列")
                result_df[col] = None

        # 选择需要的列
        available_columns = [col for col in required_columns if col in result_df.columns]
        result_df = result_df[available_columns]

        print(f"所有批次数据下载完成，共获取到 {len(result_df)} 条记录")

        # 验证数据
        if 'open' in result_df.columns and 'close' in result_df.columns:
            valid_data = result_df[result_df['open'].notna() & result_df['close'].notna()]
            if len(valid_data) < len(result_df):
                print(f"警告: 有 {len(result_df) - len(valid_data)} 条记录的开盘价或收盘价为空")

            print(f"数据验证: 有效数据 {len(valid_data)} 条")
            return result_df
        else:
            print("警告: 数据缺少关键字段")
            return result_df

    # 如果所有方法都失败，返回空DataFrame
    print("所有批量下载方法都失败，返回空DataFrame")
    return pd.DataFrame()
