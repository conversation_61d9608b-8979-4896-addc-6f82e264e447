"""
性能测试模块
用于测试股票数据下载的性能
"""

import os
import time
import pandas as pd
import datetime
import argparse
import threading
import queue
import baostock as bs
import stock_data_manager as sdm

def test_download_performance(start_date, end_date, num_threads=30, test_stocks=None, max_stocks=None):
    """
    测试下载性能
    
    参数:
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD
        num_threads: 线程数量
        test_stocks: 测试用的股票代码列表，如果为None则使用所有股票
        max_stocks: 最大测试股票数量，如果为None则不限制
        
    返回:
        dict: 性能测试结果
    """
    print(f"开始性能测试: 从 {start_date} 到 {end_date}, 使用 {num_threads} 个线程")
    
    # 记录开始时间
    total_start_time = time.time()
    
    # 登录baostock
    print("登录baostock...")
    bs_login = bs.login()
    print(f"登录状态: {bs_login.error_code}, {bs_login.error_msg}")
    
    try:
        # 获取股票代码
        if test_stocks is None:
            # 获取上证和深证所有股票列表
            rs = bs.query_all_stock(day=datetime.datetime.now().strftime('%Y-%m-%d'))
            data_list = []
            while (rs.error_code == '0') & rs.next():
                data_list.append(rs.get_row_data())
            stock_list_df = pd.DataFrame(data_list, columns=rs.fields)
            stock_codes = stock_list_df['code'].tolist()
            print(f"共找到 {len(stock_codes)} 只股票")
        else:
            stock_codes = test_stocks
            print(f"使用提供的 {len(stock_codes)} 只测试股票")
        
        # 限制股票数量
        if max_stocks is not None and max_stocks > 0 and max_stocks < len(stock_codes):
            stock_codes = stock_codes[:max_stocks]
            print(f"限制测试股票数量为 {max_stocks} 只")
        
        # 下载交易日历
        print("下载交易日历...")
        rs = bs.query_trade_dates(start_date=start_date, end_date=end_date)
        data_list = []
        while (rs.error_code == '0') & rs.next():
            data_list.append(rs.get_row_data())
        calendar_df = pd.DataFrame(data_list, columns=rs.fields)
        calendar_df['calendar_date'] = pd.to_datetime(calendar_df['calendar_date'])
        calendar_df['is_trading_day'] = calendar_df['is_trading_day'].apply(lambda x: 1 if x == '1' else 0)
        
        # 提取交易日列表
        trading_days = calendar_df[calendar_df['is_trading_day'] == 1]['calendar_date'].tolist()
        print(f"交易日历中包含 {len(trading_days)} 个交易日")
        
        # 性能统计
        performance_stats = {
            'start_date': start_date,
            'end_date': end_date,
            'num_threads': num_threads,
            'num_stocks': len(stock_codes),
            'num_trading_days': len(trading_days),
            'total_start_time': total_start_time,
            'days_stats': [],
            'total_records': 0,
            'total_success': 0,
            'total_error': 0
        }
        
        # 按日期下载和保存股票数据
        for trading_day in trading_days:
            day_str = trading_day.strftime('%Y-%m-%d')
            print(f"\n开始下载日期 {day_str} 的股票数据")
            
            # 记录该日期的开始时间
            day_start_time = time.time()
            
            # 下载该日期的所有股票数据
            day_stock_data = []
            error_count = 0
            success_count = 0
            
            # 创建线程安全的队列
            result_queue = queue.Queue()
            
            # 定义线程函数
            def download_batch(batch_codes, thread_id):
                batch_results = []
                batch_success = 0
                batch_error = 0
                batch_start_time = time.time()
                
                for code in batch_codes:
                    try:
                        # 下载单只股票单日数据
                        rs = bs.query_history_k_data_plus(
                            code,
                            "date,code,open,high,low,close,volume,amount,pctChg",
                            start_date=day_str,
                            end_date=day_str,
                            frequency="d",
                            adjustflag="3"  # 3表示前复权
                        )
                        
                        # 检查API调用是否成功
                        if rs.error_code != '0':
                            batch_error += 1
                            continue
                        
                        # 处理数据
                        data_list = []
                        while rs.next():
                            row_data = rs.get_row_data()
                            if row_data and len(row_data) > 0:
                                data_list.append(row_data)
                        
                        if data_list:
                            # 创建DataFrame
                            df = pd.DataFrame(data_list, columns=rs.fields)
                            # 转换数据类型
                            numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']
                            df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, errors='coerce')
                            batch_results.append(df)
                            batch_success += 1
                    except Exception:
                        batch_error += 1
                
                # 记录该批次的结束时间
                batch_end_time = time.time()
                batch_elapsed_time = batch_end_time - batch_start_time
                
                # 将结果放入队列
                result_queue.put((batch_results, batch_success, batch_error, batch_elapsed_time))
                print(f"线程{thread_id}: 完成 {len(batch_codes)} 只股票的下载，成功: {batch_success}，失败: {batch_error}，耗时: {batch_elapsed_time:.2f}秒")
            
            # 创建并启动线程
            threads = []
            stocks_per_thread = len(stock_codes) // num_threads
            thread_stats = []
            
            for i in range(num_threads):
                # 计算每个线程处理的股票范围
                start_idx = i * stocks_per_thread
                # 最后一个线程处理剩余的所有股票
                end_idx = len(stock_codes) if i == num_threads - 1 else (i + 1) * stocks_per_thread
                batch_codes = stock_codes[start_idx:end_idx]
                
                thread = threading.Thread(target=download_batch, args=(batch_codes, i+1))
                thread.daemon = True
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            # 收集所有线程的结果
            thread_elapsed_times = []
            while not result_queue.empty():
                batch_results, batch_success, batch_error, batch_elapsed_time = result_queue.get()
                day_stock_data.extend(batch_results)
                success_count += batch_success
                error_count += batch_error
                thread_elapsed_times.append(batch_elapsed_time)
            
            # 记录该日期的结束时间
            day_end_time = time.time()
            day_elapsed_time = day_end_time - day_start_time
            
            # 记录该日期的性能统计
            day_stats = {
                'date': day_str,
                'start_time': day_start_time,
                'end_time': day_end_time,
                'elapsed_time': day_elapsed_time,
                'success_count': success_count,
                'error_count': error_count,
                'thread_elapsed_times': thread_elapsed_times,
                'avg_thread_time': sum(thread_elapsed_times) / len(thread_elapsed_times) if thread_elapsed_times else 0,
                'max_thread_time': max(thread_elapsed_times) if thread_elapsed_times else 0,
                'min_thread_time': min(thread_elapsed_times) if thread_elapsed_times else 0
            }
            performance_stats['days_stats'].append(day_stats)
            performance_stats['total_records'] += len(day_stock_data)
            performance_stats['total_success'] += success_count
            performance_stats['total_error'] += error_count
            
            print(f"日期 {day_str} 的数据下载完成，共 {len(day_stock_data)} 条记录，成功: {success_count}，失败: {error_count}，耗时: {day_elapsed_time:.2f}秒")
            
            # 合并并保存该日期的所有股票数据
            if day_stock_data:
                day_combined_df = pd.concat(day_stock_data, ignore_index=True)
                print(f"日期 {day_str} 的股票数据合并完成，共 {len(day_combined_df)} 条记录")
                
                # 保存到按日期存储的文件
                save_start_time = time.time()
                sdm.save_daily_data(day_combined_df, day_str)
                save_end_time = time.time()
                save_elapsed_time = save_end_time - save_start_time
                day_stats['save_time'] = save_elapsed_time
                
                print(f"日期 {day_str} 的股票数据已保存，耗时: {save_elapsed_time:.2f}秒")
            else:
                print(f"日期 {day_str} 没有下载到任何股票数据")
        
        # 记录总结束时间
        total_end_time = time.time()
        total_elapsed_time = total_end_time - total_start_time
        
        # 更新性能统计
        performance_stats['total_end_time'] = total_end_time
        performance_stats['total_elapsed_time'] = total_elapsed_time
        
        # 打印性能统计
        print("\n性能测试结果:")
        print(f"总耗时: {total_elapsed_time:.2f}秒 ({total_elapsed_time/60:.2f}分钟)")
        print(f"总记录数: {performance_stats['total_records']}")
        print(f"成功数: {performance_stats['total_success']}")
        print(f"失败数: {performance_stats['total_error']}")
        print(f"平均每天耗时: {total_elapsed_time/len(trading_days):.2f}秒")
        print(f"平均每只股票耗时: {total_elapsed_time/performance_stats['total_success']:.4f}秒 (仅计算成功的)")
        
        return performance_stats
    
    finally:
        # 登出baostock
        bs.logout()
        print("已登出baostock")

def main():
    parser = argparse.ArgumentParser(description='测试股票数据下载性能')
    parser.add_argument('--start_date', type=str, default='2025-02-05', help='开始日期，格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, default='2025-05-19', help='结束日期，格式为YYYY-MM-DD')
    parser.add_argument('--threads', type=int, default=30, help='线程数量')
    parser.add_argument('--max_stocks', type=int, default=None, help='最大测试股票数量')
    parser.add_argument('--test_days', type=int, default=None, help='测试天数，如果指定则只测试前N天')
    
    args = parser.parse_args()
    
    # 如果指定了测试天数，调整结束日期
    if args.test_days is not None and args.test_days > 0:
        start_date = pd.to_datetime(args.start_date)
        end_date = start_date + pd.Timedelta(days=args.test_days)
        args.end_date = end_date.strftime('%Y-%m-%d')
        print(f"调整测试日期范围为: {args.start_date} 到 {args.end_date} (测试 {args.test_days} 天)")
    
    # 执行性能测试
    test_download_performance(args.start_date, args.end_date, args.threads, max_stocks=args.max_stocks)

if __name__ == "__main__":
    main()
