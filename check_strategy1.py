import pandas as pd
import os

# 检查策略1的Excel文件
excel_file = r'E:\机器学习\complete_excel_results\new_strategy_details\strategy_1.xlsx'

if os.path.exists(excel_file):
    print(f"文件存在: {excel_file}")
    print(f"文件大小: {os.path.getsize(excel_file)} 字节")
    
    try:
        # 获取所有sheet名称
        xl_file = pd.ExcelFile(excel_file)
        print(f"Sheet名称: {xl_file.sheet_names}")
        
        # 检查每个sheet的内容
        for sheet_name in xl_file.sheet_names:
            print(f"\n=== Sheet: {sheet_name} ===")
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            print(f"数据形状: {df.shape}")
            print(f"列名: {df.columns.tolist()}")
            
            if not df.empty:
                print("前3行数据:")
                print(df.head(3))
                
                # 检查是否有空值
                null_counts = df.isnull().sum()
                if null_counts.sum() > 0:
                    print("空值统计:")
                    print(null_counts[null_counts > 0])
                    
                # 特别检查选股明细sheet
                if sheet_name == '选股明细':
                    print("\n选股明细详细分析:")
                    for col in df.columns:
                        unique_vals = df[col].unique()
                        print(f"  {col}: {len(unique_vals)} 个唯一值")
                        if len(unique_vals) <= 10:
                            print(f"    值: {unique_vals}")
                        else:
                            print(f"    前5个值: {unique_vals[:5]}")
            else:
                print("数据为空")
                
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
else:
    print(f"文件不存在: {excel_file}")
