"""
策略回测程序 v19-fixed 版本打包脚本
修复parquet依赖问题
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def check_dependencies():
    """检查依赖包是否正确安装"""
    print("🔍 检查依赖包...")

    try:
        import pyarrow
        print(f"✅ pyarrow版本: {pyarrow.__version__}")
    except ImportError as e:
        print(f"❌ pyarrow未安装: {e}")
        return False

    try:
        import fastparquet
        print(f"✅ fastparquet版本: {fastparquet.__version__}")
    except ImportError as e:
        print(f"❌ fastparquet未安装: {e}")
        return False

    try:
        import pandas as pd
        # 测试parquet读取
        test_df = pd.DataFrame({'test': [1, 2, 3]})
        test_file = 'temp_test.parquet'
        test_df.to_parquet(test_file)
        read_df = pd.read_parquet(test_file)
        os.remove(test_file)
        print("✅ parquet读写测试通过")
        return True
    except Exception as e:
        print(f"❌ parquet读写测试失败: {e}")
        return False

def create_spec_file():
    """创建自定义的spec文件，确保包含所有依赖"""

    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 添加所有必要的隐藏导入
hiddenimports = [
    'pandas',
    'numpy',
    'openpyxl',
    'pyarrow',
    'pyarrow.lib',
    'pyarrow.compute',
    'pyarrow.dataset',
    'pyarrow.parquet',
    'fastparquet',
    'fastparquet.core',
    'fastparquet.writer',
    'fastparquet.reader',
    'cramjam',
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'config',
    'stock_data_manager',
    'backtest_local',
    'datetime',
    'os',
    'sys',
    'glob',
    'warnings',
    'time',
    'threading'
]

# 添加数据文件
datas = []

# 分析主程序
a = Analysis(
    ['backtest_gui_fast.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 创建PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 创建EXE
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='策略回测收益程序_v19-fixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''

    with open('策略回测程序_v19-fixed.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("✅ 创建自定义spec文件")
    return True

def build_fixed_exe():
    """打包修复版本"""

    print("=" * 60)
    print("🚀 策略回测程序 v19-fixed 版本打包")
    print("=" * 60)

    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请先安装缺失的包")
        return False

    # 检查快速版本GUI文件是否存在
    if not os.path.exists('backtest_gui_fast.py'):
        print("❌ 找不到backtest_gui_fast.py文件")
        print("请先运行build_exe_v19_fast.py创建快速版本GUI文件")
        return False

    # 版本信息
    version = "v19-fixed"
    build_date = datetime.now().strftime("%Y%m%d_%H%M%S")
    exe_name = f"策略回测收益程序_{version}"

    print(f"📦 版本: {version}")
    print(f"📅 构建时间: {build_date}")
    print(f"📁 程序名称: {exe_name}")

    # 创建构建目录
    build_dir = f"build_{version}_{build_date}"
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    os.makedirs(build_dir)

    print(f"\n📁 创建构建目录: {build_dir}")

    # 创建自定义spec文件
    if not create_spec_file():
        return False

    # 使用spec文件打包
    cmd = [
        'pyinstaller',
        '--distpath', build_dir,        # 输出目录
        '--workpath', f'{build_dir}/work',  # 工作目录
        '策略回测程序_v19-fixed.spec'    # 使用自定义spec文件
    ]

    print("\n🔨 开始打包（使用自定义spec文件）...")
    print("命令:", ' '.join(cmd))

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")

        # 检查生成的exe文件
        exe_path = os.path.join(build_dir, f"{exe_name}.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📁 生成文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")

            # 创建说明文件
            readme_content = f"""
策略回测收益程序 {version}
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
文件大小: {file_size:.1f} MB

🔧 v19-fixed版本修复:
✅ 修复pyarrow和fastparquet依赖问题
✅ 使用自定义spec文件确保所有依赖正确包含
✅ 支持parquet格式汇总表读取
✅ 快速模式和标准模式都可正常使用

🎯 测试步骤:
1. 运行程序
2. 先测试"快速模式"（应该几秒内完成）
3. 再测试"标准模式"（应该能正常预加载数据）
4. 观察是否还有parquet相关错误

📋 如果仍有问题:
- 检查数据目录是否正确
- 确认汇总表文件是否存在
- 查看具体错误信息
"""

            readme_file = os.path.join(build_dir, f"{exe_name}_说明.txt")
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)

            print(f"📖 说明文件: {readme_file}")

            # 清理临时文件
            if os.path.exists('策略回测程序_v19-fixed.spec'):
                os.remove('策略回测程序_v19-fixed.spec')

            print(f"\n🎉 v19-fixed版本打包完成！")
            print(f"📁 输出目录: {build_dir}")
            print(f"🚀 可执行文件: {exe_name}.exe")

            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False

    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

if __name__ == "__main__":
    success = build_fixed_exe()
    if success:
        print("\n✅ v19-fixed版本打包成功完成！")
        print("\n💡 测试建议:")
        print("1. 先测试快速模式，确认基本功能正常")
        print("2. 再测试标准模式，确认parquet支持正常")
        print("3. 如果标准模式仍有问题，使用快速模式进行回测")
    else:
        print("\n❌ 打包失败！")

    input("\n按回车键退出...")
