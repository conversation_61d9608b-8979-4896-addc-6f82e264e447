def update_strategy_summary(main_excel_file, backtest_results):
    """
    更新策略汇总Excel文件中的统计结果
    
    参数:
        main_excel_file (str): 主Excel文件路径
        backtest_results (dict): 回测结果字典，键为策略编号，值为回测结果
        
    返回:
        str: 更新后的Excel文件路径
    """
    print(f"正在更新策略汇总Excel文件: {main_excel_file}")
    
    try:
        # 读取Excel文件
        summary_df = pd.read_excel(main_excel_file, sheet_name='策略汇总')
        conditions_df = pd.read_excel(main_excel_file, sheet_name='策略条件')
        
        # 更新策略汇总表
        for i, row in summary_df.iterrows():
            strategy_index = row['策略编号']
            if strategy_index in backtest_results:
                result = backtest_results[strategy_index]
                
                # 更新统计数据
                summary_df.at[i, '总收益率(%)'] = result['summary']['总收益率(%)']
                summary_df.at[i, '平均收益率(%)'] = result['summary']['年化收益率(%)'] / 365
                summary_df.at[i, '平均胜率(%)'] = result['summary']['胜率(%)']
                summary_df.at[i, '平均每日交易笔数'] = result['summary']['平均每日交易笔数']
                summary_df.at[i, '总交易笔数'] = result['summary']['总交易笔数']
                summary_df.at[i, '交易天数'] = result['summary']['交易天数']
                summary_df.at[i, '总天数'] = result['summary']['总天数']
                summary_df.at[i, '交易频率(%)'] = result['summary']['交易频率(%)']
        
        # 更新策略条件表
        for i, row in conditions_df.iterrows():
            strategy_index = row['策略编号']
            if strategy_index in backtest_results:
                result = backtest_results[strategy_index]
                
                # 更新统计数据
                conditions_df.at[i, '总收益率(%)'] = result['summary']['总收益率(%)']
                conditions_df.at[i, '平均胜率(%)'] = result['summary']['胜率(%)']
        
        # 创建新的Excel文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(os.path.dirname(main_excel_file), f"所有策略汇总_更新_{timestamp}.xlsx")
        
        # 写入Excel文件
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            summary_df.to_excel(writer, sheet_name='策略汇总', index=False)
            conditions_df.to_excel(writer, sheet_name='策略条件', index=False)
            
            # 如果有特征数量统计表，也复制过来
            try:
                stats_df = pd.read_excel(main_excel_file, sheet_name='特征数量统计')
                stats_df.to_excel(writer, sheet_name='特征数量统计', index=False)
            except:
                pass
        
        print(f"策略汇总Excel文件更新完成: {output_file}")
        return output_file
    except Exception as e:
        print(f"更新策略汇总Excel文件时出错: {str(e)}")
        return None

def read_stock_data(stock_data_file):
    """
    读取股票数据
    
    参数:
        stock_data_file (str): 股票数据文件路径
        
    返回:
        DataFrame: 股票数据
    """
    print(f"正在读取股票数据: {stock_data_file}")
    
    try:
        # 读取Excel文件
        stock_df = pd.read_excel(stock_data_file)
        
        # 确保日期列是日期类型
        if '日期' in stock_df.columns:
            stock_df['日期'] = pd.to_datetime(stock_df['日期'])
        
        print(f"成功读取股票数据，共{len(stock_df)}条记录")
        return stock_df
    except Exception as e:
        print(f"读取股票数据时出错: {str(e)}")
        return None

def main():
    """主函数"""
    # 查找所有策略汇总Excel文件
    excel_dir = "E:\\机器学习\\complete_excel_results"
    excel_files = glob.glob(os.path.join(excel_dir, "所有策略汇总_*.xlsx"))
    
    if not excel_files:
        print("未找到策略汇总Excel文件")
        return
    
    # 按文件名排序，获取最新的文件
    excel_files.sort(reverse=True)
    main_excel_file = excel_files[0]
    
    # 设置股票数据文件
    stock_data_file = input("请输入股票数据文件路径: ")
    if not os.path.exists(stock_data_file):
        print(f"股票数据文件不存在: {stock_data_file}")
        return
    
    # 设置回测日期范围
    start_date = input("请输入回测开始日期 (YYYY-MM-DD): ")
    end_date = input("请输入回测结束日期 (YYYY-MM-DD): ")
    
    print(f"使用以下参数:")
    print(f"主Excel文件: {main_excel_file}")
    print(f"股票数据文件: {stock_data_file}")
    print(f"回测日期范围: {start_date} 至 {end_date}")
    
    # 读取股票数据
    stock_data = read_stock_data(stock_data_file)
    if stock_data is None:
        return
    
    # 读取策略条件
    try:
        conditions_df = pd.read_excel(main_excel_file, sheet_name='策略条件')
        print(f"找到 {len(conditions_df)} 个策略")
    except Exception as e:
        print(f"读取策略条件时出错: {str(e)}")
        return
    
    # 回测所有策略
    backtest_results = {}
    start_time = time.time()
    
    for i, (_, strategy_info) in enumerate(conditions_df.iterrows()):
        strategy_index = strategy_info['策略编号']
        
        # 回测策略
        result = backtest_strategy(strategy_info, stock_data, start_date, end_date)
        
        if result and result['summary']:
            backtest_results[strategy_index] = result
        
        # 每回测10个策略，打印一次进度
        if (i + 1) % 10 == 0 or i == len(conditions_df) - 1:
            elapsed_time = time.time() - start_time
            remaining_time = elapsed_time / (i + 1) * (len(conditions_df) - i - 1)
            print(f"已回测 {i+1}/{len(conditions_df)} 个策略，耗时: {elapsed_time:.2f}秒，预计剩余时间: {remaining_time:.2f}秒")
    
    # 更新策略汇总Excel文件
    if backtest_results:
        updated_file = update_strategy_summary(main_excel_file, backtest_results)
        if updated_file:
            print(f"所有策略回测完成，结果已保存到: {updated_file}")
    else:
        print("没有成功回测的策略，无法更新策略汇总Excel文件")

if __name__ == "__main__":
    main()
