#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试历史技术强度数据加载功能
"""

import sys
sys.path.append('.')

from calculate_tech_strength import load_all_historical_tech_strength, calculate_consecutive_tech_strength_from_historical_data
import pandas as pd

def test_historical_data_loading():
    """测试历史数据加载功能"""
    
    print("=== 测试历史技术强度数据加载 ===")
    
    # A股强势股选股系统路径
    tech_system_path = r"E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0"
    
    # 加载所有历史技术强度数据
    print("1. 加载所有历史技术强度数据...")
    historical_data = load_all_historical_tech_strength(tech_system_path)
    
    if historical_data.empty:
        print("❌ 未能加载历史数据")
        return False
    
    print(f"✅ 成功加载历史数据: {len(historical_data)} 条记录")
    print(f"   数据日期范围: {historical_data['日期'].min()} 至 {historical_data['日期'].max()}")
    print(f"   涵盖股票数量: {historical_data['股票代码'].nunique()}")
    
    # 检查数据样本
    print("\n2. 数据样本检查:")
    sample_data = historical_data.head(10)
    for _, row in sample_data.iterrows():
        print(f"   {row['日期'].strftime('%Y-%m-%d')}: 股票 {row['股票代码']}, 技术强度 {row['技术强度']}")
    
    # 测试连续技术强度计算
    print("\n3. 测试连续技术强度计算...")
    target_date = '2025-05-15'
    
    print("   计算3天连续技术强度...")
    consecutive_3 = calculate_consecutive_tech_strength_from_historical_data(historical_data, target_date, 3)
    
    print("   计算5天连续技术强度...")
    consecutive_5 = calculate_consecutive_tech_strength_from_historical_data(historical_data, target_date, 5)
    
    print("   计算10天连续技术强度...")
    consecutive_10 = calculate_consecutive_tech_strength_from_historical_data(historical_data, target_date, 10)
    
    print(f"   3天连续技术强度: {len(consecutive_3)} 个股票")
    print(f"   5天连续技术强度: {len(consecutive_5)} 个股票")
    print(f"   10天连续技术强度: {len(consecutive_10)} 个股票")
    
    # 检查一些具体股票的连续技术强度
    print("\n4. 具体股票的连续技术强度检查:")
    sample_stocks = list(consecutive_3.keys())[:5]
    
    for stock_code in sample_stocks:
        c3 = consecutive_3.get(stock_code, 0)
        c5 = consecutive_5.get(stock_code, 0)
        c10 = consecutive_10.get(stock_code, 0)
        
        order_ok = c3 <= c5 <= c10
        print(f"   股票 {stock_code}: 3天={c3}, 5天={c5}, 10天={c10} {'✅' if order_ok else '❌'}")
    
    # 统计信息
    print("\n5. 统计信息:")
    if consecutive_3:
        c3_values = list(consecutive_3.values())
        c5_values = list(consecutive_5.values())
        c10_values = list(consecutive_10.values())
        
        print(f"   3天连续技术强度: 最小值={min(c3_values)}, 最大值={max(c3_values)}, 平均值={sum(c3_values)/len(c3_values):.2f}")
        print(f"   5天连续技术强度: 最小值={min(c5_values)}, 最大值={max(c5_values)}, 平均值={sum(c5_values)/len(c5_values):.2f}")
        print(f"   10天连续技术强度: 最小值={min(c10_values)}, 最大值={max(c10_values)}, 平均值={sum(c10_values)/len(c10_values):.2f}")
        
        # 检查递增关系
        correct_order_count = 0
        for stock_code in sample_stocks:
            c3 = consecutive_3.get(stock_code, 0)
            c5 = consecutive_5.get(stock_code, 0)
            c10 = consecutive_10.get(stock_code, 0)
            if c3 <= c5 <= c10:
                correct_order_count += 1
        
        print(f"   递增关系正确率: {correct_order_count}/{len(sample_stocks)} = {correct_order_count/len(sample_stocks)*100:.1f}%")
    
    print("\n✅ 历史数据加载和连续技术强度计算测试完成")
    return True

if __name__ == "__main__":
    test_historical_data_loading()
