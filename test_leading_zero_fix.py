#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
sys.path.append('.')

import tech_strength_manager as tsm

def test_leading_zero_fix():
    """测试前导0修复效果"""
    
    print("=== 测试前导0修复效果 ===")
    
    try:
        # 读取原始数据的一小部分
        original_df = pd.read_excel('E:/机器学习/complete_excel_results/股票明细_完整.xlsx')
        test_data = original_df[original_df['日期'] == '2025-05-15'].head(20).copy()
        
        print(f"测试数据行数: {len(test_data)}")
        
        # 检查原始数据的技术指标特征和趋势组合
        print("\n=== 原始数据检查 ===")
        sample = test_data.iloc[0]
        print(f"原始技术指标特征: {sample['技术指标特征']} (类型: {type(sample['技术指标特征'])})")
        print(f"原始趋势组合: {sample['趋势组合']} (类型: {type(sample['趋势组合'])})")
        
        # 应用我修复后的格式化逻辑
        print("\n=== 应用修复后的格式化逻辑 ===")
        
        # 模拟技术指标特征计算和格式化
        # 假设我们有一些技术指标值
        test_data['技术指标_均线多头排列'] = [0, 1, 0, 1, 0] * 4  # 重复以填充20行
        test_data['技术指标_MACD金叉'] = [0, 0, 1, 1, 0] * 4
        test_data['技术指标_RSI反弹'] = [0, 0, 0, 1, 1] * 4
        test_data['技术指标_KDJ金叉'] = [0, 0, 1, 0, 1] * 4
        test_data['技术指标_布林带突破'] = [0, 1, 0, 1, 0] * 4
        
        # 使用修复后的计算方式
        tech_feature_values = (test_data['技术指标_均线多头排列'] * 32 +
                              test_data['技术指标_MACD金叉'] * 16 +
                              test_data['技术指标_RSI反弹'] * 8 +
                              test_data['技术指标_KDJ金叉'] * 4 +
                              test_data['技术指标_布林带突破'] * 2 +
                              0 * 1)
        
        # 关键修复：格式化为6位字符串
        test_data['技术指标特征_修复后'] = tech_feature_values.apply(lambda x: format(int(x), '06d'))
        
        # 模拟趋势组合计算和格式化
        test_data['3天技术强度上升'] = [0, 1, 0, 1, 0] * 4
        test_data['3天价格上升'] = [0, 0, 1, 1, 0] * 4
        test_data['5天技术强度上升'] = [0, 0, 0, 1, 1] * 4
        test_data['5天价格上升'] = [0, 0, 1, 0, 1] * 4
        test_data['10天技术强度上升'] = [0, 1, 0, 1, 0] * 4
        test_data['10天价格上升'] = [1, 0, 1, 1, 1] * 4
        
        # 使用修复后的计算方式
        trend_combo_values = (test_data['3天技术强度上升'] * 32 +
                             test_data['3天价格上升'] * 16 +
                             test_data['5天技术强度上升'] * 8 +
                             test_data['5天价格上升'] * 4 +
                             test_data['10天技术强度上升'] * 2 +
                             test_data['10天价格上升'] * 1)
        
        # 关键修复：格式化为6位字符串
        test_data['趋势组合_修复后'] = trend_combo_values.apply(lambda x: format(int(x), '06d'))
        
        # 检查修复效果
        print("\n=== 修复效果对比 ===")
        
        for i in range(min(10, len(test_data))):
            row = test_data.iloc[i]
            
            print(f"\n股票 {i+1} ({row['股票代码']}):")
            
            # 技术指标特征对比
            original_tech = row['技术指标特征']
            fixed_tech = row['技术指标特征_修复后']
            print(f"  技术指标特征:")
            print(f"    原始: {original_tech} (类型: {type(original_tech)})")
            print(f"    修复: {fixed_tech} (类型: {type(fixed_tech)}, 长度: {len(fixed_tech)})")
            
            # 趋势组合对比
            original_trend = row['趋势组合']
            fixed_trend = row['趋势组合_修复后']
            print(f"  趋势组合:")
            print(f"    原始: {original_trend} (类型: {type(original_trend)})")
            print(f"    修复: {fixed_trend} (类型: {type(fixed_trend)}, 长度: {len(fixed_trend)})")
        
        # 统计前导0情况
        print("\n=== 前导0统计 ===")
        
        # 技术指标特征
        tech_features_fixed = test_data['技术指标特征_修复后'].tolist()
        leading_zero_tech = [x for x in tech_features_fixed if x.startswith('0')]
        print(f"修复后技术指标特征:")
        print(f"  总数: {len(tech_features_fixed)}")
        print(f"  以0开头的数量: {len(leading_zero_tech)}")
        print(f"  以0开头的示例: {leading_zero_tech[:5] if leading_zero_tech else '无'}")
        
        # 趋势组合
        trend_combos_fixed = test_data['趋势组合_修复后'].tolist()
        leading_zero_trend = [x for x in trend_combos_fixed if x.startswith('0')]
        print(f"修复后趋势组合:")
        print(f"  总数: {len(trend_combos_fixed)}")
        print(f"  以0开头的数量: {len(leading_zero_trend)}")
        print(f"  以0开头的示例: {leading_zero_trend[:5] if leading_zero_trend else '无'}")
        
        # 验证长度
        tech_lengths = [len(x) for x in tech_features_fixed]
        trend_lengths = [len(x) for x in trend_combos_fixed]
        
        print(f"\n=== 长度验证 ===")
        print(f"技术指标特征长度都是6位: {all(l == 6 for l in tech_lengths)}")
        print(f"趋势组合长度都是6位: {all(l == 6 for l in trend_lengths)}")
        
        # 应用tech_strength_manager的格式化
        print("\n=== 应用tech_strength_manager格式化 ===")
        
        # 创建一个测试DataFrame
        test_save_data = test_data[['股票代码', '技术指标特征_修复后', '趋势组合_修复后']].copy()
        test_save_data.rename(columns={
            '技术指标特征_修复后': '技术指标特征',
            '趋势组合_修复后': '趋势组合'
        }, inplace=True)
        
        # 添加必要的列
        test_save_data['日期'] = '2025-05-15'
        test_save_data['技术强度'] = 70
        
        # 应用格式化
        formatted_data = tsm.format_data_for_saving(test_save_data)
        
        print("格式化后的数据:")
        for i in range(min(5, len(formatted_data))):
            row = formatted_data.iloc[i]
            tech_feature = row['技术指标特征']
            trend_combo = row['趋势组合']
            print(f"  股票 {i+1}: 技术指标特征='{tech_feature}', 趋势组合='{trend_combo}'")
        
        print("\n✅ 前导0修复测试完成")
        
        # 最终评估
        final_tech_features = formatted_data['技术指标特征'].astype(str).tolist()
        final_trend_combos = formatted_data['趋势组合'].astype(str).tolist()
        
        final_leading_zero_tech = any(x.startswith('0') for x in final_tech_features if x != 'nan')
        final_leading_zero_trend = any(x.startswith('0') for x in final_trend_combos if x != 'nan')
        
        print(f"\n=== 最终评估 ===")
        print(f"最终技术指标特征有前导0: {final_leading_zero_tech}")
        print(f"最终趋势组合有前导0: {final_leading_zero_trend}")
        
        if final_leading_zero_tech and final_leading_zero_trend:
            print("🎉 前导0修复成功！")
        else:
            print("❌ 前导0修复仍有问题")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_leading_zero_fix()
