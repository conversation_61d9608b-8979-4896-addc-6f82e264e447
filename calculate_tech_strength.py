#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票技术强度计算程序
只从原始系统E:\\桌面\\AI+BI\\A股强势股选股系统_v1.0.0\\A股强势股选股系统_v1.0.0读取技术强度数据
"""

import os
import sys
import pandas as pd
import numpy as np
import argparse
from tqdm import tqdm
import tech_strength_manager as tsm
import stock_data_manager as sdm
from datetime import datetime


def calculate_technical_strength(stock_data):
    """
    计算技术强度，从原始系统读取数据
    """
    print("开始计算技术强度...")

    # 初始化技术强度为-100（表示不在原始系统的数据中）
    stock_data['技术强度'] = -100

    # 直接从原始系统中读取技术强度数据
    try:
        # 获取当前日期
        current_date = None
        if 'date' in stock_data.columns:
            current_date = stock_data['date'].iloc[0]
        elif '日期' in stock_data.columns:
            current_date = stock_data['日期'].iloc[0]

        if current_date:
            # 从原始系统的日期文件夹中读取技术强度数据
            current_date_str = pd.to_datetime(current_date).strftime('%Y-%m-%d')
            original_system_dir = r"E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0"
            date_folder = os.path.join(original_system_dir, f"选股结果_{current_date_str}")

            print(f"尝试从原始系统日期文件夹读取技术强度数据: {date_folder}")

            if os.path.exists(date_folder):
                # 查找该日期文件夹中的Excel文件
                excel_files = []
                for file in os.listdir(date_folder):
                    if file.endswith('.xlsx') or file.endswith('.xls'):
                        excel_files.append(os.path.join(date_folder, file))

                print(f"在日期文件夹中找到 {len(excel_files)} 个Excel文件")

                # 尝试读取每个Excel文件
                for excel_file in excel_files:
                    try:
                        print(f"尝试从Excel文件读取技术强度数据: {excel_file}")
                        original_df = pd.read_excel(excel_file)
                        print(f"Excel文件列名: {original_df.columns.tolist()}")

                        # 检查是否有技术强度列
                        tech_strength_col = None
                        if '技术强度' in original_df.columns:
                            tech_strength_col = '技术强度'
                        elif '技术强 度' in original_df.columns:
                            tech_strength_col = '技术强 度'

                        # 检查是否有股票代码列
                        code_col = None
                        if '股票代码' in original_df.columns:
                            code_col = '股票代码'
                        elif 'code' in original_df.columns:
                            code_col = 'code'

                        # 如果有技术强度列和股票代码列，就使用这个文件
                        if tech_strength_col and code_col:
                            print(f"找到技术强度数据，技术强度列: {tech_strength_col}, 股票代码列: {code_col}")
                            print(f"原始数据前5行:")
                            print(original_df[[code_col, tech_strength_col]].head())

                            # 创建股票代码到技术强度的映射
                            code_to_strength = {}

                            # 创建映射
                            for _, row in original_df.iterrows():
                                code = str(row[code_col]).strip()
                                strength = row[tech_strength_col]
                                # 去除股票代码前缀
                                if '.' in code:
                                    code = code.split('.')[-1]
                                elif code.startswith(('sh', 'sz')):
                                    code = code[2:]
                                # 确保6位格式
                                if code.isdigit() and len(code) < 6:
                                    code = code.zfill(6)
                                code_to_strength[code] = strength

                            # 更新技术强度
                            matched_count = 0
                            # 确定股票代码列名
                            stock_code_col = None
                            if 'code' in stock_data.columns:
                                stock_code_col = 'code'
                            elif '股票代码' in stock_data.columns:
                                stock_code_col = '股票代码'
                            else:
                                print(f"警告：找不到股票代码列，可用列：{stock_data.columns.tolist()}")
                                stock_code_col = stock_data.columns[0]  # 使用第一列作为默认

                            for i, row in stock_data.iterrows():
                                code = str(row[stock_code_col]).strip()
                                # 去除股票代码前缀
                                if '.' in code:
                                    code = code.split('.')[-1]
                                elif code.startswith(('sh', 'sz')):
                                    code = code[2:]
                                # 确保6位格式
                                if code.isdigit() and len(code) < 6:
                                    code = code.zfill(6)

                                if code in code_to_strength:
                                    stock_data.loc[i, '技术强度'] = code_to_strength[code]
                                    matched_count += 1

                            print(f"成功从Excel文件读取技术强度数据，共 {len(code_to_strength)} 条记录，匹配 {matched_count} 条")
                            break
                    except Exception as e:
                        print(f"从Excel文件读取技术强度数据时出错: {excel_file}, 错误: {e}")
            else:
                print(f"原始系统日期文件夹不存在: {date_folder}")

    except Exception as e:
        print(f"从原始系统读取技术强度数据时出错: {e}")
        import traceback
        traceback.print_exc()

    # 计算技术指标
    print("计算技术指标...")

    # 初始化技术指标列（使用1和2，1表示满足条件，2表示不满足）
    stock_data['技术指标_均线多头排列'] = 2
    stock_data['技术指标_MACD金叉'] = 2
    stock_data['技术指标_RSI反弹'] = 2
    stock_data['技术指标_KDJ金叉'] = 2
    stock_data['技术指标_布林带突破'] = 2

    # 计算均线多头排列
    if all(col in stock_data.columns for col in ['MA5', 'MA10', 'MA20', 'MA60']):
        stock_data.loc[(stock_data['MA5'] > stock_data['MA10']) & (stock_data['MA10'] > stock_data['MA20']) & (stock_data['MA20'] > stock_data['MA60']), '技术指标_均线多头排列'] = 1
    elif all(col in stock_data.columns for col in ['MA5', 'MA10', 'MA20', 'MA30']):
        stock_data.loc[(stock_data['MA5'] > stock_data['MA10']) & (stock_data['MA10'] > stock_data['MA20']) & (stock_data['MA20'] > stock_data['MA30']), '技术指标_均线多头排列'] = 1

    # 计算MACD金叉
    if all(col in stock_data.columns for col in ['DIF', 'DEA']):
        stock_data.loc[(stock_data['DIF'] > stock_data['DEA']) & (stock_data['DIF'].shift(1) <= stock_data['DEA'].shift(1)), '技术指标_MACD金叉'] = 1
    elif all(col in stock_data.columns for col in ['MACD', 'MACD_SIGNAL']):
        stock_data.loc[(stock_data['MACD'] > stock_data['MACD_SIGNAL']) & (stock_data['MACD'].shift(1) <= stock_data['MACD_SIGNAL'].shift(1)), '技术指标_MACD金叉'] = 1

    # 计算RSI反弹
    if 'RSI' in stock_data.columns:
        stock_data.loc[(stock_data['RSI'] > 50) & (stock_data['RSI'].shift(1) <= 50), '技术指标_RSI反弹'] = 1

    # 计算KDJ金叉
    if all(col in stock_data.columns for col in ['K', 'D']):
        stock_data.loc[(stock_data['K'] > stock_data['D']) & (stock_data['K'].shift(1) <= stock_data['D'].shift(1)), '技术指标_KDJ金叉'] = 1

    # 计算布林带突破
    if 'BOLL_UP' in stock_data.columns and 'close' in stock_data.columns:
        stock_data.loc[stock_data['close'] > stock_data['BOLL_UP'], '技术指标_布林带突破'] = 1
    elif 'BOLL_UPPER' in stock_data.columns and 'close' in stock_data.columns:
        stock_data.loc[stock_data['close'] > stock_data['BOLL_UPPER'], '技术指标_布林带突破'] = 1

    # 计算看涨技术指标数量（现在每个指标是1或2，1表示满足，2表示不满足，需要转换为0或1来计数）
    stock_data['看涨技术指标数量'] = ((2 - stock_data['技术指标_均线多头排列']) +
                      (2 - stock_data['技术指标_MACD金叉']) +
                      (2 - stock_data['技术指标_RSI反弹']) +
                      (2 - stock_data['技术指标_KDJ金叉']) +
                      (2 - stock_data['技术指标_布林带突破']))

    # 计算技术指标特征（6位编码）
    # 直接构建字符串，避免数值计算导致的格式问题
    def build_tech_feature(row):
        # 第1位：均线多头排列
        f1 = '1' if row['技术指标_均线多头排列'] == 1 else '2'
        # 第2位：成交量放大（暂时设为2，不满足）
        f2 = '2'
        # 第3位：MACD金叉
        f3 = '1' if row['技术指标_MACD金叉'] == 1 else '2'
        # 第4位：RSI反弹
        f4 = '1' if row['技术指标_RSI反弹'] == 1 else '2'
        # 第5位：KDJ金叉
        f5 = '1' if row['技术指标_KDJ金叉'] == 1 else '2'
        # 第6位：布林带突破
        f6 = '1' if row['技术指标_布林带突破'] == 1 else '2'
        return f1 + f2 + f3 + f4 + f5 + f6

    # 应用函数生成技术指标特征
    stock_data['技术指标特征'] = stock_data.apply(build_tech_feature, axis=1)

    # 按照数据字典的正确字段名生成
    required_columns = [
        '股票代码', '股票名称', '行业', '收盘价', '涨跌幅', '技术强度', '趋势',
        '目标价', '止损价', '跟踪止损', '技术指标', '日期', '成交量是前一日几倍',
        '连续技术强度5天数', '连续技术强度3天数', '连续技术强度10天数',
        '技术指标特征', '买入日开盘涨跌幅', '日内股票标记', '卖出日开盘涨跌幅', '趋势组合'
    ]

    # 确保所有必需字段都存在，如果不存在则创建默认值
    for col in required_columns:
        if col not in stock_data.columns:
            if col == '股票代码':
                # 检查可能的股票代码列名
                if 'code' in stock_data.columns:
                    stock_data[col] = stock_data['code']
                elif '股票代码' in stock_data.columns:
                    stock_data[col] = stock_data['股票代码']
                else:
                    # 如果都没有，使用索引作为临时代码
                    stock_data[col] = stock_data.index.astype(str).str.zfill(6)
                    print(f"警告：找不到股票代码列，使用索引作为代码。可用列：{stock_data.columns.tolist()}")
            elif col == '股票名称':
                # 检查可能的股票名称列名
                if 'name' in stock_data.columns:
                    stock_data[col] = stock_data['name']
                elif '股票名称' in stock_data.columns:
                    stock_data[col] = stock_data['股票名称']
                else:
                    stock_data[col] = '未知股票'
            elif col == '行业':
                stock_data[col] = ''  # 默认空值
            elif col == '收盘价':
                stock_data[col] = stock_data['close'] if 'close' in stock_data.columns else 0.0
            elif col == '涨跌幅':
                stock_data[col] = stock_data['pctChg'] if 'pctChg' in stock_data.columns else 0.0
            elif col == '趋势':
                stock_data[col] = 'ruo'  # 默认值
            elif col in ['目标价', '止损价', '跟踪止损']:
                stock_data[col] = 0.0
            elif col == '技术指标':
                stock_data[col] = ''  # 默认空值
            elif col == '日期':
                stock_data[col] = stock_data['date'] if 'date' in stock_data.columns else ''
            elif col in ['连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                stock_data[col] = 0.0
            elif col in ['买入日开盘涨跌幅', '卖出日开盘涨跌幅']:
                stock_data[col] = 0
            elif col == '日内股票标记':
                # 计算日内股票标记（3位编码）
                def calculate_intraday_mark(row):
                    # 获取涨跌幅
                    pct_chg = row.get('涨跌幅', 0) if '涨跌幅' in stock_data.columns else row.get('pctChg', 0)

                    # 第一位：开盘涨幅状态（假设开盘涨幅等于涨跌幅）
                    if pct_chg > 5:
                        pos1 = '8'
                    elif pct_chg > 0:
                        pos1 = '6'
                    elif pct_chg >= -2.5:
                        pos1 = '4'
                    elif pct_chg >= -6:
                        pos1 = '3'
                    elif pct_chg >= -40:
                        pos1 = '2'
                    else:
                        pos1 = '1'

                    # 第二位：最高涨幅状态（假设最高涨幅略高于收盘涨幅）
                    max_pct = pct_chg + abs(pct_chg) * 0.1  # 假设最高比收盘高10%
                    if max_pct > 7:
                        pos2 = '8'
                    elif max_pct > 0:
                        pos2 = '6'
                    elif max_pct >= -2.5:
                        pos2 = '4'
                    elif max_pct >= -6:
                        pos2 = '3'
                    elif max_pct >= -40:
                        pos2 = '2'
                    else:
                        pos2 = '1'

                    # 第三位：收盘涨幅状态
                    if pct_chg > max_pct / 2 and max_pct > 7:
                        pos3 = '8'
                    elif pct_chg > 0:
                        pos3 = '6'
                    elif pct_chg >= -2.5:
                        pos3 = '4'
                    elif pct_chg >= -6:
                        pos3 = '3'
                    elif pct_chg >= -40:
                        pos3 = '2'
                    else:
                        pos3 = '1'

                    return pos1 + pos2 + pos3

                stock_data[col] = stock_data.apply(calculate_intraday_mark, axis=1)
            elif col == '趋势组合':
                stock_data[col] = '222222'  # 默认值
            elif col == '成交量是前一日几倍':
                # 计算成交量比值
                if 'volume' in stock_data.columns:
                    # 计算与前一日的成交量比值
                    stock_data[col] = stock_data['volume'] / stock_data['volume'].shift(1)
                    # 处理无穷大和NaN值
                    stock_data[col] = stock_data[col].fillna(1.0)
                    stock_data[col] = stock_data[col].replace([float('inf'), -float('inf')], 1.0)
                    # 限制在0.5-3.5范围内，以0.5为单位
                    stock_data[col] = stock_data[col].clip(0.5, 3.5)
                    stock_data[col] = (stock_data[col] * 2).round() / 2  # 四舍五入到0.5的倍数
                else:
                    stock_data[col] = 1.0  # 默认值

    # 只保留股票明细_完整.xlsx格式的字段，并按正确顺序排列
    result_df = stock_data[required_columns].copy()

    return result_df


def calculate_consecutive_strength(stock_data, sdm, date):
    """
    计算连续技术强度和趋势组合
    """
    print("开始计算连续技术强度和趋势组合...")

    try:
        # 获取历史数据来计算连续技术强度
        from datetime import timedelta

        # 计算需要的历史日期
        dates_needed = []
        for i in range(1, 11):  # 需要前10天的数据
            hist_date = date - timedelta(days=i)
            dates_needed.append(hist_date)

        # 尝试加载历史数据
        historical_data = {}
        for hist_date in dates_needed:
            try:
                hist_data = sdm.load_daily_data(hist_date)
                if not hist_data.empty:
                    historical_data[hist_date] = hist_data
            except:
                continue

        print(f"成功加载 {len(historical_data)} 天的历史数据")

        # 如果有足够的历史数据，计算连续技术强度
        if len(historical_data) >= 3:
            # 为每只股票计算连续技术强度
            for i, row in stock_data.iterrows():
                stock_code = row.get('股票代码', '')
                current_tech_strength = row.get('技术强度', 0)
                current_price = row.get('收盘价', 0)

                # 收集历史技术强度和价格
                hist_tech_strengths = [current_tech_strength]
                hist_prices = [current_price]

                # 从历史数据中查找该股票的数据
                for hist_date in sorted(historical_data.keys(), reverse=True)[:10]:
                    hist_df = historical_data[hist_date]
                    # 查找匹配的股票
                    matching_rows = hist_df[hist_df['code'].astype(str).str.contains(stock_code.replace('sh.', '').replace('sz.', ''), na=False)]
                    if not matching_rows.empty:
                        hist_tech_strengths.append(matching_rows.iloc[0].get('技术强度', 0))
                        hist_prices.append(matching_rows.iloc[0].get('close', 0))

                # 计算连续技术强度
                if len(hist_tech_strengths) >= 4:  # 至少需要4天数据（包括当天）
                    stock_data.loc[i, '连续技术强度3天数'] = sum(hist_tech_strengths[:3])
                if len(hist_tech_strengths) >= 6:
                    stock_data.loc[i, '连续技术强度5天数'] = sum(hist_tech_strengths[:5])
                if len(hist_tech_strengths) >= 11:
                    stock_data.loc[i, '连续技术强度10天数'] = sum(hist_tech_strengths[:10])

                # 计算趋势组合
                trend_combo = calculate_trend_combination(hist_tech_strengths, hist_prices)
                stock_data.loc[i, '趋势组合'] = trend_combo

        else:
            print("历史数据不足，使用默认值")
            # 如果历史数据不足，使用默认值
            stock_data['连续技术强度3天数'] = stock_data['技术强度'] * 3
            stock_data['连续技术强度5天数'] = stock_data['技术强度'] * 5
            stock_data['连续技术强度10天数'] = stock_data['技术强度'] * 10
            stock_data['趋势组合'] = '222222'  # 默认全部下降

    except Exception as e:
        print(f"计算连续技术强度时出错: {e}")
        # 使用默认值
        stock_data['连续技术强度3天数'] = stock_data['技术强度'] * 3
        stock_data['连续技术强度5天数'] = stock_data['技术强度'] * 5
        stock_data['连续技术强度10天数'] = stock_data['技术强度'] * 10
        stock_data['趋势组合'] = '222222'

    return stock_data


def calculate_trend_combination(tech_strengths, prices):
    """
    计算趋势组合（6位编码）
    """
    if len(tech_strengths) < 11 or len(prices) < 11:
        return '222222'  # 数据不足，返回默认值

    # 计算各个时间段的趋势
    trends = []

    # 3天技术强度趋势
    if tech_strengths[0] > tech_strengths[2]:
        trends.append('1')  # 上升
    else:
        trends.append('2')  # 下降或不变

    # 3天价格趋势
    if prices[0] > prices[2]:
        trends.append('1')  # 上升
    else:
        trends.append('2')  # 下降或不变

    # 5天技术强度趋势
    if tech_strengths[0] > tech_strengths[4]:
        trends.append('1')  # 上升
    else:
        trends.append('2')  # 下降或不变

    # 5天价格趋势
    if prices[0] > prices[4]:
        trends.append('1')  # 上升
    else:
        trends.append('2')  # 下降或不变

    # 10天技术强度趋势
    if tech_strengths[0] > tech_strengths[9]:
        trends.append('1')  # 上升
    else:
        trends.append('2')  # 下降或不变

    # 10天价格趋势
    if prices[0] > prices[9]:
        trends.append('1')  # 上升
    else:
        trends.append('2')  # 下降或不变

    return ''.join(trends)


def process_stock_data(start_date, end_date):
    """
    处理股票数据的主函数
    """
    try:
        # 获取可用的日期
        available_dates = sdm.get_available_dates()

        if not available_dates:
            print("错误: 没有找到可用的股票数据")
            return False

        # 过滤日期范围
        available_dates = [d for d in available_dates if start_date <= d.date() <= end_date]

        if not available_dates:
            print("错误: 指定日期范围内没有可用的股票数据")
            return False

        print(f"处理日期范围: {available_dates[0].strftime('%Y-%m-%d')} 到 {available_dates[-1].strftime('%Y-%m-%d')}")

        # 处理每个日期的数据
        for date in tqdm(available_dates, desc="处理日期"):
            # 检查该日期的技术强度数据是否已存在
            tech_strength_file = tsm.get_daily_tech_strength_path(date)
            if os.path.exists(tech_strength_file):
                print(f"日期 {date.strftime('%Y-%m-%d')} 的技术强度数据已存在，跳过")
                continue

            # 加载该日期的股票数据
            print(f"加载日期 {date.strftime('%Y-%m-%d')} 的股票数据...")
            stock_data = sdm.load_daily_data(date)

            if stock_data.empty:
                print(f"日期 {date.strftime('%Y-%m-%d')} 没有股票数据，跳过")
                continue

            print(f"成功加载日期 {date.strftime('%Y-%m-%d')} 的股票数据，共 {len(stock_data)} 条记录")

            # 计算技术强度
            print(f"计算日期 {date.strftime('%Y-%m-%d')} 的技术强度...")
            stock_data = calculate_technical_strength(stock_data)

            # 计算连续技术强度
            print(f"计算日期 {date.strftime('%Y-%m-%d')} 的连续技术强度...")
            stock_data = calculate_consecutive_strength(stock_data, sdm, date)

            # 保存技术强度数据
            print(f"保存日期 {date.strftime('%Y-%m-%d')} 的技术强度数据...")
            tsm.save_daily_tech_strength(stock_data, date)

            print(f"完成日期 {date.strftime('%Y-%m-%d')} 的技术强度计算")

        print("所有日期的技术强度计算完成")
        return True

    except Exception as e:
        print(f"处理股票数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='计算股票技术强度')
    parser.add_argument('--start', type=str, required=True, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end', type=str, required=True, help='结束日期 (YYYY-MM-DD)')

    args = parser.parse_args()

    try:
        start_date = datetime.strptime(args.start, '%Y-%m-%d').date()
        end_date = datetime.strptime(args.end, '%Y-%m-%d').date()

        print(f"开始处理日期范围: {start_date} 到 {end_date}")

        success = process_stock_data(start_date, end_date)

        if success:
            print("技术强度计算完成")
        else:
            print("技术强度计算失败")

    except Exception as e:
        print(f"主函数执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
