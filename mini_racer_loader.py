"""
自定义py_mini_racer加载器 - 解决依赖文件加载问题
"""

import os
import sys
import shutil
import ctypes
from pathlib import Path

# 需要加载的文件列表
REQUIRED_FILES = [
    "mini_racer.dll",
    "snapshot_blob.bin",
    "icudtl.dat"
]

def get_bundle_dir():
    """获取程序运行的目录"""
    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的情况
        return os.path.dirname(sys.executable)
    else:
        # 正常运行的情况
        return os.path.dirname(os.path.abspath(__file__))

def get_temp_dir():
    """获取PyInstaller的临时目录"""
    if getattr(sys, 'frozen', False):
        # 尝试找到PyInstaller的临时目录
        for env_var in ['_MEIPASS', '_MEI']:
            if hasattr(sys, env_var):
                return getattr(sys, env_var)
        
        # 如果找不到，尝试在系统临时目录中查找
        temp_base = os.environ.get('TEMP', os.path.dirname(sys.executable))
        for root, dirs, files in os.walk(temp_base):
            if any(f.endswith('.dll') for f in files):
                return root
    
    return None

def ensure_mini_racer_files():
    """确保mini_racer的所有必要文件都可用"""
    bundle_dir = get_bundle_dir()
    temp_dir = get_temp_dir()
    
    print(f"程序目录: {bundle_dir}")
    print(f"临时目录: {temp_dir}")
    
    # 检查程序目录中是否有必要的文件
    files_in_bundle = {}
    for file in REQUIRED_FILES:
        file_path = os.path.join(bundle_dir, file)
        if os.path.exists(file_path):
            files_in_bundle[file] = file_path
            print(f"在程序目录中找到: {file}")
    
    # 如果找到了临时目录，尝试复制文件
    if temp_dir:
        for file, src_path in files_in_bundle.items():
            dst_path = os.path.join(temp_dir, file)
            try:
                shutil.copy2(src_path, dst_path)
                print(f"已复制 {file} 到临时目录")
            except Exception as e:
                print(f"复制 {file} 时出错: {e}")
    
    # 尝试直接加载DLL
    if "mini_racer.dll" in files_in_bundle:
        try:
            dll_path = files_in_bundle["mini_racer.dll"]
            ctypes.cdll.LoadLibrary(dll_path)
            print("成功加载 mini_racer.dll")
        except Exception as e:
            print(f"加载 mini_racer.dll 时出错: {e}")

# 在导入时执行
ensure_mini_racer_files()
