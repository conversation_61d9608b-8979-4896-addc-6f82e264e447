@echo off
chcp 65001 > nul
echo ===================================================
echo        深度学习股票高胜率策略分析工具
echo ===================================================
echo.
echo 1. 训练深度学习模型
echo 2. 回测深度学习高胜率策略
echo 3. 生成股票推荐
echo 4. 全部执行
echo.
set /p choice=请选择操作 (1/2/3/4): 

if "%choice%"=="1" (
    echo.
    echo 正在训练深度学习模型...
    python run_deep_learning.py --train --data_file 股票明细.xlsx
) else if "%choice%"=="2" (
    echo.
    set /p start_date=请输入回测开始日期 (格式: YYYY-MM-DD): 
    set /p end_date=请输入回测结束日期 (格式: YYYY-MM-DD): 
    set /p output_file=请输入输出文件名 (默认: 深度学习高胜率策略回测结果.txt): 
    
    if "%output_file%"=="" set output_file=深度学习高胜率策略回测结果.txt
    
    echo 正在回测深度学习高胜率策略...
    python run_deep_learning.py --backtest --data_file 股票明细.xlsx --start_date %start_date% --end_date %end_date% --output %output_file%
) else if "%choice%"=="3" (
    echo.
    set /p date=请输入推荐日期 (格式: YYYY-MM-DD): 
    set /p output_file=请输入输出文件名 (默认: 深度学习高胜率策略推荐股票.xlsx): 
    
    if "%output_file%"=="" set output_file=深度学习高胜率策略推荐股票.xlsx
    
    echo 正在生成股票推荐...
    python run_deep_learning.py --recommend --data_file 股票明细.xlsx --date %date% --output %output_file%
) else if "%choice%"=="4" (
    echo.
    echo 正在训练深度学习模型...
    python run_deep_learning.py --train --data_file 股票明细.xlsx
    
    echo.
    set /p start_date=请输入回测开始日期 (格式: YYYY-MM-DD): 
    set /p end_date=请输入回测结束日期 (格式: YYYY-MM-DD): 
    set /p backtest_output=请输入回测输出文件名 (默认: 深度学习高胜率策略回测结果.txt): 
    
    if "%backtest_output%"=="" set backtest_output=深度学习高胜率策略回测结果.txt
    
    echo 正在回测深度学习高胜率策略...
    python run_deep_learning.py --backtest --data_file 股票明细.xlsx --start_date %start_date% --end_date %end_date% --output %backtest_output%
    
    echo.
    set /p date=请输入推荐日期 (格式: YYYY-MM-DD): 
    set /p recommend_output=请输入推荐输出文件名 (默认: 深度学习高胜率策略推荐股票.xlsx): 
    
    if "%recommend_output%"=="" set recommend_output=深度学习高胜率策略推荐股票.xlsx
    
    echo 正在生成股票推荐...
    python run_deep_learning.py --recommend --data_file 股票明细.xlsx --date %date% --output %recommend_output%
) else (
    echo.
    echo 无效的选择，请重新运行脚本并选择1-4之间的数字
)

echo.
pause
