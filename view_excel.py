import pandas as pd
import os

# 读取主Excel文件
excel_path = r'E:\机器学习\complete_excel_results\所有策略汇总_20250516_122946.xlsx'
print(f"正在读取Excel文件: {excel_path}")

# 检查文件是否存在
if not os.path.exists(excel_path):
    print(f"文件不存在: {excel_path}")
    exit(1)

# 读取所有工作表
try:
    all_sheets = pd.read_excel(excel_path, sheet_name=None)
    print(f"成功读取Excel文件，包含 {len(all_sheets)} 个工作表")
    
    # 打印每个工作表的信息
    for sheet_name, df in all_sheets.items():
        print(f"\n工作表: {sheet_name}")
        print(f"列名: {df.columns.tolist()}")
        print(f"行数: {len(df)}")
        
        # 打印前5行数据
        if len(df) > 0:
            print("\n前5行数据:")
            print(df.head().to_string())
except Exception as e:
    print(f"读取Excel文件时出错: {str(e)}")
