#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的修复测试脚本
"""

import pandas as pd
import tech_strength_manager as tsm

def test_fix():
    """测试修复效果"""
    
    print("=== 测试修复效果 ===")
    
    try:
        # 读取原始数据的一小部分进行测试
        print("1. 读取原始数据...")
        original_df = pd.read_excel('E:/机器学习/complete_excel_results/股票明细_完整.xlsx')
        test_data = original_df[original_df['日期'] == '2025-05-15'].head(10).copy()
        
        print(f"测试数据行数: {len(test_data)}")
        print("原始数据示例:")
        sample = test_data.iloc[0]
        print(f"  股票代码: {sample['股票代码']}")
        print(f"  技术指标特征: {sample['技术指标特征']} (类型: {type(sample['技术指标特征'])})")
        print(f"  趋势组合: {sample['趋势组合']} (类型: {type(sample['趋势组合'])})")
        print(f"  连续技术强度3天数: {sample['连续技术强度3天数']}")
        print(f"  连续技术强度5天数: {sample['连续技术强度5天数']}")
        print(f"  连续技术强度10天数: {sample['连续技术强度10天数']}")
        
        # 测试字段格式化
        print("\n2. 测试字段格式化...")
        
        # 创建测试数据
        test_df = test_data.copy()
        
        # 测试技术指标特征格式化
        def format_tech_feature(value):
            if pd.isna(value) or value == '' or value is None:
                return '000000'
            try:
                if isinstance(value, (int, float)):
                    if value == 0:
                        return '000000'
                    return str(int(value)).zfill(6)
                else:
                    return str(value).zfill(6)
            except:
                return '000000'
        
        test_df['技术指标特征'] = test_df['技术指标特征'].apply(format_tech_feature)
        
        # 测试趋势组合格式化
        def format_trend_combo(value):
            if pd.isna(value) or value == '' or value is None:
                return '000000'
            try:
                if isinstance(value, (int, float)):
                    if value == 0:
                        return '000000'
                    return str(int(value)).zfill(6)
                else:
                    return str(value).zfill(6)
            except:
                return '000000'
        
        test_df['趋势组合'] = test_df['趋势组合'].apply(format_trend_combo)
        
        # 测试其他字段格式化
        text_fields = ['买入日开盘涨跌幅', '日内股票标记', '卖出日开盘涨跌幅']
        for field in text_fields:
            if field in test_df.columns:
                def format_as_text(value):
                    if pd.isna(value) or value == '' or value is None:
                        return '0'
                    try:
                        if isinstance(value, (int, float)):
                            return str(int(value))
                        else:
                            return str(value)
                    except:
                        return '0'
                
                test_df[field] = test_df[field].apply(format_as_text)
        
        print("格式化后的数据示例:")
        sample_after = test_df.iloc[0]
        print(f"  技术指标特征: {sample_after['技术指标特征']} (类型: {type(sample_after['技术指标特征'])})")
        print(f"  趋势组合: {sample_after['趋势组合']} (类型: {type(sample_after['趋势组合'])})")
        
        for field in text_fields:
            if field in test_df.columns:
                print(f"  {field}: {sample_after[field]} (类型: {type(sample_after[field])})")
        
        # 验证修复效果
        print("\n3. 验证修复效果:")
        
        # 检查技术指标特征
        tech_features = test_df['技术指标特征'].tolist()
        print(f"技术指标特征示例: {tech_features[:5]}")
        print(f"技术指标特征都是字符串: {all(isinstance(x, str) for x in tech_features)}")
        print(f"技术指标特征都是6位: {all(len(str(x)) == 6 for x in tech_features)}")
        
        # 检查趋势组合
        trend_combos = test_df['趋势组合'].tolist()
        print(f"趋势组合示例: {trend_combos[:5]}")
        print(f"趋势组合都是字符串: {all(isinstance(x, str) for x in trend_combos)}")
        print(f"趋势组合都是6位: {all(len(str(x)) == 6 for x in trend_combos)}")
        
        # 检查其他字段
        for field in text_fields:
            if field in test_df.columns:
                values = test_df[field].tolist()
                print(f"{field}都是字符串: {all(isinstance(x, str) for x in values)}")
        
        print("\n✅ 字段格式化测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fix()
