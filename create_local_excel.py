"""
创建本地Excel文件

这个脚本用于在当前目录创建一个简单的Excel文件，测试文件创建功能是否正常。
"""

import pandas as pd
import os

# 创建一个简单的DataFrame
df = pd.DataFrame({
    '策略编号': [1],
    '策略描述': ['测试策略']
})

# 创建文件路径（在当前目录）
file_path = "strategy_1.xlsx"

# 保存到Excel
df.to_excel(file_path, index=False)

print(f"已创建Excel文件: {file_path}")

# 验证文件是否成功保存
if os.path.exists(file_path):
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size} 字节")
    if file_size > 0:
        print(f"文件创建成功")
    else:
        print(f"警告: 文件大小为0")
else:
    print(f"错误: 文件不存在")
