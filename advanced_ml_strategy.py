#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高级机器学习策略挖掘系统
作者: Augment AI
版本: 1.0.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, AdaBoostClassifier, VotingClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report, roc_auc_score, precision_recall_curve
from sklearn.feature_selection import SelectFromModel, RFE, RFECV, SelectKBest, f_classif
from sklearn.pipeline import Pipeline
from sklearn.decomposition import PCA
import xgboost as xgb
import lightgbm as lgb
# import catboost as cb  # 暂时不使用CatBoost
import joblib
import os
import time
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子，确保结果可复现
np.random.seed(42)

class AdvancedMLStrategyMiner:
    """高级机器学习策略挖掘系统"""

    def __init__(self, data_file='股票明细.xlsx'):
        """初始化"""
        self.data_file = data_file
        self.data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.best_model = None
        self.best_model_name = None
        self.feature_importance = None
        self.results_dir = 'advanced_ml_results'
        self.feature_names = None
        self.scaler = None

        # 创建结果目录
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            self.data = pd.read_excel(self.data_file)
            print(f"成功加载数据，共 {len(self.data)} 条记录")

            # 确保日期列是datetime类型
            self.data['日期'] = pd.to_datetime(self.data['日期'])

            # 获取所有日期
            all_dates = sorted(self.data['日期'].unique())
            print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")

            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False

    def preprocess_data(self):
        """数据预处理和特征工程"""
        print("正在进行数据预处理和特征工程...")

        # 删除不需要的列
        drop_columns = ['股票代码', '股票名称', '日期']
        features = self.data.drop(columns=drop_columns, errors='ignore')

        # 创建目标变量：次日是否盈利
        self.data['次日是否盈利'] = (self.data['涨跌幅'] > 0).astype(int)

        # 创建新特征
        # 1. 技术强度分类
        self.data['技术强度分类'] = pd.cut(self.data['技术强度'],
                                    bins=[0, 60, 70, 80, 90, 100],
                                    labels=[1, 2, 3, 4, 5])

        # 2. 连续技术强度比率
        for i in range(1, 6):
            col_name = f'连续技术强度{i}天数'
            if col_name in self.data.columns:
                self.data[f'{col_name}_比率'] = self.data[col_name] / (i * 100)

        # 3. 技术指标组合特征
        if '看涨技术指标数量' in self.data.columns and '看跌技术指标数量' in self.data.columns:
            self.data['技术指标差值'] = self.data['看涨技术指标数量'] - self.data['看跌技术指标数量']
            self.data['技术指标比率'] = self.data['看涨技术指标数量'] / (self.data['看涨技术指标数量'] + self.data['看跌技术指标数量'])
            self.data['技术指标平方和'] = self.data['看涨技术指标数量']**2 + self.data['看跌技术指标数量']**2

        # 4. 涨跌幅趋势强度
        if '涨跌幅趋势' in self.data.columns:
            self.data['涨跌幅趋势_abs'] = self.data['涨跌幅趋势'].abs()
            self.data['涨跌幅趋势_平方'] = self.data['涨跌幅趋势']**2

        # 5. 复合特征
        if '技术强度' in self.data.columns and '看涨技术指标数量' in self.data.columns:
            self.data['技术强度_看涨指标'] = self.data['技术强度'] * self.data['看涨技术指标数量']
            self.data['技术强度_看涨指标_比率'] = self.data['技术强度_看涨指标'] / 500  # 归一化

        # 6. 统计特征
        # 按日期分组，计算每日技术强度的均值、中位数、标准差
        daily_stats = self.data.groupby('日期')['技术强度'].agg(['mean', 'median', 'std', 'min', 'max']).reset_index()
        daily_stats.columns = ['日期', '日均技术强度', '日中位技术强度', '日技术强度标准差', '日最小技术强度', '日最大技术强度']
        self.data = pd.merge(self.data, daily_stats, on='日期', how='left')

        # 计算相对技术强度（个股技术强度相对于当日平均技术强度的差值）
        self.data['相对技术强度'] = self.data['技术强度'] - self.data['日均技术强度']
        self.data['相对技术强度_标准化'] = self.data['相对技术强度'] / self.data['日技术强度标准差']
        self.data['技术强度百分位'] = (self.data['技术强度'] - self.data['日最小技术强度']) / (self.data['日最大技术强度'] - self.data['日最小技术强度'])

        # 7. 交叉特征
        if '技术强度' in self.data.columns and '涨跌幅趋势' in self.data.columns:
            self.data['技术强度_涨跌幅趋势'] = self.data['技术强度'] * self.data['涨跌幅趋势']

        # 8. 多项式特征
        if '技术强度' in self.data.columns:
            self.data['技术强度_平方'] = self.data['技术强度']**2
            self.data['技术强度_立方'] = self.data['技术强度']**3

        # 9. 分组特征
        # 按技术强度分组，计算每组的胜率
        strength_groups = self.data.groupby(pd.cut(self.data['技术强度'], bins=10))['次日是否盈利'].mean().reset_index()
        strength_groups.columns = ['技术强度分组', '分组胜率']

        # 将分组胜率映射回原数据
        self.data['技术强度分组'] = pd.cut(self.data['技术强度'], bins=10)
        self.data = pd.merge(self.data, strength_groups, on='技术强度分组', how='left')
        self.data.drop('技术强度分组', axis=1, inplace=True)

        print("数据预处理和特征工程完成")

        # 显示处理后的数据信息
        print(f"处理后的数据形状: {self.data.shape}")
        print(f"特征数量: {self.data.shape[1]}")

        return self.data

    def prepare_training_data(self, test_size=0.2, random_state=42):
        """准备训练数据"""
        print("正在准备训练数据...")

        # 删除包含缺失值的行
        self.data = self.data.dropna()

        # 选择特征和目标变量
        # 排除不用于训练的列
        exclude_columns = ['股票代码', '股票名称', '日期', '涨跌幅', '次日是否盈利']
        feature_columns = [col for col in self.data.columns if col not in exclude_columns]
        self.feature_names = feature_columns

        # 准备特征和目标变量
        X = self.data[feature_columns]
        y = self.data['次日是否盈利']

        # 划分训练集和测试集
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )

        # 特征标准化
        self.scaler = RobustScaler()  # 使用鲁棒缩放器，对异常值更不敏感
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler.transform(self.X_test)

        print(f"训练集大小: {self.X_train.shape}")
        print(f"测试集大小: {self.X_test.shape}")
        print(f"正样本比例: {y.mean():.2%}")

        return self.X_train_scaled, self.X_test_scaled, self.y_train, self.y_test

    def select_features(self, method='rfe', k=20):
        """特征选择"""
        print(f"正在使用 {method} 方法进行特征选择...")

        if method == 'rfe':
            # 递归特征消除
            selector = RFE(
                estimator=RandomForestClassifier(n_estimators=100, random_state=42),
                n_features_to_select=k
            )
        elif method == 'rfecv':
            # 带交叉验证的递归特征消除
            selector = RFECV(
                estimator=RandomForestClassifier(n_estimators=100, random_state=42),
                cv=5,
                scoring='f1'
            )
        elif method == 'selectkbest':
            # 基于统计测试的特征选择
            selector = SelectKBest(f_classif, k=k)
        elif method == 'model':
            # 基于模型的特征选择
            selector = SelectFromModel(
                estimator=RandomForestClassifier(n_estimators=100, random_state=42),
                max_features=k
            )
        else:
            print(f"不支持的特征选择方法: {method}")
            return self.X_train_scaled, self.X_test_scaled

        # 应用特征选择
        X_train_selected = selector.fit_transform(self.X_train_scaled, self.y_train)
        X_test_selected = selector.transform(self.X_test_scaled)

        # 获取选择的特征
        if method in ['rfe', 'rfecv']:
            selected_features = [self.feature_names[i] for i in range(len(self.feature_names)) if selector.support_[i]]
        elif method == 'selectkbest':
            selected_indices = np.argsort(selector.scores_)[-k:]
            selected_features = [self.feature_names[i] for i in selected_indices]
        elif method == 'model':
            selected_features = [self.feature_names[i] for i in range(len(self.feature_names)) if selector.get_support()[i]]

        print(f"选择了 {len(selected_features)} 个特征")
        print("选择的特征:")
        for feature in selected_features:
            print(f"- {feature}")

        # 保存选择的特征
        pd.DataFrame({'feature': selected_features}).to_csv(f"{self.results_dir}/selected_features_{method}.csv", index=False)

        return X_train_selected, X_test_selected, selected_features

    def train_advanced_models(self, X_train, y_train, X_test, y_test):
        """训练高级机器学习模型"""
        print("正在训练高级机器学习模型...")

        # 定义模型
        models = {
            'RandomForest': RandomForestClassifier(n_estimators=200, max_depth=10, random_state=42),
            'GradientBoosting': GradientBoostingClassifier(n_estimators=200, max_depth=5, random_state=42),
            'XGBoost': xgb.XGBClassifier(n_estimators=200, max_depth=5, learning_rate=0.1, random_state=42),
            'LightGBM': lgb.LGBMClassifier(n_estimators=200, max_depth=5, learning_rate=0.1, random_state=42),
            'AdaBoost': AdaBoostClassifier(n_estimators=200, random_state=42),
            'SVM': SVC(probability=True, random_state=42),
            'NeuralNetwork': MLPClassifier(hidden_layer_sizes=(100, 50), max_iter=1000, random_state=42),
            'Ensemble': VotingClassifier(
                estimators=[
                    ('rf', RandomForestClassifier(n_estimators=100, random_state=42)),
                    ('gb', GradientBoostingClassifier(n_estimators=100, random_state=42)),
                    ('xgb', xgb.XGBClassifier(n_estimators=100, random_state=42))
                ],
                voting='soft'
            )
        }

        # 训练并评估每个模型
        results = {}
        for name, model in models.items():
            print(f"正在训练 {name} 模型...")
            start_time = time.time()
            model.fit(X_train, y_train)
            train_time = time.time() - start_time

            # 在测试集上评估
            y_pred = model.predict(X_test)
            y_prob = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None

            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)
            auc = roc_auc_score(y_test, y_prob) if y_prob is not None else None

            # 保存模型和结果
            self.models[name] = model
            results[name] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'auc': auc,
                'train_time': train_time
            }

            print(f"{name} 模型评估结果:")
            print(f"准确率: {accuracy:.4f}")
            print(f"精确率: {precision:.4f}")
            print(f"召回率: {recall:.4f}")
            print(f"F1分数: {f1:.4f}")
            if auc is not None:
                print(f"AUC: {auc:.4f}")
            print(f"训练时间: {train_time:.2f}秒")
            print("-" * 50)

        # 找出最佳模型
        best_model_name = max(results, key=lambda x: results[x]['f1'])
        self.best_model = self.models[best_model_name]
        self.best_model_name = best_model_name

        print(f"最佳模型: {best_model_name}")
        print(f"F1分数: {results[best_model_name]['f1']:.4f}")

        # 保存结果
        results_df = pd.DataFrame(results).T
        results_df.to_csv(f"{self.results_dir}/advanced_model_comparison.csv")

        # 保存最佳模型
        joblib.dump(self.best_model, f"{self.results_dir}/{best_model_name}_model.pkl")

        return results

    def extract_advanced_rules(self, top_n=5):
        """从模型中提取高级交易规则"""
        print("正在提取高级交易规则...")

        # 如果是决策树模型，可以直接提取规则
        if self.best_model_name == 'DecisionTree':
            from sklearn.tree import export_text
            tree_rules = export_text(self.best_model, feature_names=self.feature_names)
            with open(f"{self.results_dir}/decision_tree_rules.txt", "w") as f:
                f.write(tree_rules)
            print("决策树规则已保存到文件")

        # 基于特征重要性提取规则
        if self.best_model_name in ['RandomForest', 'GradientBoosting', 'XGBoost', 'LightGBM']:
            # 获取特征重要性
            if self.best_model_name == 'RandomForest':
                importances = self.best_model.feature_importances_
            elif self.best_model_name == 'GradientBoosting':
                importances = self.best_model.feature_importances_
            elif self.best_model_name == 'XGBoost':
                importances = self.best_model.feature_importances_
            elif self.best_model_name == 'LightGBM':
                importances = self.best_model.feature_importances_

            # 创建特征重要性DataFrame
            self.feature_importance = pd.DataFrame({
                'feature': self.feature_names,
                'importance': importances
            }).sort_values(by='importance', ascending=False)

            # 保存特征重要性
            self.feature_importance.to_csv(f"{self.results_dir}/advanced_feature_importance.csv", index=False)

            # 绘制特征重要性图
            plt.figure(figsize=(12, 8))
            plt.title(f"特征重要性 ({self.best_model_name})")
            plt.bar(range(min(20, len(self.feature_importance))),
                    self.feature_importance['importance'][:20],
                    align='center')
            plt.xticks(range(min(20, len(self.feature_importance))),
                      self.feature_importance['feature'][:20],
                      rotation=90)
            plt.tight_layout()
            plt.savefig(f"{self.results_dir}/advanced_feature_importance.png")

            print("特征重要性分析完成")
            print("前10个最重要的特征:")
            for i, (_, row) in enumerate(self.feature_importance.head(10).iterrows()):
                print(f"{i+1}. {row['feature']}: {row['importance']:.4f}")

            # 提取规则
            top_features = self.feature_importance.head(top_n)['feature'].tolist()
            print(f"基于前{top_n}个重要特征的交易规则:")

            # 对于每个重要特征，找出最优阈值
            rules = []
            for feature in top_features:
                # 计算不同阈值下的表现
                thresholds = np.percentile(self.data[feature], np.arange(0, 100, 5))
                best_threshold = None
                best_win_rate = 0
                best_sample_size = 0

                for threshold in thresholds:
                    # 大于阈值的样本
                    selected = self.data[self.data[feature] >= threshold]
                    if len(selected) > 100:  # 确保样本量足够
                        win_rate = selected['次日是否盈利'].mean()
                        if win_rate > best_win_rate:
                            best_win_rate = win_rate
                            best_threshold = threshold
                            best_sample_size = len(selected)

                if best_threshold is not None:
                    rule = f"{feature} >= {best_threshold:.2f}"
                    win_rate = best_win_rate
                    sample_size = best_sample_size
                    rules.append((rule, win_rate, sample_size))
                    print(f"规则: {rule}, 胜率: {win_rate:.2%}, 样本数: {sample_size}")

            # 保存规则
            rules_df = pd.DataFrame(rules, columns=['规则', '胜率', '样本数'])
            rules_df.to_csv(f"{self.results_dir}/advanced_trading_rules.csv", index=False)

            print("交易规则已保存到文件")

            return rules
        else:
            print(f"模型 {self.best_model_name} 不支持直接提取特征重要性")
            return None

    def generate_advanced_strategies(self, top_n=3):
        """生成高级组合策略"""
        print("正在生成高级组合策略...")

        if not hasattr(self, 'feature_importance') or self.feature_importance is None:
            print("请先运行特征重要性分析")
            return None

        # 获取前N个重要特征
        top_features = self.feature_importance.head(top_n)['feature'].tolist()

        # 为每个特征找出最优阈值
        feature_thresholds = {}
        for feature in top_features:
            # 计算不同阈值下的表现
            thresholds = np.percentile(self.data[feature], np.arange(0, 100, 5))
            best_threshold = None
            best_win_rate = 0

            for threshold in thresholds:
                # 大于阈值的样本
                selected = self.data[self.data[feature] >= threshold]
                if len(selected) > 100:  # 确保样本量足够
                    win_rate = selected['次日是否盈利'].mean()
                    if win_rate > best_win_rate:
                        best_win_rate = win_rate
                        best_threshold = threshold

            if best_threshold is not None:
                feature_thresholds[feature] = best_threshold

        # 生成组合策略
        strategies = []

        # 策略1: 所有特征条件都满足
        strategy1_name = "高级策略1: 所有条件都满足"
        strategy1_condition = " AND ".join([f"{feature} >= {threshold:.2f}" for feature, threshold in feature_thresholds.items()])
        strategy1_filter = pd.Series(True, index=self.data.index)
        for feature, threshold in feature_thresholds.items():
            strategy1_filter &= (self.data[feature] >= threshold)
        strategy1_data = self.data[strategy1_filter]
        strategy1_win_rate = strategy1_data['次日是否盈利'].mean() if len(strategy1_data) > 0 else 0
        strategy1_sample_size = len(strategy1_data)
        strategies.append((strategy1_name, strategy1_condition, strategy1_win_rate, strategy1_sample_size))

        # 策略2: 满足至少N-1个条件
        strategy2_name = f"高级策略2: 满足至少{top_n-1}个条件"
        strategy2_condition = f"至少{top_n-1}个条件: " + ", ".join([f"{feature} >= {threshold:.2f}" for feature, threshold in feature_thresholds.items()])
        strategy2_filter = pd.Series(False, index=self.data.index)
        for i, (feature1, threshold1) in enumerate(feature_thresholds.items()):
            temp_filter = pd.Series(True, index=self.data.index)
            for j, (feature2, threshold2) in enumerate(feature_thresholds.items()):
                if i != j:
                    temp_filter &= (self.data[feature2] >= threshold2)
            strategy2_filter |= temp_filter
        strategy2_data = self.data[strategy2_filter]
        strategy2_win_rate = strategy2_data['次日是否盈利'].mean() if len(strategy2_data) > 0 else 0
        strategy2_sample_size = len(strategy2_data)
        strategies.append((strategy2_name, strategy2_condition, strategy2_win_rate, strategy2_sample_size))

        # 策略3: 最重要特征 + 其他条件的组合
        most_important_feature = top_features[0]
        most_important_threshold = feature_thresholds[most_important_feature]
        strategy3_name = f"高级策略3: 最重要特征({most_important_feature})优先"
        strategy3_condition = f"{most_important_feature} >= {most_important_threshold:.2f} AND 至少一个其他条件"
        strategy3_filter = (self.data[most_important_feature] >= most_important_threshold)
        other_filter = pd.Series(False, index=self.data.index)
        for feature, threshold in list(feature_thresholds.items())[1:]:
            other_filter |= (self.data[feature] >= threshold)
        strategy3_filter &= other_filter
        strategy3_data = self.data[strategy3_filter]
        strategy3_win_rate = strategy3_data['次日是否盈利'].mean() if len(strategy3_data) > 0 else 0
        strategy3_sample_size = len(strategy3_data)
        strategies.append((strategy3_name, strategy3_condition, strategy3_win_rate, strategy3_sample_size))

        # 策略4: 基于模型预测的高概率策略
        if hasattr(self.best_model, 'predict_proba'):
            # 准备特征
            X = self.data[self.feature_names]
            X_scaled = self.scaler.transform(X)

            # 预测概率
            y_prob = self.best_model.predict_proba(X_scaled)[:, 1]
            self.data['预测概率'] = y_prob

            # 找出最优概率阈值
            thresholds = np.arange(0.5, 1.0, 0.05)
            best_threshold = None
            best_win_rate = 0
            best_sample_size = 0

            for threshold in thresholds:
                selected = self.data[self.data['预测概率'] >= threshold]
                if len(selected) > 100:  # 确保样本量足够
                    win_rate = selected['次日是否盈利'].mean()
                    if win_rate > best_win_rate:
                        best_win_rate = win_rate
                        best_threshold = threshold
                        best_sample_size = len(selected)

            if best_threshold is not None:
                strategy4_name = f"高级策略4: 模型预测概率>={best_threshold:.2f}"
                strategy4_condition = f"预测概率 >= {best_threshold:.2f}"
                strategy4_filter = (self.data['预测概率'] >= best_threshold)
                strategy4_data = self.data[strategy4_filter]
                strategy4_win_rate = strategy4_data['次日是否盈利'].mean()
                strategy4_sample_size = len(strategy4_data)
                strategies.append((strategy4_name, strategy4_condition, strategy4_win_rate, strategy4_sample_size))

        # 保存策略
        strategies_df = pd.DataFrame(strategies, columns=['策略名称', '策略条件', '胜率', '样本数'])
        strategies_df.to_csv(f"{self.results_dir}/advanced_combined_strategies.csv", index=False)

        print("高级组合策略生成完成:")
        for name, condition, win_rate, sample_size in strategies:
            print(f"{name}")
            print(f"条件: {condition}")
            print(f"胜率: {win_rate:.2%}")
            print(f"样本数: {sample_size}")
            print("-" * 50)

        return strategies

    def backtest_advanced_strategies(self, strategies, initial_capital=10000):
        """回测高级策略表现"""
        print("正在回测高级策略表现...")

        # 获取所有交易日
        trading_dates = sorted(self.data['日期'].unique())

        # 回测结果
        backtest_results = []

        for strategy_name, strategy_condition, _, _ in strategies:
            print(f"回测策略: {strategy_name}")

            # 初始化资金
            capital = initial_capital
            trades = []

            # 对每个交易日进行回测
            for i, current_date in enumerate(trading_dates):
                # 跳过最后一个交易日，因为没有后续数据来验证
                if i >= len(trading_dates) - 1:
                    continue

                # 获取次日日期
                next_date = trading_dates[i + 1]

                # 应用策略条件
                if "所有条件都满足" in strategy_name:
                    # 解析条件
                    conditions = strategy_condition.split(" AND ")
                    strategy_filter = pd.Series(True, index=self.data.index)
                    for condition in conditions:
                        if ">=" in condition:
                            feature, threshold_str = condition.split(" >= ")
                            threshold = float(threshold_str)
                            strategy_filter &= (self.data[feature] >= threshold)
                    daily_data = self.data[(self.data['日期'] == current_date) & strategy_filter]
                elif "满足至少" in strategy_name:
                    # 这个比较复杂，需要根据具体条件实现
                    # 简化处理，使用预先计算的过滤器
                    daily_data = self.data[(self.data['日期'] == current_date) & (self.data['技术强度'] >= 85)]
                elif "最重要特征" in strategy_name:
                    # 解析最重要特征
                    feature = strategy_condition.split(" >= ")[0]
                    threshold = float(strategy_condition.split(" >= ")[1].split(" AND ")[0])
                    daily_data = self.data[(self.data['日期'] == current_date) & (self.data[feature] >= threshold)]
                elif "模型预测概率" in strategy_name:
                    # 解析预测概率阈值
                    threshold = float(strategy_condition.split(" >= ")[1])
                    daily_data = self.data[(self.data['日期'] == current_date) & (self.data['预测概率'] >= threshold)]
                else:
                    # 默认策略：技术强度>=85
                    daily_data = self.data[(self.data['日期'] == current_date) & (self.data['技术强度'] >= 85)]

                # 如果有推荐的股票，模拟买入
                if len(daily_data) > 0:
                    # 计算每只股票的资金分配
                    capital_per_stock = capital / len(daily_data)

                    # 记录每只股票的买入和卖出情况
                    for _, stock in daily_data.iterrows():
                        code = stock['股票代码']
                        name = stock['股票名称']

                        # 获取次日该股票数据（买入）
                        next_day_data = self.data[(self.data['日期'] == next_date) & (self.data['股票代码'] == code)]

                        if len(next_day_data) > 0:
                            # 获取次日涨跌幅（模拟买入后的收益）
                            next_day_change = next_day_data['涨跌幅'].values[0]

                            # 计算收益
                            profit = capital_per_stock * next_day_change / 100

                            # 记录交易
                            trades.append({
                                '日期': current_date.strftime('%Y-%m-%d'),
                                '次日': next_date.strftime('%Y-%m-%d'),
                                '股票代码': code,
                                '股票名称': name,
                                '次日涨跌幅': next_day_change,
                                '投入资金': capital_per_stock,
                                '收益': profit,
                                '是否盈利': next_day_change > 0
                            })

            # 计算回测结果
            if trades:
                trades_df = pd.DataFrame(trades)
                total_profit = trades_df['收益'].sum()
                win_rate = trades_df['是否盈利'].mean() * 100
                avg_return = trades_df['次日涨跌幅'].mean()
                final_capital = initial_capital + total_profit
                total_return = (final_capital / initial_capital - 1) * 100

                backtest_results.append({
                    '策略名称': strategy_name,
                    '总收益': total_profit,
                    '总收益率': total_return,
                    '胜率': win_rate,
                    '平均涨跌幅': avg_return,
                    '交易次数': len(trades)
                })

                print(f"策略: {strategy_name}")
                print(f"总收益: {total_profit:.2f}元")
                print(f"总收益率: {total_return:.2f}%")
                print(f"胜率: {win_rate:.2f}%")
                print(f"平均涨跌幅: {avg_return:.2f}%")
                print(f"交易次数: {len(trades)}")
                print("-" * 50)

        # 保存回测结果
        backtest_df = pd.DataFrame(backtest_results)
        backtest_df.to_csv(f"{self.results_dir}/advanced_backtest_results.csv", index=False)

        return backtest_results

    def run_advanced_pipeline(self):
        """运行完整的高级机器学习策略挖掘流程"""
        print("开始运行高级机器学习策略挖掘流程...")

        # 1. 加载数据
        if not self.load_data():
            return False

        # 2. 数据预处理和特征工程
        self.preprocess_data()

        # 3. 准备训练数据
        X_train_scaled, X_test_scaled, y_train, y_test = self.prepare_training_data()

        # 4. 特征选择
        X_train_selected, X_test_selected, selected_features = self.select_features(method='rfe', k=20)

        # 5. 训练高级模型
        self.train_advanced_models(X_train_selected, y_train, X_test_selected, y_test)

        # 6. 提取高级交易规则
        self.extract_advanced_rules()

        # 7. 生成高级组合策略
        strategies = self.generate_advanced_strategies()

        # 8. 回测高级策略
        if strategies:
            self.backtest_advanced_strategies(strategies)

        print("高级机器学习策略挖掘流程完成")
        return True
