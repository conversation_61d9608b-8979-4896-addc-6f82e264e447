#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票数据处理模块
作者: Augment AI
版本: 1.0.0

该模块提供股票数据的读取、处理和特征计算功能。
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
from tqdm import tqdm

class StockDataProcessor:
    """股票数据处理器"""

    def __init__(self, data_dir, output_dir):
        """
        初始化数据处理器

        参数:
            data_dir (str): 数据目录，包含日期子目录
            output_dir (str): 输出目录
        """
        self.data_dir = data_dir
        self.output_dir = output_dir

        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

    def process_data(self, process_all=True, start_date="", end_date="", callback=None):
        """
        处理股票数据

        参数:
            process_all (bool): 是否处理所有数据
            start_date (str): 开始日期，格式：YYYYMMDD
            end_date (str): 结束日期，格式：YYYYMMDD
            callback (function): 回调函数，用于更新进度和日志

        返回:
            str: 处理后的数据文件路径
        """
        try:
            # 记录日志
            if callback:
                callback("info", "开始处理股票数据...")
                callback("info", f"数据目录: {self.data_dir}")
                callback("info", f"输出目录: {self.output_dir}")
                callback("info", f"处理所有数据: {process_all}")
                callback("info", f"开始日期: {start_date}")
                callback("info", f"结束日期: {end_date}")

            # 获取所有日期目录
            date_dirs = []
            for item in os.listdir(self.data_dir):
                item_path = os.path.join(self.data_dir, item)
                if os.path.isdir(item_path) and item.isdigit() and len(item) == 8:
                    if process_all or (start_date <= item <= end_date):
                        date_dirs.append(item)

            # 排序日期目录
            date_dirs.sort()

            # 记录日志
            if callback:
                callback("info", f"找到{len(date_dirs)}个日期目录")

            # 处理每个日期目录
            all_data = []
            total_dirs = len(date_dirs)

            for i, date_dir in enumerate(date_dirs):
                # 更新进度
                if callback:
                    progress = (i + 1) / total_dirs * 100
                    callback("progress", progress)
                    callback("info", f"正在处理日期: {date_dir} ({i+1}/{total_dirs})")

                # 处理日期目录
                date_path = os.path.join(self.data_dir, date_dir)

                # 获取所有Excel文件
                excel_files = []
                for item in os.listdir(date_path):
                    if item.endswith(".xlsx") or item.endswith(".xls"):
                        excel_files.append(os.path.join(date_path, item))

                # 记录日志
                if callback:
                    callback("info", f"找到{len(excel_files)}个Excel文件")

                # 处理每个Excel文件
                for excel_file in excel_files:
                    try:
                        # 读取Excel文件
                        df = pd.read_excel(excel_file)

                        # 添加日期列
                        df["日期"] = pd.to_datetime(date_dir, format="%Y%m%d")

                        # 添加到总数据
                        all_data.append(df)

                    except Exception as e:
                        # 记录错误日志
                        if callback:
                            callback("error", f"处理文件{excel_file}时出错: {str(e)}")

            # 合并所有数据
            if all_data:
                merged_data = pd.concat(all_data, ignore_index=True)

                # 标准化列名
                merged_data = self.standardize_columns(merged_data)

                # 计算技术指标
                processed_data = self.calculate_technical_indicators(merged_data)

                # 保存处理后的数据
                output_file = os.path.join(self.output_dir, "股票数据汇总.xlsx")
                processed_data.to_excel(output_file, index=False)

                # 记录日志
                if callback:
                    callback("info", f"数据处理完成，共处理{len(all_data)}个文件")
                    callback("info", f"合并后的数据已保存到: {output_file}")

                return output_file
            else:
                # 记录日志
                if callback:
                    callback("warning", "没有找到任何数据")

                return None

        except Exception as e:
            # 记录错误日志
            if callback:
                callback("error", f"处理数据时出错: {str(e)}")

            return None

    def standardize_columns(self, df):
        """
        标准化列名

        参数:
            df (DataFrame): 原始数据

        返回:
            DataFrame: 标准化后的数据
        """
        # 列名映射
        column_mapping = {
            # 股票基本信息
            '代码': '股票代码',
            '股票代码': '股票代码',
            '名称': '股票名称',
            '股票名称': '股票名称',

            # 价格信息
            '开盘': '开盘价',
            '开盘价': '开盘价',
            '收盘': '收盘价',
            '收盘价': '收盘价',
            '最高': '最高价',
            '最高价': '最高价',
            '最低': '最低价',
            '最低价': '最低价',

            # 交易信息
            '成交量': '成交量',
            '成交额': '成交额',
            '换手率': '换手率',

            # 涨跌信息
            '涨跌幅': '涨跌幅',
            '涨跌额': '涨跌额',

            # 技术指标
            '技术强度': '技术强度',
            '连续技术强度3天数': '连续技术强度3天数',
            '连续技术强度5天数': '连续技术强度5天数',
            '连续技术强度10天数': '连续技术强度10天数'
        }

        # 创建新的DataFrame
        new_df = df.copy()

        # 重命名列
        for old_col, new_col in column_mapping.items():
            if old_col in new_df.columns and new_col not in new_df.columns:
                new_df.rename(columns={old_col: new_col}, inplace=True)

        # 确保必要的列存在
        required_columns = ['股票代码', '股票名称', '开盘价', '收盘价', '最高价', '最低价', '成交量', '日期']
        for col in required_columns:
            if col not in new_df.columns:
                if col == '股票代码' and '代码' in new_df.columns:
                    new_df['股票代码'] = new_df['代码']
                elif col == '股票名称' and '名称' in new_df.columns:
                    new_df['股票名称'] = new_df['名称']
                elif col == '开盘价' and '开盘' in new_df.columns:
                    new_df['开盘价'] = new_df['开盘']
                elif col == '收盘价' and '收盘' in new_df.columns:
                    new_df['收盘价'] = new_df['收盘']
                elif col == '最高价' and '最高' in new_df.columns:
                    new_df['最高价'] = new_df['最高']
                elif col == '最低价' and '最低' in new_df.columns:
                    new_df['最低价'] = new_df['最低']
                else:
                    new_df[col] = np.nan

        return new_df

    def calculate_technical_indicators(self, df):
        """
        计算技术指标

        参数:
            df (DataFrame): 原始数据

        返回:
            DataFrame: 添加技术指标后的数据
        """
        # 创建新的DataFrame
        new_df = df.copy()

        # 确保日期是日期类型
        new_df['日期'] = pd.to_datetime(new_df['日期'])

        # 按股票代码和日期排序
        new_df.sort_values(['股票代码', '日期'], inplace=True)

        # 计算涨跌幅（如果不存在）
        if '涨跌幅' not in new_df.columns:
            new_df['涨跌幅'] = new_df.groupby('股票代码')['收盘价'].pct_change() * 100

        # 计算技术指标
        stock_codes = new_df['股票代码'].unique()

        # 创建进度条
        for stock_code in tqdm(stock_codes, desc="计算技术指标"):
            # 获取该股票的数据
            stock_data = new_df[new_df['股票代码'] == stock_code].copy()

            if len(stock_data) > 0:
                # 计算均线
                stock_data['MA5'] = stock_data['收盘价'].rolling(window=5).mean()
                stock_data['MA10'] = stock_data['收盘价'].rolling(window=10).mean()
                stock_data['MA20'] = stock_data['收盘价'].rolling(window=20).mean()
                stock_data['MA30'] = stock_data['收盘价'].rolling(window=30).mean()

                # 计算MACD
                exp12 = stock_data['收盘价'].ewm(span=12, adjust=False).mean()
                exp26 = stock_data['收盘价'].ewm(span=26, adjust=False).mean()
                stock_data['MACD'] = exp12 - exp26
                stock_data['MACD_SIGNAL'] = stock_data['MACD'].ewm(span=9, adjust=False).mean()
                stock_data['MACD_HIST'] = stock_data['MACD'] - stock_data['MACD_SIGNAL']

                # 计算RSI
                delta = stock_data['收盘价'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                stock_data['RSI'] = 100 - (100 / (1 + rs))

                # 计算KDJ
                low_min = stock_data['最低价'].rolling(window=9).min()
                high_max = stock_data['最高价'].rolling(window=9).max()
                rsv = (stock_data['收盘价'] - low_min) / (high_max - low_min) * 100
                stock_data['K'] = rsv.rolling(window=3).mean()
                stock_data['D'] = stock_data['K'].rolling(window=3).mean()
                stock_data['J'] = 3 * stock_data['K'] - 2 * stock_data['D']

                # 计算布林带
                stock_data['BOLL_MIDDLE'] = stock_data['收盘价'].rolling(window=20).mean()
                stock_data['BOLL_STD'] = stock_data['收盘价'].rolling(window=20).std()
                stock_data['BOLL_UPPER'] = stock_data['BOLL_MIDDLE'] + 2 * stock_data['BOLL_STD']
                stock_data['BOLL_LOWER'] = stock_data['BOLL_MIDDLE'] - 2 * stock_data['BOLL_STD']

                # 计算技术指标
                # 均线多头排列
                stock_data['技术指标_均线多头排列'] = ((stock_data['MA5'] > stock_data['MA10']) &
                                          (stock_data['MA10'] > stock_data['MA20']) &
                                          (stock_data['MA20'] > stock_data['MA30'])).astype(int)

                # MACD金叉
                stock_data['技术指标_MACD金叉'] = ((stock_data['MACD'] > stock_data['MACD_SIGNAL']) &
                                        (stock_data['MACD'].shift(1) <= stock_data['MACD_SIGNAL'].shift(1))).astype(int)

                # RSI反弹
                stock_data['技术指标_RSI反弹'] = ((stock_data['RSI'] > 50) &
                                      (stock_data['RSI'].shift(1) <= 50)).astype(int)

                # KDJ金叉
                stock_data['技术指标_KDJ金叉'] = ((stock_data['K'] > stock_data['D']) &
                                      (stock_data['K'].shift(1) <= stock_data['D'].shift(1))).astype(int)

                # 布林带突破
                stock_data['技术指标_布林带突破'] = (stock_data['收盘价'] > stock_data['BOLL_UPPER']).astype(int)

                # 计算看涨技术指标数量
                stock_data['看涨技术指标数量'] = (stock_data['技术指标_均线多头排列'] +
                                      stock_data['技术指标_MACD金叉'] +
                                      stock_data['技术指标_RSI反弹'] +
                                      stock_data['技术指标_KDJ金叉'] +
                                      stock_data['技术指标_布林带突破'])

                # 计算技术强度（如果不存在）
                if '技术强度' not in stock_data.columns:
                    # 这里使用一个简单的计算方法，实际应根据具体需求调整
                    stock_data['技术强度'] = stock_data['看涨技术指标数量'] * 20

                # 计算连续技术强度天数（如果不存在）
                if '连续技术强度3天数' not in stock_data.columns:
                    # 计算连续3天技术强度
                    stock_data['连续技术强度3天数'] = stock_data['技术强度'].rolling(window=3).sum()

                if '连续技术强度5天数' not in stock_data.columns:
                    # 计算连续5天技术强度
                    stock_data['连续技术强度5天数'] = stock_data['技术强度'].rolling(window=5).sum()

                if '连续技术强度10天数' not in stock_data.columns:
                    # 计算连续10天技术强度
                    stock_data['连续技术强度10天数'] = stock_data['技术强度'].rolling(window=10).sum()

                # 计算趋势指标
                # 价格趋势
                stock_data['价格趋势'] = (stock_data['收盘价'] > stock_data['收盘价'].shift(1)).astype(int)

                # 涨跌幅趋势
                stock_data['涨跌幅趋势'] = (stock_data['涨跌幅'] > 0).astype(int)

                # 技术强度趋势
                stock_data['技术强度趋势'] = (stock_data['技术强度'] > stock_data['技术强度'].shift(1)).astype(int)

                # 连续技术强度天数趋势
                stock_data['连续技术强度3天数趋势'] = (stock_data['连续技术强度3天数'] > stock_data['连续技术强度3天数'].shift(1)).astype(int)
                stock_data['连续技术强度5天数趋势'] = (stock_data['连续技术强度5天数'] > stock_data['连续技术强度5天数'].shift(1)).astype(int)
                stock_data['连续技术强度10天数趋势'] = (stock_data['连续技术强度10天数'] > stock_data['连续技术强度10天数'].shift(1)).astype(int)

                # 开盘涨跌
                stock_data['开盘涨跌'] = (stock_data['开盘价'] > stock_data['收盘价'].shift(1)).astype(int)

                # 更新数据
                new_df.loc[new_df['股票代码'] == stock_code] = stock_data

        return new_df
