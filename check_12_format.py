#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def check_12_format():
    """检查使用1和2格式的数据"""
    
    print("=== 检查使用1和2格式的数据 ===")
    
    # 检查强势股文件
    strong_file = 'E:/机器学习/complete_excel_results/tech_strength/daily/strong/tech_strength_strong_2025-05-15.xlsx'
    
    try:
        # 读取数据
        df = pd.read_excel(strong_file, dtype={
            '股票代码': str,
            '技术指标特征': str,
            '趋势组合': str
        })
        
        print(f"强势股数据行数: {len(df)}")
        
        # 检查前10行数据
        print(f"\n=== 前10行数据检查 ===")
        for i in range(min(10, len(df))):
            row = df.iloc[i]
            
            print(f"\n行 {i+1}:")
            print(f"  股票代码: {row.get('股票代码', 'N/A')}")
            print(f"  股票名称: {row.get('股票名称', 'N/A')}")
            print(f"  技术强度: {row.get('技术强度', 'N/A')}")
            
            # 重点检查技术指标特征和趋势组合
            tech_feature = row.get('技术指标特征', 'N/A')
            trend_combo = row.get('趋势组合', 'N/A')
            
            print(f"  技术指标特征: '{tech_feature}'")
            print(f"    类型: {type(tech_feature)}")
            print(f"    长度: {len(str(tech_feature))}")
            print(f"    包含的字符: {set(str(tech_feature))}")
            
            print(f"  趋势组合: '{trend_combo}'")
            print(f"    类型: {type(trend_combo)}")
            print(f"    长度: {len(str(trend_combo))}")
            print(f"    包含的字符: {set(str(trend_combo))}")
            
            # 检查是否只包含1和2
            tech_str = str(tech_feature)
            trend_str = str(trend_combo)
            
            tech_chars = set(tech_str)
            trend_chars = set(trend_str)
            
            if tech_chars.issubset({'1', '2'}):
                print(f"    ✅ 技术指标特征只包含1和2")
            else:
                print(f"    ❌ 技术指标特征包含其他字符: {tech_chars}")
            
            if trend_chars.issubset({'1', '2'}):
                print(f"    ✅ 趋势组合只包含1和2")
            else:
                print(f"    ❌ 趋势组合包含其他字符: {trend_chars}")
        
        # 统计字符分布
        print(f"\n=== 字符分布统计 ===")
        
        # 技术指标特征字符分布
        all_tech_chars = set()
        for value in df['技术指标特征']:
            all_tech_chars.update(set(str(value)))
        
        print(f"技术指标特征包含的所有字符: {sorted(all_tech_chars)}")
        
        # 趋势组合字符分布
        all_trend_chars = set()
        for value in df['趋势组合']:
            all_trend_chars.update(set(str(value)))
        
        print(f"趋势组合包含的所有字符: {sorted(all_trend_chars)}")
        
        # 检查长度分布
        print(f"\n=== 长度分布检查 ===")
        
        tech_lengths = [len(str(x)) for x in df['技术指标特征']]
        trend_lengths = [len(str(x)) for x in df['趋势组合']]
        
        print(f"技术指标特征长度分布: {set(tech_lengths)}")
        print(f"趋势组合长度分布: {set(trend_lengths)}")
        
        # 检查是否都是6位
        tech_all_6 = all(l == 6 for l in tech_lengths)
        trend_all_6 = all(l == 6 for l in trend_lengths)
        
        print(f"技术指标特征都是6位: {tech_all_6}")
        print(f"趋势组合都是6位: {trend_all_6}")
        
        # 显示一些示例值
        print(f"\n=== 示例值 ===")
        
        tech_unique = df['技术指标特征'].unique()[:10]
        trend_unique = df['趋势组合'].unique()[:10]
        
        print(f"技术指标特征前10个唯一值: {tech_unique}")
        print(f"趋势组合前10个唯一值: {trend_unique}")
        
        # 验证1和2格式的优势
        print(f"\n=== 1和2格式验证 ===")
        
        # 检查是否有以1开头的值（相当于原来的0开头）
        tech_start_with_1 = sum(1 for x in df['技术指标特征'] if str(x).startswith('1'))
        trend_start_with_1 = sum(1 for x in df['趋势组合'] if str(x).startswith('1'))
        
        print(f"技术指标特征以1开头的数量: {tech_start_with_1}")
        print(f"趋势组合以1开头的数量: {trend_start_with_1}")
        
        # 检查是否有以2开头的值（相当于原来的1开头）
        tech_start_with_2 = sum(1 for x in df['技术指标特征'] if str(x).startswith('2'))
        trend_start_with_2 = sum(1 for x in df['趋势组合'] if str(x).startswith('2'))
        
        print(f"技术指标特征以2开头的数量: {tech_start_with_2}")
        print(f"趋势组合以2开头的数量: {trend_start_with_2}")
        
        # 最终评估
        print(f"\n=== 最终评估 ===")
        
        success = True
        
        if not tech_all_6:
            print("❌ 技术指标特征长度不都是6位")
            success = False
        
        if not trend_all_6:
            print("❌ 趋势组合长度不都是6位")
            success = False
        
        if not all_tech_chars.issubset({'1', '2'}):
            print(f"❌ 技术指标特征包含1和2以外的字符: {all_tech_chars - {'1', '2'}}")
            success = False
        
        if not all_trend_chars.issubset({'1', '2'}):
            print(f"❌ 趋势组合包含1和2以外的字符: {all_trend_chars - {'1', '2'}}")
            success = False
        
        if success:
            print("🎉 1和2格式修复成功！")
            print("✅ 所有技术指标特征和趋势组合都是6位字符串")
            print("✅ 所有值都只包含1和2，没有前导0问题")
        else:
            print("❌ 1和2格式修复仍有问题")
        
        print(f"\n✅ 检查完成")
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_12_format()
