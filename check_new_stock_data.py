import pandas as pd
import numpy as np

# 读取股票明细表
file_path = r"E:\机器学习\complete_excel_results\股票明细_完整.xlsx"
df = pd.read_excel(file_path)

# 显示基本信息
print("数据基本信息:")
print(f"行数: {df.shape[0]}, 列数: {df.shape[1]}")
print("\n列名:")
print(df.columns.tolist())

# 显示前几行数据
print("\n前5行数据:")
print(df.head())

# 显示数据类型
print("\n数据类型:")
print(df.dtypes)

# 检查技术强度相关字段的统计信息
strength_columns = ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']
print("\n技术强度相关字段的统计信息:")
for col in strength_columns:
    if col in df.columns:
        print(f"\n{col}的统计信息:")
        print(df[col].describe())
        
        # 显示不同值的分布
        print(f"\n{col}的值分布:")
        value_counts = df[col].value_counts().sort_index()
        print(value_counts.head(10))  # 只显示前10个值

# 检查编码字段的分布
encoding_columns = ['技术指标特征', '趋势组合', '日内股票标记']
print("\n编码字段的分布:")
for col in encoding_columns:
    if col in df.columns:
        print(f"\n{col}的值分布:")
        value_counts = df[col].value_counts().sort_index()
        print(value_counts.head(10))  # 只显示前10个值

# 检查成交量是前一日几倍的分布
volume_ratio_col = '成交量是前一日几倍'
if volume_ratio_col in df.columns:
    print(f"\n{volume_ratio_col}的统计信息:")
    print(df[volume_ratio_col].describe())
    
    print(f"\n{volume_ratio_col}的值分布:")
    value_counts = df[volume_ratio_col].value_counts().sort_index()
    print(value_counts.head(10))  # 只显示前10个值

# 检查买入日开盘涨跌幅和卖出日开盘涨跌幅的分布
open_change_columns = ['买入日开盘涨跌幅', '卖出日开盘涨跌幅']
print("\n开盘涨跌幅字段的分布:")
for col in open_change_columns:
    if col in df.columns:
        print(f"\n{col}的值分布:")
        value_counts = df[col].value_counts().sort_index()
        print(value_counts)
