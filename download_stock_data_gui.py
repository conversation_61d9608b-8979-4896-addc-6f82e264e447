import pandas as pd
import os
import datetime
import time
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import sys
import traceback

# 导入错误处理模块
try:
    import error_handler
    # 安装全局错误处理器
    error_handler.install_error_handler()
except ImportError:
    print("警告: 错误处理模块未找到，将使用默认错误处理")

# 导入自定义加载器
try:
    import mini_racer_loader
    print("已加载mini_racer_loader")
except ImportError:
    print("警告: mini_racer_loader模块未找到，可能会影响程序运行")

try:
    import akshare_loader
    print("已加载akshare_loader")
except ImportError:
    print("警告: akshare_loader模块未找到，可能会影响程序运行")

# 导入其他模块
try:
    import stock_data_manager as sdm
    import akshare_wrapper as akw
    import akshare as ak  # 直接导入akshare，以便在需要时使用
except ImportError as e:
    # 显示导入错误
    error_msg = f"导入模块时出错: {e}\n\n"
    error_msg += "请确保已安装所有必要的依赖:\n"
    error_msg += "pip install pandas numpy akshare tqdm openpyxl py_mini_racer"

    # 尝试使用tkinter显示错误
    try:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("导入错误", error_msg)
        root.destroy()
    except:
        print(error_msg, file=sys.stderr)

    # 退出程序
    sys.exit(1)

class StockDataDownloader:
    def __init__(self, root):
        self.root = root
        self.root.title("下载股票交易数据")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 设置默认文件路径
        self.base_dir = r'E:\机器学习\complete_excel_results'  # 默认输出目录
        self.stock_data_dir = os.path.join(self.base_dir, 'stock_data')
        self.daily_data_dir = os.path.join(self.stock_data_dir, 'daily')
        self.stock_details_file = os.path.join(self.base_dir, '股票明细_完整.xlsx')

        # 初始化stock_data_manager的路径
        sdm.set_base_dir(self.base_dir)

        # 创建消息队列，用于线程间通信
        self.message_queue = queue.Queue()

        # 创建UI
        self.create_ui()

        # 启动消息处理
        self.process_messages()

    def create_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建设置框架
        settings_frame = ttk.LabelFrame(main_frame, text="下载设置", padding="10")
        settings_frame.pack(fill=tk.X, padx=5, pady=5)

        # 数据目录设置
        ttk.Label(settings_frame, text="数据输出目录:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.data_dir_var = tk.StringVar(value=self.base_dir)
        data_dir_entry = ttk.Entry(settings_frame, textvariable=self.data_dir_var, width=50)
        data_dir_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(settings_frame, text="浏览...", command=self.browse_data_dir).grid(row=0, column=2, padx=5, pady=5)

        # 股票明细文件设置
        ttk.Label(settings_frame, text="股票明细文件:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.details_file_var = tk.StringVar(value=self.stock_details_file)
        details_file_entry = ttk.Entry(settings_frame, textvariable=self.details_file_var, width=50)
        details_file_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(settings_frame, text="浏览...", command=self.browse_details_file).grid(row=1, column=2, padx=5, pady=5)

        # 日期范围设置
        date_frame = ttk.Frame(settings_frame)
        date_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)

        ttk.Label(date_frame, text="开始日期:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.start_year_var = tk.StringVar(value=str(datetime.datetime.now().year - 1))
        self.start_month_var = tk.StringVar(value=str(datetime.datetime.now().month))
        self.start_day_var = tk.StringVar(value="1")

        ttk.Combobox(date_frame, textvariable=self.start_year_var, values=[str(i) for i in range(2010, datetime.datetime.now().year + 1)], width=6).grid(row=0, column=1, padx=2)
        ttk.Label(date_frame, text="年").grid(row=0, column=2)
        ttk.Combobox(date_frame, textvariable=self.start_month_var, values=[str(i) for i in range(1, 13)], width=4).grid(row=0, column=3, padx=2)
        ttk.Label(date_frame, text="月").grid(row=0, column=4)
        ttk.Combobox(date_frame, textvariable=self.start_day_var, values=[str(i) for i in range(1, 32)], width=4).grid(row=0, column=5, padx=2)
        ttk.Label(date_frame, text="日").grid(row=0, column=6)

        ttk.Label(date_frame, text="结束日期:").grid(row=0, column=7, sticky=tk.W, padx=15, pady=5)
        self.end_year_var = tk.StringVar(value=str(datetime.datetime.now().year))
        self.end_month_var = tk.StringVar(value=str(datetime.datetime.now().month))
        self.end_day_var = tk.StringVar(value=str(datetime.datetime.now().day))

        ttk.Combobox(date_frame, textvariable=self.end_year_var, values=[str(i) for i in range(2010, datetime.datetime.now().year + 1)], width=6).grid(row=0, column=8, padx=2)
        ttk.Label(date_frame, text="年").grid(row=0, column=9)
        ttk.Combobox(date_frame, textvariable=self.end_month_var, values=[str(i) for i in range(1, 13)], width=4).grid(row=0, column=10, padx=2)
        ttk.Label(date_frame, text="月").grid(row=0, column=11)
        ttk.Combobox(date_frame, textvariable=self.end_day_var, values=[str(i) for i in range(1, 32)], width=4).grid(row=0, column=12, padx=2)
        ttk.Label(date_frame, text="日").grid(row=0, column=13)

        # 添加一个说明标签
        ttk.Label(settings_frame, text="将下载选定日期范围内所有A股股票的数据").grid(row=3, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)

        # 添加强制重新下载选项
        self.force_redownload_var = tk.BooleanVar(value=False)
        force_redownload_check = ttk.Checkbutton(
            settings_frame,
            text="强制重新下载（忽略本地文件）",
            variable=self.force_redownload_var
        )
        force_redownload_check.grid(row=4, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)

        # 下载按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(button_frame, text="开始下载", command=self.start_download, width=20).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="退出", command=self.root.quit, width=20).pack(side=tk.RIGHT, padx=5)

        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="下载日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建日志文本框和滚动条
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, width=80, height=20)
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_label.pack(fill=tk.X, padx=5, pady=5)

    def browse_data_dir(self):
        """浏览并选择数据输出目录"""
        directory = filedialog.askdirectory(initialdir=self.base_dir)
        if directory:
            self.data_dir_var.set(directory)
            # 更新基础目录
            self.base_dir = directory
            # 更新stock_data_manager模块中的路径
            sdm.set_base_dir(directory)
            self.log_message(f"数据输出目录已更新为: {directory}")

    def browse_details_file(self):
        """浏览并选择股票明细文件"""
        file_path = filedialog.askopenfilename(
            initialdir=self.base_dir,
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if file_path:
            self.details_file_var.set(file_path)

    def log_message(self, message):
        """将消息添加到消息队列"""
        self.message_queue.put(message)

    def process_messages(self):
        """处理消息队列中的消息"""
        try:
            while True:
                message = self.message_queue.get_nowait()
                self.log_text.insert(tk.END, message + "\n")
                self.log_text.see(tk.END)
                self.log_text.update_idletasks()
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.process_messages)

    def start_download(self):
        """开始下载数据"""
        # 禁用下载按钮，防止重复点击
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Button) and widget['text'] == "开始下载":
                widget.configure(state=tk.DISABLED)

        # 清空日志
        self.log_text.delete(1.0, tk.END)

        # 检查强制重新下载选项
        force_redownload = getattr(self, 'force_redownload_var', tk.BooleanVar(value=False)).get()
        if force_redownload:
            self.log_message("已启用强制重新下载选项，将忽略本地文件")

        # 获取设置
        self.base_dir = self.data_dir_var.get()
        self.stock_data_dir = os.path.join(self.base_dir, 'stock_data')
        self.daily_data_dir = os.path.join(self.stock_data_dir, 'daily')
        self.stock_details_file = os.path.join(self.base_dir, '股票明细_完整.xlsx')

        self.log_message(f"数据将保存到: {self.base_dir}")
        self.log_message(f"每日数据目录: {self.daily_data_dir}")

        # 确保目录存在
        if not os.path.exists(self.stock_data_dir):
            try:
                os.makedirs(self.stock_data_dir)
                self.log_message(f"创建目录: {self.stock_data_dir}")
            except Exception as e:
                messagebox.showerror("错误", f"无法创建目录: {e}")
                self.enable_download_button()
                return

        # 确保daily_data_dir目录存在
        if not os.path.exists(self.daily_data_dir):
            try:
                os.makedirs(self.daily_data_dir)
                self.log_message(f"创建目录: {self.daily_data_dir}")
            except Exception as e:
                messagebox.showerror("错误", f"无法创建目录: {e}")
                self.enable_download_button()
                return

        # 更新stock_data_manager模块中的路径
        paths = sdm.set_base_dir(self.base_dir)
        self.log_message(f"数据路径已更新: {paths['base_dir']}")
        self.log_message(f"每日数据目录: {paths['daily_data_dir']}")

        # 获取日期范围
        try:
            start_date = f"{self.start_year_var.get()}-{int(self.start_month_var.get()):02d}-{int(self.start_day_var.get()):02d}"
            end_date = f"{self.end_year_var.get()}-{int(self.end_month_var.get()):02d}-{int(self.end_day_var.get()):02d}"

            # 验证日期格式
            datetime.datetime.strptime(start_date, '%Y-%m-%d')
            datetime.datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            messagebox.showerror("错误", "日期格式无效")
            self.enable_download_button()
            return

        # 在单独的线程中执行下载任务
        download_thread = threading.Thread(target=self.download_data, args=(start_date, end_date))
        download_thread.daemon = True
        download_thread.start()

    def enable_download_button(self):
        """启用下载按钮"""
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Button) and widget['text'] == "开始下载":
                widget.configure(state=tk.NORMAL)

    def get_date_range_from_file(self):
        """获取默认日期范围"""
        # 默认使用最近一个月的数据
        end_date = datetime.datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime('%Y-%m-%d')
        self.log_message(f"使用默认日期范围: {start_date} 到 {end_date}")
        return start_date, end_date

    def get_all_stock_codes(self):
        """获取所有股票代码"""
        # 使用akshare_wrapper获取所有A股股票代码
        self.log_message("使用akshare获取所有A股股票代码...")
        try:
            # 获取所有A股股票代码
            stock_codes = akw.get_stock_codes()
            self.log_message(f"成功获取到 {len(stock_codes)} 只A股股票")
            return stock_codes
        except Exception as e:
            self.log_message(f"获取股票代码时出错: {e}")
            # 如果出错，则使用一些常用的股票代码
            default_codes = ["600000", "600036", "601398", "000001", "000002"]
            self.log_message(f"使用默认股票代码: {default_codes}")
            return default_codes

    def download_trading_calendar(self, start_date, end_date):
        """下载交易日历"""
        self.log_message("下载交易日历...")

        try:
            # 使用akshare_wrapper获取交易日历
            calendar_df = akw.get_trading_calendar(start_date, end_date)

            # 如果DataFrame为空，则生成日期范围
            if calendar_df.empty:
                self.log_message("交易日历DataFrame为空，将生成日期范围")
                return self.generate_date_range(start_date, end_date)

            self.log_message(f"交易日历下载完成，共 {len(calendar_df)} 个交易日")
            return calendar_df

        except Exception as e:
            self.log_message(f"下载交易日历时出错: {e}，将生成日期范围")
            # 如果出错，则生成日期范围
            return self.generate_date_range(start_date, end_date)

    def generate_date_range(self, start_date, end_date):
        """生成日期范围"""
        self.log_message(f"生成从 {start_date} 到 {end_date} 的日期范围")

        # 转换为日期对象
        start_date_obj = pd.to_datetime(start_date)
        end_date_obj = pd.to_datetime(end_date)

        # 生成日期范围
        date_range = pd.date_range(start=start_date_obj, end=end_date_obj)

        # 创建交易日历DataFrame
        calendar_df = pd.DataFrame()
        calendar_df['calendar_date'] = date_range
        calendar_df['is_trading_day'] = 1

        # 标记周末为非交易日
        calendar_df['is_trading_day'] = calendar_df['calendar_date'].apply(
            lambda x: 0 if x.weekday() >= 5 else 1
        )

        self.log_message(f"生成了 {len(calendar_df)} 天的日期范围，其中 {calendar_df['is_trading_day'].sum()} 天为交易日")

        return calendar_df

    def download_stock_data_for_date(self, date_str):
        """下载指定日期的所有股票数据"""
        self.log_message(f"开始下载日期 {date_str} 的股票数据")
        start_time = time.time()

        try:
            # 创建一个空的DataFrame，用于存储结果
            result_df = pd.DataFrame()

            # 获取强制重新下载选项
            force_redownload = getattr(self, 'force_redownload_var', tk.BooleanVar(value=False)).get()

            # 尝试使用本地数据（如果不是强制重新下载）
            if not force_redownload:
                try:
                    # 检查是否有本地数据
                    local_file = os.path.join(self.daily_data_dir, f"stock_data_{date_str.replace('-', '')}.xlsx")
                    if os.path.exists(local_file):
                        self.log_message(f"发现本地数据文件: {local_file}")
                        result_df = pd.read_excel(local_file)
                        if not result_df.empty:
                            self.log_message(f"成功从本地加载日期 {date_str} 的数据，共 {len(result_df)} 条记录")
                            return result_df
                except Exception as e:
                    self.log_message(f"尝试加载本地数据时出错: {e}")
            else:
                self.log_message(f"由于启用了强制重新下载选项，将忽略本地文件")

            # 使用akshare_wrapper获取所有股票数据
            self.log_message("使用akshare_wrapper获取股票数据...")

            # 尝试获取所有股票的数据
            try:
                # 获取所有股票数据
                result_df = akw.get_stock_data_for_date(date_str)

                if not result_df.empty:
                    self.log_message(f"成功获取到 {len(result_df)} 条股票数据记录")

                    # 保存数据到本地
                    try:
                        output_file = os.path.join(self.daily_data_dir, f"stock_data_{date_str.replace('-', '')}.xlsx")
                        result_df.to_excel(output_file, index=False)
                        self.log_message(f"数据已保存到: {output_file}")
                    except Exception as e:
                        self.log_message(f"保存数据到本地时出错: {e}")

                    end_time = time.time()
                    elapsed_time = end_time - start_time

                    self.log_message(f"日期 {date_str} 的股票数据下载完成，耗时: {elapsed_time:.2f}秒")

                    return result_df
            except Exception as e:
                self.log_message(f"使用akshare_wrapper获取所有股票数据时出错: {e}")

            # 如果上面的方法失败，尝试使用stock_zh_a_hist接口批量下载
            self.log_message("尝试使用stock_zh_a_hist接口批量下载数据...")

            # 获取所有股票代码
            stock_codes = self.get_all_stock_codes()
            if not stock_codes:
                self.log_message("没有找到任何股票代码")
                return pd.DataFrame()

            # 只使用前20个股票代码进行批量下载
            test_codes = stock_codes[:20]
            self.log_message(f"使用前20个股票代码进行批量下载...")

            all_stock_data = []
            for stock_code in test_codes:
                try:
                    # 根据股票代码判断是上证还是深证
                    if stock_code.startswith('6'):
                        stock_code_full = f"sh{stock_code}"
                    else:
                        stock_code_full = f"sz{stock_code}"

                    # 获取单只股票的历史数据
                    try:
                        stock_df = akw.safe_api_call(
                            ak.stock_zh_a_hist,
                            symbol=stock_code_full,
                            period="daily",
                            start_date=date_str,
                            end_date=date_str,
                            adjust="qfq"
                        )

                        if not stock_df.empty:
                            # 添加股票代码列
                            stock_df['code'] = stock_code
                            all_stock_data.append(stock_df)
                    except Exception as e:
                        self.log_message(f"获取股票 {stock_code} 数据时出错: {e}")
                        continue
                except Exception as e:
                    # 记录特定股票的错误，但继续处理其他股票
                    self.log_message(f"处理股票 {stock_code} 时出错: {e}")
                    continue

            # 合并所有股票的数据
            if all_stock_data:
                try:
                    result_df = pd.concat(all_stock_data, ignore_index=True)

                    # 重命名列，使其与我们的数据结构一致
                    result_df = result_df.rename(columns={
                        '日期': 'date',
                        '开盘': 'open',
                        '最高': 'high',
                        '最低': 'low',
                        '收盘': 'close',
                        '成交量': 'volume',
                        '成交额': 'amount',
                        '涨跌幅': 'pctChg'
                    })

                    # 确保必要的列存在
                    required_columns = ['date', 'code', 'open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']
                    for col in required_columns:
                        if col not in result_df.columns:
                            self.log_message(f"警告: 列 '{col}' 不存在，将创建空列")
                            result_df[col] = None

                    # 选择需要的列
                    available_columns = [col for col in required_columns if col in result_df.columns]
                    if available_columns:
                        result_df = result_df[available_columns]

                    # 保存数据到本地
                    try:
                        output_file = os.path.join(self.daily_data_dir, f"stock_data_{date_str.replace('-', '')}.xlsx")
                        result_df.to_excel(output_file, index=False)
                        self.log_message(f"数据已保存到: {output_file}")
                    except Exception as e:
                        self.log_message(f"保存数据到本地时出错: {e}")

                    end_time = time.time()
                    elapsed_time = end_time - start_time

                    self.log_message(f"日期 {date_str} 的股票数据下载完成，共 {len(result_df)} 条记录，耗时: {elapsed_time:.2f}秒")

                    return result_df
                except Exception as e:
                    self.log_message(f"合并股票数据时出错: {e}")
                    return pd.DataFrame()
            else:
                self.log_message(f"日期 {date_str} 没有找到任何股票数据")
                return pd.DataFrame()
        except Exception as e:
            self.log_message(f"下载日期 {date_str} 的股票数据时出错: {e}")
            import traceback
            self.log_message(traceback.format_exc())
            return pd.DataFrame()

    def download_data(self, start_date, end_date):
        """下载数据的主函数"""
        self.status_var.set("正在下载数据...")
        self.log_message(f"开始下载从 {start_date} 到 {end_date} 的股票数据")

        # 获取强制重新下载选项
        force_redownload = getattr(self, 'force_redownload_var', tk.BooleanVar(value=False)).get()
        if force_redownload:
            self.log_message("已启用强制重新下载选项，将忽略本地文件")

        # 记录日期范围
        self.log_message(f"下载日期范围: {start_date} 到 {end_date}")

        # 验证日期是否为未来日期
        try:
            current_date = datetime.datetime.now().date()
            start_date_obj = datetime.datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.datetime.strptime(end_date, '%Y-%m-%d').date()

            if start_date_obj > current_date or end_date_obj > current_date:
                self.log_message("警告: 检测到未来日期，这些日期可能没有交易数据")

                # 如果结束日期是未来日期，将其调整为当前日期
                if end_date_obj > current_date:
                    end_date = current_date.strftime('%Y-%m-%d')
                    self.log_message(f"已将结束日期调整为当前日期: {end_date}")
        except Exception as e:
            self.log_message(f"日期验证时出错: {e}")

        try:
            # 下载交易日历
            calendar_df = self.download_trading_calendar(start_date, end_date)
            self.log_message(f"交易日历下载完成，共 {len(calendar_df)} 天")

            # 提取交易日列表
            # 检查calendar_date列是否存在
            if 'calendar_date' in calendar_df.columns:
                date_column = 'calendar_date'
            elif 'date' in calendar_df.columns:
                date_column = 'date'
            else:
                # 如果找不到日期列，则使用第一列作为日期列
                date_column = calendar_df.columns[0]
                self.log_message(f"交易日历中找不到日期列，使用第一列 {date_column} 作为日期列")

            # 提取交易日列表
            trading_days = calendar_df[date_column].tolist()

            # 确保交易日期是datetime对象
            if trading_days and isinstance(trading_days[0], str):
                trading_days = [pd.to_datetime(day) for day in trading_days]

            self.log_message(f"交易日历中包含 {len(trading_days)} 个交易日")

            # 按日期下载和保存股票数据
            total_days = len(trading_days)
            all_stock_data = []  # 用于合并所有数据
            total_records = 0

            self.log_message("开始按日期下载股票历史数据...")
            for day_idx, trading_day in enumerate(trading_days):
                day_str = trading_day.strftime('%Y-%m-%d')

                # 更新进度
                day_progress = (day_idx + 1) / total_days * 100
                self.progress_var.set(day_progress)
                self.status_var.set(f"正在下载日期 {day_str} 的数据... ({day_idx+1}/{total_days})")
                self.root.update_idletasks()

                # 检查该日期的数据文件是否已存在
                daily_file_path = sdm.get_daily_data_path(day_str)
                if os.path.exists(daily_file_path) and not force_redownload:
                    self.log_message(f"日期 {day_str} 的数据文件已存在，跳过")

                    # 读取已存在的数据，用于合并
                    try:
                        day_df = sdm.load_daily_data(day_str)
                        if not day_df.empty:
                            all_stock_data.append(day_df)
                            total_records += len(day_df)
                            self.log_message(f"已加载日期 {day_str} 的现有数据，共 {len(day_df)} 条记录")
                    except Exception as e:
                        self.log_message(f"加载日期 {day_str} 的现有数据时出错: {e}")

                    continue
                elif os.path.exists(daily_file_path) and force_redownload:
                    self.log_message(f"日期 {day_str} 的数据文件已存在，但由于启用了强制重新下载选项，将重新下载")

                # 下载该日期的数据
                day_df = self.download_stock_data_for_date(day_str)

                # 保存该日期的数据
                if not day_df.empty:
                    sdm.save_daily_data(day_df, day_str)
                    self.log_message(f"日期 {day_str} 的数据已保存")

                    all_stock_data.append(day_df)
                    total_records += len(day_df)

            # 不再生成合并文件
            if total_records > 0:
                self.log_message(f"股票历史数据下载完成，共 {total_records} 条记录")
                messagebox.showinfo("完成", f"股票历史数据下载完成，共 {total_records} 条记录，已按日期分别存储")
            else:
                self.log_message("没有下载到任何股票数据")
                messagebox.showwarning("警告", "没有下载到任何股票数据")

            self.status_var.set("下载完成")
            self.enable_download_button()

        except Exception as e:
            self.log_message(f"下载过程中出错: {e}")
            messagebox.showerror("错误", f"下载过程中出错: {e}")
            self.status_var.set("下载失败")
            self.enable_download_button()

def main():
    try:
        # 设置异常处理
        if 'error_handler' not in sys.modules:
            # 如果没有导入error_handler模块，设置一个简单的异常处理器
            def simple_exception_handler(error_type, error_value, tb):
                error_msg = f"程序运行出错:\n\n{error_type.__name__}: {error_value}\n\n"
                error_msg += "详细错误信息:\n"
                error_msg += "".join(traceback.format_tb(tb))

                # 尝试使用tkinter显示错误
                try:
                    root = tk.Tk()
                    root.withdraw()
                    messagebox.showerror("程序错误", error_msg)
                    root.destroy()
                except:
                    print(error_msg, file=sys.stderr)

                # 调用原始的异常处理器
                sys.__excepthook__(error_type, error_value, tb)

            # 设置全局异常处理器
            sys.excepthook = simple_exception_handler

        # 创建主窗口
        root = tk.Tk()
        root.title("下载股票交易数据")

        # 设置窗口图标（如果有）
        try:
            root.iconbitmap("stock_icon.ico")
        except:
            pass  # 忽略图标设置错误

        # 设置窗口大小和位置
        window_width = 800
        window_height = 600
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 创建应用实例并保存到变量中，防止被垃圾回收
        app = StockDataDownloader(root)

        # 启动主循环
        root.mainloop()

    except Exception as e:
        error_msg = f"启动程序时出错: {e}\n\n"
        error_msg += traceback.format_exc()

        # 尝试使用tkinter显示错误
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("启动错误", error_msg)
            root.destroy()
        except:
            print(error_msg, file=sys.stderr)

        sys.exit(1)

if __name__ == "__main__":
    main()
