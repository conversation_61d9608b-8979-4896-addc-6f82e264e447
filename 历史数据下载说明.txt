# 历史数据下载

这是一个用于下载A股历史数据的工具，使用akshare库获取数据。

## 使用方法

### 方法一：直接运行可执行文件

1. 双击运行`dist`目录中的`历史数据下载.exe`文件
2. 如果出现错误提示缺少akshare模块，请使用方法二

### 方法二：使用批处理文件安装依赖并运行

1. 双击运行`安装依赖并运行.bat`文件
2. 批处理文件会自动安装必要的依赖库，然后启动程序

### 方法三：手动安装依赖并运行

1. 打开命令提示符或PowerShell
2. 执行以下命令安装依赖：
   ```
   pip install akshare pandas numpy lxml requests bs4 tqdm openpyxl
   ```
3. 执行以下命令运行程序：
   ```
   python download_stock_data_gui.py
   ```

## 功能说明

- 下载指定日期范围内的A股历史数据
- 支持按日期分别存储数据
- 支持生成合并文件
- 显示下载进度和日志信息

## 注意事项

- 下载数据需要联网
- 下载大量数据可能需要较长时间
- 数据存储在`complete_excel_results/stock_data`目录中
