"""
股票交易策略测试界面

提供简单的命令行界面来测试交易策略功能
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta

# 添加当前目录到系统路径，以便导入trading_strategies包
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from trading_strategies import (
        train_and_save_model,
        get_strategy_recommendations,
        get_all_strategy_recommendations
    )
except ImportError:
    print("无法导入trading_strategies包，请确保该包已正确安装")
    sys.exit(1)

def clear_screen():
    """清除屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """打印界面标题"""
    print("=" * 80)
    print("                        股票交易策略测试界面")
    print("=" * 80)
    print()

def print_menu():
    """打印主菜单"""
    print("\n主菜单:")
    print("1. 训练模型")
    print("2. 使用策略1生成推荐股票")
    print("3. 使用策略A生成推荐股票")
    print("4. 使用策略B生成推荐股票")
    print("5. 使用策略C生成推荐股票")
    print("6. 使用所有策略生成推荐股票")
    print("0. 退出")
    print()

def train_model():
    """训练模型功能"""
    clear_screen()
    print_header()
    print("训练模型")
    print("-" * 80)
    
    print("开始训练模型，这可能需要几分钟时间...")
    result = train_and_save_model(data_file_path='股票明细.xlsx', model_dir='trained_models')
    
    if result['success']:
        print("\n模型训练成功！")
        print(f"训练用时: {result['duration']:.2f} 秒")
        print(f"使用记录数: {result['valid_record_count']}")
        print(f"模型文件: {result['model_file']}")
        
        print("\n特征重要性:")
        for i, row in result['feature_importance'].iterrows():
            if i < 10:  # 只显示前10个特征
                print(f"{row['feature']}: {row['importance']:.4f}")
    else:
        print("\n模型训练失败！")
        print(f"错误信息: {result['error']}")
    
    input("\n按Enter键返回主菜单...")

def get_date_input():
    """获取日期输入"""
    while True:
        date_str = input("请输入预测日期 (格式: YYYY-MM-DD): ")
        try:
            date = datetime.strptime(date_str, "%Y-%m-%d")
            return date_str
        except ValueError:
            print("日期格式不正确，请重新输入")

def display_recommendations(recommended_stocks, risk_description):
    """显示推荐股票和风险说明"""
    if recommended_stocks is None:
        print("\n生成推荐失败！")
        return
    
    if len(recommended_stocks) == 0:
        print("\n没有找到符合条件的股票")
        return
    
    print("\n风险说明:")
    print(f"策略: {risk_description['strategy_description']}")
    print(f"预期胜率: {risk_description['expected_win_rate']}")
    print(f"预期收益率: {risk_description['expected_return']}")
    print(f"买入风险: {risk_description['buy_risk']}")
    print(f"卖出风险: {risk_description['sell_risk']}")
    print(f"重要提示: {risk_description['important_note']}")
    print(f"交易策略: {risk_description['trading_strategy']}")
    
    print("\n推荐股票:")
    print("-" * 80)
    print(f"{'股票代码':<10} {'股票名称':<15} {'技术强度':<8} {'连续技术强度5天数':<15} {'预测盈利概率':<12}")
    print("-" * 80)
    
    for i, row in recommended_stocks.iterrows():
        print(f"{row['股票代码']:<10} {row['股票名称']:<15} {row['技术强度']:<8} {row['连续技术强度5天数']:<15} {row['预测盈利概率']*100:.2f}%")
    
    print("-" * 80)
    print(f"共 {len(recommended_stocks)} 只推荐股票")
    print(f"结果已保存至: {risk_description['result_file']}")

def generate_recommendations(strategy_name):
    """生成推荐股票功能"""
    clear_screen()
    print_header()
    
    strategy_descriptions = {
        'strategy_1': "策略1：100%高胜率策略",
        'strategy_A': "策略A：最高胜率策略",
        'strategy_B': "策略B：最高收益率策略",
        'strategy_C': "策略C：平衡策略（胜率和交易机会的平衡）"
    }
    
    print(f"使用{strategy_descriptions[strategy_name]}生成推荐股票")
    print("-" * 80)
    
    date_str = get_date_input()
    
    print(f"\n正在使用{strategy_descriptions[strategy_name]}生成{date_str}的推荐股票...")
    
    try:
        recommended_stocks, risk_description = get_strategy_recommendations(
            strategy_name=strategy_name,
            prediction_date_str=date_str,
            use_saved_model=True
        )
        
        display_recommendations(recommended_stocks, risk_description)
    except Exception as e:
        print(f"\n生成推荐失败: {e}")
    
    input("\n按Enter键返回主菜单...")

def generate_all_recommendations():
    """生成所有策略推荐股票功能"""
    clear_screen()
    print_header()
    print("使用所有策略生成推荐股票")
    print("-" * 80)
    
    date_str = get_date_input()
    
    print(f"\n正在生成{date_str}的所有策略推荐股票...")
    
    try:
        strategy_results, risk_descriptions = get_all_strategy_recommendations(
            prediction_date_str=date_str,
            use_saved_model=True
        )
        
        if strategy_results is None:
            print("\n生成推荐失败！")
            input("\n按Enter键返回主菜单...")
            return
        
        print("\n各策略推荐股票数量:")
        for strategy_name, stocks in strategy_results.items():
            print(f"{risk_descriptions[strategy_name]['strategy_description']}: {len(stocks)} 只股票")
        
        while True:
            print("\n请选择要查看的策略:")
            print("1. 策略1：100%高胜率策略")
            print("2. 策略A：最高胜率策略")
            print("3. 策略B：最高收益率策略")
            print("4. 策略C：平衡策略")
            print("0. 返回主菜单")
            
            choice = input("\n请输入选项 (0-4): ")
            
            if choice == '0':
                break
            elif choice == '1':
                display_recommendations(strategy_results['strategy_1'], risk_descriptions['strategy_1'])
            elif choice == '2':
                display_recommendations(strategy_results['strategy_A'], risk_descriptions['strategy_A'])
            elif choice == '3':
                display_recommendations(strategy_results['strategy_B'], risk_descriptions['strategy_B'])
            elif choice == '4':
                display_recommendations(strategy_results['strategy_C'], risk_descriptions['strategy_C'])
            else:
                print("无效的选项，请重新输入")
            
            input("\n按Enter键继续...")
    
    except Exception as e:
        print(f"\n生成推荐失败: {e}")
    
    input("\n按Enter键返回主菜单...")

def main():
    """主函数"""
    while True:
        clear_screen()
        print_header()
        print_menu()
        
        choice = input("请输入选项 (0-6): ")
        
        if choice == '0':
            print("\n感谢使用！再见！")
            break
        elif choice == '1':
            train_model()
        elif choice == '2':
            generate_recommendations('strategy_1')
        elif choice == '3':
            generate_recommendations('strategy_A')
        elif choice == '4':
            generate_recommendations('strategy_B')
        elif choice == '5':
            generate_recommendations('strategy_C')
        elif choice == '6':
            generate_all_recommendations()
        else:
            print("无效的选项，请重新输入")
            input("\n按Enter键继续...")

if __name__ == "__main__":
    main()
