"""
重新生成所有技术强度文件的累积涨幅
严格按照用户要求：只添加3个字段
"""

import sys
import os
import pandas as pd
import glob
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入回测模块
import backtest_local

def regenerate_all_cumulative_returns():
    """重新生成所有技术强度文件的累积涨幅"""
    print("🔄 开始重新生成所有技术强度文件的累积涨幅...")
    print("=" * 60)
    
    # 1. 先加载历史数据
    print("📊 加载历史数据...")
    backtest_local.load_historical_data()
    
    if backtest_local.history_df.empty:
        print("❌ 历史数据为空，无法生成累积涨幅")
        return
    
    print(f"✅ 历史数据加载完成，共 {len(backtest_local.history_df)} 条记录")
    
    # 2. 获取技术强度文件目录
    tech_strength_daily_dir = os.path.join(backtest_local.base_dir, 'tech_strength', 'daily')
    
    if not os.path.exists(tech_strength_daily_dir):
        print(f"❌ 技术强度目录不存在: {tech_strength_daily_dir}")
        return
    
    # 3. 查找所有技术强度文件
    pattern = os.path.join(tech_strength_daily_dir, "tech_strength_strong_*_smart.xlsx")
    tech_files = glob.glob(pattern)
    
    if not tech_files:
        print(f"❌ 在 {tech_strength_daily_dir} 中没有找到技术强度文件")
        return
    
    print(f"📁 找到 {len(tech_files)} 个技术强度文件")
    
    # 4. 逐个处理文件
    files_updated = 0
    files_skipped = 0
    files_error = 0
    
    for i, file_path in enumerate(tech_files, 1):
        try:
            # 从文件名中提取日期
            filename = os.path.basename(file_path)
            date_part = filename.replace('tech_strength_strong_', '').replace('_smart.xlsx', '')
            date_obj = pd.to_datetime(date_part)
            
            print(f"[{i}/{len(tech_files)}] 处理 {date_part}...")
            
            # 读取文件
            df = pd.read_excel(file_path, engine='openpyxl')
            
            # 强制重新生成累积涨幅（清除之前的错误数据）
            updated_df, need_save = force_regenerate_cumulative_columns(df, date_obj)
            
            if need_save:
                # 保存更新后的文件
                updated_df.to_excel(file_path, index=False, engine='openpyxl')
                files_updated += 1
                print(f"  ✅ 已更新")
            else:
                files_skipped += 1
                print(f"  ⏭️ 跳过")
                
        except Exception as e:
            files_error += 1
            print(f"  ❌ 处理失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 累积涨幅重新生成完成:")
    print(f"  ✅ 更新: {files_updated} 个文件")
    print(f"  ⏭️ 跳过: {files_skipped} 个文件") 
    print(f"  ❌ 错误: {files_error} 个文件")

def force_regenerate_cumulative_columns(df, date_obj):
    """强制重新生成累积涨幅列（按用户要求：只有3个字段）"""
    date_str = date_obj.strftime('%Y-%m-%d')
    
    # 用户要求的3个字段
    required_columns = [
        '买入后连续2个交易日累计涨幅',
        '买入后连续3个交易日累计涨幅',
        '累积涨幅已生成'
    ]
    
    # 删除之前错误添加的多余列
    extra_columns = [
        '买入日起2日累计涨幅(含买入日)',
        '买入日起3日累计涨幅(含买入日)'
    ]
    
    for col in extra_columns:
        if col in df.columns:
            df = df.drop(columns=[col])
            print(f"    🗑️ 删除多余列: {col}")
    
    # 添加必需的列（如果不存在）
    for col in required_columns:
        if col not in df.columns:
            df[col] = 0
    
    # 检查是否可以计算累积涨幅
    if backtest_local.history_df.empty:
        print(f"    ❌ 历史数据为空")
        return df, False
    
    # 获取全局交易日历
    global_trading_days = sorted(pd.to_datetime(backtest_local.history_df['日期']).unique())
    
    # 判断是否可以计算完整的累积涨幅
    can_calculate, reason = backtest_local.can_calculate_cumulative_returns(date_obj, global_trading_days)
    
    if not can_calculate:
        print(f"    ⏭️ {reason}")
        df['累积涨幅已生成'] = -1  # 标记为跳过
        return df, True
    
    # 找到当前日期在交易日历中的位置
    current_date_index = None
    for i, trading_day in enumerate(global_trading_days):
        if trading_day.date() == date_obj.date():
            current_date_index = i
            break
    
    # 获取后续交易日
    next_1_date = global_trading_days[current_date_index + 1]
    next_2_date = global_trading_days[current_date_index + 2]
    next_3_date = global_trading_days[current_date_index + 3]
    
    # 创建历史数据映射（使用原始列名）
    history_key_to_pctChg = {}
    for _, row in backtest_local.history_df.iterrows():
        stock_code = row.get('证券代码', '')
        date_str_hist = row.get('日期', '')
        if isinstance(date_str_hist, pd.Timestamp):
            date_str_hist = date_str_hist.strftime('%Y-%m-%d')
        elif not isinstance(date_str_hist, str):
            date_str_hist = str(date_str_hist)
        
        key = f"{stock_code}_{date_str_hist}"
        pct_change = row.get('涨跌幅', 0)
        history_key_to_pctChg[key] = pct_change
    
    # 为每只股票计算累积涨幅
    updated_count = 0
    for idx, stock_row in df.iterrows():
        stock_code = stock_row['股票代码']
        
        # 获取买入日涨跌幅（当前日期）
        buy_key = f"{stock_code}_{date_str}"
        buy_pct = history_key_to_pctChg.get(buy_key, 0)
        
        # 获取后续交易日涨跌幅
        next_1_key = f"{stock_code}_{next_1_date.strftime('%Y-%m-%d')}"
        next_2_key = f"{stock_code}_{next_2_date.strftime('%Y-%m-%d')}"
        next_3_key = f"{stock_code}_{next_3_date.strftime('%Y-%m-%d')}"
        
        next_1_pct = history_key_to_pctChg.get(next_1_key, 0)
        next_2_pct = history_key_to_pctChg.get(next_2_key, 0)
        next_3_pct = history_key_to_pctChg.get(next_3_key, 0)
        
        # 计算累积涨幅（含买入当日，使用复利计算）
        # 买入后连续2个交易日累计涨幅 = 买入日 + 第1天 + 第2天
        cumulative_2days = (1 + buy_pct/100) * (1 + next_1_pct/100) * (1 + next_2_pct/100) - 1
        df.at[idx, '买入后连续2个交易日累计涨幅'] = round(cumulative_2days * 100, 2)
        
        # 买入后连续3个交易日累计涨幅 = 买入日 + 第1天 + 第2天 + 第3天  
        cumulative_3days = (1 + buy_pct/100) * (1 + next_1_pct/100) * (1 + next_2_pct/100) * (1 + next_3_pct/100) - 1
        df.at[idx, '买入后连续3个交易日累计涨幅'] = round(cumulative_3days * 100, 2)
        
        updated_count += 1
    
    # 设置标记为已生成
    df['累积涨幅已生成'] = 1
    
    print(f"    ✅ 已计算 {updated_count} 只股票的累积涨幅")
    return df, True

if __name__ == "__main__":
    regenerate_all_cumulative_returns()
    input("\n按回车键退出...")
