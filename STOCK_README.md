# 股票分析系统

## 简介

股票分析系统是一个用于处理股票数据、分析交易策略和回测策略表现的工具。该系统提供了图形用户界面，使用户能够方便地进行数据处理、策略分析和结果查看。

## 功能特点

1. **数据处理**
   - 从日期目录中读取股票数据
   - 标准化数据列名
   - 计算技术指标和特征
   - 保存处理后的数据

2. **策略分析**
   - 支持多种特征组合
   - 穷举所有可能的筛选条件组合
   - 基于真实历史数据回测策略
   - 生成详细的策略分析报告

3. **结果查看**
   - 查看策略汇总信息
   - 查看策略条件详情
   - 查看策略回测结果
   - 可视化策略表现

## 系统要求

- Windows 操作系统
- Python 3.7 或更高版本（如果从源代码运行）

## 安装方法

### 方法一：直接运行可执行文件

1. 下载发布的可执行文件 `股票分析系统.exe`
2. 双击运行即可

### 方法二：从源代码运行

1. 克隆或下载本仓库
2. 安装依赖库：
   ```
   pip install -r stock_requirements.txt
   ```
3. 运行主程序：
   ```
   python stock_analysis_app.py
   ```

## 使用说明

### 数据处理

1. 选择包含日期子目录的数据目录
2. 选择输出目录
3. 设置日期范围
4. 点击"开始处理数据"按钮

### 策略分析

1. 选择处理后的数据文件
2. 选择要分析的特征
3. 设置特征组合范围
4. 设置最大策略数
5. 设置回测日期范围
6. 点击"开始分析策略"按钮

### 结果查看

1. 选择策略汇总Excel文件
2. 点击"查看结果"按钮
3. 在表格中查看策略信息
4. 在图表中查看策略表现

## 打包成可执行文件

如果需要将源代码打包成可执行文件，可以使用以下命令：

```
python stock_setup.py
```

打包后的可执行文件将位于 `dist` 目录中。

## 文件说明

- `stock_analysis_app.py`：主程序，包含图形用户界面
- `stock_data_processor.py`：数据处理模块
- `strategy_analyzer.py`：策略分析模块
- `real_stock_backtest.py`：策略回测模块
- `stock_setup.py`：打包脚本
- `stock_requirements.txt`：依赖库列表

## 注意事项

- 数据目录应包含以日期命名的子目录（格式：YYYYMMDD），每个子目录中包含Excel文件
- 策略回测基于真实历史数据，结果仅供参考，不构成投资建议
- 处理大量数据和分析大量策略可能需要较长时间，请耐心等待

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
