"""
策略回测程序 v19 版本打包脚本
主要修复：
1. 修复了策略7913等策略的汇总表读取问题
2. 优先读取最新的汇总表文件（所有策略汇总_已回测.xlsx）
3. 修复了Excel工作表可见性问题和KeyError错误
4. 完善了返回值结构，确保程序稳定性
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def build_exe():
    """打包策略回测程序为exe文件"""

    print("=" * 60)
    print("🚀 策略回测程序 v19 版本打包")
    print("=" * 60)

    # 版本信息
    version = "v19"
    build_date = datetime.now().strftime("%Y%m%d_%H%M%S")
    exe_name = f"策略回测收益程序_{version}"

    print(f"📦 版本: {version}")
    print(f"📅 构建时间: {build_date}")
    print(f"📁 程序名称: {exe_name}")

    # 检查必要文件
    required_files = [
        'backtest_gui.py',
        'backtest_local.py',
        'config.py',
        'stock_data_manager.py'
    ]

    print("\n🔍 检查必要文件...")
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (缺失)")
            missing_files.append(file)

    if missing_files:
        print(f"\n❌ 缺失必要文件: {missing_files}")
        print("请确保所有必要文件都在当前目录中")
        return False

    # 检查PyInstaller
    print("\n🔍 检查PyInstaller...")
    try:
        result = subprocess.run(['pyinstaller', '--version'],
                              capture_output=True, text=True, check=True)
        print(f"  ✅ PyInstaller版本: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("  ❌ PyInstaller未安装")
        print("  正在安装PyInstaller...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'],
                         check=True)
            print("  ✅ PyInstaller安装成功")
        except subprocess.CalledProcessError:
            print("  ❌ PyInstaller安装失败")
            return False

    # 创建构建目录
    build_dir = f"build_{version}_{build_date}"
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    os.makedirs(build_dir)

    print(f"\n📁 创建构建目录: {build_dir}")

    # PyInstaller命令（简化版本，不使用--add-data）
    cmd = [
        'pyinstaller',
        '--onefile',                    # 单文件模式
        '--windowed',                   # 无控制台窗口
        '--name', exe_name,             # 程序名称
        '--distpath', build_dir,        # 输出目录
        '--workpath', f'{build_dir}/work',  # 工作目录
        '--specpath', f'{build_dir}/spec',  # spec文件目录
        '--hidden-import', 'pandas',
        '--hidden-import', 'numpy',
        '--hidden-import', 'openpyxl',
        '--hidden-import', 'pyarrow',
        '--hidden-import', 'fastparquet',
        '--hidden-import', 'cramjam',
        '--hidden-import', 'tkinter',
        '--hidden-import', 'tkinter.ttk',
        '--hidden-import', 'tkinter.messagebox',
        '--hidden-import', 'tkinter.filedialog',
        '--hidden-import', 'datetime',
        '--hidden-import', 'os',
        '--hidden-import', 'sys',
        '--hidden-import', 'glob',
        '--hidden-import', 'warnings',
        '--hidden-import', 'time',
        '--hidden-import', 'threading',
        '--hidden-import', 'config',
        '--hidden-import', 'stock_data_manager',
        '--hidden-import', 'backtest_local',
        'backtest_gui.py'               # 主程序文件
    ]

    print("\n🔨 开始打包...")
    print("命令:", ' '.join(cmd))

    try:
        # 执行打包
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")

        # 检查生成的exe文件
        exe_path = os.path.join(build_dir, f"{exe_name}.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📁 生成文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")

            # 创建版本信息文件
            version_info = f"""
策略回测收益程序 {version}
构建时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
文件大小: {file_size:.1f} MB

v19版本更新内容:
✅ 修复策略7913等策略的汇总表读取问题
✅ 优先读取最新的汇总表文件（所有策略汇总_已回测.xlsx）
✅ 修复Excel工作表可见性问题和KeyError错误
✅ 完善返回值结构，确保程序稳定性
✅ 支持11,868个策略的完整汇总表
✅ 程序稳定性和性能显著提升
✅ 包含pyarrow和fastparquet支持，启用高效内部模式

使用说明:
1. 双击运行 {exe_name}.exe
2. 选择数据目录（包含汇总表和技术强度数据）
3. 设置策略范围和批处理大小
4. 点击开始回测

注意事项:
- 确保数据目录包含完整的汇总表和技术强度数据
- 建议批处理大小设置为10-50之间
- 程序会自动创建输出目录和结果文件
"""

            version_file = os.path.join(build_dir, f"{exe_name}_版本说明.txt")
            with open(version_file, 'w', encoding='utf-8') as f:
                f.write(version_info)

            print(f"📝 版本说明: {version_file}")

            # 创建使用说明
            usage_info = f"""
{exe_name} 使用说明

1. 运行程序
   - 双击 {exe_name}.exe 启动程序

2. 配置数据目录
   - 点击"浏览"按钮选择数据目录
   - 数据目录应包含：
     * 所有策略汇总_已回测.xlsx（或所有策略汇总.xlsx）
     * tech_strength/daily/ 目录（包含技术强度数据）
     * stock_data/daily/ 目录（包含历史股票数据）

3. 设置回测参数
   - 起始策略ID：要回测的第一个策略编号
   - 结束策略ID：要回测的最后一个策略编号
   - 批处理大小：建议设置为10-50

4. 开始回测
   - 点击"开始回测"按钮
   - 程序会显示执行进度和日志
   - 结果文件保存在 new_strategy_details 目录中

5. 查看结果
   - 每个策略生成一个Excel文件（strategy_XXXX.xlsx）
   - 包含策略汇总参数、选股明细、每日收益等信息

技术支持:
- 版本: {version}
- 构建时间: {build_date}
"""

            usage_file = os.path.join(build_dir, f"{exe_name}_使用说明.txt")
            with open(usage_file, 'w', encoding='utf-8') as f:
                f.write(usage_info)

            print(f"📖 使用说明: {usage_file}")

            print(f"\n🎉 打包完成！")
            print(f"📁 输出目录: {build_dir}")
            print(f"🚀 可执行文件: {exe_name}.exe")

            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False

    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

if __name__ == "__main__":
    success = build_exe()
    if success:
        print("\n✅ 打包成功完成！")
    else:
        print("\n❌ 打包失败！")

    input("\n按回车键退出...")
