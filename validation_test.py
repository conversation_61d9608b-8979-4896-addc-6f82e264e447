import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta

def validate_strategy():
    """使用5月8日的数据预测5月9日的股价变动，并与实际结果进行对比"""
    print("开始验证连续两日技术强度为100的股票预测效果...")
    
    # 创建结果目录
    if not os.path.exists('validation_results'):
        os.makedirs('validation_results')
    
    # 加载股票数据
    print("\n加载股票数据...")
    try:
        stock_data = pd.read_excel('股票明细.xlsx')
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
        
        # 转换日期格式
        if isinstance(stock_data['日期'].iloc[0], str):
            stock_data['日期'] = pd.to_datetime(stock_data['日期'])
        
        # 获取所有日期并排序
        all_dates = sorted(stock_data['日期'].unique())
        print(f"数据集中的日期范围: {all_dates[0]} 到 {all_dates[-1]}")
        
        # 找到5月8日和5月9日的数据
        target_date = pd.to_datetime('2025-05-09')
        prediction_date = pd.to_datetime('2025-05-08')
        
        # 如果没有找到这些日期，尝试找到最后两个交易日
        if target_date not in all_dates or prediction_date not in all_dates:
            print(f"未找到5月8日或5月9日的数据，使用最后两个交易日")
            target_date = all_dates[-1]
            prediction_date = all_dates[-2]
        
        print(f"预测日期: {prediction_date}")
        print(f"验证日期: {target_date}")
        
        # 获取预测日期的数据
        prediction_data = stock_data[stock_data['日期'] == prediction_date]
        
        # 获取前一日的数据
        previous_date = all_dates[all_dates.index(prediction_date) - 1]
        previous_data = stock_data[stock_data['日期'] == previous_date]
        
        print(f"前一日日期: {previous_date}")
        
        # 合并数据，找出连续两日技术强度为100的股票
        merged_data = pd.merge(
            prediction_data[['股票代码', '股票名称', '当前价格', '技术强度', '日期']],
            previous_data[['股票代码', '技术强度']],
            on='股票代码',
            suffixes=('', '_prev')
        )
        
        # 筛选连续两日技术强度为100的股票
        consecutive_stocks = merged_data[
            (merged_data['技术强度'] == 100) & 
            (merged_data['技术强度_prev'] == 100)
        ]
        
        print(f"找到 {len(consecutive_stocks)} 只连续两日技术强度为100的股票")
        
        if len(consecutive_stocks) == 0:
            print("没有找到连续两日技术强度为100的股票，尝试使用单日技术强度为100的股票...")
            single_day_stocks = prediction_data[prediction_data['技术强度'] == 100]
            print(f"找到 {len(single_day_stocks)} 只单日技术强度为100的股票")
            
            if len(single_day_stocks) > 0:
                selected_stocks = single_day_stocks
                strategy_name = "单日技术强度为100"
            else:
                print("没有找到技术强度为100的股票，使用预测日期的前10只股票")
                selected_stocks = prediction_data.head(10)
                strategy_name = "随机选择"
        else:
            selected_stocks = consecutive_stocks
            strategy_name = "连续两日技术强度为100"
        
        # 获取验证日期的数据
        validation_data = stock_data[stock_data['日期'] == target_date]
        
        # 合并预测股票和验证数据
        results = pd.merge(
            selected_stocks[['股票代码', '股票名称', '当前价格', '技术强度']],
            validation_data[['股票代码', '当前价格']],
            on='股票代码',
            suffixes=('_pred', '_actual')
        )
        
        # 计算涨跌幅
        results['涨跌幅'] = (results['当前价格_actual'] / results['当前价格_pred'] - 1) * 100
        results['涨跌'] = ['上涨' if x > 0 else '下跌' for x in results['涨跌幅']]
        
        # 计算策略表现
        up_count = sum(results['涨跌幅'] > 0)
        total_count = len(results)
        success_rate = up_count / total_count if total_count > 0 else 0
        avg_return = results['涨跌幅'].mean()
        
        print(f"\n策略 '{strategy_name}' 验证结果:")
        print(f"总股票数: {total_count}")
        print(f"上涨股票数: {up_count}")
        print(f"下跌股票数: {total_count - up_count}")
        print(f"成功率: {success_rate:.4f} ({success_rate*100:.2f}%)")
        print(f"平均涨跌幅: {avg_return:.4f}%")
        
        # 按涨跌幅降序排序
        results = results.sort_values('涨跌幅', ascending=False)
        
        print("\n各股票表现:")
        print(results[['股票代码', '股票名称', '当前价格_pred', '当前价格_actual', '涨跌幅', '涨跌']])
        
        # 保存结果
        results.to_excel(f'validation_results/{strategy_name}_验证结果.xlsx', index=False)
        
        print(f"\n验证完成！结果已保存至 validation_results/{strategy_name}_验证结果.xlsx")
        
        return results, success_rate, avg_return
    
    except Exception as e:
        print(f"验证失败: {e}")
        import traceback
        traceback.print_exc()
        return None, 0, 0

if __name__ == "__main__":
    validate_strategy()
