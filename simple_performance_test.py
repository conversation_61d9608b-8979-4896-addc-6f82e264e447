"""
简化的性能测试脚本
"""

import time
import os
import sys
import pandas as pd
from datetime import datetime

def log_time(message, start_time=None):
    """记录时间"""
    current_time = time.time()
    if start_time:
        elapsed = current_time - start_time
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message} - 耗时: {elapsed:.3f}秒")
    else:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    return current_time

def main():
    """主测试函数"""
    print("🔍 简化性能测试")
    print("=" * 50)
    
    # 检查运行环境
    if getattr(sys, 'frozen', False):
        print("✅ 运行环境: PyInstaller打包程序")
    else:
        print("✅ 运行环境: Python开发环境")
    
    overall_start = time.time()
    
    # 测试1: 模块导入
    print("\n📦 测试模块导入...")
    start = time.time()
    import numpy as np
    log_time("导入numpy", start)
    
    start = time.time()
    import pandas as pd
    log_time("导入pandas", start)
    
    start = time.time()
    import openpyxl
    log_time("导入openpyxl", start)
    
    # 测试2: 创建DataFrame
    print("\n📊 测试DataFrame操作...")
    start = time.time()
    df = pd.DataFrame({
        '股票代码': [f'00000{i}' for i in range(1000)],
        '技术强度': [50 + i % 50 for i in range(1000)],
        '日期': ['2025-01-01'] * 1000
    })
    log_time("创建DataFrame (1000行)", start)
    
    # 测试3: 数据筛选
    start = time.time()
    filtered = df[df['技术强度'] > 70]
    log_time(f"数据筛选 (筛选出{len(filtered)}行)", start)
    
    # 测试4: Excel操作
    print("\n💾 测试Excel操作...")
    start = time.time()
    test_file = 'temp_test.xlsx'
    df.head(100).to_excel(test_file, index=False)
    log_time("保存Excel文件", start)
    
    start = time.time()
    df_read = pd.read_excel(test_file)
    log_time(f"读取Excel文件 ({len(df_read)}行)", start)
    
    # 清理
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # 测试5: 模拟策略筛选
    print("\n🎯 模拟策略筛选...")
    start = time.time()
    
    # 模拟多个条件筛选
    result1 = df[df['技术强度'] == 85]
    result2 = df[(df['技术强度'] > 70) & (df['技术强度'] < 90)]
    result3 = df[df['股票代码'].str.contains('0001')]
    
    total_results = len(result1) + len(result2) + len(result3)
    log_time(f"策略筛选 (共{total_results}个结果)", start)
    
    # 总结
    total_elapsed = time.time() - overall_start
    print("\n" + "=" * 50)
    print("📋 测试总结")
    print("=" * 50)
    print(f"🕐 总测试时间: {total_elapsed:.3f}秒")
    
    if total_elapsed > 10:
        print("⚠️  警告: 测试时间较长，可能存在性能问题")
    else:
        print("✅ 测试时间正常")
    
    return total_elapsed

if __name__ == "__main__":
    try:
        elapsed = main()
        print(f"\n🎉 测试完成，总耗时: {elapsed:.3f}秒")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
    
    input("\n按回车键退出...")
