import pandas as pd
import os
import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置数据目录
base_dir = r'E:\机器学习\complete_excel_results'
details_file = os.path.join(base_dir, '股票明细_完整.xlsx')

print(f"读取股票明细文件: {details_file}")
stock_df = pd.read_excel(details_file)

# 将日期列转换为日期类型
stock_df['日期'] = pd.to_datetime(stock_df['日期'])

# 按股票代码和日期排序
stock_df = stock_df.sort_values(['股票代码', '日期'])

print(f"总记录数: {len(stock_df)}")
print(f"列名: {stock_df.columns.tolist()}")

# 检查技术强度列
if '技术强度' in stock_df.columns:
    print(f"技术强度值范围: {stock_df['技术强度'].min()} - {stock_df['技术强度'].max()}")
    print(f"技术强度值分布: {stock_df['技术强度'].value_counts().sort_index()}")
else:
    print("错误: 技术强度列不存在")

# 测试单一特征筛选
def test_single_feature(feature_name, operator, value):
    print(f"\n测试筛选条件: {feature_name} {operator} {value}")
    
    # 应用筛选条件
    if operator == "==":
        filtered_df = stock_df[stock_df[feature_name] == value]
    elif operator == ">=":
        filtered_df = stock_df[stock_df[feature_name] >= value]
    elif operator == "<=":
        filtered_df = stock_df[stock_df[feature_name] <= value]
    
    print(f"筛选结果记录数: {len(filtered_df)}")
    
    if len(filtered_df) > 0:
        print(f"筛选结果示例 (前5行):")
        print(filtered_df[['股票代码', '股票名称', '日期', feature_name]].head())
    else:
        print("警告: 没有筛选出任何记录")
    
    return filtered_df

# 测试技术强度等于28
test_single_feature('技术强度', '==', 28)

# 测试技术强度大于等于28
test_single_feature('技术强度', '>=', 28)

# 测试连续技术强度5天数大于等于355
if '连续技术强度5天数' in stock_df.columns:
    test_single_feature('连续技术强度5天数', '>=', 355)
else:
    print("\n错误: 连续技术强度5天数列不存在")

# 测试连续技术强度10天数大于等于579
if '连续技术强度10天数' in stock_df.columns:
    test_single_feature('连续技术强度10天数', '>=', 579)
else:
    print("\n错误: 连续技术强度10天数列不存在")

# 测试技术指标特征为111111
if '技术指标特征' in stock_df.columns:
    # 确保以字符串形式比较
    stock_df['技术指标特征'] = stock_df['技术指标特征'].astype(str)
    test_single_feature('技术指标特征', '==', '111111')
else:
    print("\n错误: 技术指标特征列不存在")

# 测试趋势组合为111111
if '趋势组合' in stock_df.columns:
    # 确保以字符串形式比较
    stock_df['趋势组合'] = stock_df['趋势组合'].astype(str)
    test_single_feature('趋势组合', '==', '111111')
else:
    print("\n错误: 趋势组合列不存在")

# 测试日内股票标记为664
if '日内股票标记' in stock_df.columns:
    # 确保以字符串形式比较
    stock_df['日内股票标记'] = stock_df['日内股票标记'].astype(str)
    test_single_feature('日内股票标记', '==', '664')
else:
    print("\n错误: 日内股票标记列不存在")

# 测试成交量是前一日几倍大于等于2.5
if '成交量是前一日几倍' in stock_df.columns:
    test_single_feature('成交量是前一日几倍', '>=', 2.5)
else:
    print("\n错误: 成交量是前一日几倍列不存在")

print("\n所有测试完成")
