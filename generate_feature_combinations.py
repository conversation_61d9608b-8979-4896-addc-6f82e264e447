import pandas as pd
import itertools
import os

# 读取股票明细_完整_带成交量比.xlsx文件，获取所有可用特征
def get_available_features():
    # 这里列出所有可能的特征
    all_features = [
        '成交量是前一日几倍',  # 必须包含的特征
        '技术强度',
        '技术强度趋势',
        '价格趋势',
        '涨跌幅趋势',
        '看涨技术指标数量',
        '连续技术强度3天数',
        '连续技术强度5天数',
        '连续技术强度10天数',
        '连续技术强度3天数趋势',
        '连续技术强度3天数价格趋势',
        '连续技术强度3天数涨跌幅趋势',
        '连续技术强度5天数趋势',
        '连续技术强度5天数价格趋势',
        '连续技术强度5天数涨跌幅趋势',
        '连续技术强度10天数趋势',
        '连续技术强度10天数价格趋势',
        '连续技术强度10天数涨跌幅趋势',
        '技术指标_均线多头排列',
        '技术指标_MACD金叉',
        '技术指标_RSI反弹',
        '技术指标_KDJ金叉',
        '技术指标_布林带突破',
        '开盘涨跌'
    ]
    return all_features

# 生成特征组合
def generate_combinations(features, required_feature, sizes):
    combinations = []

    # 移除必选特征和股票强度相关特征，后面会单独添加
    strength_features = ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']
    other_features = [f for f in features if f != required_feature and f not in strength_features]

    # 为"成交量是前一日几倍"特征定义不同的筛选条件类型
    volume_ratio_types = [
        ('成交量是前一日几倍_0.5', 0.5),  # 成交量是前一日的0.5倍
        ('成交量是前一日几倍_1.0', 1.0),  # 成交量是前一日的1.0倍
        ('成交量是前一日几倍_1.5', 1.5),  # 成交量是前一日的1.5倍
        ('成交量是前一日几倍_2.0', 2.0),  # 成交量是前一日的2.0倍
        ('成交量是前一日几倍_2.5', 2.5),  # 成交量是前一日的2.5倍
        ('成交量是前一日几倍_3.0', 3.0),  # 成交量是前一日的3.0倍
        ('成交量是前一日几倍_3.5', 3.5),  # 成交量是前一日的3.5倍
    ]

    # 为股票强度相关特征定义不同的筛选条件类型
    strength_values = [28, 42, 57, 71, 85, 100]

    # 生成股票强度特征的所有可能组合
    strength_combinations = []
    for feature in strength_features:
        for value in strength_values:
            strength_combinations.append((feature, value))

    for size in sizes:
        # 计算需要选择的其他特征数量
        # 减2是因为必须包含required_feature和至少一个股票强度特征
        max_other_size = size - 2

        if max_other_size < 0:
            continue

        # 生成其他特征的组合
        for other_size in range(min(max_other_size + 1, len(other_features) + 1)):
            for other_combo in itertools.combinations(other_features, other_size):
                # 计算需要选择的股票强度特征数量
                strength_size = size - other_size - 1  # 减1是因为必须包含required_feature

                if strength_size <= 0 or strength_size > len(strength_combinations):
                    continue

                # 生成股票强度特征的组合
                for strength_combo in itertools.combinations(strength_combinations, strength_size):
                    # 为每种"成交量是前一日几倍"的筛选条件类型生成一个组合
                    for volume_type, threshold in volume_ratio_types:
                        # 添加其他特征
                        full_combo = list(other_combo)

                        # 添加股票强度特征
                        for strength_feature, strength_value in strength_combo:
                            full_combo.append((strength_feature, strength_value))

                        # 添加成交量比特征
                        full_combo.append((required_feature, threshold))

                        combinations.append((full_combo, threshold))

    return combinations

# 生成策略代码
def generate_strategy_code(feature_combo, volume_threshold):
    # 为每个特征生成条件
    conditions = []

    # 处理所有特征
    for item in feature_combo:
        if isinstance(item, tuple):
            # 这是带有阈值的特征
            feature, value = item

            if feature == '成交量是前一日几倍':
                # 对于阈值小于等于1.5的情况，使用等于(=)操作符
                # 对于阈值大于1.5的情况，使用大于等于(>=)操作符
                if value <= 1.5:
                    conditions.append(f"df['{feature}'] == {value}")
                else:
                    conditions.append(f"df['{feature}'] >= {value}")
            elif feature == '技术强度':
                # 技术强度使用等于操作符
                conditions.append(f"df['{feature}'] == {value}")
            elif feature in ['连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                # 连续技术强度使用大于等于操作符
                conditions.append(f"df['{feature}'] >= {value}")
        elif isinstance(item, str):
            # 这是普通特征
            feature = item
            if '趋势' in feature:
                conditions.append(f"df['{feature}'] == 1")
            elif '技术指标_' in feature:
                conditions.append(f"df['{feature}'] == 1")
            elif feature == '看涨技术指标数量':
                conditions.append(f"df['{feature}'] >= 3")
            elif feature == '开盘涨跌':
                conditions.append(f"df['{feature}'] == 1")
            else:
                conditions.append(f"df['{feature}'] > 0")

    # 使用 & 连接条件
    return "df[" + " & ".join(conditions) + "]"

# 生成策略条件描述
def generate_strategy_description(feature_combo, volume_threshold):
    # 为每个特征生成条件描述
    conditions = []

    # 处理所有特征
    for item in feature_combo:
        if isinstance(item, tuple):
            # 这是带有阈值的特征
            feature, value = item

            if feature == '成交量是前一日几倍':
                # 对于阈值小于等于1.5的情况，使用等于操作符
                # 对于阈值大于1.5的情况，使用大于等于操作符
                if value <= 1.5:
                    conditions.append(f"{feature} 等于 {value}")
                else:
                    conditions.append(f"{feature} 大于等于 {value}")
            elif feature == '技术强度':
                # 技术强度使用等于操作符
                conditions.append(f"{feature} 等于 {value}")
            elif feature in ['连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                # 连续技术强度使用大于等于操作符
                conditions.append(f"{feature} 大于等于 {value}")
        elif isinstance(item, str):
            # 这是普通特征
            feature = item
            if '趋势' in feature:
                conditions.append(f"{feature} 为 1（是）")
            elif '技术指标_' in feature:
                conditions.append(f"{feature} 为 1（是）")
            elif feature == '看涨技术指标数量':
                conditions.append(f"{feature} 大于等于 3")
            elif feature == '开盘涨跌':
                conditions.append(f"{feature} 为 1（是）")
            else:
                conditions.append(f"{feature} 大于 0")

    return " AND ".join(conditions)

# 生成详细分析文件名
def generate_detail_filename(strategy_num):
    return f"strategy_{strategy_num}.xlsx"

# 保存特征组合到Excel
def save_combinations_to_excel(combinations, output_path):
    # Excel文件的最大行数限制
    MAX_EXCEL_ROWS = 1000000  # 略小于Excel的实际限制1,048,576，留一些余量

    # 如果组合数量超过Excel的最大行数限制，则拆分成多个文件
    if len(combinations) > MAX_EXCEL_ROWS:
        # 计算需要拆分的文件数量
        num_files = (len(combinations) + MAX_EXCEL_ROWS - 1) // MAX_EXCEL_ROWS

        # 拆分组合并保存到多个文件
        for i in range(num_files):
            # 计算当前文件的起始和结束索引
            start_idx = i * MAX_EXCEL_ROWS
            end_idx = min((i + 1) * MAX_EXCEL_ROWS, len(combinations))

            # 当前文件的组合
            current_combinations = combinations[start_idx:end_idx]

            # 生成当前文件的输出路径
            file_name, file_ext = os.path.splitext(output_path)
            current_output_path = f"{file_name}_part{i+1}{file_ext}"

            # 保存当前文件
            _save_combinations_to_excel_internal(current_combinations, current_output_path, start_idx + 1)

        print(f"已将 {len(combinations)} 个特征组合拆分成 {num_files} 个文件保存")
    else:
        # 如果组合数量不超过Excel的最大行数限制，则直接保存
        _save_combinations_to_excel_internal(combinations, output_path, 1)
        print(f"已生成 {len(combinations)} 个特征组合，并保存到 {output_path}")

# 内部函数，实际保存特征组合到Excel
def _save_combinations_to_excel_internal(combinations, output_path, start_index):
    # 创建DataFrame
    data = []
    for i, (combo, volume_threshold) in enumerate(combinations, start_index):
        # 提取特征名称，处理元组形式的特征
        feature_names = []
        unique_features = set()  # 用于跟踪不同的特征字段

        for item in combo:
            if isinstance(item, tuple):
                feature_name = item[0]
                feature_names.append(feature_name)
                unique_features.add(feature_name)
            else:
                feature_names.append(item)
                unique_features.add(item)

        strategy_combo = ", ".join(feature_names)
        # 特征数量应该是不同字段的数量，而不是条件的数量
        feature_count = len(unique_features)
        strategy_code = generate_strategy_code(combo, volume_threshold)
        strategy_description = generate_strategy_description(combo, volume_threshold)
        detail_filename = generate_detail_filename(i)

        data.append({
            '策略编号': i,
            '策略组合': strategy_combo,
            '特征数量': feature_count,
            '总收益率(%)': None,
            '平均收益率(%)': None,
            '平均胜率(%)': None,
            '平均每日交易笔数': None,
            '总交易笔数': None,
            '交易天数': None,
            '总天数': None,
            '交易频率(%)': None,
            '策略条件描述': strategy_description,
            '策略代码': strategy_code,
            '详细分析文件': detail_filename,
            '成交量阈值': volume_threshold
        })

    df = pd.DataFrame(data)

    # 按特征数量降序排序，使特征数量多的组合排在前面
    df = df.sort_values(by='特征数量', ascending=False)

    # 重新编号策略编号
    df['策略编号'] = range(start_index, start_index + len(df))

    # 更新详细分析文件名
    df['详细分析文件'] = df['策略编号'].apply(lambda x: f"strategy_{x}.xlsx")

    # 保存到Excel
    df.to_excel(output_path, index=False)

def main():
    # 获取可用特征
    features = get_available_features()

    # 必须包含的特征
    required_feature = '成交量是前一日几倍'

    # 要生成的特征组合大小
    sizes = [3, 4, 5, 6, 7]

    # 为"成交量是前一日几倍"特征定义不同的筛选条件类型（根据实际数据分布）
    volume_ratio_thresholds = [0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5]

    # 输出目录
    output_dir = r"E:\机器学习\complete_excel_results\特征组合_按技术强度"
    # 确保目录名不包含空格
    output_dir = output_dir.replace(" ", "_")

    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)

    # 移除必选特征和股票强度相关特征，后面会单独添加
    strength_features = ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']
    other_features = [f for f in features if f != required_feature and f not in strength_features]

    # 为技术强度特征定义不同的筛选条件类型（根据实际数据分布）
    tech_strength_values = [28, 42, 57, 71, 85, 100]

    # 为连续技术强度特征定义不同的筛选条件类型（根据实际数据分布）
    # 连续技术强度3天数的值
    continuous_strength_3d_values = [85, 126, 156, 199]

    # 连续技术强度5天数的值
    continuous_strength_5d_values = [140, 210, 285, 355]

    # 连续技术强度10天数的值
    continuous_strength_10d_values = [226, 438, 579]

    # 为每个股票强度特征创建不同值的选项
    strength_feature_options = {
        '技术强度': [('技术强度', value) for value in tech_strength_values],
        '连续技术强度3天数': [('连续技术强度3天数', value) for value in continuous_strength_3d_values],
        '连续技术强度5天数': [('连续技术强度5天数', value) for value in continuous_strength_5d_values],
        '连续技术强度10天数': [('连续技术强度10天数', value) for value in continuous_strength_10d_values]
    }

    # 按技术强度分别生成和保存特征组合
    for tech_strength in tech_strength_values:
        # 为每个技术强度创建一个子目录
        tech_strength_dir = os.path.join(output_dir, f"技术强度_{tech_strength}")
        os.makedirs(tech_strength_dir, exist_ok=True)

        print(f"\n正在处理技术强度 {tech_strength} 的特征组合...")

        # 按特征数量分别处理
        for size in sizes:
            print(f"  正在处理 {size} 个特征的组合...")

            # 特征数量是指不同字段的数量
            # 必须包含成交量比特征和至少一个股票强度特征
            # 所以其他特征的最大数量是 size - 2
            max_other_size = size - 2

            if max_other_size < 0:
                print(f"  跳过 {size} 个特征的组合，因为无法满足必须包含成交量比和至少一个股票强度特征的条件")
                continue

            # 按成交量阈值分别处理
            for threshold in volume_ratio_thresholds:
                print(f"    正在处理成交量阈值 {threshold}...")

                # 生成特征组合
                combinations = []

                # 生成其他特征的组合（除了技术强度）
                other_features_without_tech_strength = [f for f in other_features if f != '技术强度']

                for other_size in range(min(max_other_size + 1, len(other_features_without_tech_strength) + 1)):
                    for other_combo in itertools.combinations(other_features_without_tech_strength, other_size):
                        # 计算需要选择的连续技术强度特征数量
                        continuous_strength_size = size - other_size - 2  # 减2是因为必须包含required_feature和技术强度

                        if continuous_strength_size < 0:
                            # 如果不需要选择连续技术强度特征
                            # 添加其他特征
                            full_combo = list(other_combo)

                            # 添加技术强度特征
                            full_combo.append(('技术强度', tech_strength))

                            # 添加成交量比特征
                            full_combo.append((required_feature, threshold))

                            combinations.append((full_combo, threshold))
                        elif continuous_strength_size <= len(strength_features) - 1:  # -1是因为已经使用了技术强度
                            # 如果需要选择连续技术强度特征
                            continuous_strength_features = [f for f in strength_features if f != '技术强度']

                            for selected_continuous_strength_features in itertools.combinations(continuous_strength_features, continuous_strength_size):
                                # 为每个选定的连续技术强度特征选择一个值
                                # 使用笛卡尔积生成所有可能的值组合
                                strength_value_options = []
                                for feature in selected_continuous_strength_features:
                                    if feature == '连续技术强度3天数':
                                        strength_value_options.append([('连续技术强度3天数', value) for value in continuous_strength_3d_values])
                                    elif feature == '连续技术强度5天数':
                                        strength_value_options.append([('连续技术强度5天数', value) for value in continuous_strength_5d_values])
                                    elif feature == '连续技术强度10天数':
                                        strength_value_options.append([('连续技术强度10天数', value) for value in continuous_strength_10d_values])

                                if not strength_value_options:
                                    continue

                                for strength_value_combo in itertools.product(*strength_value_options):
                                    # 添加其他特征
                                    full_combo = list(other_combo)

                                    # 添加技术强度特征
                                    full_combo.append(('技术强度', tech_strength))

                                    # 添加连续技术强度特征及其值
                                    for strength_feature_value in strength_value_combo:
                                        full_combo.append(strength_feature_value)

                                    # 添加成交量比特征
                                    full_combo.append((required_feature, threshold))

                                    combinations.append((full_combo, threshold))

                if not combinations:
                    print(f"    跳过成交量阈值 {threshold}，因为没有生成任何组合")
                    continue

                # 输出文件路径
                output_filename = f"特征组合_{size}个特征_技术强度_{tech_strength}_成交量阈值_{threshold}.xlsx"
                # 确保文件名不包含空格
                output_filename = output_filename.replace(" ", "_")
                output_path = os.path.join(tech_strength_dir, output_filename)

                # 保存到Excel
                save_combinations_to_excel(combinations, output_path)

                print(f"    已生成 {len(combinations)} 个 {size} 个特征的组合，并保存到 {output_path}")

    print("\n所有特征组合已生成完毕，按技术强度和成交量阈值分别保存到不同的文件中。")

if __name__ == "__main__":
    main()
