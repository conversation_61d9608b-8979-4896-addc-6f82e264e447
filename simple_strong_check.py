import pandas as pd

print("检查强势股表格...")

df = pd.read_excel('E:/机器学习/complete_excel_results/tech_strength/daily/strong/tech_strength_strong_2025-05-15.xlsx')

print(f"数据行数: {len(df)}")

# 检查前5行
for i in range(5):
    row = df.iloc[i]
    tech_feature = row['技术指标特征']
    trend_combo = row['趋势组合']
    
    print(f"行{i+1}: 技术指标特征={tech_feature} (类型:{type(tech_feature)}), 趋势组合={trend_combo} (类型:{type(trend_combo)})")

# 检查字段类型
print(f"技术指标特征类型: {df['技术指标特征'].dtype}")
print(f"趋势组合类型: {df['趋势组合'].dtype}")

# 检查唯一值
print(f"技术指标特征前5个值: {df['技术指标特征'].unique()[:5]}")
print(f"趋势组合前5个值: {df['趋势组合'].unique()[:5]}")

print("检查完成")
