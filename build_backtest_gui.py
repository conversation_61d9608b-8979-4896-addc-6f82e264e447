#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略回测GUI程序打包脚本
使用PyInstaller将backtest_gui.py打包成独立的可执行文件
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def build_backtest_gui():
    """构建策略回测GUI可执行文件"""
    
    print("=" * 60)
    print("🚀 策略回测GUI程序打包工具")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        'backtest_gui.py',
        'backtest_local.py',
        'config.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 所有必要文件检查完成")
    
    # 检查是否安装了PyInstaller
    try:
        import PyInstaller
        print(f"✅ 已安装PyInstaller版本：{PyInstaller.__version__}")
    except ImportError:
        print("❌ 未安装PyInstaller，请先安装：pip install pyinstaller")
        return False
    
    # 创建版本信息
    version = datetime.now().strftime("%Y.%m.%d")
    app_name = f"策略回测系统_v{version}_修复版"
    
    print(f"📦 开始打包: {app_name}")
    
    # PyInstaller命令参数
    pyinstaller_args = [
        'pyinstaller',
        '--onefile',                    # 打包成单个文件
        '--windowed',                   # 不显示控制台窗口
        '--name', app_name,             # 可执行文件名称
        '--add-data', 'config.py;.',    # 包含配置文件
        '--add-data', 'backtest_local.py;.',  # 包含回测逻辑文件
        '--hidden-import', 'pandas',
        '--hidden-import', 'numpy',
        '--hidden-import', 'openpyxl',
        '--hidden-import', 'tkinter',
        '--hidden-import', 'tkinter.ttk',
        '--hidden-import', 'tkinter.messagebox',
        '--hidden-import', 'tkinter.filedialog',
        '--hidden-import', 'tkinter.scrolledtext',
        '--hidden-import', 'threading',
        '--hidden-import', 'queue',
        '--hidden-import', 'datetime',
        '--hidden-import', 'glob',
        '--hidden-import', 'time',
        '--collect-all', 'pandas',
        '--collect-all', 'openpyxl',
        'backtest_gui.py'               # 主程序文件
    ]
    
    # 检查是否有图标文件
    icon_files = ['icon.ico', 'app.ico', 'backtest.ico']
    icon_found = False
    for icon_file in icon_files:
        if os.path.exists(icon_file):
            pyinstaller_args.extend(['--icon', icon_file])
            print(f"✅ 使用图标文件: {icon_file}")
            icon_found = True
            break
    
    if not icon_found:
        print("ℹ️ 未找到图标文件，将使用默认图标")
    
    try:
        print("🔨 正在执行PyInstaller...")
        print(f"命令: {' '.join(pyinstaller_args)}")
        
        # 执行打包命令
        result = subprocess.run(pyinstaller_args, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PyInstaller执行成功")
            
            # 检查生成的文件
            exe_path = os.path.join('dist', f'{app_name}.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"🎉 可执行文件生成成功!")
                print(f"📍 文件位置: {exe_path}")
                print(f"📏 文件大小: {file_size:.1f} MB")
                
                # 创建发布文件夹
                release_dir = f"release_backtest_{version}"
                if os.path.exists(release_dir):
                    shutil.rmtree(release_dir)
                os.makedirs(release_dir)
                
                # 复制可执行文件到发布文件夹
                release_exe = os.path.join(release_dir, f'{app_name}.exe')
                shutil.copy2(exe_path, release_exe)
                
                # 创建说明文件
                readme_content = f"""# 策略回测系统 v{version} 修复版

## 📋 程序说明
这是一个股票策略回测系统GUI程序，可以对多种交易策略进行历史数据回测分析。

## 🔧 修复内容
- ✅ 修复了买入日期和卖出日期涨跌幅更新问题
- ✅ 优化了股票代码格式处理逻辑
- ✅ 改进了数据关联准确性
- ✅ 精简了日志输出，提升运行效率

## 🚀 使用方法
1. 双击 `{app_name}.exe` 启动程序
2. 在GUI界面中选择要回测的策略ID
3. 点击"开始回测"按钮执行回测
4. 查看实时日志输出和进度
5. 回测完成后查看生成的Excel结果文件

## 📁 数据要求
程序需要以下数据文件：
- **技术强度数据**：E:\\机器学习\\complete_excel_results\\tech_strength\\daily\\*.xlsx
- **历史股票数据**：E:\\机器学习\\complete_excel_results\\stock_data\\daily\\*.xlsx  
- **策略配置文件**：complete_excel_results\\所有策略汇总.xlsx

## 📊 输出结果
- **策略汇总表**：所有策略汇总_已回测.xlsx
- **详细分析**：new_strategy_details\\strategy_X.xlsx

## 🎯 功能特点
- 支持单个策略回测和批量策略回测
- 实时显示回测进度和日志
- 自动生成详细的Excel分析报告
- 包含买入日、卖出日涨跌幅等关键指标
- 计算连续交易日累计涨幅
- 提供完整的策略绩效统计

## 🔧 技术支持
如有问题，请检查：
1. 数据文件路径是否正确
2. Excel文件是否可以正常打开
3. 系统是否有足够的内存和磁盘空间（建议8GB以上内存）
4. 确保没有其他程序占用Excel文件

## 📅 版本信息
- **版本号**：v{version} 修复版
- **构建时间**：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Python版本**：{sys.version.split()[0]}
- **主要修复**：股票代码格式处理和数据关联逻辑

## 🆕 更新日志
### v{version} 修复版
- 修复买入日期和卖出日期涨跌幅个别未更新的问题
- 优化股票代码格式处理，保持原始前缀格式
- 改进数据关联逻辑，提高数据匹配准确性
- 精简日志输出，减少冗余信息
- 提升程序运行效率和稳定性
"""
                
                readme_path = os.path.join(release_dir, 'README.md')
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(readme_content)
                
                print(f"📦 发布包创建完成: {release_dir}/")
                print(f"📖 说明文件: {readme_path}")
                
                return True
            else:
                print("❌ 可执行文件未生成")
                return False
        else:
            print("❌ PyInstaller执行失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程中出现错误: {e}")
        return False

def clean_build_files():
    """清理构建文件"""
    print("\n🧹 清理构建文件...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"  删除目录: {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file in glob.glob(pattern):
            os.remove(file)
            print(f"  删除文件: {file}")
    
    print("✅ 清理完成")

if __name__ == "__main__":
    try:
        success = build_backtest_gui()
        
        if success:
            print("\n" + "=" * 60)
            print("🎉 策略回测GUI程序打包完成！")
            print("=" * 60)
            
            # 询问是否清理构建文件
            response = input("\n是否清理构建文件？(y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                clean_build_files()
        else:
            print("\n" + "=" * 60)
            print("❌ 打包失败！")
            print("=" * 60)
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户取消打包")
    except Exception as e:
        print(f"\n❌ 打包过程中出现未知错误: {e}")
        import traceback
        traceback.print_exc()
