"""
股票交易策略UI示例

展示如何在UI中使用交易策略
"""

from trading_strategies import (
    train_and_save_model,
    get_strategy_recommendations,
    get_all_strategy_recommendations
)

def train_model_button_click():
    """
    模拟点击"训练模型"按钮的操作
    """
    print("点击了'训练模型'按钮")
    
    # 训练模型并保存
    result = train_and_save_model(data_file_path='股票明细.xlsx', model_dir='trained_models')
    
    if result['success']:
        print("\n模型训练成功！")
        print(f"训练用时: {result['duration']:.2f} 秒")
        print(f"使用记录数: {result['valid_record_count']}")
        print(f"模型文件: {result['model_file']}")
        
        # 在UI中显示特征重要性
        print("\n特征重要性:")
        for i, row in result['feature_importance'].iterrows():
            if i < 10:  # 只显示前10个特征
                print(f"{row['feature']}: {row['importance']:.4f}")
    else:
        print("\n模型训练失败！")
        print(f"错误信息: {result['error']}")

def generate_recommendations_button_click(prediction_date, strategy_name):
    """
    模拟点击"生成推荐"按钮的操作
    
    参数:
    prediction_date: 预测日期，格式为'YYYY-MM-DD'
    strategy_name: 策略名称，可选值为 'strategy_1', 'strategy_A', 'strategy_B', 'strategy_C'
    """
    print(f"点击了'生成推荐'按钮，日期: {prediction_date}, 策略: {strategy_name}")
    
    # 使用保存的模型生成推荐
    recommended_stocks, risk_description = get_strategy_recommendations(
        strategy_name=strategy_name,
        prediction_date_str=prediction_date,
        use_saved_model=True
    )
    
    if recommended_stocks is not None and len(recommended_stocks) > 0:
        # 在UI中显示推荐股票
        print("\n推荐股票:")
        for i, row in recommended_stocks.iterrows():
            if i < 10:  # 只显示前10只股票
                print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
        
        # 在UI中显示风险说明
        print("\n风险说明:")
        print(f"策略: {risk_description['strategy_description']}")
        print(f"预期胜率: {risk_description['expected_win_rate']}")
        print(f"预期收益率: {risk_description['expected_return']}")
        print(f"买入风险: {risk_description['buy_risk']}")
        print(f"卖出风险: {risk_description['sell_risk']}")
        print(f"重要提示: {risk_description['important_note']}")
        print(f"交易策略: {risk_description['trading_strategy']}")
    else:
        print("\n没有找到符合条件的股票")

def generate_all_recommendations_button_click(prediction_date):
    """
    模拟点击"生成所有策略推荐"按钮的操作
    
    参数:
    prediction_date: 预测日期，格式为'YYYY-MM-DD'
    """
    print(f"点击了'生成所有策略推荐'按钮，日期: {prediction_date}")
    
    # 使用保存的模型生成所有策略的推荐
    strategy_results, risk_descriptions = get_all_strategy_recommendations(
        prediction_date_str=prediction_date,
        use_saved_model=True
    )
    
    if strategy_results is not None:
        # 在UI中显示各策略的推荐股票数量
        print("\n各策略推荐股票数量:")
        for strategy_name, stocks in strategy_results.items():
            print(f"{risk_descriptions[strategy_name]['strategy_description']}: {len(stocks)} 只股票")
        
        # 在UI中显示各策略的风险说明
        print("\n各策略风险说明:")
        for strategy_name, risk_desc in risk_descriptions.items():
            print(f"\n{risk_desc['strategy_description']}:")
            print(f"预期胜率: {risk_desc['expected_win_rate']}")
            print(f"预期收益率: {risk_desc['expected_return']}")
            print(f"买入风险: {risk_desc['buy_risk']}")
            print(f"卖出风险: {risk_desc['sell_risk']}")
    else:
        print("\n生成推荐失败")

if __name__ == "__main__":
    # 示例1：点击"训练模型"按钮
    # train_model_button_click()
    
    # 示例2：点击"生成推荐"按钮
    generate_recommendations_button_click('2025-05-10', 'strategy_1')
    
    # 示例3：点击"生成所有策略推荐"按钮
    # generate_all_recommendations_button_click('2025-05-10')
