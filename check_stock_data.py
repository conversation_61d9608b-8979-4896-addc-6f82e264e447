import pandas as pd
import numpy as np

# 读取股票明细_完整.xlsx文件
file_path = r"E:\机器学习\complete_excel_results\股票明细_完整.xlsx"
df = pd.read_excel(file_path)

# 显示基本信息
print("数据基本信息:")
print(f"行数: {df.shape[0]}, 列数: {df.shape[1]}")
print("\n列名:")
print(df.columns.tolist())

# 显示前几行数据
print("\n前5行数据:")
print(df.head())

# 显示数据类型
print("\n数据类型:")
print(df.dtypes)

# 检查技术强度相关字段的统计信息
strength_columns = [col for col in df.columns if '技术强度' in col]
print("\n技术强度相关字段的统计信息:")
for col in strength_columns:
    print(f"\n{col}的统计信息:")
    print(df[col].describe())
    
    # 显示不同值的分布
    print(f"\n{col}的值分布:")
    value_counts = df[col].value_counts().sort_index()
    print(value_counts.head(10))  # 只显示前10个值

# 检查成交量相关字段的统计信息
volume_columns = [col for col in df.columns if '成交量' in col]
if volume_columns:
    print("\n成交量相关字段的统计信息:")
    for col in volume_columns:
        print(f"\n{col}的统计信息:")
        print(df[col].describe())
        
        # 显示不同值的分布
        print(f"\n{col}的值分布:")
        value_counts = df[col].value_counts().sort_index()
        print(value_counts.head(10))  # 只显示前10个值
else:
    print("\n没有找到成交量相关字段")
