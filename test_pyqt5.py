import sys
from PyQt5.QtWidgets import QA<PERSON><PERSON>, QMainWindow, QLabel, QPushButton, QVBoxLayout, QWidget

def main():
    app = QApplication(sys.argv)
    
    window = QMainWindow()
    window.setWindowTitle("测试窗口")
    window.setGeometry(100, 100, 300, 200)
    
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    
    layout = QVBoxLayout(central_widget)
    
    label = QLabel("如果您能看到这个窗口，说明PyQt5正常工作")
    layout.addWidget(label)
    
    button = QPushButton("关闭")
    button.clicked.connect(window.close)
    layout.addWidget(button)
    
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
