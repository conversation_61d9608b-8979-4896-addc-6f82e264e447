#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票明细Excel分析程序
作者: Augment AI
版本: 1.0.0

该脚本用于从股票明细Excel文件中读取数据，对所有可能的特征组合和条件组合进行回测，
并生成与complete_excel_results目录中相同结构的Excel文件。
"""

import os
import pandas as pd
import numpy as np
import itertools
from datetime import datetime

def read_stock_excel(excel_file):
    """
    从Excel文件读取股票数据

    参数:
        excel_file (str): Excel文件路径

    返回:
        DataFrame: 股票数据
    """
    print(f"正在读取股票明细Excel文件: {excel_file}")

    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        print(f"成功读取Excel文件，共{len(df)}条记录")

        # 确保日期列是日期类型
        if '日期' in df.columns:
            df['日期'] = pd.to_datetime(df['日期'])

        # 计算特征（如果需要）
        df = calculate_features(df)

        return df
    except Exception as e:
        print(f"读取Excel文件时出错: {str(e)}")
        return None

def generate_feature_conditions(feature):
    """
    生成特征的所有可能筛选条件

    参数:
        feature (str): 特征名称

    返回:
        list: 条件列表
    """
    if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
                 '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势',
                 '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势',
                 '连续技术强度10天数趋势', '连续技术强度3天数价格趋势',
                 '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势',
                 '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势',
                 '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
        # 二元特征，只有一种条件：== 1
        return [{
            'feature': feature,
            'condition': '== 1',
            'description': f"{feature} 为 1（是）"
        }]
    elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
        # 连续值特征，有多种条件
        conditions = []

        # 使用不同的阈值
        thresholds = [60, 70, 75, 80, 85, 90, 95]  # 更多阈值

        for threshold in thresholds:
            conditions.append({
                'feature': feature,
                'condition': f">= {threshold}",
                'description': f"{feature} 大于等于 {threshold}"
            })

        return conditions
    elif feature == '看涨技术指标数量':
        # 看涨技术指标数量，有多种条件
        conditions = []

        # 使用不同的阈值
        for threshold in range(1, 6):  # 1到5
            conditions.append({
                'feature': feature,
                'condition': f'>= {threshold}',
                'description': f"{feature} 大于等于 {threshold}"
            })

        return conditions

    return []

def generate_all_condition_combinations(feature_combination):
    """
    生成特征组合的所有可能筛选条件组合

    参数:
        feature_combination (tuple): 特征组合

    返回:
        list: 条件组合列表
    """
    # 获取每个特征的所有可能筛选条件
    feature_conditions = []
    for feature in feature_combination:
        conditions = generate_feature_conditions(feature)
        if conditions:
            feature_conditions.append(conditions)

    # 生成所有可能的条件组合
    if feature_conditions:
        all_condition_combinations = list(itertools.product(*feature_conditions))
        return all_condition_combinations
    else:
        return []

def backtest_strategy(df, strategy_conditions, start_date=None, end_date=None):
    """
    回测策略

    参数:
        df (DataFrame): 股票历史数据
        strategy_conditions (list): 策略条件列表
        start_date (str): 回测开始日期，格式：YYYY-MM-DD
        end_date (str): 回测结束日期，格式：YYYY-MM-DD

    返回:
        dict: 回测结果
    """
    # 打印策略条件
    conditions_str = " AND ".join([cond['description'] for cond in strategy_conditions])
    print(f"正在回测策略，条件: {conditions_str}")

    # 转换日期格式
    if start_date:
        start_date = pd.to_datetime(start_date)
    else:
        start_date = df['日期'].min()

    if end_date:
        end_date = pd.to_datetime(end_date)
    else:
        end_date = df['日期'].max()

    # 筛选日期范围内的数据
    mask = (df['日期'] >= start_date) & (df['日期'] <= end_date)
    data = df[mask].copy()

    # 如果数据为空，返回空结果
    if len(data) == 0:
        print("选定日期范围内没有数据")
        return None

    # 获取日期范围内的交易日
    trading_dates = sorted(data['日期'].unique())

    # 初始化结果
    results = {
        'daily_performance': [],
        'trades': [],
        'summary': {}
    }

    # 初始化资金
    initial_capital = 1000000  # 初始资金100万
    current_capital = initial_capital

    # 初始化持仓
    positions = {}

    # 遍历每个交易日
    for i, date in enumerate(trading_dates[:-1]):  # 最后一天不买入
        # 当前日期的数据
        current_day_data = data[data['日期'] == date]

        # 下一个交易日
        next_date = trading_dates[i + 1]
        next_day_data = data[data['日期'] == next_date]

        # 卖出昨日持仓
        if positions:
            for stock_code, position in list(positions.items()):
                # 获取今日该股票的数据
                next_stock_data = next_day_data[next_day_data['证券代码'] == stock_code]

                if len(next_stock_data) > 0:
                    # 使用开盘价作为卖出价格
                    sell_price = next_stock_data['开盘价'].values[0]

                    # 计算卖出收益
                    sell_value = position['quantity'] * sell_price
                    profit = sell_value - position['cost']
                    profit_rate = profit / position['cost'] * 100

                    # 更新资金
                    current_capital += sell_value

                    # 记录交易
                    results['trades'].append({
                        '日期': next_date,
                        '股票代码': stock_code,
                        '操作': '卖出',
                        '价格': sell_price,
                        '数量': position['quantity'],
                        '金额': sell_value,
                        '收益': profit,
                        '收益率(%)': profit_rate
                    })

                    # 从持仓中移除
                    del positions[stock_code]
                else:
                    # 如果今日没有该股票的数据，假设以昨日价格卖出
                    sell_price = position['price']
                    sell_value = position['quantity'] * sell_price
                    profit = sell_value - position['cost']
                    profit_rate = profit / position['cost'] * 100

                    # 更新资金
                    current_capital += sell_value

                    # 记录交易
                    results['trades'].append({
                        '日期': next_date,
                        '股票代码': stock_code,
                        '操作': '卖出(无数据)',
                        '价格': sell_price,
                        '数量': position['quantity'],
                        '金额': sell_value,
                        '收益': profit,
                        '收益率(%)': profit_rate
                    })

                    # 从持仓中移除
                    del positions[stock_code]

        # 应用策略条件筛选股票
        filtered_data = current_day_data.copy()

        for condition in strategy_conditions:
            feature = condition['feature']
            cond = condition['condition']

            # 确保特征存在
            if feature not in filtered_data.columns:
                print(f"特征 {feature} 不存在于数据中")
                continue

            # 应用条件
            if '>=' in cond:
                threshold = float(cond.split('>=')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] >= threshold]
            elif '<=' in cond:
                threshold = float(cond.split('<=')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] <= threshold]
            elif '==' in cond:
                threshold = float(cond.split('==')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] == threshold]
            elif '>' in cond:
                threshold = float(cond.split('>')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] > threshold]
            elif '<' in cond:
                threshold = float(cond.split('<')[1].strip())
                filtered_data = filtered_data[filtered_data[feature] < threshold]

        # 获取符合条件的股票
        selected_stocks = filtered_data.copy()

        # 买入股票
        if len(selected_stocks) > 0:
            # 计算每只股票的买入金额
            max_stocks = 10  # 最多买入10只股票
            num_stocks = min(len(selected_stocks), max_stocks)
            per_stock_value = current_capital / num_stocks

            # 买入股票
            for _, stock in selected_stocks.head(max_stocks).iterrows():
                stock_code = stock['证券代码']
                buy_price = stock['开盘价']  # 使用开盘价作为买入价格

                # 计算买入数量（整百股）
                quantity = int(per_stock_value / buy_price / 100) * 100

                if quantity > 0:
                    # 计算买入金额
                    cost = quantity * buy_price

                    # 更新资金
                    current_capital -= cost

                    # 添加到持仓
                    positions[stock_code] = {
                        'quantity': quantity,
                        'price': buy_price,
                        'cost': cost,
                        'buy_date': date
                    }

                    # 记录交易
                    results['trades'].append({
                        '日期': date,
                        '股票代码': stock_code,
                        '操作': '买入',
                        '价格': buy_price,
                        '数量': quantity,
                        '金额': cost,
                        '收益': 0,
                        '收益率(%)': 0
                    })

        # 计算当日总资产
        total_position_value = sum([position['quantity'] * position['price'] for position in positions.values()])
        total_assets = current_capital + total_position_value

        # 计算当日收益率
        if i == 0:
            daily_return = 0
        else:
            prev_assets = results['daily_performance'][-1]['总资产']
            daily_return = (total_assets - prev_assets) / prev_assets * 100

        # 记录每日表现
        results['daily_performance'].append({
            '日期': date,
            '现金': current_capital,
            '持仓市值': total_position_value,
            '总资产': total_assets,
            '日收益率(%)': daily_return,
            '持仓数量': len(positions)
        })

    # 计算汇总统计
    if results['daily_performance']:
        # 计算总收益率
        initial_assets = initial_capital
        final_assets = results['daily_performance'][-1]['总资产']
        total_return = (final_assets - initial_assets) / initial_assets * 100

        # 计算年化收益率
        days = (trading_dates[-1] - trading_dates[0]).days
        annual_return = total_return * 365 / days if days > 0 else 0

        # 计算最大回撤
        max_drawdown = 0
        peak = initial_assets

        for day in results['daily_performance']:
            if day['总资产'] > peak:
                peak = day['总资产']
            drawdown = (peak - day['总资产']) / peak * 100
            max_drawdown = max(max_drawdown, drawdown)

        # 计算胜率
        if results['trades']:
            win_trades = [trade for trade in results['trades'] if trade['操作'].startswith('卖出') and trade['收益'] > 0]
            win_rate = len(win_trades) / len([trade for trade in results['trades'] if trade['操作'].startswith('卖出')]) * 100 if len([trade for trade in results['trades'] if trade['操作'].startswith('卖出')]) > 0 else 0
        else:
            win_rate = 0

        # 计算平均每日交易笔数
        daily_trades = {}
        for trade in results['trades']:
            date = trade['日期']
            if date not in daily_trades:
                daily_trades[date] = 0
            daily_trades[date] += 1

        avg_daily_trades = sum(daily_trades.values()) / len(trading_dates) if trading_dates else 0

        # 汇总统计
        results['summary'] = {
            '初始资金': initial_capital,
            '最终资金': final_assets,
            '总收益率(%)': total_return,
            '年化收益率(%)': annual_return,
            '最大回撤(%)': max_drawdown,
            '胜率(%)': win_rate,
            '总交易笔数': len(results['trades']),
            '平均每日交易笔数': avg_daily_trades,
            '交易天数': len(daily_trades),
            '总天数': len(trading_dates),
            '交易频率(%)': len(daily_trades) / len(trading_dates) * 100 if trading_dates else 0
        }

    print(f"回测完成，总收益率: {results['summary']['总收益率(%)']}%，胜率: {results['summary']['胜率(%)']}%")
    return results

def calculate_features(df):
    """
    计算特征

    参数:
        df (DataFrame): 原始数据

    返回:
        DataFrame: 添加特征后的数据
    """
    print("正在计算特征...")

    # 创建新的DataFrame
    new_df = df.copy()

    # 确保必要的列存在
    required_columns = ['证券代码', '股票名称', '开盘价', '收盘价', '最高价', '最低价', '成交量', '日期']
    for col in required_columns:
        if col not in new_df.columns:
            if col == '证券代码' and '代码' in new_df.columns:
                new_df['证券代码'] = new_df['代码']
            elif col == '股票名称' and '名称' in new_df.columns:
                new_df['股票名称'] = new_df['名称']
            elif col == '开盘价' and '开盘' in new_df.columns:
                new_df['开盘价'] = new_df['开盘']
            elif col == '收盘价' and '收盘' in new_df.columns:
                new_df['收盘价'] = new_df['收盘']
            elif col == '最高价' and '最高' in new_df.columns:
                new_df['最高价'] = new_df['最高']
            elif col == '最低价' and '最低' in new_df.columns:
                new_df['最低价'] = new_df['最低']
            else:
                print(f"警告: 列 {col} 不存在，将创建空列")
                new_df[col] = np.nan

    # 确保日期是日期类型
    new_df['日期'] = pd.to_datetime(new_df['日期'])

    # 按证券代码和日期排序
    new_df.sort_values(['证券代码', '日期'], inplace=True)

    # 计算涨跌幅（如果不存在）
    if '涨跌幅' not in new_df.columns:
        new_df['涨跌幅'] = new_df.groupby('证券代码')['收盘价'].pct_change() * 100

    # 检查是否已经有技术指标
    technical_indicators = [
        '技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数',
        '看涨技术指标数量', '涨跌幅趋势', '技术强度趋势', '连续技术强度5天数趋势',
        '技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
        '技术指标_KDJ金叉', '技术指标_布林带突破', '开盘涨跌'
    ]

    missing_indicators = [indicator for indicator in technical_indicators if indicator not in new_df.columns]

    if missing_indicators:
        print(f"以下技术指标不存在，将计算: {missing_indicators}")

        # 计算特征
        stock_codes = new_df['证券代码'].unique()
        total_stocks = len(stock_codes)

        for i, stock_code in enumerate(stock_codes):
            # 更新进度
            progress = (i + 1) / total_stocks * 100
            print(f"\r正在计算特征: {stock_code} ({i+1}/{total_stocks}) - {progress:.2f}%", end="")

            # 获取该股票的数据
            stock_data = new_df[new_df['证券代码'] == stock_code].copy()

            if len(stock_data) > 0:
                # 计算均线
                if 'MA5' not in stock_data.columns:
                    stock_data['MA5'] = stock_data['收盘价'].rolling(window=5).mean()
                if 'MA10' not in stock_data.columns:
                    stock_data['MA10'] = stock_data['收盘价'].rolling(window=10).mean()
                if 'MA20' not in stock_data.columns:
                    stock_data['MA20'] = stock_data['收盘价'].rolling(window=20).mean()
                if 'MA30' not in stock_data.columns:
                    stock_data['MA30'] = stock_data['收盘价'].rolling(window=30).mean()

                # 计算MACD
                if 'MACD' not in stock_data.columns:
                    exp12 = stock_data['收盘价'].ewm(span=12, adjust=False).mean()
                    exp26 = stock_data['收盘价'].ewm(span=26, adjust=False).mean()
                    stock_data['MACD'] = exp12 - exp26
                    stock_data['MACD_SIGNAL'] = stock_data['MACD'].ewm(span=9, adjust=False).mean()
                    stock_data['MACD_HIST'] = stock_data['MACD'] - stock_data['MACD_SIGNAL']

                # 计算RSI
                if 'RSI' not in stock_data.columns:
                    delta = stock_data['收盘价'].diff()
                    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                    rs = gain / loss
                    stock_data['RSI'] = 100 - (100 / (1 + rs))

                # 计算KDJ
                if 'K' not in stock_data.columns:
                    low_min = stock_data['最低价'].rolling(window=9).min()
                    high_max = stock_data['最高价'].rolling(window=9).max()
                    rsv = (stock_data['收盘价'] - low_min) / (high_max - low_min) * 100
                    stock_data['K'] = rsv.rolling(window=3).mean()
                    stock_data['D'] = stock_data['K'].rolling(window=3).mean()
                    stock_data['J'] = 3 * stock_data['K'] - 2 * stock_data['D']

                # 计算布林带
                if 'BOLL_MIDDLE' not in stock_data.columns:
                    stock_data['BOLL_MIDDLE'] = stock_data['收盘价'].rolling(window=20).mean()
                    stock_data['BOLL_STD'] = stock_data['收盘价'].rolling(window=20).std()
                    stock_data['BOLL_UPPER'] = stock_data['BOLL_MIDDLE'] + 2 * stock_data['BOLL_STD']
                    stock_data['BOLL_LOWER'] = stock_data['BOLL_MIDDLE'] - 2 * stock_data['BOLL_STD']

                # 计算技术指标
                # 均线多头排列
                if '技术指标_均线多头排列' not in stock_data.columns and '技术指标_均线多头排列' in missing_indicators:
                    stock_data['技术指标_均线多头排列'] = ((stock_data['MA5'] > stock_data['MA10']) &
                                              (stock_data['MA10'] > stock_data['MA20']) &
                                              (stock_data['MA20'] > stock_data['MA30'])).astype(int)

                # MACD金叉
                if '技术指标_MACD金叉' not in stock_data.columns and '技术指标_MACD金叉' in missing_indicators:
                    stock_data['技术指标_MACD金叉'] = ((stock_data['MACD'] > stock_data['MACD_SIGNAL']) &
                                            (stock_data['MACD'].shift(1) <= stock_data['MACD_SIGNAL'].shift(1))).astype(int)

                # RSI反弹
                if '技术指标_RSI反弹' not in stock_data.columns and '技术指标_RSI反弹' in missing_indicators:
                    stock_data['技术指标_RSI反弹'] = ((stock_data['RSI'] > 50) &
                                          (stock_data['RSI'].shift(1) <= 50)).astype(int)

                # KDJ金叉
                if '技术指标_KDJ金叉' not in stock_data.columns and '技术指标_KDJ金叉' in missing_indicators:
                    stock_data['技术指标_KDJ金叉'] = ((stock_data['K'] > stock_data['D']) &
                                          (stock_data['K'].shift(1) <= stock_data['D'].shift(1))).astype(int)

                # 布林带突破
                if '技术指标_布林带突破' not in stock_data.columns and '技术指标_布林带突破' in missing_indicators:
                    stock_data['技术指标_布林带突破'] = (stock_data['收盘价'] > stock_data['BOLL_UPPER']).astype(int)

                # 计算看涨技术指标数量
                if '看涨技术指标数量' not in stock_data.columns and '看涨技术指标数量' in missing_indicators:
                    stock_data['看涨技术指标数量'] = (stock_data['技术指标_均线多头排列'] +
                                          stock_data['技术指标_MACD金叉'] +
                                          stock_data['技术指标_RSI反弹'] +
                                          stock_data['技术指标_KDJ金叉'] +
                                          stock_data['技术指标_布林带突破'])

                # 计算技术强度（如果不存在）
                if '技术强度' not in stock_data.columns and '技术强度' in missing_indicators:
                    # 这里使用一个简单的计算方法，实际应根据具体需求调整
                    stock_data['技术强度'] = stock_data['看涨技术指标数量'] * 20

                # 计算连续技术强度天数（如果不存在）
                if '连续技术强度3天数' not in stock_data.columns and '连续技术强度3天数' in missing_indicators:
                    # 计算连续3天技术强度
                    stock_data['连续技术强度3天数'] = stock_data['技术强度'].rolling(window=3).sum()

                if '连续技术强度5天数' not in stock_data.columns and '连续技术强度5天数' in missing_indicators:
                    # 计算连续5天技术强度
                    stock_data['连续技术强度5天数'] = stock_data['技术强度'].rolling(window=5).sum()

                if '连续技术强度10天数' not in stock_data.columns and '连续技术强度10天数' in missing_indicators:
                    # 计算连续10天技术强度
                    stock_data['连续技术强度10天数'] = stock_data['技术强度'].rolling(window=10).sum()

                # 计算趋势指标
                # 价格趋势
                if '价格趋势' not in stock_data.columns and '价格趋势' in missing_indicators:
                    stock_data['价格趋势'] = (stock_data['收盘价'] > stock_data['收盘价'].shift(1)).astype(int)

                # 涨跌幅趋势
                if '涨跌幅趋势' not in stock_data.columns and '涨跌幅趋势' in missing_indicators:
                    stock_data['涨跌幅趋势'] = (stock_data['涨跌幅'] > 0).astype(int)

                # 技术强度趋势
                if '技术强度趋势' not in stock_data.columns and '技术强度趋势' in missing_indicators:
                    stock_data['技术强度趋势'] = (stock_data['技术强度'] > stock_data['技术强度'].shift(1)).astype(int)

                # 连续技术强度天数趋势
                if '连续技术强度3天数趋势' not in stock_data.columns and '连续技术强度3天数趋势' in missing_indicators:
                    stock_data['连续技术强度3天数趋势'] = (stock_data['连续技术强度3天数'] > stock_data['连续技术强度3天数'].shift(1)).astype(int)

                if '连续技术强度5天数趋势' not in stock_data.columns and '连续技术强度5天数趋势' in missing_indicators:
                    stock_data['连续技术强度5天数趋势'] = (stock_data['连续技术强度5天数'] > stock_data['连续技术强度5天数'].shift(1)).astype(int)

                if '连续技术强度10天数趋势' not in stock_data.columns and '连续技术强度10天数趋势' in missing_indicators:
                    stock_data['连续技术强度10天数趋势'] = (stock_data['连续技术强度10天数'] > stock_data['连续技术强度10天数'].shift(1)).astype(int)

                # 开盘涨跌
                if '开盘涨跌' not in stock_data.columns and '开盘涨跌' in missing_indicators:
                    stock_data['开盘涨跌'] = (stock_data['开盘价'] > stock_data['收盘价'].shift(1)).astype(int)

                # 更新数据
                new_df.loc[new_df['证券代码'] == stock_code] = stock_data

        print("\n特征计算完成")
    else:
        print("所有技术指标已存在，无需计算")

    return new_df
