#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义股票交易策略
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys

def custom_strategy(data, date):
    """
    自定义策略：技术强度=85 + 涨跌幅趋势=1 + 看涨技术指标数量>=4
    """
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['技术强度'] == 85) &
        (daily_data['涨跌幅趋势'] == 1) &
        (daily_data['看涨技术指标数量'] >= 4)
    ]
    return selected

def backtest(data, strategy_fn, start_date=None, end_date=None, initial_capital=10000):
    """回测策略"""
    # 设置日期范围
    if start_date is None:
        start_date = data['日期'].min()
    else:
        start_date = pd.to_datetime(start_date)
        
    if end_date is None:
        end_date = data['日期'].max()
    else:
        end_date = pd.to_datetime(end_date)
        
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    print(f"回测日期范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
    print(f"交易日数量: {len(trading_dates)}")
    
    # 初始化回测结果
    capital = initial_capital
    trades = []
    
    # 对每个交易日进行回测
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 应用策略
        recommended_stocks = strategy_fn(data, current_date)
        
        print(f"日期: {current_date.strftime('%Y-%m-%d')}, 推荐股票数量: {len(recommended_stocks)}")
        
        # 如果有推荐的股票，模拟买入
        if len(recommended_stocks) > 0:
            # 计算每只股票的资金分配
            capital_per_stock = capital / len(recommended_stocks)
            
            # 记录每只股票的买入和卖出情况
            for _, stock in recommended_stocks.iterrows():
                code = stock['股票代码']
                name = stock['股票名称']
                
                # 获取次日该股票数据（买入）
                next_day_data = data[(data['日期'] == next_date) & (data['股票代码'] == code)]
                
                if len(next_day_data) > 0:
                    # 获取次日涨跌幅（模拟买入后的收益）
                    next_day_change = next_day_data['涨跌幅'].values[0]
                    
                    # 计算收益
                    profit = capital_per_stock * next_day_change / 100
                    
                    # 记录交易
                    trades.append({
                        '日期': current_date.strftime('%Y-%m-%d'),
                        '次日': next_date.strftime('%Y-%m-%d'),
                        '股票代码': code,
                        '股票名称': name,
                        '技术强度': stock['技术强度'],
                        '连续技术强度5天数': stock['连续技术强度5天数'],
                        '看涨技术指标数量': stock['看涨技术指标数量'],
                        '涨跌幅趋势': stock['涨跌幅趋势'],
                        '次日涨跌幅': next_day_change,
                        '投入资金': capital_per_stock,
                        '收益': profit,
                        '是否盈利': next_day_change > 0
                    })
    
    # 计算回测结果
    if trades:
        trades_df = pd.DataFrame(trades)
        total_profit = trades_df['收益'].sum()
        win_rate = trades_df['是否盈利'].mean() * 100
        avg_return = trades_df['次日涨跌幅'].mean()
        final_capital = initial_capital + total_profit
        total_return = (final_capital / initial_capital - 1) * 100
        
        # 打印回测结果
        print("\n回测结果:")
        print(f"初始资金: {initial_capital:,.2f}元")
        print(f"最终资金: {final_capital:,.2f}元")
        print(f"总收益: {total_profit:,.2f}元")
        print(f"总收益率: {total_return:.2f}%")
        print(f"交易次数: {len(trades)}")
        print(f"胜率: {win_rate:.2f}%")
        print(f"平均涨跌幅: {avg_return:.2f}%")
        
        # 打印每笔交易
        print("\n交易明细:")
        for trade in trades:
            print(f"{trade['日期']} 买入 {trade['股票代码']} {trade['股票名称']}, 次日涨跌幅: {trade['次日涨跌幅']:.2f}%, 收益: {trade['收益']:.2f}元")
        
        return trades_df
    else:
        print("\n回测结果: 无交易记录")
        return pd.DataFrame()

def recommend_stocks(data, strategy_fn, date=None):
    """推荐股票"""
    if date is None:
        # 使用最新日期
        date = data['日期'].max()
    
    print(f"推荐日期: {date.strftime('%Y-%m-%d')}")
    
    recommended_stocks = strategy_fn(data, date)
    
    if len(recommended_stocks) > 0:
        print(f"\n推荐股票 ({len(recommended_stocks)}只):")
        for i, (_, stock) in enumerate(recommended_stocks.iterrows(), 1):
            print(f"{i}. {stock['股票代码']} {stock['股票名称']}: 技术强度={stock['技术强度']}, 连续技术强度5天数={stock['连续技术强度5天数']}, 看涨技术指标数量={stock['看涨技术指标数量']}, 涨跌幅趋势={stock['涨跌幅趋势']}")
    else:
        print("没有符合条件的股票")
    
    return recommended_stocks

if __name__ == "__main__":
    # 加载数据
    try:
        data_file = 'stock_data.xlsx'
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
    except Exception as e:
        print(f"加载数据失败: {e}")
        sys.exit(1)
    
    # 确保日期列是datetime类型
    df['日期'] = pd.to_datetime(df['日期'])
    
    # 获取最新日期
    latest_date = df['日期'].max()
    
    # 推荐最新日期的股票
    print("\n生成最新日期的推荐股票:")
    recommend_stocks(df, custom_strategy, latest_date)
    
    # 回测策略
    print("\n回测策略:")
    # 获取所有日期
    all_dates = sorted(df['日期'].unique())
    
    # 如果有足够的日期，回测倒数第三天和倒数第二天
    if len(all_dates) >= 3:
        start_date = all_dates[-3]
        end_date = all_dates[-2]
        print(f"\n回测日期: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        backtest(df, custom_strategy, start_date, end_date)
    else:
        print("数据不足，无法回测")
