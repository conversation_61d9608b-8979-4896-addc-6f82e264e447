import pandas as pd
import itertools
import os
import numpy as np

def get_available_features():
    """
    获取可用于组合的特征列表
    """
    # 根据调整后的股票数据字典定义可用特征
    features = [
        # 技术强度相关特征
        '技术强度',
        '连续技术强度3天数',
        '连续技术强度5天数',
        '连续技术强度10天数',

        # 编码特征
        '技术指标特征',
        '趋势组合',
        '日内股票标记',

        # 交易相关特征
        '成交量是前一日几倍'
    ]

    # 排除的字段：
    # - 买入日开盘涨跌幅
    # - 卖出日开盘涨跌幅
    # - 股票代码
    # - 股票名称
    # - 日期
    # - 收盘价
    # - 涨跌幅

    return features

def generate_strategy_code(feature_combo, tech_strength_value=None):
    """
    生成策略代码
    """
    conditions = []

    for item in feature_combo:
        if isinstance(item, tuple):
            feature, value = item

            # 处理不同类型的特征
            if feature == '技术强度':
                conditions.append(f"df['{feature}'] == {value}")
            elif feature in ['连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                conditions.append(f"df['{feature}'] >= {value}")
            elif feature == '成交量是前一日几倍':
                if value == 1.0:
                    conditions.append(f"df['{feature}'] <= {value}")
                elif value == 1.5:
                    conditions.append(f"df['{feature}'] == {value}")
                else:
                    conditions.append(f"df['{feature}'] >= {value}")
            # 已移除对买入日开盘涨跌幅和卖出日开盘涨跌幅的处理
            elif feature in ['技术指标特征', '趋势组合', '日内股票标记']:
                conditions.append(f"df['{feature}'] == '{value}'")
        else:
            # 如果是单独的特征名（不应该出现这种情况）
            print(f"警告: 特征 {item} 没有指定值")

    # 使用 & 连接条件，确保每个条件都用括号括起来
    if len(conditions) == 1:
        return "df[" + conditions[0] + "]"
    else:
        # 将每个条件用括号括起来，并使用 & 连接
        return "df[" + " & ".join(["(" + cond + ")" for cond in conditions]) + "]"

def generate_strategy_description(feature_combo, tech_strength_value=None):
    """
    生成策略条件描述
    """
    conditions = []

    for item in feature_combo:
        if isinstance(item, tuple):
            feature, value = item

            # 处理不同类型的特征
            if feature == '技术强度':
                conditions.append(f"{feature} 等于 {value}")
            elif feature in ['连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                conditions.append(f"{feature} 大于等于 {value}")
            elif feature == '成交量是前一日几倍':
                if value == 1.0:
                    conditions.append(f"{feature} 小于等于 {value}")
                elif value == 1.5:
                    conditions.append(f"{feature} 等于 {value}")
                else:
                    conditions.append(f"{feature} 大于等于 {value}")
            # 已移除对买入日开盘涨跌幅和卖出日开盘涨跌幅的处理
            elif feature == '技术指标特征':
                conditions.append(f"{feature} 为 {value}")
                # 可以添加对编码的解释（1=满足，2=不满足）
                indicators = []
                if value[0] == '1': indicators.append("均线多头排列")
                if value[1] == '1': indicators.append("成交量放大")
                if value[2] == '1': indicators.append("MACD金叉")
                if value[3] == '1': indicators.append("RSI反弹")
                if value[4] == '1': indicators.append("KDJ金叉")
                if value[5] == '1': indicators.append("布林带突破")
                if indicators:
                    conditions[-1] += f"（满足: {', '.join(indicators)}）"
            elif feature == '趋势组合':
                conditions.append(f"{feature} 为 {value}")
                # 可以添加对编码的解释（1=上升，2=下降或不变）
                trends = []
                if value[0] == '1': trends.append("3天技术强度上升")
                if value[1] == '1': trends.append("3天价格上升")
                if value[2] == '1': trends.append("5天技术强度上升")
                if value[3] == '1': trends.append("5天价格上升")
                if value[4] == '1': trends.append("10天技术强度上升")
                if value[5] == '1': trends.append("10天价格上升")
                if trends:
                    conditions[-1] += f"（满足: {', '.join(trends)}）"
            elif feature == '日内股票标记':
                conditions.append(f"{feature} 为 {value}")
                # 可以添加对编码的解释（1=其他情况，2-8=不同强度）
                marks = []
                if value[0] in ['8', '6']: marks.append("开盘上涨")
                elif value[0] == '1': marks.append("开盘其他情况")
                if value[1] in ['8', '6']: marks.append("日内最高点强势")
                elif value[1] == '1': marks.append("日内其他情况")
                if value[2] in ['8', '6']: marks.append("收盘强势")
                elif value[2] == '1': marks.append("收盘其他情况")
                if marks:
                    conditions[-1] += f"（{', '.join(marks)}）"

    return " AND ".join(conditions)

def generate_detail_filename(strategy_num):
    """
    生成详细分析文件名
    """
    return f"strategy_{strategy_num}.xlsx"

def save_combinations_to_excel(combinations, output_path):
    """
    保存特征组合到Excel
    """
    # Excel文件的最大行数限制
    MAX_COMBINATIONS = 500000  # 设置为50万，超过这个数量就创建新的文件夹

    # 如果组合数量超过限制，则拆分成多个文件夹
    if len(combinations) > MAX_COMBINATIONS:
        # 计算需要拆分的文件夹数量
        num_folders = (len(combinations) + MAX_COMBINATIONS - 1) // MAX_COMBINATIONS

        # 拆分组合并保存到多个文件夹
        for i in range(num_folders):
            # 计算当前文件夹的起始和结束索引
            start_idx = i * MAX_COMBINATIONS
            end_idx = min((i + 1) * MAX_COMBINATIONS, len(combinations))

            # 当前文件夹的组合
            current_combinations = combinations[start_idx:end_idx]

            # 生成当前文件夹的输出路径
            dir_name = os.path.dirname(output_path)
            file_name = os.path.basename(output_path)
            file_name_without_ext, file_ext = os.path.splitext(file_name)

            # 创建新的文件夹
            new_folder_name = f"{file_name_without_ext}_folder{i+1}"
            new_folder_path = os.path.join(dir_name, new_folder_name)
            os.makedirs(new_folder_path, exist_ok=True)

            # 新的输出文件路径
            new_output_path = os.path.join(new_folder_path, file_name)

            # 保存当前文件
            _save_combinations_to_excel_internal(current_combinations, new_output_path, start_idx + 1)

            print(f"已将 {len(current_combinations)} 个特征组合保存到文件夹 {new_folder_path}")

        print(f"已将总共 {len(combinations)} 个特征组合拆分成 {num_folders} 个文件夹保存")
    else:
        # 如果组合数量不超过限制，则直接保存
        _save_combinations_to_excel_internal(combinations, output_path, 1)
        print(f"已生成 {len(combinations)} 个特征组合，并保存到 {output_path}")

def _save_combinations_to_excel_internal(combinations, output_path, start_index):
    """
    内部函数，实际保存特征组合到Excel
    """
    # 创建DataFrame
    data = []
    for i, (combo, tech_strength_value) in enumerate(combinations, start_index):
        # 提取特征名称，处理元组形式的特征
        feature_names = []
        unique_features = set()  # 用于跟踪不同的特征字段

        for item in combo:
            if isinstance(item, tuple):
                feature_name = item[0]
                feature_names.append(feature_name)
                unique_features.add(feature_name)
            else:
                feature_names.append(item)
                unique_features.add(item)

        strategy_combo = ", ".join(feature_names)
        # 特征数量应该是不同字段的数量，而不是条件的数量
        feature_count = len(unique_features)
        strategy_code = generate_strategy_code(combo, tech_strength_value)
        strategy_description = generate_strategy_description(combo, tech_strength_value)
        detail_filename = generate_detail_filename(i)

        data.append({
            '策略编号': i,
            '策略组合': strategy_combo,
            '特征数量': feature_count,
            '总收益率(%)': None,
            '平均收益率(%)': None,
            '平均胜率(%)': None,
            '平均每日交易笔数': None,
            '总交易笔数': None,
            '交易天数': None,
            '总天数': None,
            '交易频率(%)': None,
            '策略条件描述': strategy_description,
            '策略代码': strategy_code,
            '详细分析文件': detail_filename,
            '技术强度': tech_strength_value
        })

    df = pd.DataFrame(data)

    # 按特征数量降序排序，使特征数量多的组合排在前面
    df = df.sort_values(by='特征数量', ascending=False)

    # 重新编号策略编号
    df['策略编号'] = range(start_index, start_index + len(df))

    # 更新详细分析文件名
    df['详细分析文件'] = df['策略编号'].apply(lambda x: f"strategy_{x}.xlsx")

    # 保存到Excel
    df.to_excel(output_path, index=False)

def main():
    print("开始生成特征组合...")

    # 获取可用特征
    features = get_available_features()
    print(f"可用特征: {features}")

    # 技术强度的值
    tech_strength_values = [28, 42, 57, 71, 85, 100]

    # 连续技术强度的值（基于>=操作符的正确三等分）
    # 3天范围84-300，设置84(全部), 156(中高), 228(高)
    continuous_strength_3d_values = [84, 156, 228]
    # 5天范围140-500，设置140(全部), 260(中高), 380(高)
    continuous_strength_5d_values = [140, 260, 380]
    # 10天范围280-1000，设置280(全部), 520(中高), 760(高)
    continuous_strength_10d_values = [280, 520, 760]

    # 成交量是前一日几倍的值（优化后）
    # <=1, =1.5, >=2, >=3
    volume_ratio_values = [1.0, 1.5, 2.0, 3.0]

    # 已从特征列表中移除买入日开盘涨跌幅和卖出日开盘涨跌幅

    # 生成技术指标特征的全部有意义组合（1=满足，2=不满足）
    def generate_tech_indicator_values():
        values = []
        # 单个指标满足
        for i in range(6):
            value = ['2'] * 6
            value[i] = '1'
            values.append(''.join(value))

        # 两个指标满足的常见组合
        common_pairs = [(0,1), (0,2), (0,4), (1,2), (2,4), (3,4)]  # 常见的技术指标组合
        for i, j in common_pairs:
            value = ['2'] * 6
            value[i] = '1'
            value[j] = '1'
            values.append(''.join(value))

        # 三个指标满足的强势组合
        strong_triplets = [(0,1,2), (0,2,4), (1,2,4), (0,1,4)]
        for i, j, k in strong_triplets:
            value = ['2'] * 6
            value[i] = '1'
            value[j] = '1'
            value[k] = '1'
            values.append(''.join(value))

        # 四个及以上指标满足
        values.extend(['111122', '111112', '111121', '111111'])

        return values

    tech_indicator_values = generate_tech_indicator_values()

    # 生成趋势组合的全部有意义组合（1=上升，2=下降或不变）
    def generate_trend_combo_values():
        values = []
        # 单个趋势上升
        for i in range(6):
            value = ['2'] * 6
            value[i] = '1'
            values.append(''.join(value))

        # 短期趋势组合（3天相关）
        values.extend(['112222', '121222', '112122'])

        # 中期趋势组合（5天相关）
        values.extend(['111222', '111122', '112112'])

        # 长期趋势组合（10天相关）
        values.extend(['111112', '111121', '112111'])

        # 全面上升
        values.append('111111')

        return values

    trend_combo_values = generate_trend_combo_values()

    # 生成日内股票标记的全部有意义组合（1=其他情况，2-8=不同强度）
    def generate_stock_mark_values():
        values = []
        # 强势走势
        strong_patterns = ['888', '886', '868', '866', '688', '686', '668', '666']
        values.extend(strong_patterns)

        # 中等走势
        medium_patterns = ['664', '646', '644', '466', '464', '446', '444']
        values.extend(medium_patterns)

        # 弱势走势
        weak_patterns = ['222', '224', '242', '244', '422', '424', '442']
        values.extend(weak_patterns)

        # 其他情况
        values.append('111')

        return values

    stock_mark_values = generate_stock_mark_values()

    # 要生成的特征组合大小
    sizes = [3, 4, 5, 6, 7]

    # 输出目录
    output_dir = r"E:\机器学习\complete_excel_results\新特征组合_按技术强度"
    # 确保目录名不包含空格
    output_dir = output_dir.replace(" ", "_")
    os.makedirs(output_dir, exist_ok=True)

    # 按技术强度分别生成和保存特征组合
    for tech_strength in tech_strength_values:
        # 为每个技术强度创建一个子目录
        tech_strength_dir = os.path.join(output_dir, f"技术强度_{tech_strength}")
        # 确保目录名不包含空格
        tech_strength_dir = tech_strength_dir.replace(" ", "_")
        os.makedirs(tech_strength_dir, exist_ok=True)

        print(f"\n正在处理技术强度 {tech_strength} 的特征组合...")

        # 创建特征值选项字典
        feature_options = {
            '技术强度': [('技术强度', tech_strength)],
            '连续技术强度3天数': [('连续技术强度3天数', value) for value in continuous_strength_3d_values],
            '连续技术强度5天数': [('连续技术强度5天数', value) for value in continuous_strength_5d_values],
            '连续技术强度10天数': [('连续技术强度10天数', value) for value in continuous_strength_10d_values],
            '成交量是前一日几倍': [('成交量是前一日几倍', value) for value in volume_ratio_values],
            '技术指标特征': [('技术指标特征', value) for value in tech_indicator_values],
            '趋势组合': [('趋势组合', value) for value in trend_combo_values],
            '日内股票标记': [('日内股票标记', value) for value in stock_mark_values]
        }

        # 按特征数量分别处理
        for size in sizes:
            print(f"  正在处理 {size} 个特征的组合...")

            # 特征数量是指不同字段的数量
            # 必须包含技术强度特征
            # 所以其他特征的最大数量是 size - 1
            max_other_size = size - 1

            if max_other_size <= 0:
                print(f"  跳过 {size} 个特征的组合，因为无法满足必须包含技术强度特征的条件")
                continue

            # 获取除技术强度外的所有特征
            other_features = [f for f in features if f != '技术强度']

            # 生成特征组合
            combinations = []

            # 从其他特征中选择max_other_size个特征
            for other_combo in itertools.combinations(other_features, max_other_size):
                # 创建特征组合
                feature_combo = [('技术强度', tech_strength)]

                # 为每个特征生成所有可能的值组合
                feature_value_options = []
                for feature in other_combo:
                    feature_value_options.append(feature_options[feature])

                # 使用笛卡尔积生成所有可能的值组合
                for value_combo in itertools.product(*feature_value_options):
                    full_combo = feature_combo + list(value_combo)
                    combinations.append((full_combo, tech_strength))

            if not combinations:
                print(f"  跳过 {size} 个特征的组合，因为没有生成任何组合")
                continue

            # 输出文件路径
            output_filename = f"特征组合_{size}个特征_技术强度_{tech_strength}.xlsx"
            # 确保文件名不包含空格
            output_filename = output_filename.replace(" ", "_")
            output_path = os.path.join(tech_strength_dir, output_filename)

            # 保存到Excel
            save_combinations_to_excel(combinations, output_path)

            print(f"  已生成 {len(combinations)} 个 {size} 个特征的组合，并保存到 {output_path}")

    print("\n所有特征组合已生成完毕，按技术强度分别保存到不同的文件中。")

if __name__ == "__main__":
    main()
