"""
测试2025-05-20日期的API数据与Excel数据是否一致
"""

import pandas as pd
import akshare as ak
import os

def test_api_data():
    """测试API数据与Excel数据是否一致"""
    print("=" * 50)
    print("测试2025-05-20日期的API数据与Excel数据是否一致")
    print("=" * 50)

    # 测试日期
    test_date = "2025-05-20"

    # 读取Excel文件
    excel_file = r"E:\机器学习\complete_excel_results\stock_data\daily\stock_data_20250520.xlsx"
    if os.path.exists(excel_file):
        excel_df = pd.read_excel(excel_file)
        print(f"Excel文件中的数据行数: {len(excel_df)}")
        print("Excel文件中的前5行数据:")
        print(excel_df.head())
    else:
        print(f"Excel文件不存在: {excel_file}")
        return

    # 测试正确的历史数据接口
    print("\n测试历史数据接口...")

    # 从Excel中获取前5个股票代码进行测试
    test_codes = excel_df['code'].astype(str).head(5).tolist()

    for code in test_codes:
        try:
            # 根据股票代码判断是上证还是深证
            if code.startswith('6'):
                stock_code_full = f"sh{code}"
            else:
                stock_code_full = f"sz{code}"

            print(f"\n获取股票 {code} ({stock_code_full}) 的历史数据...")

            # 尝试使用stock_zh_a_hist接口
            try:
                hist_df = ak.stock_zh_a_hist(
                    symbol=stock_code_full,
                    period="daily",
                    start_date=test_date,
                    end_date=test_date,
                    adjust="qfq"
                )

                if hist_df is not None and not hist_df.empty:
                    print(f"stock_zh_a_hist接口返回的数据行数: {len(hist_df)}")
                    print("stock_zh_a_hist接口返回的数据:")
                    print(hist_df)

                    # 从Excel中获取该股票的数据
                    excel_stock_df = excel_df[excel_df['code'].astype(str) == code]
                    if not excel_stock_df.empty:
                        print("Excel中该股票的数据:")
                        print(excel_stock_df)

                        # 比较关键字段
                        excel_open = excel_stock_df['open'].values[0] if 'open' in excel_stock_df.columns else None
                        excel_close = excel_stock_df['close'].values[0] if 'close' in excel_stock_df.columns else None

                        hist_open = hist_df['开盘'].values[0] if '开盘' in hist_df.columns else None
                        hist_close = hist_df['收盘'].values[0] if '收盘' in hist_df.columns else None

                        print(f"Excel开盘价: {excel_open}, API开盘价: {hist_open}")
                        print(f"Excel收盘价: {excel_close}, API收盘价: {hist_close}")
                else:
                    print(f"stock_zh_a_hist接口没有返回股票 {code} 的数据")
            except Exception as e:
                print(f"调用stock_zh_a_hist接口获取股票 {code} 数据时出错: {e}")

            # 尝试使用stock_zh_a_daily接口
            try:
                print(f"\n尝试使用stock_zh_a_daily接口获取股票 {code} 的数据...")
                daily_df = ak.stock_zh_a_daily(symbol=stock_code_full, start_date=test_date, end_date=test_date)

                if daily_df is not None and not daily_df.empty:
                    print(f"stock_zh_a_daily接口返回的数据行数: {len(daily_df)}")
                    print("stock_zh_a_daily接口返回的数据:")
                    print(daily_df)

                    # 从Excel中获取该股票的数据
                    excel_stock_df = excel_df[excel_df['code'].astype(str) == code]
                    if not excel_stock_df.empty:
                        print("Excel中该股票的数据:")
                        print(excel_stock_df)

                        # 比较关键字段
                        excel_open = excel_stock_df['open'].values[0] if 'open' in excel_stock_df.columns else None
                        excel_close = excel_stock_df['close'].values[0] if 'close' in excel_stock_df.columns else None

                        daily_open = daily_df['开盘'].values[0] if '开盘' in daily_df.columns else None
                        daily_close = daily_df['收盘'].values[0] if '收盘' in daily_df.columns else None

                        print(f"Excel开盘价: {excel_open}, API开盘价: {daily_open}")
                        print(f"Excel收盘价: {excel_close}, API收盘价: {daily_close}")
                else:
                    print(f"stock_zh_a_daily接口没有返回股票 {code} 的数据")
            except Exception as e:
                print(f"调用stock_zh_a_daily接口获取股票 {code} 数据时出错: {e}")
        except Exception as e:
            print(f"处理股票 {code} 时出错: {e}")

    # 测试其他历史数据接口
    print("\n测试其他历史数据接口...")

    # 尝试使用stock_zh_a_history接口
    try:
        print("\n尝试使用stock_zh_a_history接口获取历史数据...")
        # 这个接口可能需要不同的参数
        history_df = ak.stock_zh_a_history(symbol="000001", period="daily", start_date=test_date, end_date=test_date, adjust="qfq")

        if history_df is not None and not history_df.empty:
            print(f"stock_zh_a_history接口返回的数据行数: {len(history_df)}")
            print("stock_zh_a_history接口返回的数据:")
            print(history_df)
        else:
            print("stock_zh_a_history接口返回空数据")
    except Exception as e:
        print(f"调用stock_zh_a_history接口时出错: {e}")

    # 尝试使用stock_zh_index_daily接口获取指数数据
    try:
        print("\n尝试使用stock_zh_index_daily接口获取指数数据...")
        index_df = ak.stock_zh_index_daily(symbol="sh000001", start_date=test_date, end_date=test_date)

        if index_df is not None and not index_df.empty:
            print(f"stock_zh_index_daily接口返回的数据行数: {len(index_df)}")
            print("stock_zh_index_daily接口返回的数据:")
            print(index_df)
        else:
            print("stock_zh_index_daily接口返回空数据")
    except Exception as e:
        print(f"调用stock_zh_index_daily接口时出错: {e}")

    print("\n测试完成")

if __name__ == "__main__":
    test_api_data()
