#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化的特征组合挖掘系统
作者: Augment AI
版本: 1.0.0
"""

import pandas as pd
import numpy as np
import os
import itertools
import time
from datetime import datetime
from tqdm import tqdm
import random
import pickle
import sys

# 创建结果目录
results_dir = 'optimized_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
        
        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")
        
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def get_feature_combinations(features, min_features=2, max_features=4, max_combinations=None):
    """生成特征组合，可以限制最大组合数量"""
    all_combinations = []
    for r in range(min_features, max_features + 1):
        combinations = list(itertools.combinations(features, r))
        print(f"{r}特征组合数量: {len(combinations)}")
        
        # 如果指定了最大组合数量且组合太多，随机选择一部分
        if max_combinations and len(combinations) > max_combinations:
            print(f"随机选择{max_combinations}个{r}特征组合")
            combinations = random.sample(combinations, max_combinations)
        
        all_combinations.extend(combinations)
    
    return all_combinations

def fast_backtest(data, feature_combination, thresholds, operators, start_date, end_date, initial_capital=10000):
    """快速回测策略，优化性能"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    # 初始化回测结果
    total_profit = 0
    total_trades = 0
    win_count = 0
    total_return_pct = 0
    
    # 对每个交易日进行回测
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 获取当日数据
        daily_data = date_range_data[date_range_data['日期'] == current_date]
        
        # 应用策略条件
        selected = daily_data.copy()
        for feature, threshold, operator in zip(feature_combination, thresholds, operators):
            if operator == '>=':
                selected = selected[selected[feature] >= threshold]
            elif operator == '<=':
                selected = selected[selected[feature] <= threshold]
            elif operator == '==':
                selected = selected[selected[feature] == threshold]
        
        # 如果有推荐的股票，模拟买入
        if len(selected) > 0:
            # 获取推荐股票的代码
            recommended_codes = selected['股票代码'].tolist()
            
            # 获取次日这些股票的数据
            next_day_data = date_range_data[
                (date_range_data['日期'] == next_date) & 
                (date_range_data['股票代码'].isin(recommended_codes))
            ]
            
            # 如果次日有这些股票的数据
            if len(next_day_data) > 0:
                # 计算平均涨跌幅
                avg_return = next_day_data['涨跌幅'].mean()
                
                # 计算盈利股票数量
                win_stocks = len(next_day_data[next_day_data['涨跌幅'] > 0])
                
                # 更新统计数据
                total_return_pct += avg_return
                total_trades += len(next_day_data)
                win_count += win_stocks
    
    # 计算回测结果
    if total_trades > 0:
        win_rate = win_count / total_trades * 100
        avg_return = total_return_pct / (len(trading_dates) - 1)
        total_return = avg_return * (len(trading_dates) - 1)
        
        return {
            'total_return': total_return,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'trade_count': total_trades
        }
    else:
        return {
            'total_return': 0,
            'win_rate': 0,
            'avg_return': 0,
            'trade_count': 0
        }

def find_optimal_parameters(data, feature_combination, start_date, end_date, max_trials=10):
    """为特征组合找到最优参数，使用随机搜索"""
    best_return = -float('inf')
    best_params = None
    best_result = None
    
    # 随机搜索最优参数
    for _ in range(max_trials):
        thresholds = []
        operators = []
        
        for feature in feature_combination:
            # 根据特征类型选择合适的阈值和运算符
            if feature in ['技术强度', '连续技术强度5天数', '连续技术强度3天数', '连续技术强度10天数']:
                # 对于技术强度类特征，使用较高的阈值和>=运算符
                threshold = random.choice([70, 75, 80, 85, 90, 95, 100])
                operator = '>='
            elif feature == '看涨技术指标数量':
                # 对于看涨技术指标数量，使用固定值和>=运算符
                threshold = random.choice([3, 4, 5])
                operator = '>='
            elif feature in ['涨跌幅趋势', '技术强度趋势', '连续技术强度5天数趋势', 
                           '连续技术强度5天数价格趋势', '连续技术强度5天数涨跌幅趋势',
                           '技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
                           '技术指标_KDJ金叉', '技术指标_布林带突破', '开盘涨跌']:
                # 对于二元特征，只使用1作为阈值和==运算符
                threshold = 1
                operator = '=='
            else:
                # 对于其他特征，使用分位数作为阈值
                threshold = random.choice([70, 75, 80, 85, 90, 95])
                operator = random.choice(['>=', '<='])
            
            thresholds.append(threshold)
            operators.append(operator)
        
        # 生成策略名称
        strategy_parts = []
        for feature, threshold, operator in zip(feature_combination, thresholds, operators):
            strategy_parts.append(f"{feature}{operator}{threshold:.2f}")
        strategy_name = ' & '.join(strategy_parts)
        
        # 快速回测策略
        result = fast_backtest(data, feature_combination, thresholds, operators, start_date, end_date)
        result['strategy_name'] = strategy_name
        
        # 更新最优结果
        if result['total_return'] > best_return and result['trade_count'] >= 5:
            best_return = result['total_return']
            best_params = (thresholds, operators)
            best_result = result
    
    return best_params, best_result

def explore_feature_combinations(data, features, start_date, end_date, min_features=2, max_features=4, 
                                max_combinations_per_size=50, max_trials_per_combination=10, top_n=10):
    """探索特征组合"""
    print(f"正在探索特征组合 (最小特征数: {min_features}, 最大特征数: {max_features})...")
    
    # 生成特征组合
    combinations = get_feature_combinations(features, min_features, max_features, max_combinations_per_size)
    print(f"共生成 {len(combinations)} 个特征组合")
    
    # 为每个组合找到最优参数并回测
    results = []
    for i, combination in enumerate(tqdm(combinations)):
        print(f"\n正在探索组合 {i+1}/{len(combinations)}: {combination}")
        params, result = find_optimal_parameters(data, combination, start_date, end_date, max_trials_per_combination)
        if result and result['trade_count'] > 0:
            results.append(result)
            print(f"最佳参数: {result['strategy_name']}")
            print(f"收益率: {result['total_return']:.2f}%, 胜率: {result['win_rate']:.2f}%, 交易次数: {result['trade_count']}")
        
        # 每10个组合保存一次中间结果
        if (i + 1) % 10 == 0 or i == len(combinations) - 1:
            # 按总收益率排序
            results.sort(key=lambda x: x['total_return'], reverse=True)
            
            # 保存中间结果
            interim_results_df = pd.DataFrame([{
                '策略名称': result['strategy_name'],
                '总收益率': result['total_return'],
                '胜率': result['win_rate'],
                '平均涨跌幅': result['avg_return'],
                '交易次数': result['trade_count']
            } for result in results])
            
            interim_results_df.to_csv(f"{results_dir}/interim_results_{i+1}.csv", index=False)
            print(f"已保存中间结果到 {results_dir}/interim_results_{i+1}.csv")
    
    # 按总收益率排序
    results.sort(key=lambda x: x['total_return'], reverse=True)
    
    # 保存结果
    results_df = pd.DataFrame([{
        '策略名称': result['strategy_name'],
        '总收益率': result['total_return'],
        '胜率': result['win_rate'],
        '平均涨跌幅': result['avg_return'],
        '交易次数': result['trade_count']
    } for result in results])
    
    results_df.to_csv(f"{results_dir}/optimized_results.csv", index=False)
    print(f"已保存结果到 {results_dir}/optimized_results.csv")
    
    # 返回前N个最佳组合
    return results[:top_n]

def generate_strategy_function(feature_combination, thresholds, operators):
    """根据特征组合生成策略函数"""
    def strategy(data, date):
        daily_data = data[data['日期'] == date]
        selected = daily_data.copy()
        for feature, threshold, operator in zip(feature_combination, thresholds, operators):
            if operator == '>=':
                selected = selected[selected[feature] >= threshold]
            elif operator == '<=':
                selected = selected[selected[feature] <= threshold]
            elif operator == '==':
                selected = selected[selected[feature] == threshold]
        return selected
    
    return strategy

def parse_strategy_name(strategy_name):
    """解析策略名称，提取特征、阈值和运算符"""
    parts = strategy_name.split(' & ')
    feature_combination = []
    thresholds = []
    operators = []
    
    for part in parts:
        if '>=' in part:
            feature, threshold = part.split('>=')
            feature_combination.append(feature.strip())
            thresholds.append(float(threshold))
            operators.append('>=')
        elif '<=' in part:
            feature, threshold = part.split('<=')
            feature_combination.append(feature.strip())
            thresholds.append(float(threshold))
            operators.append('<=')
        elif '==' in part:
            feature, threshold = part.split('==')
            feature_combination.append(feature.strip())
            thresholds.append(float(threshold))
            operators.append('==')
    
    return feature_combination, thresholds, operators

def generate_recommendations(data, strategy_name, date):
    """根据策略名称生成股票推荐"""
    # 确保日期是datetime类型
    date = pd.to_datetime(date)
    
    # 解析策略名称
    feature_combination, thresholds, operators = parse_strategy_name(strategy_name)
    
    # 生成策略函数
    strategy_fn = generate_strategy_function(feature_combination, thresholds, operators)
    
    # 应用策略
    recommended_stocks = strategy_fn(data, date)
    
    print(f"策略: {strategy_name}")
    print(f"日期: {date.strftime('%Y-%m-%d')}, 推荐股票数量: {len(recommended_stocks)}")
    
    # 保存推荐股票到文件
    safe_strategy_name = f"strategy_{int(time.time())}"
    output_file = f"{results_dir}/{safe_strategy_name}_{date.strftime('%Y%m%d')}.csv"
    recommended_stocks.to_csv(output_file, index=False)
    
    print(f"推荐股票已保存到 {output_file}")
    
    # 打印推荐股票
    print("\n推荐股票列表:")
    for i, (_, stock) in enumerate(recommended_stocks.iterrows()):
        if i < 20:  # 只显示前20只
            print(f"{i+1}. {stock['股票代码']} {stock['股票名称']}, 技术强度: {stock['技术强度']}, 连续技术强度5天数: {stock['连续技术强度5天数']}, 看涨技术指标数量: {stock['看涨技术指标数量']}")
    
    if len(recommended_stocks) > 20:
        print(f"... 共 {len(recommended_stocks)} 只股票")
    
    return recommended_stocks

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)
    
    # 获取最新日期
    latest_date = df['日期'].max()
    print(f"最新日期: {latest_date.strftime('%Y-%m-%d')}")
    
    # 定义要探索的特征
    features = [
        '技术强度',
        '连续技术强度5天数',
        '连续技术强度3天数',
        '连续技术强度10天数',
        '看涨技术指标数量',
        '涨跌幅趋势',
        '技术强度趋势',
        '连续技术强度5天数趋势',
        '连续技术强度5天数价格趋势',
        '连续技术强度5天数涨跌幅趋势',
        '技术指标_均线多头排列',
        '技术指标_MACD金叉',
        '技术指标_RSI反弹',
        '技术指标_KDJ金叉',
        '技术指标_布林带突破',
        '开盘涨跌'
    ]
    
    # 探索特征组合
    best_combinations = explore_feature_combinations(
        df, features, '2025-04-01', '2025-04-30', 
        min_features=2, max_features=4, 
        max_combinations_per_size=20,  # 每种特征数量最多探索20个组合
        max_trials_per_combination=5,  # 每个组合最多尝试5次参数
        top_n=10
    )
    
    # 打印最佳组合
    print("\n最佳特征组合:")
    for i, result in enumerate(best_combinations):
        print(f"{i+1}. {result['strategy_name']}")
        print(f"   总收益率: {result['total_return']:.2f}%")
        print(f"   胜率: {result['win_rate']:.2f}%")
        print(f"   平均涨跌幅: {result['avg_return']:.2f}%")
        print(f"   交易次数: {result['trade_count']}")
        print("-" * 50)
    
    # 生成最新日期的股票推荐 (使用最佳组合)
    if best_combinations:
        best_strategy_name = best_combinations[0]['strategy_name']
        print("\n生成最新日期的股票推荐 (使用最佳组合):")
        generate_recommendations(df, best_strategy_name, latest_date)
