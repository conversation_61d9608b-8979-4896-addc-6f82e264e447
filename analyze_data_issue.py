#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析数据问题的脚本
"""

import pandas as pd
import numpy as np

def analyze_specific_stock():
    """分析具体股票的数据问题"""
    
    print("=== 数据问题分析 ===")
    
    # 1. 读取生成的数据
    print("1. 读取生成的技术强度数据...")
    generated_df = pd.read_excel('E:/机器学习/complete_excel_results/tech_strength/daily/tech_strength_2025-05-15.xlsx', 
                                dtype={'股票代码': str, '技术指标特征': str, '趋势组合': str})
    
    # 2. 读取原始数据
    print("2. 读取原始股票明细数据...")
    original_df = pd.read_excel('E:/机器学习/complete_excel_results/股票明细_完整.xlsx')
    original_filtered = original_df[original_df['日期'] == '2025-05-15']
    
    # 3. 选择一个具体的股票进行分析
    sample_stock_code = 'sz.000006'
    print(f"\n3. 分析股票: {sample_stock_code}")
    
    # 在生成数据中查找
    generated_stock = generated_df[generated_df['股票代码'] == sample_stock_code]
    if not generated_stock.empty:
        print("生成数据中的股票信息:")
        stock_info = generated_stock.iloc[0]
        print(f"  股票代码: {stock_info['股票代码']}")
        print(f"  技术强度: {stock_info['技术强度']}")
        print(f"  趋势: {stock_info['趋势']}")
        print(f"  技术指标特征: {stock_info['技术指标特征']} (类型: {type(stock_info['技术指标特征'])})")
        print(f"  趋势组合: {stock_info['趋势组合']} (类型: {type(stock_info['趋势组合'])})")
        print(f"  连续技术强度3天数: {stock_info['连续技术强度3天数']}")
        print(f"  连续技术强度5天数: {stock_info['连续技术强度5天数']}")
        print(f"  连续技术强度10天数: {stock_info['连续技术强度10天数']}")
    else:
        print("在生成数据中未找到该股票")
    
    # 在原始数据中查找
    original_stock = original_filtered[original_filtered['股票代码'].str.contains('000006')]
    if not original_stock.empty:
        print("\n原始数据中的股票信息:")
        stock_info = original_stock.iloc[0]
        print(f"  股票代码: {stock_info['股票代码']}")
        print(f"  技术强度: {stock_info['技术强度']}")
        print(f"  趋势: {stock_info['趋势']}")
        print(f"  技术指标特征: {stock_info['技术指标特征']} (类型: {type(stock_info['技术指标特征'])})")
        print(f"  趋势组合: {stock_info['趋势组合']} (类型: {type(stock_info['趋势组合'])})")
        print(f"  连续技术强度3天数: {stock_info['连续技术强度3天数']}")
        print(f"  连续技术强度5天数: {stock_info['连续技术强度5天数']}")
        print(f"  连续技术强度10天数: {stock_info['连续技术强度10天数']}")
    else:
        print("在原始数据中未找到该股票")
    
    # 4. 分析问题
    print("\n4. 问题分析:")
    
    # 问题1: 趋势组合
    print("问题1 - 趋势组合分析:")
    print(f"  生成数据中趋势组合的唯一值: {generated_df['趋势组合'].unique()}")
    print(f"  原始数据中趋势组合的唯一值数量: {len(original_filtered['趋势组合'].unique())}")
    print(f"  原始数据中趋势组合的前10个值: {original_filtered['趋势组合'].unique()[:10]}")
    
    # 问题2: 连续技术强度
    print("\n问题2 - 连续技术强度分析:")
    sample_stocks = generated_df.head(5)
    for i, row in sample_stocks.iterrows():
        print(f"  股票 {row['股票代码']}: 3天={row['连续技术强度3天数']}, 5天={row['连续技术强度5天数']}, 10天={row['连续技术强度10天数']}")
    
    # 问题3: 数据匹配情况
    print("\n问题3 - 数据匹配分析:")
    print(f"  生成数据总数: {len(generated_df)}")
    print(f"  原始数据总数: {len(original_filtered)}")
    print(f"  技术强度为-100的股票数: {len(generated_df[generated_df['技术强度'] == -100])}")
    print(f"  技术强度不为-100的股票数: {len(generated_df[generated_df['技术强度'] != -100])}")
    
    # 5. 查找匹配的股票在原始数据中的趋势组合
    print("\n5. 匹配股票的趋势组合分析:")
    matched_stocks = generated_df[generated_df['技术强度'] != -100].head(5)
    for i, row in matched_stocks.iterrows():
        stock_code = row['股票代码']
        # 在原始数据中查找对应的股票
        original_match = original_filtered[original_filtered['股票代码'] == stock_code]
        if not original_match.empty:
            original_trend = original_match.iloc[0]['趋势组合']
            print(f"  股票 {stock_code}: 生成={row['趋势组合']}, 原始={original_trend}")
        else:
            print(f"  股票 {stock_code}: 在原始数据中未找到匹配")

if __name__ == "__main__":
    analyze_specific_stock()
