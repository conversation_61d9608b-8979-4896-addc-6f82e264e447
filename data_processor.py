#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票交易系统 - 数据处理模块
作者: Augment AI
版本: 1.0.0
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
import glob
import warnings
warnings.filterwarnings('ignore')

class DataProcessor:
    """数据处理类，用于处理原始股票数据"""
    
    def __init__(self, data_dir=None):
        """初始化数据处理器"""
        self.data_dir = data_dir
        self.processed_data = None
        
    def process_directory(self, output_file='股票明细.xlsx'):
        """处理目录中的所有数据文件"""
        if self.data_dir is None or not os.path.exists(self.data_dir):
            print(f"错误: 目录 {self.data_dir} 不存在")
            return False
            
        print(f"开始处理目录: {self.data_dir}")
        
        # 获取所有Excel文件
        excel_files = []
        for root, dirs, files in os.walk(self.data_dir):
            for file in files:
                if file.endswith('.xlsx') or file.endswith('.xls'):
                    excel_files.append(os.path.join(root, file))
        
        if not excel_files:
            print("错误: 未找到Excel文件")
            return False
            
        print(f"找到 {len(excel_files)} 个Excel文件")
        
        # 处理所有文件
        all_data = []
        for file in excel_files:
            try:
                print(f"处理文件: {file}")
                file_data = self.process_file(file)
                if file_data is not None and not file_data.empty:
                    all_data.append(file_data)
            except Exception as e:
                print(f"处理文件 {file} 时出错: {e}")
        
        if not all_data:
            print("错误: 没有成功处理任何数据")
            return False
            
        # 合并所有数据
        self.processed_data = pd.concat(all_data, ignore_index=True)
        
        # 去除重复数据
        self.processed_data = self.processed_data.drop_duplicates(subset=['日期', '股票代码'])
        
        # 按日期和股票代码排序
        self.processed_data = self.processed_data.sort_values(['日期', '股票代码'])
        
        # 保存处理后的数据
        self.processed_data.to_excel(output_file, index=False)
        print(f"数据处理完成，共 {len(self.processed_data)} 条记录，已保存到 {output_file}")
        
        return True
    
    def process_file(self, file_path):
        """处理单个Excel文件"""
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            # 检查必要的列是否存在
            required_columns = ['日期', '股票代码', '股票名称', '技术强度', '连续技术强度5天数', 
                               '看涨技术指标数量', '价格趋势', '涨跌幅趋势', '涨跌幅']
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"警告: 文件 {file_path} 缺少必要的列: {missing_columns}")
                
                # 尝试根据文件名和内容推断日期
                file_name = os.path.basename(file_path)
                date_str = None
                
                # 从文件名中提取日期
                for fmt in ['%Y%m%d', '%Y-%m-%d', '%Y_%m_%d']:
                    try:
                        for part in file_name.split('.')[0].split('_'):
                            try:
                                date_str = datetime.strptime(part, fmt).strftime('%Y-%m-%d')
                                break
                            except:
                                continue
                        if date_str:
                            break
                    except:
                        continue
                
                if date_str and '日期' in missing_columns:
                    print(f"从文件名推断日期: {date_str}")
                    df['日期'] = date_str
                    missing_columns.remove('日期')
                
                # 如果仍然缺少必要的列，跳过该文件
                if missing_columns:
                    return None
            
            # 确保日期列是datetime类型
            if '日期' in df.columns:
                df['日期'] = pd.to_datetime(df['日期'])
            
            # 确保数值列是数值类型
            numeric_columns = ['技术强度', '连续技术强度5天数', '看涨技术指标数量', 
                              '价格趋势', '涨跌幅趋势', '涨跌幅']
            
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 处理缺失值
            if df.isnull().sum().sum() > 0:
                print(f"警告: 文件 {file_path} 包含 {df.isnull().sum().sum()} 个缺失值")
                
                # 对于关键列，删除包含缺失值的行
                critical_columns = ['日期', '股票代码', '股票名称', '技术强度', '涨跌幅']
                df = df.dropna(subset=critical_columns)
                
                # 对于非关键列，用合适的值填充缺失值
                if '连续技术强度5天数' in df.columns:
                    df['连续技术强度5天数'] = df['连续技术强度5天数'].fillna(0)
                
                if '看涨技术指标数量' in df.columns:
                    df['看涨技术指标数量'] = df['看涨技术指标数量'].fillna(0)
                
                if '价格趋势' in df.columns:
                    df['价格趋势'] = df['价格趋势'].fillna(0)
                
                if '涨跌幅趋势' in df.columns:
                    df['涨跌幅趋势'] = df['涨跌幅趋势'].fillna(0)
            
            # 标准化股票代码格式
            if '股票代码' in df.columns:
                df['股票代码'] = df['股票代码'].astype(str)
                
                # 添加市场前缀（如果没有）
                def format_stock_code(code):
                    code = str(code).strip()
                    # 去除非数字字符
                    code = ''.join(c for c in code if c.isdigit())
                    
                    # 确保代码是6位数
                    if len(code) < 6:
                        code = code.zfill(6)
                    elif len(code) > 6:
                        code = code[-6:]
                    
                    # 添加市场前缀
                    if code.startswith('6'):
                        return 'sh.' + code
                    else:
                        return 'sz.' + code
                
                df['股票代码'] = df['股票代码'].apply(format_stock_code)
            
            return df
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            return None
    
    def merge_data_files(self, file_pattern, output_file='股票明细.xlsx'):
        """合并多个数据文件"""
        files = glob.glob(file_pattern)
        if not files:
            print(f"错误: 未找到匹配的文件: {file_pattern}")
            return False
            
        print(f"找到 {len(files)} 个匹配的文件")
        
        all_data = []
        for file in files:
            try:
                print(f"处理文件: {file}")
                df = pd.read_excel(file)
                all_data.append(df)
            except Exception as e:
                print(f"处理文件 {file} 时出错: {e}")
        
        if not all_data:
            print("错误: 没有成功处理任何数据")
            return False
            
        # 合并所有数据
        self.processed_data = pd.concat(all_data, ignore_index=True)
        
        # 去除重复数据
        self.processed_data = self.processed_data.drop_duplicates(subset=['日期', '股票代码'])
        
        # 按日期和股票代码排序
        self.processed_data = self.processed_data.sort_values(['日期', '股票代码'])
        
        # 保存处理后的数据
        self.processed_data.to_excel(output_file, index=False)
        print(f"数据合并完成，共 {len(self.processed_data)} 条记录，已保存到 {output_file}")
        
        return True
    
    def calculate_additional_features(self, input_file='股票明细.xlsx', output_file='股票明细_增强.xlsx'):
        """计算额外的特征"""
        try:
            # 读取数据
            df = pd.read_excel(input_file)
            print(f"读取数据，共 {len(df)} 条记录")
            
            # 确保日期列是datetime类型
            df['日期'] = pd.to_datetime(df['日期'])
            
            # 按日期和股票代码排序
            df = df.sort_values(['股票代码', '日期'])
            
            # 计算技术强度与看涨技术指标数量的乘积
            df['技术强度_指标数量'] = df['技术强度'] * df['看涨技术指标数量']
            
            # 计算技术强度与连续技术强度5天数的比值
            df['技术强度_连续比'] = df['技术强度'] / df['连续技术强度5天数'].replace(0, 1)
            
            # 计算技术强度与价格趋势的交互
            df['技术强度_价格趋势'] = df['技术强度'] * df['价格趋势']
            
            # 计算技术强度与涨跌幅趋势的交互
            df['技术强度_涨跌幅趋势'] = df['技术强度'] * df['涨跌幅趋势']
            
            # 计算技术强度的平方
            df['技术强度_平方'] = df['技术强度'] ** 2
            
            # 创建技术强度分类特征
            df['技术强度_高'] = (df['技术强度'] >= 85).astype(int)
            df['技术强度_中'] = ((df['技术强度'] >= 70) & (df['技术强度'] < 85)).astype(int)
            df['技术强度_低'] = (df['技术强度'] < 70).astype(int)
            
            # 创建组合特征
            df['高技术强度_高看涨'] = ((df['技术强度'] >= 85) & (df['看涨技术指标数量'] == 5)).astype(int)
            df['高技术强度_上升趋势'] = ((df['技术强度'] >= 85) & (df['涨跌幅趋势'] == 1)).astype(int)
            
            # 创建综合得分特征
            df['综合技术得分'] = (
                df['技术强度'] / 100 * 0.4 +
                df['看涨技术指标数量'] / 5 * 0.3 +
                df['连续技术强度5天数'] / 500 * 0.2 +
                df['涨跌幅趋势'] * 0.1
            )
            
            # 保存增强后的数据
            df.to_excel(output_file, index=False)
            print(f"特征计算完成，共 {len(df)} 条记录，已保存到 {output_file}")
            
            return True
            
        except Exception as e:
            print(f"计算特征时出错: {e}")
            return False
    
    def add_next_day_returns(self, input_file='股票明细.xlsx', output_file='股票明细_带次日涨跌幅.xlsx'):
        """添加次日涨跌幅"""
        try:
            # 读取数据
            df = pd.read_excel(input_file)
            print(f"读取数据，共 {len(df)} 条记录")
            
            # 确保日期列是datetime类型
            df['日期'] = pd.to_datetime(df['日期'])
            
            # 按股票代码和日期排序
            df = df.sort_values(['股票代码', '日期'])
            
            # 计算次日涨跌幅
            df['次日涨跌幅'] = df.groupby('股票代码')['涨跌幅'].shift(-1)
            
            # 删除没有次日数据的记录
            df = df.dropna(subset=['次日涨跌幅'])
            
            # 保存处理后的数据
            df.to_excel(output_file, index=False)
            print(f"添加次日涨跌幅完成，共 {len(df)} 条记录，已保存到 {output_file}")
            
            return True
            
        except Exception as e:
            print(f"添加次日涨跌幅时出错: {e}")
            return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='股票数据处理工具')
    parser.add_argument('--data_dir', type=str, help='数据目录路径')
    parser.add_argument('--output', type=str, default='股票明细.xlsx', help='输出文件路径')
    parser.add_argument('--mode', type=str, choices=['process', 'merge', 'features', 'returns'], 
                        default='process', help='处理模式')
    parser.add_argument('--file_pattern', type=str, help='文件匹配模式，用于merge模式')
    parser.add_argument('--input', type=str, help='输入文件，用于features和returns模式')
    
    args = parser.parse_args()
    
    processor = DataProcessor(args.data_dir)
    
    if args.mode == 'process':
        if args.data_dir:
            processor.process_directory(args.output)
        else:
            print("错误: 请提供数据目录路径")
    elif args.mode == 'merge':
        if args.file_pattern:
            processor.merge_data_files(args.file_pattern, args.output)
        else:
            print("错误: 请提供文件匹配模式")
    elif args.mode == 'features':
        if args.input:
            processor.calculate_additional_features(args.input, args.output)
        else:
            print("错误: 请提供输入文件路径")
    elif args.mode == 'returns':
        if args.input:
            processor.add_next_day_returns(args.input, args.output)
        else:
            print("错误: 请提供输入文件路径")
