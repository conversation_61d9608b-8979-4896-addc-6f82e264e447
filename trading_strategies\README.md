# 股票交易策略包

这个包包含多种高胜率股票交易策略的实现，可以用于预测股票的盈利概率和推荐买入的股票。

## 策略说明

### 策略1：100%高胜率策略

- **条件**：
  - 预测盈利概率>78%
  - 技术强度≥70
  - 连续技术强度5天数≥400
  - 只买入开盘时上涨的股票

- **预期表现**：
  - 胜率：约100%
  - 平均收益率：约2.38%
  - 交易机会：较少

### 策略A：最高胜率策略

- **条件**：
  - 技术强度=21-30
  - 连续技术强度5天数=301-400
  - 预测盈利概率>75%
  - 只买入开盘时上涨的股票

- **预期表现**：
  - 胜率：约86.77%
  - 平均收益率：约3.42%
  - 交易机会：中等

### 策略B：最高收益率策略

- **条件**：
  - 技术强度=100
  - 连续技术强度5天数=401-500
  - 预测盈利概率>75%
  - 只买入开盘时上涨的股票

- **预期表现**：
  - 胜率：约83.67%
  - 平均收益率：约4.83%
  - 交易机会：较少

### 策略C：平衡策略（胜率和交易机会的平衡）

- **条件**：
  - 技术强度=71-80
  - 连续技术强度5天数=201-300
  - 预测盈利概率>75%
  - 只买入开盘时上涨的股票

- **预期表现**：
  - 胜率：约84.82%
  - 平均收益率：约2.59%
  - 交易机会：较多

## 使用方法

### 导入包

```python
from trading_strategies import (
    train_and_save_model,
    get_strategy_recommendations,
    get_all_strategy_recommendations,
    compare_strategies
)
```

### 训练模型

```python
# 训练模型并保存
result = train_and_save_model(data_file_path='股票明细.xlsx', model_dir='trained_models')

if result['success']:
    print("模型训练成功！")
    print(f"训练用时: {result['duration']:.2f} 秒")
    print(f"模型文件: {result['model_file']}")
else:
    print("模型训练失败！")
    print(f"错误信息: {result['error']}")
```

### 使用单个策略进行预测（包含风险说明）

```python
# 使用策略1预测2025-05-10的股票
strategy_1_stocks, risk_description = get_strategy_recommendations(
    strategy_name='strategy_1',
    prediction_date_str='2025-05-10',
    use_saved_model=True  # 使用保存的模型
)

# 显示风险说明
print(f"策略: {risk_description['strategy_description']}")
print(f"预期胜率: {risk_description['expected_win_rate']}")
print(f"预期收益率: {risk_description['expected_return']}")
print(f"买入风险: {risk_description['buy_risk']}")
print(f"卖出风险: {risk_description['sell_risk']}")
```

### 使用所有策略进行预测

```python
# 使用所有策略预测2025-05-10的股票
strategy_results, risk_descriptions = get_all_strategy_recommendations(
    prediction_date_str='2025-05-10',
    use_saved_model=True  # 使用保存的模型
)

# 显示各策略的风险说明
for strategy_name, risk_desc in risk_descriptions.items():
    print(f"\n{risk_desc['strategy_description']}:")
    print(f"预期胜率: {risk_desc['expected_win_rate']}")
    print(f"预期收益率: {risk_desc['expected_return']}")
    print(f"买入风险: {risk_desc['buy_risk']}")
    print(f"卖出风险: {risk_desc['sell_risk']}")
```

### 比较不同策略在一段时间内的表现

```python
# 比较不同策略在2025-05-01至2025-05-09的表现
results, summary = compare_strategies('2025-05-01', '2025-05-09')

# 打印策略汇总
print(summary)
```

## UI开发示例

以下是在UI中使用这些功能的示例：

### 训练模型按钮

```python
def train_model_button_click():
    """
    模拟点击"训练模型"按钮的操作
    """
    # 训练模型并保存
    result = train_and_save_model(data_file_path='股票明细.xlsx', model_dir='trained_models')

    if result['success']:
        # 在UI中显示训练结果
        print("模型训练成功！")
        print(f"训练用时: {result['duration']:.2f} 秒")
    else:
        # 在UI中显示错误信息
        print("模型训练失败！")
        print(f"错误信息: {result['error']}")
```

### 生成推荐按钮

```python
def generate_recommendations_button_click(prediction_date, strategy_name):
    """
    模拟点击"生成推荐"按钮的操作
    """
    # 使用保存的模型生成推荐
    recommended_stocks, risk_description = get_strategy_recommendations(
        strategy_name=strategy_name,
        prediction_date_str=prediction_date,
        use_saved_model=True
    )

    if recommended_stocks is not None and len(recommended_stocks) > 0:
        # 在UI中显示推荐股票和风险说明
        print(f"策略: {risk_description['strategy_description']}")
        print(f"预期胜率: {risk_description['expected_win_rate']}")
        print(f"预期收益率: {risk_description['expected_return']}")
        print(f"买入风险: {risk_description['buy_risk']}")
        print(f"卖出风险: {risk_description['sell_risk']}")
    else:
        print("没有找到符合条件的股票")
```

## 注意事项

1. **数据文件**：默认使用当前目录下的`股票明细.xlsx`文件作为数据源，可以通过参数指定其他文件路径。

2. **模型保存**：训练好的模型会保存在`trained_models`目录下，可以通过`use_saved_model=True`参数使用保存的模型进行预测。

3. **买入条件**：所有策略都要求"只买入开盘时上涨的股票"，这是保持高胜率的关键条件。在实际交易中，需要在开盘时观察股票价格走势，只买入开盘时上涨的股票。

4. **卖出时机**：买入后在第二个交易日开盘时卖出，这是所有策略的共同卖出条件。

5. **风险说明**：每个策略都有对应的买入风险和卖出风险说明，可以帮助用户了解策略的风险特性。

6. **结果保存**：所有预测结果都会保存在`strategy_results`目录下，可以通过Excel文件查看详细的推荐股票列表。

## 策略选择建议

- 如果您追求最高胜率，选择策略1或策略A
- 如果您追求最高收益率，选择策略B
- 如果您希望有更多交易机会，选择策略C
- 如果您希望平衡胜率、收益率和交易机会，可以同时使用多个策略，根据实际情况选择最适合的股票
