#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票交易系统 - 主程序
作者: Augment AI
版本: 1.0.0
"""

import os
import sys
import pandas as pd
import numpy as np
import argparse
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from stock_trading_system import StockTradingSystem

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='股票交易系统')
    parser.add_argument('--data', type=str, default='股票明细.xlsx', help='数据文件路径')
    parser.add_argument('--mode', type=str, choices=['train', 'backtest', 'recommend'], default='recommend', help='运行模式')
    parser.add_argument('--strategy', type=str, choices=['rule1', 'rule4', 'rule4plus', 'ml', 'hybrid'], default='rule4plus', help='策略选择')
    parser.add_argument('--start_date', type=str, help='回测开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end_date', type=str, help='回测结束日期 (YYYY-MM-DD)')
    parser.add_argument('--capital', type=float, default=10000, help='初始资金')
    parser.add_argument('--output', type=str, default='results.txt', help='输出文件路径')
    return parser.parse_args()

def select_strategy(system, strategy_name):
    """根据策略名称选择策略函数"""
    if strategy_name == 'rule1':
        return system.rule_strategy_1
    elif strategy_name == 'rule4':
        return system.rule_strategy_4
    elif strategy_name == 'rule4plus':
        return system.rule_strategy_4_plus
    elif strategy_name == 'ml':
        return system.ml_strategy
    elif strategy_name == 'hybrid':
        return system.hybrid_strategy
    else:
        raise ValueError(f"未知策略: {strategy_name}")

def train_model(system):
    """训练模型"""
    print("开始训练模型...")
    system.train_model(save_model=True)
    print("模型训练完成")

def backtest_strategy(system, strategy_name, start_date=None, end_date=None, initial_capital=10000):
    """回测策略"""
    print(f"开始回测策略: {strategy_name}")
    strategy_fn = select_strategy(system, strategy_name)
    result = system.backtest(strategy_fn, start_date, end_date, initial_capital)
    
    if result and len(result['trades']) > 0:
        # 绘制收益曲线
        plot_returns(result, strategy_name)
    
    return result

def recommend_stocks(system, strategy_name, date=None):
    """推荐股票"""
    if date is None:
        # 使用最新日期
        date = system.data['日期'].max()
    
    print(f"推荐日期: {date.strftime('%Y-%m-%d')}")
    
    strategy_fn = select_strategy(system, strategy_name)
    recommended_stocks = strategy_fn(system.data, date)
    
    if len(recommended_stocks) > 0:
        print(f"\n推荐股票 ({len(recommended_stocks)}只):")
        for i, (_, stock) in enumerate(recommended_stocks.iterrows(), 1):
            print(f"{i}. {stock['股票代码']} {stock['股票名称']}: 技术强度={stock['技术强度']}, 连续技术强度5天数={stock['连续技术强度5天数']}, 看涨技术指标数量={stock['看涨技术指标数量']}, 涨跌幅趋势={stock['涨跌幅趋势']}")
        
        # 保存推荐结果到文件
        save_recommendations(recommended_stocks, strategy_name, date)
    else:
        print("没有符合条件的股票")
    
    return recommended_stocks

def plot_returns(result, strategy_name):
    """绘制收益曲线"""
    if 'daily_trades_df' in result and not result['daily_trades_df'].empty:
        plt.figure(figsize=(12, 6))
        
        # 绘制累计收益率曲线
        plt.subplot(2, 1, 1)
        plt.plot(result['daily_trades_df']['日期'], result['daily_trades_df']['累计收益率'], 'b-', linewidth=2)
        plt.title(f'策略 {strategy_name} 累计收益率')
        plt.xlabel('日期')
        plt.ylabel('累计收益率 (%)')
        plt.grid(True)
        
        # 绘制每日收益柱状图
        plt.subplot(2, 1, 2)
        plt.bar(result['daily_trades_df']['日期'], result['daily_trades_df']['当日收益'], color='g')
        plt.title(f'策略 {strategy_name} 每日收益')
        plt.xlabel('日期')
        plt.ylabel('收益 (元)')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(f'strategy_{strategy_name}_returns.png')
        print(f"收益曲线已保存到 strategy_{strategy_name}_returns.png")

def save_recommendations(recommended_stocks, strategy_name, date):
    """保存推荐股票到文件"""
    filename = f"recommended_stocks_{strategy_name}_{date.strftime('%Y%m%d')}.txt"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(f"推荐日期: {date.strftime('%Y-%m-%d')}\n")
        f.write(f"策略: {strategy_name}\n")
        f.write(f"推荐股票数量: {len(recommended_stocks)}\n\n")
        
        for i, (_, stock) in enumerate(recommended_stocks.iterrows(), 1):
            f.write(f"{i}. {stock['股票代码']} {stock['股票名称']}\n")
            f.write(f"   技术强度: {stock['技术强度']}\n")
            f.write(f"   连续技术强度5天数: {stock['连续技术强度5天数']}\n")
            f.write(f"   看涨技术指标数量: {stock['看涨技术指标数量']}\n")
            f.write(f"   涨跌幅趋势: {stock['涨跌幅趋势']}\n")
            if '预测概率' in stock:
                f.write(f"   预测概率: {stock['预测概率']:.4f}\n")
            f.write("\n")
    
    print(f"推荐股票已保存到 {filename}")

def save_backtest_results(result, strategy_name, output_file):
    """保存回测结果到文件"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"策略: {strategy_name}\n")
        f.write(f"初始资金: {result['final_capital'] - result['total_profit']:,.2f}元\n")
        f.write(f"最终资金: {result['final_capital']:,.2f}元\n")
        f.write(f"总收益: {result['total_profit']:,.2f}元\n")
        f.write(f"总收益率: {result['total_return']:.2f}%\n")
        f.write(f"交易次数: {len(result['trades'])}\n")
        f.write(f"胜率: {result['win_rate']:.2f}%\n")
        f.write(f"平均涨跌幅: {result['avg_return']:.2f}%\n\n")
        
        if not result['daily_trades_df'].empty:
            f.write("每日收益:\n")
            for _, row in result['daily_trades_df'].iterrows():
                f.write(f"{row['日期']}: 交易数量={row['交易数量']}, 平均涨跌幅={row['平均涨跌幅']:.2f}%, 胜率={row['胜率']:.2f}%, 当日收益={row['当日收益']:.2f}元, 累计收益率={row['累计收益率']:.2f}%\n")
            
            f.write("\n最佳交易日:\n")
            best_day = result['daily_trades_df'].loc[result['daily_trades_df']['平均涨跌幅'].idxmax()]
            f.write(f"{best_day['日期']}: 平均涨跌幅={best_day['平均涨跌幅']:.2f}%, 胜率={best_day['胜率']:.2f}%\n")
            
            f.write("\n最差交易日:\n")
            worst_day = result['daily_trades_df'].loc[result['daily_trades_df']['平均涨跌幅'].idxmin()]
            f.write(f"{worst_day['日期']}: 平均涨跌幅={worst_day['平均涨跌幅']:.2f}%, 胜率={worst_day['胜率']:.2f}%\n")
        
        if not result['trades_df'].empty:
            f.write("\n最佳5笔交易:\n")
            best_trades = result['trades_df'].nlargest(5, '次日涨跌幅')
            for _, trade in best_trades.iterrows():
                f.write(f"{trade['股票代码']} {trade['股票名称']}: 次日涨跌幅={trade['次日涨跌幅']:.2f}%, 技术强度={trade['技术强度']}\n")
    
    print(f"回测结果已保存到 {output_file}")

def main():
    """主函数"""
    args = parse_args()
    
    # 创建交易系统
    system = StockTradingSystem(args.data)
    
    # 加载数据
    if not system.load_data():
        print("加载数据失败，程序退出")
        return
    
    # 根据模式执行不同操作
    if args.mode == 'train':
        train_model(system)
    elif args.mode == 'backtest':
        # 如果策略是机器学习或混合策略，需要先加载模型
        if args.strategy in ['ml', 'hybrid']:
            if not os.path.exists(system.model_path):
                print("模型文件不存在，开始训练模型...")
                train_model(system)
            else:
                system.load_model()
        
        # 执行回测
        result = backtest_strategy(
            system, 
            args.strategy, 
            args.start_date, 
            args.end_date, 
            args.capital
        )
        
        # 保存回测结果
        if result:
            save_backtest_results(result, args.strategy, args.output)
    elif args.mode == 'recommend':
        # 如果策略是机器学习或混合策略，需要先加载模型
        if args.strategy in ['ml', 'hybrid']:
            if not os.path.exists(system.model_path):
                print("模型文件不存在，开始训练模型...")
                train_model(system)
            else:
                system.load_model()
        
        # 推荐股票
        recommend_stocks(system, args.strategy)

if __name__ == "__main__":
    main()
