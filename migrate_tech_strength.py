"""
技术强度数据迁移工具

将现有的技术强度数据迁移到按天存储的格式
"""

import os
import sys
import pandas as pd
import argparse
from tqdm import tqdm
import tech_strength_manager as tsm

def migrate_tech_strength_data(input_file, output_dir=None):
    """
    将技术强度数据从单个文件迁移到按天存储的格式
    
    参数:
        input_file: 输入文件路径
        output_dir: 输出目录路径，默认为None，使用tech_strength_manager中的默认目录
    
    返回:
        bool: 是否成功迁移
    """
    try:
        # 设置输出目录
        if output_dir:
            tsm.set_base_dir(output_dir)
        
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            print(f"错误: 输入文件 {input_file} 不存在")
            return False
        
        # 加载输入文件
        print(f"从文件 {input_file} 加载数据...")
        df = pd.read_excel(input_file)
        
        if df.empty:
            print("错误: 输入文件为空")
            return False
        
        # 确保日期列是日期类型
        if 'date' in df.columns:
            date_column = 'date'
        elif '日期' in df.columns:
            date_column = '日期'
        else:
            print("错误: 找不到日期列，无法迁移")
            return False
        
        # 转换日期列为日期类型
        df[date_column] = pd.to_datetime(df[date_column])
        
        # 按日期分组并保存
        print(f"开始迁移数据，共 {len(df)} 条记录...")
        for date, group in tqdm(df.groupby(date_column), desc="迁移数据"):
            tsm.save_daily_tech_strength(group, date)
        
        print(f"成功迁移数据，共 {len(df)} 条记录，涵盖 {df[date_column].nunique()} 个交易日")
        print(f"数据已保存到目录: {tsm.daily_tech_strength_dir}")
        return True
    except Exception as e:
        print(f"迁移数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='技术强度数据迁移工具')
    parser.add_argument('--input', '-i', type=str, required=True, help='输入文件路径')
    parser.add_argument('--output', '-o', type=str, help='输出目录路径')
    args = parser.parse_args()
    
    # 迁移数据
    success = migrate_tech_strength_data(args.input, args.output)
    
    if success:
        print("数据迁移成功")
    else:
        print("数据迁移失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
