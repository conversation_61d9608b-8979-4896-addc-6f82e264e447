#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高级特征组合挖掘系统
作者: Augment AI
版本: 1.0.0
"""

import pandas as pd
import numpy as np
import os
import itertools
import time
from datetime import datetime
from tqdm import tqdm
import random
import pickle
import matplotlib.pyplot as plt
from collections import Counter

# 创建结果目录
results_dir = 'advanced_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
        
        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")
        
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def analyze_feature_distribution(data):
    """分析特征分布"""
    print("分析特征分布...")
    
    # 选择数值型特征
    numeric_features = data.select_dtypes(include=['int64', 'float64']).columns.tolist()
    
    # 排除不需要分析的列
    exclude_columns = ['股票代码', '日期', '涨跌幅', '次日是否盈利']
    features_to_analyze = [f for f in numeric_features if f not in exclude_columns]
    
    # 创建分布统计
    distribution_stats = {}
    
    for feature in features_to_analyze:
        # 计算分位数
        percentiles = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
        percentile_values = np.percentile(data[feature].dropna(), percentiles)
        
        # 计算均值和标准差
        mean = data[feature].mean()
        std = data[feature].std()
        
        # 保存统计结果
        distribution_stats[feature] = {
            'percentiles': dict(zip(percentiles, percentile_values)),
            'mean': mean,
            'std': std,
            'min': data[feature].min(),
            'max': data[feature].max()
        }
        
        print(f"特征 {feature} 的分布:")
        print(f"  均值: {mean:.2f}, 标准差: {std:.2f}")
        print(f"  最小值: {distribution_stats[feature]['min']:.2f}, 最大值: {distribution_stats[feature]['max']:.2f}")
        print(f"  分位数: {', '.join([f'{p}%: {v:.2f}' for p, v in zip(percentiles, percentile_values)])}")
        print("-" * 50)
    
    return distribution_stats

def analyze_feature_correlation(data):
    """分析特征相关性"""
    print("分析特征相关性...")
    
    # 选择数值型特征
    numeric_features = data.select_dtypes(include=['int64', 'float64']).columns.tolist()
    
    # 排除不需要分析的列
    exclude_columns = ['股票代码', '日期']
    features_to_analyze = [f for f in numeric_features if f not in exclude_columns]
    
    # 计算相关性矩阵
    correlation_matrix = data[features_to_analyze].corr()
    
    # 找出与'涨跌幅'相关性最高的特征
    if '涨跌幅' in correlation_matrix.columns:
        correlation_with_target = correlation_matrix['涨跌幅'].sort_values(ascending=False)
        print("与涨跌幅相关性最高的特征:")
        print(correlation_with_target)
    
    # 找出相互之间相关性高的特征对
    high_correlation_pairs = []
    for i in range(len(features_to_analyze)):
        for j in range(i+1, len(features_to_analyze)):
            feature1 = features_to_analyze[i]
            feature2 = features_to_analyze[j]
            correlation = correlation_matrix.loc[feature1, feature2]
            if abs(correlation) > 0.7:  # 相关性阈值
                high_correlation_pairs.append((feature1, feature2, correlation))
    
    # 按相关性绝对值排序
    high_correlation_pairs.sort(key=lambda x: abs(x[2]), reverse=True)
    
    print("\n相互之间相关性高的特征对:")
    for feature1, feature2, correlation in high_correlation_pairs[:10]:  # 只显示前10对
        print(f"{feature1} 和 {feature2}: {correlation:.4f}")
    
    return correlation_matrix, high_correlation_pairs

def analyze_by_technical_strength(data, target_feature='涨跌幅'):
    """按技术强度区间分析特征表现"""
    print(f"按技术强度区间分析 {target_feature} 表现...")
    
    # 定义技术强度区间
    strength_bins = [0, 50, 70, 80, 85, 90, 95, 100]
    labels = ['0-50', '50-70', '70-80', '80-85', '85-90', '90-95', '95-100']
    
    # 创建技术强度区间列
    data['技术强度区间'] = pd.cut(data['技术强度'], bins=strength_bins, labels=labels, right=True)
    
    # 按区间分组计算统计量
    grouped_stats = data.groupby('技术强度区间')[target_feature].agg(['mean', 'std', 'count'])
    
    # 计算正收益比例
    positive_ratio = data.groupby('技术强度区间')[target_feature].apply(lambda x: (x > 0).mean() * 100)
    grouped_stats['正收益比例'] = positive_ratio
    
    print(f"按技术强度区间的 {target_feature} 统计:")
    print(grouped_stats)
    
    return grouped_stats

def fast_backtest(data, feature_combination, thresholds, operators, start_date, end_date, initial_capital=10000):
    """快速回测策略，优化性能"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    # 初始化回测结果
    total_profit = 0
    total_trades = 0
    win_count = 0
    total_return_pct = 0
    
    # 对每个交易日进行回测
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 获取当日数据
        daily_data = date_range_data[date_range_data['日期'] == current_date]
        
        # 应用策略条件
        selected = daily_data.copy()
        for feature, threshold, operator in zip(feature_combination, thresholds, operators):
            if operator == '>=':
                selected = selected[selected[feature] >= threshold]
            elif operator == '<=':
                selected = selected[selected[feature] <= threshold]
            elif operator == '==':
                selected = selected[selected[feature] == threshold]
        
        # 如果有推荐的股票，模拟买入
        if len(selected) > 0:
            # 获取推荐股票的代码
            recommended_codes = selected['股票代码'].tolist()
            
            # 获取次日这些股票的数据
            next_day_data = date_range_data[
                (date_range_data['日期'] == next_date) & 
                (date_range_data['股票代码'].isin(recommended_codes))
            ]
            
            # 如果次日有这些股票的数据
            if len(next_day_data) > 0:
                # 计算平均涨跌幅
                avg_return = next_day_data['涨跌幅'].mean()
                
                # 计算盈利股票数量
                win_stocks = len(next_day_data[next_day_data['涨跌幅'] > 0])
                
                # 更新统计数据
                total_return_pct += avg_return
                total_trades += len(next_day_data)
                win_count += win_stocks
    
    # 计算回测结果
    if total_trades > 0:
        win_rate = win_count / total_trades * 100
        avg_return = total_return_pct / (len(trading_dates) - 1)
        total_return = avg_return * (len(trading_dates) - 1)
        
        return {
            'total_return': total_return,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'trade_count': total_trades
        }
    else:
        return {
            'total_return': 0,
            'win_rate': 0,
            'avg_return': 0,
            'trade_count': 0
        }

def explore_top_feature_combinations(data, start_date, end_date):
    """探索已知表现最佳的特征组合"""
    print("探索已知表现最佳的特征组合...")
    
    # 定义已知表现最佳的特征组合
    top_combinations = [
        # 双特征组合
        (('连续技术强度5天数趋势', '技术指标_KDJ金叉'), (1, 1), ('==', '==')),
        (('连续技术强度5天数趋势', '连续技术强度5天数涨跌幅趋势'), (1, 1), ('==', '==')),
        (('技术指标_RSI反弹', '技术指标_KDJ金叉'), (1, 1), ('==', '==')),
        
        # 三特征组合
        (('连续技术强度10天数', '技术强度趋势', '技术指标_KDJ金叉'), (70, 1, 1), ('>=', '==', '==')),
        (('连续技术强度5天数趋势', '连续技术强度5天数涨跌幅趋势', '技术指标_KDJ金叉'), (1, 1, 1), ('==', '==', '==')),
        (('连续技术强度5天数', '技术强度趋势', '连续技术强度5天数涨跌幅趋势'), (75, 1, 1), ('>=', '==', '==')),
        
        # 四特征组合
        (('技术强度趋势', '连续技术强度5天数趋势', '连续技术强度5天数价格趋势', '技术指标_KDJ金叉'), (1, 1, 1, 1), ('==', '==', '==', '==')),
        (('连续技术强度3天数', '涨跌幅趋势', '技术强度趋势', '技术指标_KDJ金叉'), (80, 1, 1, 1), ('>=', '==', '==', '=='))
    ]
    
    # 回测每个组合
    results = []
    for features, thresholds, operators in top_combinations:
        # 生成策略名称
        strategy_parts = []
        for feature, threshold, operator in zip(features, thresholds, operators):
            strategy_parts.append(f"{feature}{operator}{threshold}")
        strategy_name = ' & '.join(strategy_parts)
        
        print(f"回测策略: {strategy_name}")
        
        # 回测策略
        result = fast_backtest(data, features, thresholds, operators, start_date, end_date)
        result['strategy_name'] = strategy_name
        result['features'] = features
        result['thresholds'] = thresholds
        result['operators'] = operators
        
        results.append(result)
        
        print(f"收益率: {result['total_return']:.2f}%, 胜率: {result['win_rate']:.2f}%, 交易次数: {result['trade_count']}")
        print("-" * 50)
    
    # 按总收益率排序
    results.sort(key=lambda x: x['total_return'], reverse=True)
    
    return results

def analyze_by_date_range(data, feature_combination, thresholds, operators, start_date, end_date):
    """按日期范围分析策略表现"""
    print("按日期范围分析策略表现...")
    
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    # 初始化每日表现数据
    daily_performance = []
    
    # 对每个交易日进行分析
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 获取当日数据
        daily_data = date_range_data[date_range_data['日期'] == current_date]
        
        # 应用策略条件
        selected = daily_data.copy()
        for feature, threshold, operator in zip(feature_combination, thresholds, operators):
            if operator == '>=':
                selected = selected[selected[feature] >= threshold]
            elif operator == '<=':
                selected = selected[selected[feature] <= threshold]
            elif operator == '==':
                selected = selected[selected[feature] == threshold]
        
        # 如果有推荐的股票，计算表现
        if len(selected) > 0:
            # 获取推荐股票的代码
            recommended_codes = selected['股票代码'].tolist()
            
            # 获取次日这些股票的数据
            next_day_data = date_range_data[
                (date_range_data['日期'] == next_date) & 
                (date_range_data['股票代码'].isin(recommended_codes))
            ]
            
            # 如果次日有这些股票的数据
            if len(next_day_data) > 0:
                # 计算平均涨跌幅
                avg_return = next_day_data['涨跌幅'].mean()
                
                # 计算盈利股票数量和比例
                win_stocks = len(next_day_data[next_day_data['涨跌幅'] > 0])
                win_rate = win_stocks / len(next_day_data) * 100
                
                # 记录每日表现
                daily_performance.append({
                    '日期': current_date,
                    '推荐股票数量': len(next_day_data),
                    '平均涨跌幅': avg_return,
                    '胜率': win_rate
                })
    
    # 转换为DataFrame
    daily_df = pd.DataFrame(daily_performance)
    
    # 计算累计收益
    if len(daily_df) > 0:
        daily_df['累计收益'] = daily_df['平均涨跌幅'].cumsum()
    
    return daily_df

def generate_strategy_function(feature_combination, thresholds, operators):
    """根据特征组合生成策略函数"""
    def strategy(data, date):
        daily_data = data[data['日期'] == date]
        selected = daily_data.copy()
        for feature, threshold, operator in zip(feature_combination, thresholds, operators):
            if operator == '>=':
                selected = selected[selected[feature] >= threshold]
            elif operator == '<=':
                selected = selected[selected[feature] <= threshold]
            elif operator == '==':
                selected = selected[selected[feature] == threshold]
        return selected
    
    return strategy

def generate_recommendations(data, feature_combination, thresholds, operators, date):
    """生成股票推荐"""
    # 确保日期是datetime类型
    date = pd.to_datetime(date)
    
    # 生成策略函数
    strategy_fn = generate_strategy_function(feature_combination, thresholds, operators)
    
    # 应用策略
    recommended_stocks = strategy_fn(data, date)
    
    # 生成策略名称
    strategy_parts = []
    for feature, threshold, operator in zip(feature_combination, thresholds, operators):
        strategy_parts.append(f"{feature}{operator}{threshold}")
    strategy_name = ' & '.join(strategy_parts)
    
    print(f"策略: {strategy_name}")
    print(f"日期: {date.strftime('%Y-%m-%d')}, 推荐股票数量: {len(recommended_stocks)}")
    
    # 保存推荐股票到文件
    safe_strategy_name = f"strategy_{int(time.time())}"
    output_file = f"{results_dir}/{safe_strategy_name}_{date.strftime('%Y%m%d')}.csv"
    recommended_stocks.to_csv(output_file, index=False)
    
    print(f"推荐股票已保存到 {output_file}")
    
    # 打印推荐股票
    print("\n推荐股票列表:")
    for i, (_, stock) in enumerate(recommended_stocks.iterrows()):
        if i < 20:  # 只显示前20只
            print(f"{i+1}. {stock['股票代码']} {stock['股票名称']}, 技术强度: {stock['技术强度']}, 连续技术强度5天数: {stock['连续技术强度5天数']}, 看涨技术指标数量: {stock['看涨技术指标数量']}")
    
    if len(recommended_stocks) > 20:
        print(f"... 共 {len(recommended_stocks)} 只股票")
    
    return recommended_stocks

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)
    
    # 获取最新日期
    latest_date = df['日期'].max()
    print(f"最新日期: {latest_date.strftime('%Y-%m-%d')}")
    
    # 分析特征分布
    distribution_stats = analyze_feature_distribution(df)
    
    # 分析特征相关性
    correlation_matrix, high_correlation_pairs = analyze_feature_correlation(df)
    
    # 按技术强度区间分析涨跌幅表现
    strength_stats = analyze_by_technical_strength(df)
    
    # 探索已知表现最佳的特征组合
    top_results = explore_top_feature_combinations(df, '2025-04-01', '2025-04-30')
    
    # 按日期范围分析最佳策略表现
    best_strategy = top_results[0]
    daily_performance = analyze_by_date_range(
        df, 
        best_strategy['features'], 
        best_strategy['thresholds'], 
        best_strategy['operators'],
        '2025-04-01', 
        '2025-04-30'
    )
    
    # 保存每日表现到文件
    if len(daily_performance) > 0:
        daily_performance.to_csv(f"{results_dir}/daily_performance.csv", index=False)
        print(f"每日表现已保存到 {results_dir}/daily_performance.csv")
    
    # 生成最新日期的股票推荐 (使用最佳组合)
    print("\n生成最新日期的股票推荐 (使用最佳组合):")
    generate_recommendations(
        df, 
        best_strategy['features'], 
        best_strategy['thresholds'], 
        best_strategy['operators'],
        latest_date
    )
