"""
自定义akshare加载器 - 解决akshare依赖文件加载问题
"""

import os
import sys
import shutil
import glob

def get_bundle_dir():
    """获取程序运行的目录"""
    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的情况
        return os.path.dirname(sys.executable)
    else:
        # 正常运行的情况
        return os.path.dirname(os.path.abspath(__file__))

def get_temp_dir():
    """获取PyInstaller的临时目录"""
    if getattr(sys, 'frozen', False):
        # 尝试找到PyInstaller的临时目录
        for env_var in ['_MEIPASS', '_MEI']:
            if hasattr(sys, env_var):
                return getattr(sys, env_var)
        
        # 如果找不到，尝试在系统临时目录中查找
        temp_base = os.environ.get('TEMP', os.path.dirname(sys.executable))
        for root, dirs, files in os.walk(temp_base):
            if root.endswith('_MEI') and any(f.endswith('.dll') for f in files):
                return root
    
    return None

def ensure_akshare_files():
    """确保akshare的所有必要文件都可用"""
    bundle_dir = get_bundle_dir()
    temp_dir = get_temp_dir()
    
    print(f"程序目录: {bundle_dir}")
    print(f"临时目录: {temp_dir}")
    
    # 检查程序目录中是否有akshare_files目录
    akshare_files_dir = os.path.join(bundle_dir, 'akshare_files')
    if os.path.exists(akshare_files_dir):
        print(f"找到akshare_files目录: {akshare_files_dir}")
        
        # 如果找到了临时目录，尝试复制文件
        if temp_dir:
            # 创建akshare目录结构
            akshare_temp_dir = os.path.join(temp_dir, 'akshare')
            os.makedirs(akshare_temp_dir, exist_ok=True)
            
            # 创建file_fold目录
            file_fold_dir = os.path.join(akshare_temp_dir, 'file_fold')
            os.makedirs(file_fold_dir, exist_ok=True)
            
            # 复制calendar.json文件
            calendar_json_src = os.path.join(akshare_files_dir, 'calendar.json')
            calendar_json_dst = os.path.join(file_fold_dir, 'calendar.json')
            
            if os.path.exists(calendar_json_src):
                try:
                    shutil.copy2(calendar_json_src, calendar_json_dst)
                    print(f"已复制 calendar.json 到 {calendar_json_dst}")
                except Exception as e:
                    print(f"复制 calendar.json 时出错: {e}")
            
            # 复制其他可能的文件
            for file_name in glob.glob(os.path.join(akshare_files_dir, '*')):
                if os.path.isfile(file_name):
                    base_name = os.path.basename(file_name)
                    if base_name != 'calendar.json':  # 已经复制过了
                        try:
                            dst_file = os.path.join(file_fold_dir, base_name)
                            shutil.copy2(file_name, dst_file)
                            print(f"已复制 {base_name} 到 {dst_file}")
                        except Exception as e:
                            print(f"复制 {base_name} 时出错: {e}")

# 在导入时执行
ensure_akshare_files()
