#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全面特征组合挖掘系统
作者: Augment AI
版本: 1.0.0
"""

import pandas as pd
import numpy as np
import os
import itertools
import time
from datetime import datetime
from tqdm import tqdm
import random
import pickle

# 创建结果目录
results_dir = 'comprehensive_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")

        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])

        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")

        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def get_feature_combinations(features, min_features=2, max_features=4, max_combinations=100):
    """生成特征组合，限制最大组合数量"""
    all_combinations = []
    for r in range(min_features, max_features + 1):
        combinations = list(itertools.combinations(features, r))
        # 如果组合太多，随机选择一部分
        if len(combinations) > max_combinations:
            combinations = random.sample(combinations, max_combinations)
        all_combinations.extend(combinations)
    return all_combinations

def generate_strategy_from_combination(feature_combination, thresholds, operators):
    """根据特征组合生成策略函数"""
    def strategy(data, date):
        daily_data = data[data['日期'] == date]
        conditions = []

        for feature, threshold, operator in zip(feature_combination, thresholds, operators):
            if operator == '>=':
                conditions.append(f"(daily_data['{feature}'] >= {threshold})")
            elif operator == '<=':
                conditions.append(f"(daily_data['{feature}'] <= {threshold})")
            elif operator == '==':
                conditions.append(f"(daily_data['{feature}'] == {threshold})")

        query = ' & '.join(conditions)
        selected = eval(query)
        return daily_data[selected]

    return strategy

def backtest(data, strategy_fn, start_date, end_date, initial_capital=10000, strategy_name="未命名策略"):
    """回测策略"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)

    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]

    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())

    # 初始化回测结果
    capital = initial_capital
    trades = []

    # 对每个交易日进行回测
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue

        # 获取次日日期
        next_date = trading_dates[i + 1]

        # 应用策略
        recommended_stocks = strategy_fn(data, current_date)

        # 如果有推荐的股票，模拟买入
        if len(recommended_stocks) > 0:
            # 计算每只股票的资金分配
            capital_per_stock = capital / len(recommended_stocks)

            # 记录每只股票的买入和卖出情况
            for _, stock in recommended_stocks.iterrows():
                code = stock['股票代码']

                # 获取次日该股票数据（买入）
                next_day_data = data[(data['日期'] == next_date) & (data['股票代码'] == code)]

                if len(next_day_data) > 0:
                    # 获取次日涨跌幅（模拟买入后的收益）
                    next_day_change = next_day_data['涨跌幅'].values[0]

                    # 计算收益
                    profit = capital_per_stock * next_day_change / 100

                    # 记录交易
                    trades.append({
                        '日期': current_date.strftime('%Y-%m-%d'),
                        '次日': next_date.strftime('%Y-%m-%d'),
                        '股票代码': code,
                        '次日涨跌幅': next_day_change,
                        '投入资金': capital_per_stock,
                        '收益': profit,
                        '是否盈利': next_day_change > 0
                    })

    # 计算回测结果
    if trades:
        trades_df = pd.DataFrame(trades)
        total_profit = trades_df['收益'].sum()
        win_rate = trades_df['是否盈利'].mean() * 100
        avg_return = trades_df['次日涨跌幅'].mean()
        final_capital = initial_capital + total_profit
        total_return = (final_capital / initial_capital - 1) * 100

        return {
            'strategy_name': strategy_name,
            'total_profit': total_profit,
            'total_return': total_return,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'trade_count': len(trades),
            'trades': trades
        }
    else:
        return {
            'strategy_name': strategy_name,
            'total_profit': 0,
            'total_return': 0,
            'win_rate': 0,
            'avg_return': 0,
            'trade_count': 0,
            'trades': []
        }

def find_optimal_parameters(data, feature_combination, start_date, end_date, max_trials=20):
    """为特征组合找到最优参数，使用随机搜索"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)

    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]

    # 为每个特征生成候选阈值和运算符
    best_return = -float('inf')
    best_params = None
    best_result = None

    # 随机搜索最优参数
    for _ in range(max_trials):
        thresholds = []
        operators = []

        for feature in feature_combination:
            # 根据特征类型选择合适的阈值和运算符
            if feature in ['技术强度', '连续技术强度5天数', '连续技术强度3天数', '连续技术强度10天数']:
                # 对于技术强度类特征，使用较高的阈值和>=运算符
                threshold = random.choice([70, 75, 80, 85, 90, 95, 100])
                operator = '>='
            elif feature == '看涨技术指标数量':
                # 对于看涨技术指标数量，使用固定值和>=运算符
                threshold = random.choice([3, 4, 5])
                operator = '>='
            elif feature in ['涨跌幅趋势', '技术强度趋势', '连续技术强度5天数趋势',
                           '连续技术强度5天数价格趋势', '连续技术强度5天数涨跌幅趋势',
                           '技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
                           '技术指标_KDJ金叉', '技术指标_布林带突破', '开盘涨跌']:
                # 对于二元特征，只使用1作为阈值和==运算符
                threshold = 1
                operator = '=='
            else:
                # 对于其他特征，使用分位数作为阈值
                percentile = random.uniform(50, 90)
                threshold = np.percentile(date_range_data[feature], percentile)
                operator = random.choice(['>=', '<='])

            thresholds.append(threshold)
            operators.append(operator)

        # 生成策略函数
        strategy_fn = generate_strategy_from_combination(feature_combination, thresholds, operators)

        # 生成策略名称
        strategy_parts = []
        for feature, threshold, operator in zip(feature_combination, thresholds, operators):
            strategy_parts.append(f"{feature}{operator}{threshold:.2f}")
        strategy_name = ' & '.join(strategy_parts)

        # 回测策略
        result = backtest(data, strategy_fn, start_date, end_date, 10000, strategy_name)

        # 更新最优结果
        if result['total_return'] > best_return and result['trade_count'] >= 5:
            best_return = result['total_return']
            best_params = (thresholds, operators)
            best_result = result

    return best_params, best_result

def explore_feature_combinations(data, features, start_date, end_date, min_features=2, max_features=4,
                                max_combinations_per_size=50, max_trials_per_combination=20, top_n=10):
    """探索特征组合"""
    print(f"正在探索特征组合 (最小特征数: {min_features}, 最大特征数: {max_features})...")

    # 生成特征组合
    combinations = get_feature_combinations(features, min_features, max_features, max_combinations_per_size)
    print(f"共生成 {len(combinations)} 个特征组合")

    # 为每个组合找到最优参数并回测
    results = []
    for i, combination in enumerate(tqdm(combinations)):
        print(f"\n正在探索组合 {i+1}/{len(combinations)}: {combination}")
        params, result = find_optimal_parameters(data, combination, start_date, end_date, max_trials_per_combination)
        if result and result['trade_count'] > 0:
            results.append(result)
            print(f"最佳参数: {result['strategy_name']}")
            print(f"收益率: {result['total_return']:.2f}%, 胜率: {result['win_rate']:.2f}%, 交易次数: {result['trade_count']}")

        # 每10个组合保存一次中间结果
        if (i + 1) % 10 == 0:
            # 按总收益率排序
            results.sort(key=lambda x: x['total_return'], reverse=True)

            # 保存中间结果
            interim_results_df = pd.DataFrame([{
                '策略名称': result['strategy_name'],
                '总收益率': result['total_return'],
                '胜率': result['win_rate'],
                '平均涨跌幅': result['avg_return'],
                '交易次数': result['trade_count']
            } for result in results])

            interim_results_df.to_csv(f"{results_dir}/interim_results_{i+1}.csv", index=False)
            print(f"已保存中间结果到 {results_dir}/interim_results_{i+1}.csv")

    # 按总收益率排序
    results.sort(key=lambda x: x['total_return'], reverse=True)

    # 保存结果
    results_df = pd.DataFrame([{
        '策略名称': result['strategy_name'],
        '总收益率': result['total_return'],
        '胜率': result['win_rate'],
        '平均涨跌幅': result['avg_return'],
        '交易次数': result['trade_count']
    } for result in results])

    results_df.to_csv(f"{results_dir}/comprehensive_results.csv", index=False)
    print(f"已保存结果到 {results_dir}/comprehensive_results.csv")

    # 保存完整结果对象
    with open(f"{results_dir}/full_results.pkl", 'wb') as f:
        pickle.dump(results, f)

    # 返回前N个最佳组合
    return results[:top_n]

def generate_recommendations(data, strategy_name, date):
    """根据策略名称生成股票推荐"""
    # 确保日期是datetime类型
    date = pd.to_datetime(date)

    # 解析策略名称
    parts = strategy_name.split(' & ')
    feature_combination = []
    thresholds = []
    operators = []

    for part in parts:
        if '>=' in part:
            feature, threshold = part.split('>=')
            feature_combination.append(feature.strip())
            thresholds.append(float(threshold))
            operators.append('>=')
        elif '<=' in part:
            feature, threshold = part.split('<=')
            feature_combination.append(feature.strip())
            thresholds.append(float(threshold))
            operators.append('<=')
        elif '==' in part:
            feature, threshold = part.split('==')
            feature_combination.append(feature.strip())
            thresholds.append(float(threshold))
            operators.append('==')

    # 生成策略函数
    strategy_fn = generate_strategy_from_combination(feature_combination, thresholds, operators)

    # 应用策略
    recommended_stocks = strategy_fn(data, date)

    print(f"策略: {strategy_name}")
    print(f"日期: {date.strftime('%Y-%m-%d')}, 推荐股票数量: {len(recommended_stocks)}")

    # 保存推荐股票到文件
    safe_strategy_name = f"strategy_{int(time.time())}"
    output_file = f"{results_dir}/{safe_strategy_name}_{date.strftime('%Y%m%d')}.csv"
    recommended_stocks.to_csv(output_file, index=False)

    print(f"推荐股票已保存到 {output_file}")

    # 保存策略描述
    with open(f"{results_dir}/{safe_strategy_name}_description_{date.strftime('%Y%m%d')}.txt", 'w', encoding='utf-8') as f:
        f.write(f"策略: {strategy_name}\n")
        f.write(f"日期: {date.strftime('%Y-%m-%d')}\n")
        f.write(f"推荐股票数量: {len(recommended_stocks)}\n")

    # 打印推荐股票
    print("\n推荐股票列表:")
    for i, (_, stock) in enumerate(recommended_stocks.iterrows()):
        if i < 20:  # 只显示前20只
            print(f"{i+1}. {stock['股票代码']} {stock['股票名称']}, 技术强度: {stock['技术强度']}, 连续技术强度5天数: {stock['连续技术强度5天数']}, 看涨技术指标数量: {stock['看涨技术指标数量']}")

    if len(recommended_stocks) > 20:
        print(f"... 共 {len(recommended_stocks)} 只股票")

    return recommended_stocks

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)

    # 获取最新日期
    latest_date = df['日期'].max()
    print(f"最新日期: {latest_date.strftime('%Y-%m-%d')}")

    # 定义要探索的特征
    features = [
        '技术强度',
        '连续技术强度5天数',
        '连续技术强度3天数',
        '连续技术强度10天数',
        '看涨技术指标数量',
        '涨跌幅趋势',
        '技术强度趋势',
        '连续技术强度5天数趋势',
        '连续技术强度5天数价格趋势',
        '连续技术强度5天数涨跌幅趋势',
        '技术指标_均线多头排列',
        '技术指标_MACD金叉',
        '技术指标_RSI反弹',
        '技术指标_KDJ金叉',
        '技术指标_布林带突破',
        '开盘涨跌'
    ]

    # 探索特征组合
    best_combinations = explore_feature_combinations(
        df, features, '2025-04-01', '2025-04-30',
        min_features=2, max_features=3,  # 限制最大特征数为3，加快运行速度
        max_combinations_per_size=10,  # 每种特征数量最多探索10个组合
        max_trials_per_combination=5,  # 每个组合最多尝试5次参数
        top_n=10
    )

    # 打印最佳组合
    print("\n最佳特征组合:")
    for i, result in enumerate(best_combinations):
        print(f"{i+1}. {result['strategy_name']}")
        print(f"   总收益率: {result['total_return']:.2f}%")
        print(f"   胜率: {result['win_rate']:.2f}%")
        print(f"   平均涨跌幅: {result['avg_return']:.2f}%")
        print(f"   交易次数: {result['trade_count']}")
        print("-" * 50)

    # 生成最新日期的股票推荐 (使用最佳组合)
    if best_combinations:
        best_strategy_name = best_combinations[0]['strategy_name']
        print("\n生成最新日期的股票推荐 (使用最佳组合):")
        generate_recommendations(df, best_strategy_name, latest_date)
