#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行机器学习策略挖掘系统
作者: Augment AI
版本: 1.0.0
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from ml_strategy_mining import MLStrategyMiner

def main():
    """主函数"""
    print("=" * 80)
    print("机器学习策略挖掘系统")
    print("版本: 1.0.0")
    print("=" * 80)
    
    # 检查数据文件是否存在
    data_file = '股票明细.xlsx'
    if not os.path.exists(data_file):
        print(f"错误: 数据文件 '{data_file}' 不存在")
        return False
    
    # 创建策略挖掘器实例
    miner = MLStrategyMiner(data_file=data_file)
    
    # 运行完整流程
    success = miner.run_pipeline()
    
    if success:
        print("\n策略挖掘完成，结果保存在 'ml_strategy_results' 目录中")
        
        # 显示最佳策略
        if os.path.exists('ml_strategy_results/backtest_results.csv'):
            backtest_results = pd.read_csv('ml_strategy_results/backtest_results.csv')
            if not backtest_results.empty:
                # 按总收益率排序
                backtest_results = backtest_results.sort_values(by='总收益率', ascending=False)
                
                print("\n最佳策略排名:")
                for i, (_, row) in enumerate(backtest_results.iterrows()):
                    print(f"{i+1}. {row['策略名称']}")
                    print(f"   总收益率: {row['总收益率']:.2f}%")
                    print(f"   胜率: {row['胜率']:.2f}%")
                    print(f"   平均涨跌幅: {row['平均涨跌幅']:.2f}%")
                    print(f"   交易次数: {row['交易次数']}")
                    print("-" * 50)
        
        # 显示重要特征
        if os.path.exists('ml_strategy_results/feature_importance.csv'):
            feature_importance = pd.read_csv('ml_strategy_results/feature_importance.csv')
            if not feature_importance.empty:
                print("\n重要特征排名:")
                for i, (_, row) in enumerate(feature_importance.head(10).iterrows()):
                    print(f"{i+1}. {row['feature']}: {row['importance']:.4f}")
        
        # 显示交易规则
        if os.path.exists('ml_strategy_results/trading_rules.csv'):
            trading_rules = pd.read_csv('ml_strategy_results/trading_rules.csv')
            if not trading_rules.empty:
                print("\n交易规则:")
                for i, (_, row) in enumerate(trading_rules.iterrows()):
                    print(f"{i+1}. {row['规则']}")
                    print(f"   胜率: {row['胜率']:.2%}")
                    print(f"   样本数: {row['样本数']}")
        
        return True
    else:
        print("\n策略挖掘失败")
        return False

def generate_recommendations(date_str=None):
    """生成特定日期的股票推荐"""
    print("=" * 80)
    print("机器学习策略推荐股票生成")
    print("=" * 80)
    
    # 检查数据文件是否存在
    data_file = '股票明细.xlsx'
    if not os.path.exists(data_file):
        print(f"错误: 数据文件 '{data_file}' 不存在")
        return False
    
    # 加载数据
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
    except Exception as e:
        print(f"加载数据失败: {e}")
        return False
    
    # 确保日期列是datetime类型
    df['日期'] = pd.to_datetime(df['日期'])
    
    # 获取最新日期
    all_dates = sorted(df['日期'].unique())
    latest_date = all_dates[-1]
    
    # 如果指定了日期，则使用指定日期
    if date_str:
        try:
            target_date = pd.to_datetime(date_str)
            if target_date not in all_dates:
                print(f"警告: 指定的日期 {date_str} 不在数据中，使用最新日期 {latest_date.strftime('%Y-%m-%d')}")
                target_date = latest_date
        except:
            print(f"警告: 日期格式错误，使用最新日期 {latest_date.strftime('%Y-%m-%d')}")
            target_date = latest_date
    else:
        target_date = latest_date
    
    print(f"生成 {target_date.strftime('%Y-%m-%d')} 的股票推荐")
    
    # 加载策略
    if not os.path.exists('ml_strategy_results/combined_strategies.csv'):
        print("错误: 策略文件不存在，请先运行策略挖掘")
        return False
    
    strategies = pd.read_csv('ml_strategy_results/combined_strategies.csv')
    if strategies.empty:
        print("错误: 策略文件为空")
        return False
    
    # 按胜率排序
    strategies = strategies.sort_values(by='胜率', ascending=False)
    best_strategy = strategies.iloc[0]
    
    print(f"使用最佳策略: {best_strategy['策略名称']}")
    print(f"策略条件: {best_strategy['策略条件']}")
    print(f"胜率: {best_strategy['胜率']:.2%}")
    
    # 解析策略条件
    conditions = best_strategy['策略条件'].split(" AND ")
    strategy_filter = pd.Series(True, index=df.index)
    
    for condition in conditions:
        if ">=" in condition:
            feature, threshold_str = condition.split(" >= ")
            threshold = float(threshold_str)
            strategy_filter &= (df[feature] >= threshold)
    
    # 应用策略条件
    daily_data = df[(df['日期'] == target_date) & strategy_filter]
    
    print(f"符合条件的股票数量: {len(daily_data)}")
    
    if len(daily_data) > 0:
        # 保存推荐股票
        output_file = f"recommended_stocks_{target_date.strftime('%Y%m%d')}.csv"
        daily_data.to_csv(output_file, index=False)
        print(f"推荐股票已保存到 {output_file}")
        
        # 显示推荐股票
        print("\n推荐股票列表:")
        for i, (_, stock) in enumerate(daily_data.iterrows()):
            if i < 20:  # 只显示前20只
                print(f"{i+1}. {stock['股票代码']} {stock['股票名称']}")
        
        if len(daily_data) > 20:
            print(f"... 共 {len(daily_data)} 只股票")
        
        return True
    else:
        print("没有符合条件的股票")
        return False

if __name__ == "__main__":
    # 解析命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "recommend":
            # 生成推荐股票
            date_str = sys.argv[2] if len(sys.argv) > 2 else None
            generate_recommendations(date_str)
        else:
            # 默认运行完整流程
            main()
    else:
        # 默认运行完整流程
        main()
