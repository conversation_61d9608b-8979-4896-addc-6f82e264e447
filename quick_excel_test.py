import pandas as pd
from openpyxl import Workbook

# 创建测试数据
data = {
    '技术指标特征': ['000001', '010111', '001010'],
    '趋势组合': ['000001', '010111', '001010']
}

df = pd.DataFrame(data)

print("原始数据:")
print(df)

# 使用openpyxl保存，设置文本格式
wb = Workbook()
ws = wb.active

# 添加标题
ws.cell(row=1, column=1, value='技术指标特征')
ws.cell(row=1, column=2, value='趋势组合')

# 添加数据，设置为文本格式
for i, row in df.iterrows():
    # 技术指标特征
    cell1 = ws.cell(row=i+2, column=1, value=str(row['技术指标特征']))
    cell1.number_format = '@'
    
    # 趋势组合
    cell2 = ws.cell(row=i+2, column=2, value=str(row['趋势组合']))
    cell2.number_format = '@'

# 保存文件
wb.save('test_leading_zeros.xlsx')
print("文件已保存")

# 读取验证
df_read = pd.read_excel('test_leading_zeros.xlsx', dtype={'技术指标特征': str, '趋势组合': str})
print("\n读取后的数据:")
print(df_read)

print("\n验证前导0:")
for i, row in df_read.iterrows():
    tech = str(row['技术指标特征'])
    trend = str(row['趋势组合'])
    print(f"行{i+1}: 技术指标特征='{tech}' (长度:{len(tech)}), 趋势组合='{trend}' (长度:{len(trend)})")
