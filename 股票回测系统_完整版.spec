# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集数据文件
datas = []

# 收集隐藏导入
hiddenimports = [
    'pandas',
    'numpy',
    'openpyxl',
    'xlsxwriter',
    'sklearn',
    'sklearn.ensemble',
    'sklearn.tree',
    'sklearn.linear_model',
    'sklearn.metrics',
    'sklearn.model_selection',
    'sklearn.preprocessing',
    'xgboost',
    'lightgbm',
    'matplotlib',
    'matplotlib.pyplot',
    'seaborn',
    'joblib',
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'threading',
    'subprocess',
    'json',
    'datetime',
    'time',
    'glob',
    'argparse',
    'traceback',
    'warnings',
    'collections',
    'itertools',
    'functools',
    'pathlib',
    'config',
    'stock_data_manager',
    'backtest_local',
]

# 收集所有sklearn子模块
try:
    hiddenimports.extend(collect_submodules('sklearn'))
except:
    pass

# 收集所有pandas子模块
try:
    hiddenimports.extend(collect_submodules('pandas'))
except:
    pass

# 收集所有numpy子模块
try:
    hiddenimports.extend(collect_submodules('numpy'))
except:
    pass

block_cipher = None

a = Analysis(
    ['backtest_gui.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='股票回测系统_完整版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
