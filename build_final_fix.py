"""
最终修复版打包脚本 - 专门解决py_mini_racer的DLL加载问题
"""

import os
import subprocess
import sys
import platform
import shutil
import site
import glob
import importlib.util
import tempfile

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'pyinstaller',
        'pandas',
        'numpy',
        'akshare',
        'tqdm',
        'openpyxl',
        'pyarrow',
        'requests',
        'lxml',
        'beautifulsoup4',
        'pywin32',
        'py_mini_racer',
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print("\n需要安装以下依赖:")
        for package in missing_packages:
            print(f"  - {package}")
        
        install = input("\n是否自动安装这些依赖? (y/n): ")
        if install.lower() == 'y':
            for package in missing_packages:
                print(f"\n正在安装 {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"{package} 安装完成")
        else:
            print("\n请手动安装缺失的依赖后再运行此脚本")
            sys.exit(1)

def find_mini_racer_files():
    """查找py_mini_racer的所有相关文件"""
    mini_racer_files = []
    
    # 查找所有可能的site-packages目录
    site_packages = site.getsitepackages()
    if hasattr(site, 'getusersitepackages'):
        site_packages.append(site.getusersitepackages())
    
    # 查找py_mini_racer目录
    for site_pkg in site_packages:
        mini_racer_path = os.path.join(site_pkg, 'py_mini_racer')
        if os.path.exists(mini_racer_path):
            # 添加整个目录
            mini_racer_files.append((mini_racer_path, 'py_mini_racer'))
            
            # 特别查找icudtl.dat文件
            for root, dirs, files in os.walk(mini_racer_path):
                for file in files:
                    if file == 'icudtl.dat' or file.endswith('.dll'):
                        file_path = os.path.join(root, file)
                        # 添加到根目录
                        mini_racer_files.append((file_path, '.'))
    
    return mini_racer_files

def find_akshare_files():
    """查找akshare的所有相关文件"""
    akshare_files = []
    
    # 获取akshare模块的路径
    try:
        akshare_spec = importlib.util.find_spec('akshare')
        if akshare_spec and akshare_spec.origin:
            akshare_dir = os.path.dirname(akshare_spec.origin)
            
            # 添加整个目录
            akshare_files.append((akshare_dir, 'akshare'))
            
            # 查找所有DLL文件
            for root, dirs, files in os.walk(akshare_dir):
                for file in files:
                    if file.endswith('.dll') or file.endswith('.dat'):
                        file_path = os.path.join(root, file)
                        # 添加到根目录
                        akshare_files.append((file_path, '.'))
    except Exception as e:
        print(f"查找akshare文件时出错: {e}")
    
    return akshare_files

def create_hook_file():
    """创建自定义钩子文件来处理py_mini_racer"""
    hook_content = """
# 自定义钩子文件，用于处理py_mini_racer
from PyInstaller.utils.hooks import collect_all

# 收集py_mini_racer的所有依赖
datas, binaries, hiddenimports = collect_all('py_mini_racer')

# 确保添加了所有必要的隐藏导入
hiddenimports += ['py_mini_racer']
"""
    
    hook_dir = os.path.join(tempfile.gettempdir(), 'custom_hooks')
    os.makedirs(hook_dir, exist_ok=True)
    
    hook_file = os.path.join(hook_dir, 'hook-py_mini_racer.py')
    with open(hook_file, 'w') as f:
        f.write(hook_content)
    
    return hook_dir

def build_executable(mini_racer_files, akshare_files, hook_dir):
    """使用PyInstaller构建可执行文件"""
    print("\n开始构建可执行文件...")
    
    # 准备命令行参数
    cmd = [
        sys.executable, 
        "-m", 
        "PyInstaller",
        "--clean",
        "--windowed",  # 不显示控制台窗口
        "--onefile",   # 生成单个可执行文件
        "--name", "自动下载程序",
        "--additional-hooks-dir", hook_dir,
        "--hidden-import", "akshare",
        "--hidden-import", "py_mini_racer",
        "--hidden-import", "stock_data_manager",
    ]
    
    # 添加数据文件
    for src, dst in mini_racer_files + akshare_files:
        cmd.extend(["--add-data", f"{src}{os.pathsep}{dst}"])
    
    # 添加主脚本
    cmd.append("download_stock_data_gui.py")
    
    # 执行命令
    subprocess.check_call(cmd)
    
    print("\n构建完成!")
    
    # 检查是否成功创建了可执行文件
    if os.path.exists(os.path.join('dist', '自动下载程序.exe')):
        print(f"\n可执行文件已创建: {os.path.abspath(os.path.join('dist', '自动下载程序.exe'))}")
        
        # 创建一个包含必要文件的发布目录
        create_release_package()
    else:
        print("\n错误: 未能创建可执行文件")

def create_release_package():
    """创建一个包含所有必要文件的发布包"""
    release_dir = "股票数据下载工具_最终版"
    
    # 如果目录已存在，先删除
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    
    # 创建发布目录
    os.makedirs(release_dir)
    
    # 复制可执行文件
    shutil.copy(os.path.join('dist', '自动下载程序.exe'), os.path.join(release_dir, '自动下载程序.exe'))
    
    # 创建默认数据目录
    os.makedirs(os.path.join(release_dir, 'data', 'stock_data', 'daily'), exist_ok=True)
    
    # 创建说明文件
    with open(os.path.join(release_dir, '使用说明.txt'), 'w', encoding='utf-8') as f:
        f.write("""股票数据下载工具使用说明
====================

1. 运行方法:
   双击"自动下载程序.exe"即可启动程序

2. 功能说明:
   - 可以下载指定日期范围内的A股股票历史数据
   - 数据按日期分别存储在Excel文件中
   - 默认保存在程序所在目录的data文件夹中

3. 使用步骤:
   a. 设置数据输出目录(可选)
   b. 设置日期范围
   c. 点击"开始下载"按钮
   d. 等待下载完成

4. 注意事项:
   - 首次运行时可能需要等待较长时间
   - 下载过程中请保持网络连接
   - 如遇到问题，请查看程序日志窗口的提示信息
""")
    
    print(f"\n发布包已创建: {os.path.abspath(release_dir)}")
    print("包含以下文件:")
    print(f"  - {release_dir}/自动下载程序.exe")
    print(f"  - {release_dir}/使用说明.txt")
    print(f"  - {release_dir}/data/ (默认数据目录)")

def main():
    """主函数"""
    print("=" * 50)
    print("股票数据下载工具打包脚本 (最终修复版)")
    print("=" * 50)
    
    # 检查操作系统
    if platform.system() != 'Windows':
        print("警告: 此脚本设计用于Windows系统，在其他系统上可能无法正常工作")
    
    # 检查依赖
    print("\n检查依赖...")
    check_dependencies()
    
    # 查找py_mini_racer文件
    print("\n查找py_mini_racer文件...")
    mini_racer_files = find_mini_racer_files()
    if mini_racer_files:
        print(f"找到 {len(mini_racer_files)} 个py_mini_racer相关文件")
    else:
        print("警告: 未找到py_mini_racer文件，打包可能会失败")
    
    # 查找akshare文件
    print("\n查找akshare文件...")
    akshare_files = find_akshare_files()
    if akshare_files:
        print(f"找到 {len(akshare_files)} 个akshare相关文件")
    else:
        print("警告: 未找到akshare文件，打包可能会失败")
    
    # 创建钩子文件
    print("\n创建自定义钩子文件...")
    hook_dir = create_hook_file()
    print(f"钩子文件已创建: {hook_dir}")
    
    # 构建可执行文件
    build_executable(mini_racer_files, akshare_files, hook_dir)
    
    print("\n打包过程完成!")
    print("=" * 50)

if __name__ == "__main__":
    main()
