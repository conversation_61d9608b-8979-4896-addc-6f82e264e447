import os
import pandas as pd
import glob
import datetime
import re
import numpy as np

# 定义路径
new_data_dir = r"E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0"
output_dir = r"E:\机器学习\complete_excel_results"
output_file = os.path.join(output_dir, "股票明细_完整.xlsx")
# 如果文件被占用，使用备用文件名
backup_output_file = os.path.join(output_dir, "股票明细_完整_新.xlsx")
# 历史数据文件
history_data_file = os.path.join(output_dir, "stock_data", "stock_history_data.xlsx")

# 读取现有的股票明细表
print(f"读取现有的股票明细表: {output_file}")
try:
    existing_df = pd.read_excel(output_file)
    print(f"成功读取现有数据，共 {len(existing_df)} 条记录")
except Exception as e:
    print(f"读取现有数据失败: {e}")
    existing_df = pd.DataFrame()
    print("创建新的数据框")

# 读取历史数据
print(f"读取历史数据: {history_data_file}")
try:
    history_df = pd.read_excel(history_data_file)
    print(f"成功读取历史数据，共 {len(history_df)} 条记录")

    # 确保日期列是日期类型
    if 'date' in history_df.columns:
        history_df['date'] = pd.to_datetime(history_df['date'])

    # 确保代码列是字符串类型
    if 'code' in history_df.columns:
        history_df['code'] = history_df['code'].astype(str)
except Exception as e:
    print(f"读取历史数据失败: {e}")
    history_df = pd.DataFrame()
    print("无法读取历史数据，将使用现有数据计算成交量比例")

# 获取所有选股结果文件夹
folders = [f for f in os.listdir(new_data_dir) if f.startswith("选股结果_")]
print(f"找到 {len(folders)} 个选股结果文件夹")

# 创建一个空的DataFrame来存储所有数据
all_data = []

# 处理每个文件夹
for folder in folders:
    folder_path = os.path.join(new_data_dir, folder)

    # 从文件夹名称中提取日期
    date_match = re.search(r'选股结果_(\d{4}-\d{2}-\d{2})', folder)
    if date_match:
        date_str = date_match.group(1)
    else:
        print(f"无法从文件夹名称 {folder} 中提取日期，跳过")
        continue

    # 查找Excel文件
    excel_files = glob.glob(os.path.join(folder_path, "*.xlsx"))

    if not excel_files:
        print(f"在文件夹 {folder} 中没有找到Excel文件，跳过")
        continue

    # 使用第一个Excel文件
    excel_file = excel_files[0]
    print(f"处理文件: {excel_file}")

    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)

        # 添加日期列
        df['日期'] = pd.to_datetime(date_str)

        # 将数据添加到列表中
        all_data.append(df)
        print(f"成功读取 {len(df)} 条记录")
    except Exception as e:
        print(f"处理文件 {excel_file} 时出错: {e}")

# 如果有新数据，合并它们
if all_data:
    # 合并所有新数据
    new_data_df = pd.concat(all_data, ignore_index=True)
    print(f"合并了 {len(new_data_df)} 条新记录")

    # 不在这里计算特征，而是在合并所有数据后统一计算

    # 合并所有数据并重新计算所有特征
    print("合并所有数据并重新计算所有特征...")

    # 如果存在现有数据，合并新旧数据
    if not existing_df.empty:
        # 创建一个唯一标识符列（股票代码+日期）
        if '日期' in existing_df.columns and '日期' in new_data_df.columns:
            existing_df['标识符'] = existing_df['股票代码'] + '_' + existing_df['日期'].astype(str)
            new_data_df['标识符'] = new_data_df['股票代码'] + '_' + new_data_df['日期'].astype(str)

            # 找出不在新数据中的旧记录
            new_identifiers = set(new_data_df['标识符'])
            old_unique_data = existing_df[~existing_df['标识符'].isin(new_identifiers)]

            print(f"保留 {len(old_unique_data)} 条旧记录")

            # 标记需要更新的记录
            new_data_df['需要更新'] = True
            old_unique_data['需要更新'] = False

            print(f"标记了 {len(new_data_df)} 条需要更新的记录")

            # 删除标识符列
            old_unique_data = old_unique_data.drop('标识符', axis=1)
            new_data_df = new_data_df.drop('标识符', axis=1)

            # 合并新数据和旧的唯一数据
            all_data_df = pd.concat([new_data_df, old_unique_data], ignore_index=True)
            print(f"合并后共有 {len(all_data_df)} 条记录")
        else:
            # 如果没有日期列，直接合并
            # 标记需要更新的记录
            new_data_df['需要更新'] = True
            existing_df['需要更新'] = False

            print(f"标记了 {len(new_data_df)} 条需要更新的记录")

            all_data_df = pd.concat([existing_df, new_data_df], ignore_index=True)
            print(f"合并后共有 {len(all_data_df)} 条记录")
    else:
        # 如果没有现有数据，所有记录都需要更新
        new_data_df['需要更新'] = True
        all_data_df = new_data_df
        print(f"没有现有数据，使用 {len(all_data_df)} 条新记录")
        print(f"标记了 {len(all_data_df)} 条需要更新的记录")

    # 重新计算所有特征
    print("重新计算所有特征...")

    # 处理技术指标列，将其拆分为多个布尔列
    if '技术指标' in all_data_df.columns:
        # 创建新的布尔列
        indicators = [
            '均线多头排列', '成交量放大', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破'
        ]

        # 只处理需要更新的记录
        update_mask = all_data_df['需要更新'] == True
        update_count = update_mask.sum()
        print(f"处理 {update_count} 条需要更新的记录的技术指标...")

        # 初始化列（对所有记录）
        for indicator in indicators:
            if f'技术指标_{indicator}' not in all_data_df.columns:
                all_data_df[f'技术指标_{indicator}'] = 0

        # 更新需要更新的记录
        for indicator in indicators:
            all_data_df.loc[update_mask, f'技术指标_{indicator}'] = all_data_df.loc[update_mask, '技术指标'].str.contains(indicator).astype(int)

        print(f"已将技术指标列拆分为 {len(indicators)} 个布尔列")

    # 添加买入日开盘涨跌幅和卖出日开盘涨跌幅列
    if '涨跌幅' in all_data_df.columns:
        # 只处理需要更新的记录
        update_mask = all_data_df['需要更新'] == True
        update_count = update_mask.sum()
        print(f"处理 {update_count} 条需要更新的记录的买入日开盘涨跌幅...")

        # 初始化列（如果不存在）
        if '买入日开盘涨跌幅' not in all_data_df.columns:
            all_data_df['买入日开盘涨跌幅'] = 0

        # 将开盘涨跌改名为买入日开盘涨跌幅（只更新需要更新的记录）
        all_data_df.loc[update_mask, '买入日开盘涨跌幅'] = (all_data_df.loc[update_mask, '涨跌幅'] > 0).astype(int)
        print("已添加买入日开盘涨跌幅列")

        # 添加卖出日开盘涨跌幅列
        if not history_df.empty and 'code' in history_df.columns and 'date' in history_df.columns:
            print("计算卖出日开盘涨跌幅...")

            # 处理股票代码格式
            def format_stock_code(code):
                if code.startswith('sh.') or code.startswith('sz.'):
                    return code
                elif code.startswith('6'):
                    return f"sh.{code}"
                elif code.startswith('0') or code.startswith('3'):
                    return f"sz.{code}"
                else:
                    return code

            # 为每条记录创建历史数据格式的股票代码
            all_data_df['历史股票代码'] = all_data_df['股票代码'].apply(format_stock_code)

            # 添加日内股票标记列和卖出日开盘涨跌幅列
            print("批量计算日内股票标记和卖出日开盘涨跌幅...")

            # 只处理需要更新的记录
            update_mask = all_data_df['需要更新'] == True
            update_count = update_mask.sum()
            print(f"处理 {update_count} 条需要更新的记录的日内股票标记和卖出日开盘涨跌幅...")

            # 初始化列（如果不存在）
            if '日内股票标记' not in all_data_df.columns:
                all_data_df['日内股票标记'] = '000'
            if '卖出日开盘涨跌幅' not in all_data_df.columns:
                all_data_df['卖出日开盘涨跌幅'] = 0

            # 创建日期到下一个交易日的映射
            print("创建日期到下一个交易日的映射...")
            unique_dates = sorted(history_df['date'].unique())
            next_trading_day_map = {}

            for i in range(len(unique_dates) - 1):
                next_trading_day_map[unique_dates[i]] = unique_dates[i + 1]

            # 准备历史数据
            print("准备历史数据...")

            # 创建股票代码和日期的索引，加速查询
            history_df_indexed = history_df.set_index(['code', 'date'])

            # 计算历史数据中的涨跌幅
            if 'open' in history_df.columns and 'high' in history_df.columns and 'pctChg' in history_df.columns:
                # 收盘价涨跌幅就是当日的股票涨跌幅
                history_df['close_pct_chg'] = history_df['pctChg']

                # 计算开盘价和最高价的涨跌幅
                if 'preclose' in history_df.columns:
                    # 如果有前一日收盘价，直接计算
                    history_df['open_pct_chg'] = (history_df['open'] / history_df['preclose'] - 1) * 100
                    history_df['high_pct_chg'] = (history_df['high'] / history_df['preclose'] - 1) * 100
                else:
                    # 如果没有前一日收盘价，根据当日涨跌幅和收盘价推算
                    history_df['preclose'] = history_df['close'] / (1 + history_df['pctChg'] / 100)
                    history_df['open_pct_chg'] = (history_df['open'] / history_df['preclose'] - 1) * 100
                    history_df['high_pct_chg'] = (history_df['high'] / history_df['preclose'] - 1) * 100

                # 创建一个包含所有必要信息的DataFrame
                history_info = history_df[['code', 'date', 'open_pct_chg', 'high_pct_chg', 'close_pct_chg', 'pctChg']].copy()

                # 添加下一个交易日信息
                history_info['next_date'] = history_info['date'].map(next_trading_day_map)

                # 创建用于合并的键
                history_info['merge_key'] = history_info['code'] + '_' + history_info['date'].dt.strftime('%Y-%m-%d')

                # 创建用于查找下一个交易日数据的键
                history_info['next_key'] = history_info['code'] + '_' + history_info['next_date'].dt.strftime('%Y-%m-%d')

                # 创建一个映射，用于查找下一个交易日的涨跌幅
                next_day_pct_chg_map = history_info.set_index('merge_key')['pctChg'].to_dict()

                # 在all_data_df中创建合并键（只为需要更新的记录）
                all_data_df.loc[update_mask, 'merge_key'] = all_data_df.loc[update_mask, '历史股票代码'] + '_' + all_data_df.loc[update_mask, '日期'].dt.strftime('%Y-%m-%d')

                # 批量获取当天的涨跌幅数据（只为需要更新的记录）
                all_data_df.loc[update_mask, 'open_pct_chg'] = all_data_df.loc[update_mask, 'merge_key'].map(history_info.set_index('merge_key')['open_pct_chg'])
                all_data_df.loc[update_mask, 'high_pct_chg'] = all_data_df.loc[update_mask, 'merge_key'].map(history_info.set_index('merge_key')['high_pct_chg'])
                all_data_df.loc[update_mask, 'close_pct_chg'] = all_data_df.loc[update_mask, 'merge_key'].map(history_info.set_index('merge_key')['close_pct_chg'])

                # 批量获取下一个交易日的涨跌幅（只为需要更新的记录）
                all_data_df.loc[update_mask, 'next_key'] = all_data_df.loc[update_mask, '历史股票代码'] + '_' + all_data_df.loc[update_mask, '日期'].apply(
                    lambda x: next_trading_day_map.get(x, x).strftime('%Y-%m-%d') if x in next_trading_day_map else '')
                all_data_df.loc[update_mask, 'next_day_pct_chg'] = all_data_df.loc[update_mask, 'next_key'].map(next_day_pct_chg_map)

                # 计算卖出日开盘涨跌幅（只为需要更新的记录）
                all_data_df.loc[update_mask, '卖出日开盘涨跌幅'] = (all_data_df.loc[update_mask, 'next_day_pct_chg'] > 0).astype(int)

                # 填充缺失值（只为需要更新的记录）
                all_data_df.loc[update_mask, '卖出日开盘涨跌幅'] = all_data_df.loc[update_mask, '卖出日开盘涨跌幅'].fillna(0).astype(int)

                # 批量计算日内股票标记
                print("批量计算日内股票标记...")

                # 定义函数，根据涨跌幅计算编码
                def get_open_code(pct):
                    if pct > 5:
                        return '8'
                    elif 0 <= pct <= 5:
                        return '6'
                    elif -2.5 <= pct < 0:
                        return '4'
                    elif -6 <= pct < -2.5:
                        return '3'
                    elif -40 <= pct < -6:
                        return '2'
                    else:
                        return '0'

                def get_high_code(pct):
                    if pct > 7:
                        return '8'
                    elif 0 <= pct <= 7:
                        return '6'
                    elif -2.5 <= pct < 0:
                        return '4'
                    elif -6 <= pct < -2.5:
                        return '3'
                    elif -40 <= pct < -6:
                        return '2'
                    else:
                        return '0'

                def get_close_code(close_pct, high_pct):
                    if high_pct > 7 and close_pct > high_pct / 2:
                        return '8'
                    elif close_pct > 0:
                        return '6'
                    elif -2.5 <= close_pct < 0:
                        return '4'
                    elif -6 <= close_pct < -2.5:
                        return '3'
                    elif -40 <= close_pct < -6:
                        return '2'
                    else:
                        return '0'

                # 应用函数计算编码（只为需要更新的记录）
                all_data_df.loc[update_mask, 'open_code'] = all_data_df.loc[update_mask, 'open_pct_chg'].apply(get_open_code)
                all_data_df.loc[update_mask, 'high_code'] = all_data_df.loc[update_mask, 'high_pct_chg'].apply(get_high_code)
                all_data_df.loc[update_mask, 'close_code'] = all_data_df.loc[update_mask].apply(lambda x: get_close_code(x['close_pct_chg'], x['high_pct_chg']), axis=1)

                # 组合三位编码（只为需要更新的记录）
                all_data_df.loc[update_mask, '日内股票标记'] = all_data_df.loc[update_mask, 'open_code'] + all_data_df.loc[update_mask, 'high_code'] + all_data_df.loc[update_mask, 'close_code']

                # 删除临时列
                all_data_df = all_data_df.drop(['merge_key', 'next_key', 'open_pct_chg', 'high_pct_chg', 'close_pct_chg',
                                               'next_day_pct_chg', 'open_code', 'high_code', 'close_code'], axis=1)

                print("已批量计算日内股票标记和卖出日开盘涨跌幅")
            else:
                print("警告: 历史数据中缺少必要的列(open, high, pctChg)，无法计算涨跌幅")
                # 设置默认值
                all_data_df['日内股票标记'] = '666'  # 默认为中性状态
                all_data_df['卖出日开盘涨跌幅'] = 0

            # 删除临时列
            all_data_df = all_data_df.drop('历史股票代码', axis=1)

            print("已计算日内股票标记和卖出日开盘涨跌幅")
        else:
            print("警告: 历史数据不完整，无法计算卖出日开盘涨跌幅")

    # 添加技术强度相关列
    if '技术强度' in all_data_df.columns:
        # 只处理需要更新的记录
        update_mask = all_data_df['需要更新'] == True
        update_count = update_mask.sum()
        print(f"处理 {update_count} 条需要更新的记录的连续技术强度...")

        # 确保技术强度列是数值类型
        all_data_df['技术强度'] = pd.to_numeric(all_data_df['技术强度'], errors='coerce')

        # 按股票代码和日期排序
        all_data_df = all_data_df.sort_values(['股票代码', '日期'])

        # 计算连续技术强度
        print("批量计算连续技术强度...")

        # 初始化连续技术强度列（不包括1天数）
        for days in [3, 5, 10]:
            if f'连续技术强度{days}天数' not in all_data_df.columns:
                all_data_df[f'连续技术强度{days}天数'] = np.nan

        # 使用groupby和rolling计算连续技术强度
        # 为每个股票创建一个组
        all_data_df['group_key'] = all_data_df['股票代码']

        # 获取需要更新的股票代码列表
        update_stocks = all_data_df.loc[update_mask, '股票代码'].unique()
        print(f"需要更新的股票数量: {len(update_stocks)}")

        # 使用更高效的批量处理方法计算连续技术强度
        print("使用批量处理方法计算连续技术强度...")

        # 按股票代码分组，使用transform进行批量计算
        for days in [3, 5, 10]:
            # 使用transform确保结果与原始DataFrame对齐
            all_data_df[f'连续技术强度{days}天数'] = all_data_df.groupby('股票代码')['技术强度'].transform(
                lambda x: x.rolling(window=days, min_periods=1).sum()
            )

            print(f"已计算连续技术强度{days}天数")

        # 删除临时列
        all_data_df = all_data_df.drop('group_key', axis=1)

        print("已批量计算连续技术强度")

        # 计算连续技术强度趋势和价格趋势
        print("批量计算连续技术强度趋势和价格趋势...")

        # 使用历史数据计算趋势
        if not history_df.empty and 'code' in history_df.columns and 'date' in history_df.columns:
            # 处理股票代码格式
            def format_stock_code(code):
                if code.startswith('sh.') or code.startswith('sz.'):
                    return code
                elif code.startswith('6'):
                    return f"sh.{code}"
                elif code.startswith('0') or code.startswith('3'):
                    return f"sz.{code}"
                else:
                    return code

            # 为每条记录创建历史数据格式的股票代码
            if 'merge_key' not in all_data_df.columns:
                all_data_df['历史股票代码'] = all_data_df['股票代码'].apply(format_stock_code)
                all_data_df['merge_key'] = all_data_df['历史股票代码'] + '_' + all_data_df['日期'].dt.strftime('%Y-%m-%d')

            # 初始化趋势列
            for days in [3, 5, 10]:
                all_data_df[f'连续技术强度{days}天数趋势'] = np.nan
                all_data_df[f'连续技术强度{days}天数价格趋势'] = np.nan

            # 准备历史价格数据
            print("准备历史价格数据...")

            # 优化1: 只处理需要更新的股票的历史数据
            update_stocks = all_data_df.loc[update_mask, '历史股票代码'].unique()
            print(f"需要处理的股票数量: {len(update_stocks)}")

            if 'close' in history_df.columns:
                # 优化2: 只处理需要的股票数据
                filtered_history_df = history_df[history_df['code'].isin(update_stocks)].copy()
                print(f"过滤后的历史数据记录数: {len(filtered_history_df)}")

                # 使用批量处理方法计算价格趋势
                print("使用批量处理方法计算价格趋势...")

                # 优化3: 按股票代码和日期排序
                filtered_history_df = filtered_history_df.sort_values(['code', 'date'])

                # 使用交易日计算价格趋势
                print("使用交易日计算价格趋势...")

                # 为每个股票单独计算价格趋势
                for code, group in filtered_history_df.groupby('code'):
                    # 按日期排序
                    group = group.sort_values('date')

                    # 对每个天数计算价格趋势
                    for days in [3, 5, 10]:
                        # 创建一个新的Series来存储价格趋势
                        trend_series = pd.Series(index=group.index)

                        # 对每条记录计算与N个交易日前的价格变化
                        for i in range(len(group)):
                            if i >= days:  # 如果有足够的历史数据
                                prev_close = group['close'].iloc[i-days]
                                if prev_close > 0:  # 避免除以零
                                    trend_series.iloc[i] = (group['close'].iloc[i] - prev_close) / prev_close * 100
                            else:  # 如果历史数据不够，使用最早的可用数据
                                prev_close = group['close'].iloc[0]
                                if prev_close > 0:  # 避免除以零
                                    trend_series.iloc[i] = (group['close'].iloc[i] - prev_close) / prev_close * 100

                        # 将结果添加到原始DataFrame
                        filtered_history_df.loc[group.index, f'price_trend_{days}d'] = trend_series

                # 创建用于合并的键
                filtered_history_df['merge_key'] = filtered_history_df['code'] + '_' + filtered_history_df['date'].dt.strftime('%Y-%m-%d')

                # 创建映射字典（将价格趋势转换为布尔值：1表示上升，0表示下降或不变）
                price_trend_maps = {}
                for days in [3, 5, 10]:
                    # 只包含非NaN的值
                    valid_data = filtered_history_df[~filtered_history_df[f'price_trend_{days}d'].isna()]
                    # 将价格趋势转换为布尔值（1表示上升，0表示下降或不变）
                    price_trend_bool = (valid_data[f'price_trend_{days}d'] > 0).astype(int)
                    price_trend_maps[days] = dict(zip(valid_data['merge_key'], price_trend_bool))
                    print(f"创建了 {len(price_trend_maps[days])} 条价格趋势{days}天数映射（布尔值：1=上升，0=下降或不变）")

                # 优化5: 使用映射进行批量更新
                all_data_df.loc[update_mask, 'merge_key'] = all_data_df.loc[update_mask, '历史股票代码'] + '_' + all_data_df.loc[update_mask, '日期'].dt.strftime('%Y-%m-%d')

                for days in [3, 5, 10]:
                    # 初始化列（如果不存在）
                    if f'连续技术强度{days}天数价格趋势' not in all_data_df.columns:
                        all_data_df[f'连续技术强度{days}天数价格趋势'] = np.nan

                    # 使用map进行批量更新
                    all_data_df.loc[update_mask, f'连续技术强度{days}天数价格趋势'] = all_data_df.loc[update_mask, 'merge_key'].map(price_trend_maps[days])
                    print(f"已批量更新连续技术强度{days}天数价格趋势（布尔值：1=上升，0=下降或不变）")

                # 使用批量处理方法计算技术强度趋势
                print("使用批量处理方法计算技术强度趋势...")

                # 按股票代码分组，使用transform进行批量计算
                for days in [3, 5, 10]:
                    # 初始化列（如果不存在）
                    if f'连续技术强度{days}天数趋势' not in all_data_df.columns:
                        all_data_df[f'连续技术强度{days}天数趋势'] = np.nan

                    # 使用交易日计算技术强度趋势（比较当前技术强度与N个交易日前的技术强度）
                    # 首先按股票代码分组，然后对每组单独处理
                    trend_diffs = []

                    for stock_code, group in all_data_df.groupby('股票代码'):
                        # 按日期排序
                        group = group.sort_values('日期')

                        # 创建一个新的Series来存储差值
                        diff_series = pd.Series(index=group.index)

                        # 对每条记录计算与N个交易日前的差值
                        for i in range(len(group)):
                            if i >= days:  # 如果有足够的历史数据
                                diff_series.iloc[i] = group['技术强度'].iloc[i] - group['技术强度'].iloc[i-days]
                            else:  # 如果历史数据不够，使用最早的可用数据
                                diff_series.iloc[i] = group['技术强度'].iloc[i] - group['技术强度'].iloc[0]

                        # 将结果添加到列表中
                        trend_diffs.append(diff_series)

                    # 合并所有结果
                    trend_diff = pd.concat(trend_diffs)

                    # 将差值转换为布尔值（1表示上升，0表示下降或不变）
                    all_data_df[f'连续技术强度{days}天数趋势'] = (trend_diff > 0).astype(int)

                    print(f"已计算连续技术强度{days}天数趋势（布尔值：1=上升，0=下降或不变）")

                # 创建趋势组合字段
                print("创建趋势组合字段...")

                # 初始化趋势组合字段
                all_data_df['趋势组合'] = ''

                # 构建6位编码（按顺序：3天技术强度趋势、3天价格趋势、5天技术强度趋势、5天价格趋势、10天技术强度趋势、10天价格趋势）
                trend_columns = [
                    '连续技术强度3天数趋势', '连续技术强度3天数价格趋势',
                    '连续技术强度5天数趋势', '连续技术强度5天数价格趋势',
                    '连续技术强度10天数趋势', '连续技术强度10天数价格趋势'
                ]

                # 确保所有趋势列都存在
                for col in trend_columns:
                    if col not in all_data_df.columns:
                        print(f"警告: 缺少 {col} 列，将使用默认值0")
                        all_data_df[col] = 0

                # 构建趋势组合编码
                for col in trend_columns:
                    # 将趋势值转换为字符串并添加到趋势组合字段
                    all_data_df['趋势组合'] = all_data_df['趋势组合'] + all_data_df[col].fillna(0).astype(int).astype(str)

                print(f"已创建趋势组合字段，编码长度: {len(trend_columns)}位")

                # 将趋势列添加到要移除的列表中
                columns_to_remove_trends = trend_columns.copy()
                print(f"将在保存前移除原始趋势列: {', '.join(columns_to_remove_trends)}")
                print("已批量计算连续技术强度趋势和价格趋势")
            else:
                print("警告: 历史数据中缺少close列，无法计算价格趋势")

            # 删除临时列
            if 'merge_key' in all_data_df.columns:
                all_data_df = all_data_df.drop('merge_key', axis=1)
            if '历史股票代码' in all_data_df.columns:
                all_data_df = all_data_df.drop('历史股票代码', axis=1)
        else:
            print("警告: 历史数据不完整，无法计算连续技术强度趋势和价格趋势")

    # 添加成交量比例列
    if '股票代码' in all_data_df.columns and '日期' in all_data_df.columns:
        # 只处理需要更新的记录
        update_mask = all_data_df['需要更新'] == True
        update_count = update_mask.sum()
        print(f"处理 {update_count} 条需要更新的记录的成交量比例...")

        # 初始化成交量比例列（如果不存在）
        if '成交量是前一日几倍' not in all_data_df.columns:
            all_data_df['成交量是前一日几倍'] = np.nan

        # 使用历史数据计算成交量比例
        if not history_df.empty and 'code' in history_df.columns and 'date' in history_df.columns and 'volume' in history_df.columns:
            print("使用历史数据计算成交量比例...")

            # 处理股票代码格式
            def format_stock_code(code):
                if code.startswith('sh.') or code.startswith('sz.'):
                    return code
                elif code.startswith('6'):
                    return f"sh.{code}"
                elif code.startswith('0') or code.startswith('3'):
                    return f"sz.{code}"
                else:
                    return code

            # 为需要更新的记录创建历史数据格式的股票代码
            all_data_df.loc[update_mask, '历史股票代码'] = all_data_df.loc[update_mask, '股票代码'].apply(format_stock_code)

            # 创建一个映射，用于快速查找历史数据
            print("创建历史数据映射...")
            history_volume_map = {}

            # 使用批量处理方法计算成交量比例
            print("使用批量处理方法计算成交量比例...")

            # 只处理需要的股票数据
            update_stock_codes = all_data_df.loc[update_mask, '历史股票代码'].unique()
            filtered_history_df = history_df[history_df['code'].isin(update_stock_codes)].copy()
            print(f"过滤后的历史数据记录数: {len(filtered_history_df)}")

            # 按股票代码和日期排序
            filtered_history_df = filtered_history_df.sort_values(['code', 'date'])

            # 一次性计算所有成交量比例
            filtered_history_df['volume_ratio'] = filtered_history_df.groupby('code')['volume'].transform(
                lambda x: x / x.shift(1)
            )

            # 创建用于合并的键
            filtered_history_df['merge_key'] = filtered_history_df['code'] + '_' + filtered_history_df['date'].dt.strftime('%Y-%m-%d')

            # 创建映射字典（只包含非NaN的值）
            valid_data = filtered_history_df[~filtered_history_df['volume_ratio'].isna()]
            history_volume_map = dict(zip(valid_data['merge_key'], valid_data['volume_ratio']))

            print(f"创建了 {len(history_volume_map)} 条历史成交量比例记录")

            # 为每条记录查找成交量比例
            print("为每条记录查找成交量比例...")

            # 创建关联键（只为需要更新的记录）
            all_data_df.loc[update_mask, '关联键'] = all_data_df.loc[update_mask, '历史股票代码'] + '_' + all_data_df.loc[update_mask, '日期'].dt.strftime('%Y-%m-%d')

            # 使用映射获取成交量比例（只为需要更新的记录）
            all_data_df.loc[update_mask, '成交量是前一日几倍'] = all_data_df.loc[update_mask, '关联键'].map(history_volume_map)

            # 填充缺失值（只为需要更新的记录）
            missing_count = all_data_df.loc[update_mask, '成交量是前一日几倍'].isna().sum()
            if missing_count > 0:
                print(f"警告: 有 {missing_count} 条需要更新的记录无法从历史数据中获取成交量比例")
                all_data_df.loc[update_mask, '成交量是前一日几倍'] = all_data_df.loc[update_mask, '成交量是前一日几倍'].fillna(1.0)

            # 删除临时列
            all_data_df = all_data_df.drop(['历史股票代码', '关联键'], axis=1)

            print("已使用历史数据计算成交量比例")
        else:
            print("警告: 历史数据不完整，将使用现有数据计算成交量比例")

            # 检查是否有成交量列
            if '成交量' not in all_data_df.columns:
                print("警告: 数据中没有'成交量'列，尝试查找其他可能的列名...")
                # 查找可能的成交量列
                volume_columns = [col for col in all_data_df.columns if '成交量' in col or 'volume' in col.lower()]
                if volume_columns:
                    print(f"找到可能的成交量列: {volume_columns}")
                    volume_column = volume_columns[0]
                else:
                    print("未找到成交量列，无法计算成交量比例")
                    # 设置默认值
                    all_data_df['成交量是前一日几倍'] = 1.0
                    print("已将成交量比例设置为默认值 1.0")

                    # 验证成交量比例列
                    print("验证成交量比例列...")
                    non_null_count = all_data_df['成交量是前一日几倍'].count()
                    print(f"成交量是前一日几倍: {non_null_count} 条非空记录 ({non_null_count/len(all_data_df)*100:.2f}%)")
            else:
                volume_column = '成交量'

            # 确保成交量列是数值类型
            all_data_df[volume_column] = pd.to_numeric(all_data_df[volume_column], errors='coerce')

            # 获取需要更新的股票代码列表
            update_stocks = all_data_df.loc[update_mask, '股票代码'].unique()
            print(f"需要更新的股票数量: {len(update_stocks)}")

            # 只为需要更新的股票计算成交量比例
            for stock_code in update_stocks:
                # 获取该股票的所有记录（包括不需要更新的记录）
                group = all_data_df[all_data_df['股票代码'] == stock_code].sort_values('日期')

                # 获取成交量值
                volume_values = group[volume_column].values

                # 计算成交量比例
                ratios = []
                for i in range(len(group)):
                    if i == 0 or pd.isna(volume_values[i-1]) or volume_values[i-1] == 0:
                        ratios.append(1.0)  # 第一条记录或前一条记录成交量为0，设置为默认值1.0
                    else:
                        ratios.append(volume_values[i] / volume_values[i-1])

                # 更新组内的成交量比例
                group['成交量是前一日几倍'] = ratios

                # 更新原始DataFrame（只更新需要更新的记录）
                update_indices = group.index[group.index.isin(all_data_df.loc[update_mask].index)]
                all_data_df.loc[update_indices, '成交量是前一日几倍'] = group.loc[update_indices, '成交量是前一日几倍']

            print("已计算成交量比例")

        # 确保成交量比例列已正确计算
        print("验证成交量比例列...")
        non_null_count = all_data_df['成交量是前一日几倍'].count()
        print(f"成交量是前一日几倍: {non_null_count} 条非空记录 ({non_null_count/len(all_data_df)*100:.2f}%)")

    # 移除多余字段
    columns_to_remove = [
        '看涨技术指标数量', '后一天结果', '后一天强度', '趋势.1', '状态',
        '成交量是前一日0.5倍', '成交量是前一日1.0倍', '成交量是前一日1.5倍',
        '成交量是前一日2.0倍', '成交量是前一日2.5倍', '成交量是前一日3.0倍',
        '成交量是前一日3.5倍', '是否盈利', '价格趋势', '涨跌幅趋势', '技术强度趋势',
        '连续技术强度1天数', '开盘涨跌'  # 移除连续技术强度1天数和旧的开盘涨跌列
    ]

    # 添加趋势列到要移除的列表中（如果存在）
    if 'columns_to_remove_trends' in locals():
        columns_to_remove.extend(columns_to_remove_trends)
        print(f"添加了 {len(columns_to_remove_trends)} 个趋势列到要移除的列表中")

    # 只移除存在的列
    columns_to_remove = [col for col in columns_to_remove if col in all_data_df.columns]

    if columns_to_remove:
        print(f"移除多余字段: {', '.join(columns_to_remove)}")
        all_data_df = all_data_df.drop(columns_to_remove, axis=1)

    # 将成交量是前一日几倍改为以0.5为单位，最大上限3.5
    if '成交量是前一日几倍' in all_data_df.columns:
        # 只处理需要更新的记录
        update_mask = all_data_df['需要更新'] == True
        update_count = update_mask.sum()
        print(f"处理 {update_count} 条需要更新的记录的成交量比例格式...")

        print("将成交量是前一日几倍改为以0.5为单位，最大上限3.5")
        # 将值四舍五入到最接近的0.5的倍数（只为需要更新的记录）
        all_data_df.loc[update_mask, '成交量是前一日几倍'] = (all_data_df.loc[update_mask, '成交量是前一日几倍'] / 0.5).round() * 0.5
        # 限制最大值为3.5（只为需要更新的记录）
        all_data_df.loc[update_mask, '成交量是前一日几倍'] = all_data_df.loc[update_mask, '成交量是前一日几倍'].clip(upper=3.5)
        print("已调整成交量是前一日几倍")

    # 将技术指标布尔列合并为一个6位编码的技术指标特征列
    indicator_columns = [
        '技术指标_均线多头排列', '技术指标_成交量放大', '技术指标_MACD金叉',
        '技术指标_RSI反弹', '技术指标_KDJ金叉', '技术指标_布林带突破'
    ]

    # 检查所有列是否存在
    if all(col in all_data_df.columns for col in indicator_columns):
        # 只处理需要更新的记录
        update_mask = all_data_df['需要更新'] == True
        update_count = update_mask.sum()
        print(f"处理 {update_count} 条需要更新的记录的技术指标特征...")

        print("将技术指标布尔列合并为一个6位编码的技术指标特征列")

        # 初始化技术指标特征列（如果不存在）
        if '技术指标特征' not in all_data_df.columns:
            all_data_df['技术指标特征'] = ''

        # 为需要更新的记录创建临时列
        all_data_df.loc[update_mask, '技术指标特征_temp'] = ''

        # 构建6位编码（只为需要更新的记录）
        for i, col in enumerate(indicator_columns):  # 使用所有6个指标
            # 将布尔值转换为0或1的字符串，并添加到技术指标特征临时列
            all_data_df.loc[update_mask, '技术指标特征_temp'] = all_data_df.loc[update_mask, '技术指标特征_temp'] + all_data_df.loc[update_mask, col].astype(str)

        # 更新技术指标特征列
        all_data_df.loc[update_mask, '技术指标特征'] = all_data_df.loc[update_mask, '技术指标特征_temp']

        # 删除临时列
        all_data_df = all_data_df.drop('技术指标特征_temp', axis=1)

        # 移除原始的技术指标布尔列
        all_data_df = all_data_df.drop(indicator_columns, axis=1)

        print("已创建技术指标特征列并移除原始技术指标布尔列")

    # 删除临时列
    if '需要更新' in all_data_df.columns:
        all_data_df = all_data_df.drop('需要更新', axis=1)
        print("已删除临时列 '需要更新'")

    # 使用合并后的数据
    combined_df = all_data_df
    print(f"最终数据共有 {len(combined_df)} 条记录")

    # 保存合并后的数据
    print(f"正在保存数据到 {output_file}...")
    # 先尝试关闭可能占用文件的程序
    import os
    try:
        # 在Windows上尝试关闭Excel进程
        os.system("taskkill /f /im excel.exe 2>nul")
        print("已尝试关闭Excel进程")
    except:
        print("尝试关闭Excel进程失败，继续执行")

    # 等待一段时间，确保文件被释放
    import time
    time.sleep(2)

    # 尝试保存到原文件
    try:
        combined_df.to_excel(output_file, index=False)
        print(f"已将合并后的数据保存到 {output_file}")
    except Exception as e:
        print(f"无法保存到 {output_file}，错误: {e}")
        print("尝试保存到备用文件...")
        try:
            combined_df.to_excel(backup_output_file, index=False)
            print(f"已将合并后的数据保存到 {backup_output_file}")

            # 尝试复制备用文件到原文件
            import shutil
            try:
                # 先删除原文件（如果存在）
                if os.path.exists(output_file):
                    os.remove(output_file)
                # 复制备用文件到原文件
                shutil.copy2(backup_output_file, output_file)
                print(f"已将备用文件复制到 {output_file}")
            except Exception as e2:
                print(f"无法复制备用文件到原文件，错误: {e2}")
                print(f"请手动将 {backup_output_file} 重命名为 {output_file}")
        except Exception as e3:
            print(f"无法保存到备用文件，错误: {e3}")
            print("无法保存数据，请确保没有程序正在使用Excel文件")

    # 验证所有特征列是否已正确计算
    print("\n验证特征列计算结果:")

    # 检查技术指标特征列
    if '技术指标特征' in combined_df.columns:
        non_null_count = combined_df['技术指标特征'].count()
        print(f"  技术指标特征: {non_null_count} 条非空记录 ({non_null_count/len(combined_df)*100:.2f}%)")

        # 检查编码长度
        code_length = combined_df['技术指标特征'].str.len().mean()
        print(f"  技术指标特征平均编码长度: {code_length:.2f}")
    else:
        print("  警告: 缺少 技术指标特征 列")

    # 检查趋势组合列
    if '趋势组合' in combined_df.columns:
        non_null_count = combined_df['趋势组合'].count()
        print(f"  趋势组合: {non_null_count} 条非空记录 ({non_null_count/len(combined_df)*100:.2f}%)")

        # 检查编码长度
        code_length = combined_df['趋势组合'].str.len().mean()
        print(f"  趋势组合平均编码长度: {code_length:.2f}")

        # 检查值分布（前10个最常见的编码）
        value_counts = combined_df['趋势组合'].value_counts().head(10)
        print(f"  趋势组合前10个最常见编码: {value_counts.to_dict()}")
    else:
        print("  警告: 缺少 趋势组合 列")

    # 检查连续技术强度列
    for days in [3, 5, 10]:
        col_name = f'连续技术强度{days}天数'
        if col_name in combined_df.columns:
            non_null_count = combined_df[col_name].count()
            print(f"  {col_name}: {non_null_count} 条非空记录 ({non_null_count/len(combined_df)*100:.2f}%)")
        else:
            print(f"  警告: 缺少 {col_name} 列")

        # 检查趋势列
        trend_col = f'连续技术强度{days}天数趋势'
        if trend_col in combined_df.columns:
            non_null_count = combined_df[trend_col].count()
            print(f"  {trend_col}: {non_null_count} 条非空记录 ({non_null_count/len(combined_df)*100:.2f}%)")
        else:
            print(f"  警告: 缺少 {trend_col} 列")

        # 检查价格趋势列
        price_trend_col = f'连续技术强度{days}天数价格趋势'
        if price_trend_col in combined_df.columns:
            non_null_count = combined_df[price_trend_col].count()
            print(f"  {price_trend_col}: {non_null_count} 条非空记录 ({non_null_count/len(combined_df)*100:.2f}%)")
        else:
            print(f"  警告: 缺少 {price_trend_col} 列")

    # 检查成交量比例列
    if '成交量是前一日几倍' in combined_df.columns:
        non_null_count = combined_df['成交量是前一日几倍'].count()
        print(f"  成交量是前一日几倍: {non_null_count} 条非空记录 ({non_null_count/len(combined_df)*100:.2f}%)")

        # 检查值分布
        value_counts = combined_df['成交量是前一日几倍'].value_counts().sort_index()
        print(f"  成交量是前一日几倍值分布: {value_counts.head(10).to_dict()}")
    else:
        print("  警告: 缺少 成交量是前一日几倍 列")

    # 检查买入日开盘涨跌幅
    if '买入日开盘涨跌幅' in combined_df.columns:
        non_null_count = combined_df['买入日开盘涨跌幅'].count()
        print(f"  买入日开盘涨跌幅: {non_null_count} 条非空记录 ({non_null_count/len(combined_df)*100:.2f}%)")

        # 检查值分布
        value_counts = combined_df['买入日开盘涨跌幅'].value_counts()
        print(f"  买入日开盘涨跌幅值分布: {value_counts.to_dict()}")
    else:
        print("  警告: 缺少 买入日开盘涨跌幅 列")

    # 检查卖出日开盘涨跌幅
    if '卖出日开盘涨跌幅' in combined_df.columns:
        non_null_count = combined_df['卖出日开盘涨跌幅'].count()
        print(f"  卖出日开盘涨跌幅: {non_null_count} 条非空记录 ({non_null_count/len(combined_df)*100:.2f}%)")

        # 检查值分布
        value_counts = combined_df['卖出日开盘涨跌幅'].value_counts()
        print(f"  卖出日开盘涨跌幅值分布: {value_counts.to_dict()}")
    else:
        print("  警告: 缺少 卖出日开盘涨跌幅 列")

    # 检查日内股票标记
    if '日内股票标记' in combined_df.columns:
        non_null_count = combined_df['日内股票标记'].count()
        print(f"  日内股票标记: {non_null_count} 条非空记录 ({non_null_count/len(combined_df)*100:.2f}%)")

        # 检查编码长度
        code_length = combined_df['日内股票标记'].str.len().mean()
        print(f"  日内股票标记平均编码长度: {code_length:.2f}")

        # 检查值分布（前10个最常见的编码）
        value_counts = combined_df['日内股票标记'].value_counts().head(10)
        print(f"  日内股票标记前10个最常见编码: {value_counts.to_dict()}")
    else:
        print("  警告: 缺少 日内股票标记 列")
else:
    print("没有找到新数据，不进行合并")

print("处理完成")
