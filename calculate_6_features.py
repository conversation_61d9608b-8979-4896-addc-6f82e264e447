import itertools

# 特征值数量
feature_counts = {
    '连续技术强度3天数': 4,
    '连续技术强度5天数': 4,
    '连续技术强度10天数': 3,
    '成交量是前一日几倍': 7,
    '技术指标特征': 20,
    '趋势组合': 16,
    '日内股票标记': 23
}

print("6个特征组合的数量计算（技术强度 + 其他5个特征）:")

# 从7个其他特征中选择5个的所有组合
other_features = list(feature_counts.keys())
total_6_features = 0

for i, combo in enumerate(itertools.combinations(other_features, 5), 1):
    combo_count = 1
    for feature in combo:
        combo_count *= feature_counts[feature]
    
    total_6_features += combo_count
    print(f"{i:2d}. {combo}")
    print(f"    组合数: {combo_count:,}")

print(f"\n单个技术强度的6个特征组合总数: {total_6_features:,}")
print(f"所有技术强度(6个)的6个特征组合总数: {total_6_features * 6:,}")

# 检查是否超过Excel限制
if total_6_features > 500000:
    print(f"\n⚠️  6个特征组合超过Excel限制(50万行)")
    num_files = (total_6_features + 499999) // 500000
    print(f"需要拆分成 {num_files} 个文件")
else:
    print(f"\n✅ 6个特征组合在Excel限制范围内")

if total_6_features * 6 > 500000:
    print(f"⚠️  所有技术强度的6个特征组合超过Excel限制")
    num_files_all = (total_6_features * 6 + 499999) // 500000
    print(f"需要拆分成 {num_files_all} 个文件")
