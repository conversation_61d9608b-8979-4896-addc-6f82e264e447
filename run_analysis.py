"""
股票高胜率策略分析工具

功能：
1. 整合股票数据
2. 训练机器学习模型
3. 回测高胜率策略
4. 生成股票推荐
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
import argparse
import integrate_stock_data
import stock_strategy

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header(title):
    """打印标题"""
    print("\n" + " " * 24 + title)
    print("=" * 80 + "\n")

def train_model(data_file):
    """训练机器学习模型"""
    print_header("训练机器学习模型")

    # 加载数据
    data = pd.read_excel(data_file)
    print(f"成功加载股票数据，共 {len(data)} 行记录")

    # 定义特征和目标变量
    features = [
        '技术强度', '连续技术强度5天数', '技术强度趋势', '价格趋势', '涨跌幅趋势',
        '技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹',
        '技术指标_KDJ金叉', '技术指标_布林带突破', '看涨技术指标数量'
    ]

    target = '是否盈利'

    # 检查特征和目标变量是否存在
    missing_features = [f for f in features if f not in data.columns]
    if missing_features:
        print(f"缺少以下特征: {missing_features}")
        return None

    if target not in data.columns:
        print(f"缺少目标变量: {target}")
        return None

    # 提取特征和目标变量
    X = data[features]
    y = data[target]

    # 处理缺失值
    X = X.fillna(0)

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # 标准化特征
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # 训练随机森林模型
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train_scaled, y_train)

    # 评估模型
    train_score = model.score(X_train_scaled, y_train)
    test_score = model.score(X_test_scaled, y_test)

    print(f"训练集准确率: {train_score:.4f}")
    print(f"测试集准确率: {test_score:.4f}")

    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': features,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)

    print("\n特征重要性:")
    for i, row in feature_importance.iterrows():
        print(f"{row['feature']}: {row['importance']:.4f}")

    # 保存模型
    model_dir = 'models'
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    model_path = os.path.join(model_dir, f"{datetime.now().strftime('%Y%m%d_%H%M%S')}.joblib")

    model_data = {
        'model': model,
        'scaler': scaler,
        'features': features,
        'train_score': train_score,
        'test_score': test_score,
        'feature_importance': feature_importance.to_dict('records')
    }

    joblib.dump(model_data, model_path)

    print(f"\n模型已保存到: {model_path}")

    return model_data

def backtest_strategy(data_file, model_data, start_date, end_date, output_file):
    """回测高胜率策略"""
    print_header("回测高胜率策略")

    # 加载数据
    data = pd.read_excel(data_file)
    print(f"成功加载股票数据，共 {len(data)} 行记录")

    # 确保日期列是datetime类型
    if '日期' in data.columns:
        data['日期'] = pd.to_datetime(data['日期'])

    # 回测策略
    result = stock_strategy.backtest_strategy(data, model_data, start_date, end_date, output_file)

    return result

def generate_recommendations(data_file, model_data, date, output_file):
    """生成股票推荐"""
    print_header("生成股票推荐")

    # 加载数据
    data = pd.read_excel(data_file)
    print(f"成功加载股票数据，共 {len(data)} 行记录")

    # 确保日期列是datetime类型
    if '日期' in data.columns:
        data['日期'] = pd.to_datetime(data['日期'])

    # 获取指定日期的数据
    date_obj = pd.to_datetime(date)
    current_data = data[data['日期'] == date_obj]

    if len(current_data) == 0:
        print(f"没有找到 {date} 的数据")
        return None

    # 获取模型信息
    model = model_data['model']
    scaler = model_data['scaler']
    features = model_data['features']

    # 提取特征
    X_pred = current_data[features]

    # 处理预测数据中的缺失值
    X_pred = X_pred.fillna(0)

    # 标准化特征
    X_pred_scaled = scaler.transform(X_pred)

    # 预测盈利概率
    pred_proba = model.predict_proba(X_pred_scaled)[:, 1]

    # 创建预测结果DataFrame
    predictions = pd.DataFrame({
        '日期': date,
        '股票代码': current_data['股票代码'].values,
        '股票名称': current_data['股票名称'].values,
        '技术强度': current_data['技术强度'].values,
        '连续技术强度5天数': current_data['连续技术强度5天数'].values,
        '技术强度趋势': current_data['技术强度趋势'].values,
        '价格趋势': current_data['价格趋势'].values,
        '涨跌幅趋势': current_data['涨跌幅趋势'].values,
        '涨跌幅': current_data['涨跌幅'].values,
        '技术指标_均线多头排列': current_data['技术指标_均线多头排列'].values,
        '技术指标_MACD金叉': current_data['技术指标_MACD金叉'].values,
        '技术指标_RSI反弹': current_data['技术指标_RSI反弹'].values,
        '技术指标_KDJ金叉': current_data['技术指标_KDJ金叉'].values,
        '技术指标_布林带突破': current_data['技术指标_布林带突破'].values,
        '看涨技术指标数量': current_data['看涨技术指标数量'].values,
        '开盘涨跌': current_data['开盘涨跌'].values,
        '预测盈利概率': pred_proba
    })

    # 应用高胜率组合策略
    strategy_stocks = stock_strategy.apply_high_win_rate_strategy(predictions)

    # 保存推荐结果
    if len(strategy_stocks) > 0:
        strategy_stocks.to_excel(output_file, index=False)
        print(f"推荐股票数: {len(strategy_stocks)}")
        print(f"推荐结果已保存到: {output_file}")

        # 显示推荐股票
        print("\n推荐股票:")
        for i, row in strategy_stocks.iterrows():
            print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 看涨技术指标数量={row['看涨技术指标数量']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
    else:
        print("没有推荐的股票")

    return strategy_stocks

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票高胜率策略分析工具')
    parser.add_argument('--integrate', action='store_true', help='整合股票数据')
    parser.add_argument('--train', action='store_true', help='训练机器学习模型')
    parser.add_argument('--backtest', action='store_true', help='回测高胜率策略')
    parser.add_argument('--recommend', action='store_true', help='生成股票推荐')
    parser.add_argument('--data_dir', type=str, default=r"E:\桌面\AI+BI\A股强势股选股系统_v1.0.0\A股强势股选股系统_v1.0.0", help='股票数据目录')
    parser.add_argument('--data_file', type=str, default='股票明细.xlsx', help='股票数据文件')
    parser.add_argument('--start_date', type=str, help='回测开始日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--end_date', type=str, help='回测结束日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--date', type=str, help='推荐日期 (格式: YYYY-MM-DD)')
    parser.add_argument('--output', type=str, help='输出文件')

    args = parser.parse_args()

    clear_screen()

    try:
        # 整合股票数据
        if args.integrate:
            print_header("整合股票数据")
            all_data = integrate_stock_data.integrate_stock_data(args.data_dir)
            processed_data = integrate_stock_data.preprocess_data(all_data)
            processed_data.to_excel(args.data_file, index=False)
            print(f"\n数据已保存到 {args.data_file}，共 {len(processed_data)} 行")

        # 训练机器学习模型
        if args.train:
            model_data = train_model(args.data_file)
        else:
            # 加载最新的模型
            model_dir = 'models'
            model_files = [f for f in os.listdir(model_dir) if f.endswith('.joblib')]
            if not model_files:
                print("没有找到模型文件，请先训练模型")
                return

            latest_model_file = max(model_files)
            model_path = os.path.join(model_dir, latest_model_file)

            model_data = joblib.load(model_path)
            print(f"成功加载模型 (训练时间: {latest_model_file.split('.')[0]})")

        # 回测高胜率策略
        if args.backtest:
            if not args.start_date or not args.end_date:
                print("回测需要指定开始日期和结束日期")
                return

            output_file = args.output if args.output else f"高胜率策略回测结果_{args.start_date}至{args.end_date}.txt"
            backtest_strategy(args.data_file, model_data, args.start_date, args.end_date, output_file)

        # 生成股票推荐
        if args.recommend:
            if not args.date:
                print("推荐需要指定日期")
                return

            output_file = args.output if args.output else f"高胜率策略推荐股票_{args.date}.xlsx"
            generate_recommendations(args.data_file, model_data, args.date, output_file)

    except Exception as e:
        print(f"执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
