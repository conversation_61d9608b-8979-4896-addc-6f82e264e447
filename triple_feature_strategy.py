#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
三特征组合策略
作者: Augment AI
版本: 1.0.0
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

# 创建结果目录
results_dir = 'triple_feature_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
        
        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")
        
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def triple_feature_strategy(data, date, tech_strength_threshold=85, 
                           consecutive_strength_threshold=400, 
                           bullish_indicators_threshold=4):
    """三特征组合策略: 技术强度 + 连续技术强度5天数 + 看涨技术指标数量"""
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['技术强度'] >= tech_strength_threshold) &
        (daily_data['连续技术强度5天数'] >= consecutive_strength_threshold) &
        (daily_data['看涨技术指标数量'] >= bullish_indicators_threshold)
    ]
    return selected

def backtest(data, start_date, end_date, tech_strength_threshold=85, 
             consecutive_strength_threshold=400, bullish_indicators_threshold=4, 
             initial_capital=10000):
    """回测策略"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    print(f"回测日期范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
    print(f"交易日数量: {len(trading_dates)}")
    
    # 初始化回测结果
    capital = initial_capital
    trades = []
    
    # 对每个交易日进行回测
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 应用策略
        recommended_stocks = triple_feature_strategy(
            data, current_date, 
            tech_strength_threshold, 
            consecutive_strength_threshold, 
            bullish_indicators_threshold
        )
        
        # 如果有推荐的股票，模拟买入
        if len(recommended_stocks) > 0:
            # 计算每只股票的资金分配
            capital_per_stock = capital / len(recommended_stocks)
            
            # 记录每只股票的买入和卖出情况
            for _, stock in recommended_stocks.iterrows():
                code = stock['股票代码']
                
                # 获取次日该股票数据（买入）
                next_day_data = data[(data['日期'] == next_date) & (data['股票代码'] == code)]
                
                if len(next_day_data) > 0:
                    # 获取次日涨跌幅（模拟买入后的收益）
                    next_day_change = next_day_data['涨跌幅'].values[0]
                    
                    # 计算收益
                    profit = capital_per_stock * next_day_change / 100
                    
                    # 记录交易
                    trades.append({
                        '日期': current_date.strftime('%Y-%m-%d'),
                        '次日': next_date.strftime('%Y-%m-%d'),
                        '股票代码': code,
                        '次日涨跌幅': next_day_change,
                        '投入资金': capital_per_stock,
                        '收益': profit,
                        '是否盈利': next_day_change > 0
                    })
    
    # 计算回测结果
    if trades:
        trades_df = pd.DataFrame(trades)
        total_profit = trades_df['收益'].sum()
        win_rate = trades_df['是否盈利'].mean() * 100
        avg_return = trades_df['次日涨跌幅'].mean()
        final_capital = initial_capital + total_profit
        total_return = (final_capital / initial_capital - 1) * 100
        
        # 计算每日收益率
        daily_trades_df = trades_df.groupby('日期').agg({
            '次日涨跌幅': 'mean',
            '是否盈利': 'mean',
            '收益': 'sum',
            '股票代码': 'count'
        }).reset_index()
        
        daily_trades_df.columns = ['日期', '平均涨跌幅', '胜率', '当日收益', '交易数量']
        daily_trades_df['胜率'] = daily_trades_df['胜率'] * 100
        daily_trades_df['累计收益'] = daily_trades_df['当日收益'].cumsum()
        daily_trades_df['累计收益率'] = daily_trades_df['累计收益'] / initial_capital * 100
        
        # 打印回测结果
        print("\n回测结果:")
        print(f"技术强度阈值: {tech_strength_threshold}")
        print(f"连续技术强度5天数阈值: {consecutive_strength_threshold}")
        print(f"看涨技术指标数量阈值: {bullish_indicators_threshold}")
        print(f"初始资金: {initial_capital:,.2f}元")
        print(f"最终资金: {final_capital:,.2f}元")
        print(f"总收益: {total_profit:,.2f}元")
        print(f"总收益率: {total_return:.2f}%")
        print(f"交易次数: {len(trades)}")
        print(f"胜率: {win_rate:.2f}%")
        print(f"平均涨跌幅: {avg_return:.2f}%")
        
        # 保存回测结果到文件
        output_file = f"{results_dir}/backtest_results_ts{tech_strength_threshold}_cs{consecutive_strength_threshold}_bi{bullish_indicators_threshold}.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"回测日期范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}\n")
            f.write(f"技术强度阈值: {tech_strength_threshold}\n")
            f.write(f"连续技术强度5天数阈值: {consecutive_strength_threshold}\n")
            f.write(f"看涨技术指标数量阈值: {bullish_indicators_threshold}\n")
            f.write(f"初始资金: {initial_capital:,.2f}元\n")
            f.write(f"最终资金: {final_capital:,.2f}元\n")
            f.write(f"总收益: {total_profit:,.2f}元\n")
            f.write(f"总收益率: {total_return:.2f}%\n")
            f.write(f"交易次数: {len(trades)}\n")
            f.write(f"胜率: {win_rate:.2f}%\n")
            f.write(f"平均涨跌幅: {avg_return:.2f}%\n\n")
            
            f.write("每日收益:\n")
            for _, row in daily_trades_df.iterrows():
                f.write(f"{row['日期']}: 交易数量={row['交易数量']}, 平均涨跌幅={row['平均涨跌幅']:.2f}%, 胜率={row['胜率']:.2f}%, 当日收益={row['当日收益']:.2f}元, 累计收益率={row['累计收益率']:.2f}%\n")
        
        print(f"\n回测结果已保存到 {output_file}")
        
        return {
            'tech_strength_threshold': tech_strength_threshold,
            'consecutive_strength_threshold': consecutive_strength_threshold,
            'bullish_indicators_threshold': bullish_indicators_threshold,
            'total_profit': total_profit,
            'total_return': total_return,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'trade_count': len(trades),
            'trades': trades,
            'daily_trades_df': daily_trades_df
        }
    else:
        print("\n回测结果: 无交易记录")
        return {
            'tech_strength_threshold': tech_strength_threshold,
            'consecutive_strength_threshold': consecutive_strength_threshold,
            'bullish_indicators_threshold': bullish_indicators_threshold,
            'total_profit': 0,
            'total_return': 0,
            'win_rate': 0,
            'avg_return': 0,
            'trade_count': 0,
            'trades': []
        }

def generate_recommendations(data, date, tech_strength_threshold=85, 
                            consecutive_strength_threshold=400, 
                            bullish_indicators_threshold=4):
    """生成股票推荐"""
    # 确保日期是datetime类型
    date = pd.to_datetime(date)
    
    # 应用策略
    recommended_stocks = triple_feature_strategy(
        data, date, 
        tech_strength_threshold, 
        consecutive_strength_threshold, 
        bullish_indicators_threshold
    )
    
    print(f"三特征组合策略:")
    print(f"技术强度阈值: {tech_strength_threshold}")
    print(f"连续技术强度5天数阈值: {consecutive_strength_threshold}")
    print(f"看涨技术指标数量阈值: {bullish_indicators_threshold}")
    print(f"日期: {date.strftime('%Y-%m-%d')}, 推荐股票数量: {len(recommended_stocks)}")
    
    # 保存推荐股票到文件
    output_file = f"{results_dir}/recommended_stocks_ts{tech_strength_threshold}_cs{consecutive_strength_threshold}_bi{bullish_indicators_threshold}_{date.strftime('%Y%m%d')}.csv"
    recommended_stocks.to_csv(output_file, index=False)
    
    print(f"推荐股票已保存到 {output_file}")
    
    # 打印推荐股票
    print("\n推荐股票列表:")
    for i, (_, stock) in enumerate(recommended_stocks.iterrows()):
        if i < 20:  # 只显示前20只
            print(f"{i+1}. {stock['股票代码']} {stock['股票名称']}, 技术强度: {stock['技术强度']}, 连续技术强度5天数: {stock['连续技术强度5天数']}, 看涨技术指标数量: {stock['看涨技术指标数量']}")
    
    if len(recommended_stocks) > 20:
        print(f"... 共 {len(recommended_stocks)} 只股票")
    
    return recommended_stocks

def grid_search(data, start_date, end_date):
    """网格搜索最优参数"""
    print("正在进行网格搜索...")
    
    # 定义参数网格
    tech_strength_values = [80, 85, 90, 95]
    consecutive_strength_values = [350, 400, 450, 500]
    bullish_indicators_values = [3, 4, 5]
    
    # 初始化结果
    results = []
    
    # 网格搜索
    for ts in tech_strength_values:
        for cs in consecutive_strength_values:
            for bi in bullish_indicators_values:
                print(f"\n测试参数: 技术强度>={ts}, 连续技术强度5天数>={cs}, 看涨技术指标数量>={bi}")
                result = backtest(data, start_date, end_date, ts, cs, bi)
                if result['trade_count'] > 0:
                    results.append(result)
    
    # 按总收益率排序
    results.sort(key=lambda x: x['total_return'], reverse=True)
    
    # 打印最佳参数
    print("\n最佳参数组合:")
    for i, result in enumerate(results[:5]):
        print(f"{i+1}. 技术强度>={result['tech_strength_threshold']}, 连续技术强度5天数>={result['consecutive_strength_threshold']}, 看涨技术指标数量>={result['bullish_indicators_threshold']}")
        print(f"   总收益率: {result['total_return']:.2f}%")
        print(f"   胜率: {result['win_rate']:.2f}%")
        print(f"   平均涨跌幅: {result['avg_return']:.2f}%")
        print(f"   交易次数: {result['trade_count']}")
        print("-" * 50)
    
    # 保存结果
    results_df = pd.DataFrame([{
        '技术强度阈值': result['tech_strength_threshold'],
        '连续技术强度5天数阈值': result['consecutive_strength_threshold'],
        '看涨技术指标数量阈值': result['bullish_indicators_threshold'],
        '总收益率': result['total_return'],
        '胜率': result['win_rate'],
        '平均涨跌幅': result['avg_return'],
        '交易次数': result['trade_count']
    } for result in results])
    
    results_df.to_csv(f"{results_dir}/grid_search_results.csv", index=False)
    print(f"网格搜索结果已保存到 {results_dir}/grid_search_results.csv")
    
    return results

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)
    
    # 获取最新日期
    latest_date = df['日期'].max()
    print(f"最新日期: {latest_date.strftime('%Y-%m-%d')}")
    
    # 网格搜索最优参数
    best_params = grid_search(df, '2025-04-01', '2025-04-30')
    
    # 使用最佳参数生成最新日期的股票推荐
    if best_params:
        best_result = best_params[0]
        print("\n使用最佳参数生成最新日期的股票推荐:")
        generate_recommendations(
            df, latest_date, 
            best_result['tech_strength_threshold'], 
            best_result['consecutive_strength_threshold'], 
            best_result['bullish_indicators_threshold']
        )
