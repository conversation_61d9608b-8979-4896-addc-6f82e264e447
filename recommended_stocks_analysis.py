import pandas as pd
import numpy as np
import os
import joblib
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

def analyze_recommended_stocks():
    """
    基于最优模型推荐股票，并分析推荐股票中技术强度为100的股票胜率
    """
    print("开始分析推荐股票的胜率...")
    
    # 创建结果目录
    if not os.path.exists('recommendation_results'):
        os.makedirs('recommendation_results')
    
    # 加载股票数据
    print("\n加载股票数据...")
    try:
        stock_data = pd.read_excel('股票明细.xlsx')
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
        
        # 转换日期格式
        if isinstance(stock_data['日期'].iloc[0], str):
            stock_data['日期'] = pd.to_datetime(stock_data['日期'])
        
        # 获取所有日期并排序
        all_dates = sorted(stock_data['日期'].unique())
        print(f"数据集中的日期范围: {all_dates[0]} 到 {all_dates[-1]}")
        
        # 按股票代码和日期排序
        stock_data = stock_data.sort_values(['股票代码', '日期'])
        
        # 数据预处理
        print("\n数据预处理...")
        
        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')
            
            # 计算连续技术强度天数（连续多少天为100）
            consecutive_days = []
            current_count = 0
            
            for strength in group['技术强度'].values:
                if strength == 100:
                    current_count += 1
                else:
                    current_count = 0
                consecutive_days.append(current_count)
            
            # 更新原始数据
            stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days
            
            # 计算9天技术强度累积值（最优周期）
            cumulative_strength = group['技术强度'].copy()
            for i in range(1, 9):
                cumulative_strength += group['技术强度'].shift(i).fillna(0)
            
            # 更新原始数据
            stock_data.loc[group.index, '连续技术强度9天数'] = cumulative_strength
            
            # 计算次日涨跌方向和收益率
            group['次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)
            group['次日收益率'] = (group['当前价格'].shift(-1) / group['当前价格'] - 1) * 100
            
            # 计算后日涨跌方向和收益率（买入后的第二天，即卖出日）
            group['后日涨跌方向'] = (group['当前价格'].shift(-2) > group['当前价格'].shift(-1)).astype(int)
            group['后日收益率'] = (group['当前价格'].shift(-2) / group['当前价格'].shift(-1) - 1) * 100
            
            # 计算两日收益率（买入后持有两天的总收益）
            group['两日收益率'] = (group['当前价格'].shift(-2) / group['当前价格'] - 1) * 100
            
            # 计算是否盈利（两日收益率为正）
            group['是否盈利'] = (group['两日收益率'] > 0).astype(int)
            
            # 更新原始数据
            stock_data.loc[group.index, '次日涨跌方向'] = group['次日涨跌方向']
            stock_data.loc[group.index, '次日收益率'] = group['次日收益率']
            stock_data.loc[group.index, '后日涨跌方向'] = group['后日涨跌方向']
            stock_data.loc[group.index, '后日收益率'] = group['后日收益率']
            stock_data.loc[group.index, '两日收益率'] = group['两日收益率']
            stock_data.loc[group.index, '是否盈利'] = group['是否盈利']
        
        # 删除没有完整数据的记录
        stock_data = stock_data.dropna(subset=['次日涨跌方向', '次日收益率', '后日涨跌方向', '后日收益率', '两日收益率', '是否盈利'])
        
        print(f"处理后的数据集大小: {len(stock_data)} 条记录")
        
        # 创建回测结果DataFrame
        backtest_results = pd.DataFrame(columns=['日期', '推荐股票数', '技术强度100股票数', '上涨股票数', '胜率', '平均收益率'])
        
        # 回测每个日期
        for i in range(len(all_dates) - 2):  # 需要有后两天的数据
            current_date = all_dates[i]
            next_date = all_dates[i + 1]
            after_next_date = all_dates[i + 2]
            
            print(f"\n回测日期: {current_date}")
            
            # 获取当前日期的数据
            current_data = stock_data[stock_data['日期'] == current_date]
            
            # 准备训练数据（使用当前日期之前的所有数据）
            train_data = stock_data[stock_data['日期'] < current_date]
            
            if len(train_data) < 1000:  # 确保有足够的训练数据
                print(f"训练数据不足，跳过日期 {current_date}")
                continue
            
            # 特征和目标变量
            features = ['技术强度', '连续技术强度9天数', '涨跌幅', '当前价格']
            X_train = train_data[features]
            y_train = train_data['是否盈利']
            
            # 处理缺失值
            valid_indices = ~X_train.isnull().any(axis=1)
            X_train = X_train[valid_indices]
            y_train = y_train[valid_indices]
            
            # 标准化特征
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            
            # 训练随机森林模型
            rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
            rf_model.fit(X_train_scaled, y_train)
            
            # 准备预测数据
            X_pred = current_data[features]
            
            # 处理预测数据中的缺失值
            valid_pred_indices = ~X_pred.isnull().any(axis=1)
            X_pred = X_pred[valid_pred_indices]
            current_data_filtered = current_data.loc[valid_pred_indices.index[valid_pred_indices]]
            
            if len(X_pred) == 0:
                print(f"预测数据不足，跳过日期 {current_date}")
                continue
            
            # 标准化预测数据
            X_pred_scaled = scaler.transform(X_pred)
            
            # 预测盈利概率
            pred_proba = rf_model.predict_proba(X_pred_scaled)[:, 1]
            
            # 创建预测结果DataFrame
            predictions = pd.DataFrame({
                '股票代码': current_data_filtered['股票代码'],
                '股票名称': current_data_filtered['股票名称'],
                '当前价格': current_data_filtered['当前价格'],
                '技术强度': current_data_filtered['技术强度'],
                '连续技术强度天数': current_data_filtered['连续技术强度天数'],
                '连续技术强度9天数': current_data_filtered['连续技术强度9天数'],
                '预测盈利概率': pred_proba,
                '实际是否盈利': current_data_filtered['是否盈利'],
                '两日收益率': current_data_filtered['两日收益率']
            })
            
            # 按预测盈利概率降序排序
            predictions = predictions.sort_values('预测盈利概率', ascending=False)
            
            # 选择预测盈利概率最高的前10%股票作为推荐
            top_percent = 0.1
            top_n = max(int(len(predictions) * top_percent), 10)  # 至少10只股票
            recommended_stocks = predictions.head(top_n)
            
            # 分析推荐股票中技术强度为100的股票
            strength_100_stocks = recommended_stocks[recommended_stocks['技术强度'] == 100]
            
            # 计算推荐股票的实际盈利情况
            up_count = sum(recommended_stocks['实际是否盈利'])
            total_count = len(recommended_stocks)
            success_rate = up_count / total_count if total_count > 0 else 0
            avg_return = recommended_stocks['两日收益率'].mean()
            
            # 计算技术强度为100的股票的实际盈利情况
            strength_100_up_count = sum(strength_100_stocks['实际是否盈利'])
            strength_100_count = len(strength_100_stocks)
            strength_100_success_rate = strength_100_up_count / strength_100_count if strength_100_count > 0 else 0
            strength_100_avg_return = strength_100_stocks['两日收益率'].mean() if strength_100_count > 0 else 0
            
            print(f"推荐股票数: {total_count}")
            print(f"推荐股票胜率: {success_rate:.4f} ({success_rate*100:.2f}%)")
            print(f"推荐股票平均收益率: {avg_return:.4f}%")
            print(f"技术强度为100的股票数: {strength_100_count}")
            if strength_100_count > 0:
                print(f"技术强度为100的股票胜率: {strength_100_success_rate:.4f} ({strength_100_success_rate*100:.2f}%)")
                print(f"技术强度为100的股票平均收益率: {strength_100_avg_return:.4f}%")
            
            # 添加到回测结果
            new_row = pd.DataFrame({
                '日期': [current_date],
                '推荐股票数': [total_count],
                '技术强度100股票数': [strength_100_count],
                '上涨股票数': [up_count],
                '胜率': [success_rate],
                '平均收益率': [avg_return],
                '技术强度100胜率': [strength_100_success_rate if strength_100_count > 0 else np.nan],
                '技术强度100平均收益率': [strength_100_avg_return if strength_100_count > 0 else np.nan]
            })
            backtest_results = pd.concat([backtest_results, new_row], ignore_index=True)
            
            # 保存当日推荐股票
            recommended_stocks.to_excel(f'recommendation_results/{current_date.strftime("%Y-%m-%d")}_推荐股票.xlsx', index=False)
        
        # 保存回测结果
        backtest_results.to_excel('recommendation_results/回测结果.xlsx', index=False)
        
        # 计算整体统计
        overall_success_rate = backtest_results['上涨股票数'].sum() / backtest_results['推荐股票数'].sum()
        overall_avg_return = backtest_results['平均收益率'].mean()
        
        # 计算技术强度为100的股票的整体统计
        strength_100_results = backtest_results.dropna(subset=['技术强度100胜率'])
        if len(strength_100_results) > 0:
            strength_100_overall_success_rate = (strength_100_results['技术强度100股票数'] * strength_100_results['技术强度100胜率']).sum() / strength_100_results['技术强度100股票数'].sum()
            strength_100_overall_avg_return = strength_100_results['技术强度100平均收益率'].mean()
        else:
            strength_100_overall_success_rate = np.nan
            strength_100_overall_avg_return = np.nan
        
        print("\n整体回测结果:")
        print(f"回测天数: {len(backtest_results)}")
        print(f"推荐股票总数: {backtest_results['推荐股票数'].sum()}")
        print(f"整体胜率: {overall_success_rate:.4f} ({overall_success_rate*100:.2f}%)")
        print(f"平均每日收益率: {overall_avg_return:.4f}%")
        
        if not np.isnan(strength_100_overall_success_rate):
            print(f"\n技术强度为100的股票整体回测结果:")
            print(f"技术强度为100的股票总数: {backtest_results['技术强度100股票数'].sum()}")
            print(f"整体胜率: {strength_100_overall_success_rate:.4f} ({strength_100_overall_success_rate*100:.2f}%)")
            print(f"平均每日收益率: {strength_100_overall_avg_return:.4f}%")
        
        # 绘制胜率随时间变化图表
        plt.figure(figsize=(12, 6))
        plt.plot(backtest_results['日期'], backtest_results['胜率'], marker='o', linestyle='-', linewidth=2, label='所有推荐股票')
        if len(strength_100_results) > 0:
            plt.plot(strength_100_results['日期'], strength_100_results['技术强度100胜率'], marker='x', linestyle='--', linewidth=2, color='red', label='技术强度100股票')
        plt.title('推荐股票胜率随时间变化')
        plt.xlabel('日期')
        plt.ylabel('胜率')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('recommendation_results/胜率随时间变化.png')
        
        # 绘制收益率随时间变化图表
        plt.figure(figsize=(12, 6))
        plt.plot(backtest_results['日期'], backtest_results['平均收益率'], marker='o', linestyle='-', linewidth=2, label='所有推荐股票')
        if len(strength_100_results) > 0:
            plt.plot(strength_100_results['日期'], strength_100_results['技术强度100平均收益率'], marker='x', linestyle='--', linewidth=2, color='red', label='技术强度100股票')
        plt.title('推荐股票平均收益率随时间变化')
        plt.xlabel('日期')
        plt.ylabel('平均收益率 (%)')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('recommendation_results/收益率随时间变化.png')
        
        print("\n分析完成！结果已保存至 recommendation_results 目录")
        
        return backtest_results
    
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    analyze_recommended_stocks()
