#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最佳特征组合策略测试
作者: Augment AI
版本: 1.0.0
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

# 创建结果目录
results_dir = 'best_strategies_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
        
        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")
        
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def strategy_1(data, date):
    """策略1: 连续技术强度5天数涨跌幅趋势==1 & 技术指标_RSI反弹==1"""
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['连续技术强度5天数涨跌幅趋势'] == 1) &
        (daily_data['技术指标_RSI反弹'] == 1)
    ]
    return selected

def strategy_2(data, date):
    """策略2: 连续技术强度3天数>=75 & 连续技术强度5天数价格趋势==1"""
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['连续技术强度3天数'] >= 75) &
        (daily_data['连续技术强度5天数价格趋势'] == 1)
    ]
    return selected

def strategy_3(data, date):
    """策略3: 技术强度>=70 & 技术强度趋势==1"""
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['技术强度'] >= 70) &
        (daily_data['技术强度趋势'] == 1)
    ]
    return selected

def strategy_4(data, date):
    """策略4: 连续技术强度5天数价格趋势==1 & 技术指标_RSI反弹==1"""
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['连续技术强度5天数价格趋势'] == 1) &
        (daily_data['技术指标_RSI反弹'] == 1)
    ]
    return selected

def strategy_5(data, date):
    """策略5: 连续技术强度10天数>=70 & 技术指标_RSI反弹==1"""
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['连续技术强度10天数'] >= 70) &
        (daily_data['技术指标_RSI反弹'] == 1)
    ]
    return selected

def strategy_6(data, date):
    """策略6: 技术强度>=85 & 技术指标_KDJ金叉==1"""
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['技术强度'] >= 85) &
        (daily_data['技术指标_KDJ金叉'] == 1)
    ]
    return selected

def strategy_7(data, date):
    """策略7: 连续技术强度5天数>=285 & 涨跌幅趋势>=1 (来自之前的双特征组合策略)"""
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['连续技术强度5天数'] >= 285) &
        (daily_data['涨跌幅趋势'] >= 1)
    ]
    return selected

def strategy_8(data, date):
    """策略8: 技术强度>=80 & 连续技术强度5天数>=350 & 看涨技术指标数量>=5 (来自之前的三特征组合策略)"""
    daily_data = data[data['日期'] == date]
    selected = daily_data[
        (daily_data['技术强度'] >= 80) &
        (daily_data['连续技术强度5天数'] >= 350) &
        (daily_data['看涨技术指标数量'] >= 5)
    ]
    return selected

def backtest(data, strategy_fn, start_date, end_date, initial_capital=10000, strategy_name="未命名策略"):
    """回测策略"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    print(f"回测策略: {strategy_name}")
    print(f"回测日期范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
    print(f"交易日数量: {len(trading_dates)}")
    
    # 初始化回测结果
    capital = initial_capital
    trades = []
    daily_results = []
    
    # 对每个交易日进行回测
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 应用策略
        recommended_stocks = strategy_fn(data, current_date)
        
        # 记录每日推荐股票
        daily_results.append({
            '日期': current_date.strftime('%Y-%m-%d'),
            '推荐股票数量': len(recommended_stocks)
        })
        
        # 如果有推荐的股票，模拟买入
        if len(recommended_stocks) > 0:
            # 计算每只股票的资金分配
            capital_per_stock = capital / len(recommended_stocks)
            
            # 记录每只股票的买入和卖出情况
            for _, stock in recommended_stocks.iterrows():
                code = stock['股票代码']
                name = stock['股票名称']
                
                # 获取次日该股票数据（买入）
                next_day_data = data[(data['日期'] == next_date) & (data['股票代码'] == code)]
                
                if len(next_day_data) > 0:
                    # 获取次日涨跌幅（模拟买入后的收益）
                    next_day_change = next_day_data['涨跌幅'].values[0]
                    
                    # 计算收益
                    profit = capital_per_stock * next_day_change / 100
                    
                    # 记录交易
                    trades.append({
                        '日期': current_date.strftime('%Y-%m-%d'),
                        '次日': next_date.strftime('%Y-%m-%d'),
                        '股票代码': code,
                        '股票名称': name,
                        '次日涨跌幅': next_day_change,
                        '投入资金': capital_per_stock,
                        '收益': profit,
                        '是否盈利': next_day_change > 0
                    })
    
    # 计算回测结果
    if trades:
        trades_df = pd.DataFrame(trades)
        total_profit = trades_df['收益'].sum()
        win_rate = trades_df['是否盈利'].mean() * 100
        avg_return = trades_df['次日涨跌幅'].mean()
        final_capital = initial_capital + total_profit
        total_return = (final_capital / initial_capital - 1) * 100
        
        # 计算每日收益率
        daily_trades_df = trades_df.groupby('日期').agg({
            '次日涨跌幅': 'mean',
            '是否盈利': 'mean',
            '收益': 'sum',
            '股票代码': 'count'
        }).reset_index()
        
        daily_trades_df.columns = ['日期', '平均涨跌幅', '胜率', '当日收益', '交易数量']
        daily_trades_df['胜率'] = daily_trades_df['胜率'] * 100
        daily_trades_df['累计收益'] = daily_trades_df['当日收益'].cumsum()
        daily_trades_df['累计收益率'] = daily_trades_df['累计收益'] / initial_capital * 100
        
        # 打印回测结果
        print(f"初始资金: {initial_capital:,.2f}元")
        print(f"最终资金: {final_capital:,.2f}元")
        print(f"总收益: {total_profit:,.2f}元")
        print(f"总收益率: {total_return:.2f}%")
        print(f"交易次数: {len(trades)}")
        print(f"胜率: {win_rate:.2f}%")
        print(f"平均涨跌幅: {avg_return:.2f}%")
        print("-" * 50)
        
        # 保存回测结果到文件
        output_file = f"{results_dir}/{strategy_name.replace(' ', '_')}_回测结果.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"策略: {strategy_name}\n")
            f.write(f"回测日期范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}\n")
            f.write(f"初始资金: {initial_capital:,.2f}元\n")
            f.write(f"最终资金: {final_capital:,.2f}元\n")
            f.write(f"总收益: {total_profit:,.2f}元\n")
            f.write(f"总收益率: {total_return:.2f}%\n")
            f.write(f"交易次数: {len(trades)}\n")
            f.write(f"胜率: {win_rate:.2f}%\n")
            f.write(f"平均涨跌幅: {avg_return:.2f}%\n\n")
            
            f.write("每日收益:\n")
            for _, row in daily_trades_df.iterrows():
                f.write(f"{row['日期']}: 交易数量={row['交易数量']}, 平均涨跌幅={row['平均涨跌幅']:.2f}%, 胜率={row['胜率']:.2f}%, 当日收益={row['当日收益']:.2f}元, 累计收益率={row['累计收益率']:.2f}%\n")
        
        return {
            'strategy_name': strategy_name,
            'total_profit': total_profit,
            'total_return': total_return,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'trade_count': len(trades),
            'trades': trades,
            'daily_trades_df': daily_trades_df
        }
    else:
        print(f"回测结果: 无交易记录")
        print("-" * 50)
        return {
            'strategy_name': strategy_name,
            'total_profit': 0,
            'total_return': 0,
            'win_rate': 0,
            'avg_return': 0,
            'trade_count': 0,
            'trades': []
        }

def generate_recommendations(data, strategy_fn, date, strategy_name):
    """生成股票推荐"""
    # 确保日期是datetime类型
    date = pd.to_datetime(date)
    
    # 应用策略
    recommended_stocks = strategy_fn(data, date)
    
    print(f"策略: {strategy_name}")
    print(f"日期: {date.strftime('%Y-%m-%d')}, 推荐股票数量: {len(recommended_stocks)}")
    
    # 保存推荐股票到文件
    output_file = f"{results_dir}/{strategy_name.replace(' ', '_')}_推荐股票_{date.strftime('%Y%m%d')}.csv"
    recommended_stocks.to_csv(output_file, index=False)
    
    print(f"推荐股票已保存到 {output_file}")
    
    # 打印推荐股票
    print("\n推荐股票列表:")
    for i, (_, stock) in enumerate(recommended_stocks.iterrows()):
        if i < 20:  # 只显示前20只
            print(f"{i+1}. {stock['股票代码']} {stock['股票名称']}, 技术强度: {stock['技术强度']}, 连续技术强度5天数: {stock['连续技术强度5天数']}, 看涨技术指标数量: {stock['看涨技术指标数量']}")
    
    if len(recommended_stocks) > 20:
        print(f"... 共 {len(recommended_stocks)} 只股票")
    
    return recommended_stocks

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)
    
    # 获取最新日期
    latest_date = df['日期'].max()
    print(f"最新日期: {latest_date.strftime('%Y-%m-%d')}")
    
    # 定义策略列表
    strategies = [
        (strategy_1, "连续技术强度5天数涨跌幅趋势==1 & 技术指标_RSI反弹==1"),
        (strategy_2, "连续技术强度3天数>=75 & 连续技术强度5天数价格趋势==1"),
        (strategy_3, "技术强度>=70 & 技术强度趋势==1"),
        (strategy_4, "连续技术强度5天数价格趋势==1 & 技术指标_RSI反弹==1"),
        (strategy_5, "连续技术强度10天数>=70 & 技术指标_RSI反弹==1"),
        (strategy_6, "技术强度>=85 & 技术指标_KDJ金叉==1"),
        (strategy_7, "连续技术强度5天数>=285 & 涨跌幅趋势>=1"),
        (strategy_8, "技术强度>=80 & 连续技术强度5天数>=350 & 看涨技术指标数量>=5")
    ]
    
    # 回测所有策略
    backtest_results = []
    for strategy_fn, strategy_name in strategies:
        result = backtest(df, strategy_fn, '2025-04-01', '2025-04-30', 10000, strategy_name)
        backtest_results.append(result)
    
    # 按总收益率排序
    backtest_results.sort(key=lambda x: x['total_return'], reverse=True)
    
    # 打印排序后的结果
    print("\n策略排名 (按总收益率):")
    for i, result in enumerate(backtest_results):
        print(f"{i+1}. {result['strategy_name']}: 收益率={result['total_return']:.2f}%, 胜率={result['win_rate']:.2f}%, 交易次数={result['trade_count']}")
    
    # 生成最新日期的股票推荐 (使用排名前3的策略)
    print("\n生成最新日期的股票推荐 (使用排名前3的策略):")
    for i in range(min(3, len(backtest_results))):
        strategy_fn, strategy_name = next((s for s in strategies if s[1] == backtest_results[i]['strategy_name']), (None, None))
        if strategy_fn:
            generate_recommendations(df, strategy_fn, latest_date, strategy_name)
