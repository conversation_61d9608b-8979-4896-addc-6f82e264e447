"""
修复回测程序中的问题，确保即使没有筛选出数据，也会创建一个空的Excel文件，并继续执行下一个组合。

使用方法：
1. 将此文件放在与backtest_local.py相同的目录下
2. 运行此文件：python fix_backtest_local.py
3. 程序会自动修改backtest_local.py文件，添加创建空Excel文件的功能
"""

import os
import re
import shutil
import pandas as pd
from datetime import datetime

def backup_file(file_path):
    """创建文件备份"""
    backup_path = file_path + '.bak'
    shutil.copy2(file_path, backup_path)
    print(f"已创建备份文件: {backup_path}")
    return backup_path

def fix_process_strategy_function(file_path):
    """修复process_strategy函数，确保即使没有筛选出数据，也会创建一个空的Excel文件"""
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找process_strategy函数
    process_strategy_pattern = r'def process_strategy\(.*?\):(.*?)return False'
    process_strategy_match = re.search(process_strategy_pattern, content, re.DOTALL)
    
    if not process_strategy_match:
        print("无法找到process_strategy函数，请手动修改")
        return False
    
    # 查找函数中处理没有选出股票的部分
    no_stocks_pattern = r'(.*?策略 \{strategy_id\} 没有选出任何股票，不生成详细分析文件\n.*?return False)'
    no_stocks_match = re.search(no_stocks_pattern, process_strategy_match.group(1), re.DOTALL)
    
    if not no_stocks_match:
        print("无法找到处理没有选出股票的代码，请手动修改")
        return False
    
    # 替换为创建空Excel文件的代码
    new_code = """            print(f"策略 {strategy_id} 没有选出任何股票，创建空的Excel文件")
            
            # 创建空的Excel文件
            detail_file = os.path.join(output_dir, f"strategy_{strategy_id}.xlsx")
            
            # 创建一个Excel写入器
            with pd.ExcelWriter(detail_file, engine='openpyxl') as writer:
                # 1. 策略汇总参数
                strategy_summary = pd.DataFrame({
                    '策略编号': [strategy_id],
                    '策略组合': [summary_df.loc[idx, '策略组合']],
                    '特征数量': [summary_df.loc[idx, '特征数量']],
                    '总收益率(%)': [0],
                    '平均收益率(%)': [0],
                    '平均胜率(%)': [0],
                    '平均每日交易笔数': [0],
                    '总交易笔数': [0],
                    '交易天数': [0],
                    '总天数': [len(stock_df['日期'].unique())],
                    '交易频率(%)': [0],
                })
                strategy_summary.to_excel(writer, sheet_name='策略汇总参数', index=False)

                # 2. 策略参数
                strategy_params = pd.DataFrame({
                    '策略编号': [strategy_id],
                    '策略条件描述': [summary_df.loc[idx, '策略条件描述']],
                })
                strategy_params.to_excel(writer, sheet_name='策略参数', index=False)
                
                # 3. 空的股票选择结果
                empty_df = pd.DataFrame(columns=[
                    '股票代码', '股票名称', '日期', '收盘价', '涨跌幅', 
                    '技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数',
                    '技术指标特征', '趋势组合', '成交量是前一日几倍', 
                    '买入日开盘涨跌幅', '卖出日开盘涨跌幅', '日内股票标记'
                ])
                empty_df.to_excel(writer, sheet_name='股票选择', index=False)
                
                # 4. 说明
                info_df = pd.DataFrame({
                    '说明': [
                        f'策略编号: {strategy_id}',
                        f'创建时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                        '该策略没有筛选出符合条件的股票',
                        '这是一个空的Excel文件，用于确保程序能够继续执行下一个组合'
                    ]
                })
                info_df.to_excel(writer, sheet_name='说明', index=False)
            
            print(f"已创建空的Excel文件: {detail_file}")
            return True"""
    
    # 替换代码
    new_content = content.replace(no_stocks_match.group(0), new_code)
    
    # 写入修改后的文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("已成功修复process_strategy函数")
    return True

def main():
    """主函数"""
    # 查找backtest_local.py文件
    backtest_local_path = 'backtest_local.py'
    
    if not os.path.exists(backtest_local_path):
        print(f"错误: 找不到{backtest_local_path}文件")
        print("请确保此脚本与backtest_local.py在同一目录下")
        return
    
    # 创建备份
    backup_path = backup_file(backtest_local_path)
    
    # 修复process_strategy函数
    if fix_process_strategy_function(backtest_local_path):
        print(f"修复完成！原文件已备份为: {backup_path}")
        print("现在即使没有筛选出数据，程序也会创建一个空的Excel文件，并继续执行下一个组合")
    else:
        print("修复失败，请手动修改backtest_local.py文件")
        print("需要修改的部分是process_strategy函数中处理没有选出股票的代码")
        print("确保即使没有筛选出数据，也会创建一个空的Excel文件，并返回True")

if __name__ == "__main__":
    main()
