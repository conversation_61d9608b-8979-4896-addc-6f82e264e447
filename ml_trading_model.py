import pandas as pd
import numpy as np
import os
import sys
import joblib
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from sentiment_analyzer import SentimentAnalyzer

def build_ml_trading_model():
    """
    构建机器学习交易模型，预测次日买入后一日卖出的盈利概率
    """
    print("开始构建机器学习交易模型...")

    # 创建结果目录
    if not os.path.exists('ml_model_results'):
        os.makedirs('ml_model_results')

    # 加载股票数据（包括新的历史数据）
    print("\n加载股票数据...")
    try:
        # 尝试加载所有可能的数据文件并合并
        all_data = []

        # 尝试加载主数据文件
        try:
            main_data = pd.read_excel('股票明细.xlsx')
            print(f"成功加载主数据文件，共 {len(main_data)} 条记录")
            all_data.append(main_data)
        except Exception as e:
            print(f"加载主数据文件失败: {e}")

        # 尝试加载历史数据文件
        try:
            history_data = pd.read_excel('股票历史数据.xlsx')
            print(f"成功加载历史数据文件，共 {len(history_data)} 条记录")
            all_data.append(history_data)
        except Exception as e:
            print(f"加载历史数据文件失败: {e}")

        # 尝试加载其他可能的数据文件
        for i in range(1, 5):  # 尝试加载股票历史数据1.xlsx到股票历史数据4.xlsx
            try:
                additional_data = pd.read_excel(f'股票历史数据{i}.xlsx')
                print(f"成功加载额外数据文件{i}，共 {len(additional_data)} 条记录")
                all_data.append(additional_data)
            except Exception as e:
                print(f"加载额外数据文件{i}失败: {e}")

        # 合并所有数据
        if len(all_data) > 0:
            stock_data = pd.concat(all_data, ignore_index=True)
            # 去除可能的重复数据
            stock_data = stock_data.drop_duplicates(subset=['股票代码', '日期'])
            print(f"成功合并所有数据，共 {len(stock_data)} 条记录")
        else:
            raise Exception("没有找到任何数据文件")

        # 转换日期格式
        if isinstance(stock_data['日期'].iloc[0], str):
            stock_data['日期'] = pd.to_datetime(stock_data['日期'])

        # 获取所有日期并排序
        all_dates = sorted(stock_data['日期'].unique())
        print(f"数据集中的日期范围: {all_dates[0]} 到 {all_dates[-1]}")

        # 按股票代码和日期排序
        stock_data = stock_data.sort_values(['股票代码', '日期'])

        # 数据预处理
        print("\n数据预处理...")

        # 按股票代码分组处理
        for code, group in stock_data.groupby('股票代码'):
            # 确保数据按日期排序
            group = group.sort_values('日期')

            # 计算连续技术强度天数（连续多少天为100）
            consecutive_days = []
            current_count = 0

            for strength in group['技术强度'].values:
                if strength == 100:
                    current_count += 1
                else:
                    current_count = 0
                consecutive_days.append(current_count)

            # 更新原始数据
            stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days

            # 计算连续技术强度1天数（当日的技术强度）
            stock_data.loc[group.index, '连续技术强度1天数'] = group['技术强度']

            # 计算连续技术强度2天数（连续2天技术强度求和）
            stock_data.loc[group.index, '连续技术强度2天数'] = group['技术强度'] + group['技术强度'].shift(1).fillna(0)

            # 计算连续技术强度3天数（连续3天技术强度求和）
            stock_data.loc[group.index, '连续技术强度3天数'] = (
                group['技术强度'] +
                group['技术强度'].shift(1).fillna(0) +
                group['技术强度'].shift(2).fillna(0)
            )

            # 计算连续技术强度4天数（连续4天技术强度求和）
            stock_data.loc[group.index, '连续技术强度4天数'] = (
                group['技术强度'] +
                group['技术强度'].shift(1).fillna(0) +
                group['技术强度'].shift(2).fillna(0) +
                group['技术强度'].shift(3).fillna(0)
            )

            # 计算连续技术强度5天数（连续5天技术强度求和）
            stock_data.loc[group.index, '连续技术强度5天数'] = (
                group['技术强度'] +
                group['技术强度'].shift(1).fillna(0) +
                group['技术强度'].shift(2).fillna(0) +
                group['技术强度'].shift(3).fillna(0) +
                group['技术强度'].shift(4).fillna(0)
            )

            # 计算连续技术强度6天数（连续6天技术强度求和）
            stock_data.loc[group.index, '连续技术强度6天数'] = (
                group['技术强度'] +
                group['技术强度'].shift(1).fillna(0) +
                group['技术强度'].shift(2).fillna(0) +
                group['技术强度'].shift(3).fillna(0) +
                group['技术强度'].shift(4).fillna(0) +
                group['技术强度'].shift(5).fillna(0)
            )

            # 计算连续技术强度7天数（连续7天技术强度求和）
            stock_data.loc[group.index, '连续技术强度7天数'] = (
                group['技术强度'] +
                group['技术强度'].shift(1).fillna(0) +
                group['技术强度'].shift(2).fillna(0) +
                group['技术强度'].shift(3).fillna(0) +
                group['技术强度'].shift(4).fillna(0) +
                group['技术强度'].shift(5).fillna(0) +
                group['技术强度'].shift(6).fillna(0)
            )

            # 计算连续技术强度8天数（连续8天技术强度求和）
            stock_data.loc[group.index, '连续技术强度8天数'] = (
                group['技术强度'] +
                group['技术强度'].shift(1).fillna(0) +
                group['技术强度'].shift(2).fillna(0) +
                group['技术强度'].shift(3).fillna(0) +
                group['技术强度'].shift(4).fillna(0) +
                group['技术强度'].shift(5).fillna(0) +
                group['技术强度'].shift(6).fillna(0) +
                group['技术强度'].shift(7).fillna(0)
            )

            # 计算技术强度变化率（当日技术强度与前一日的比值）
            stock_data.loc[group.index, '技术强度变化率'] = (
                group['技术强度'] / group['技术强度'].shift(1).replace(0, 1)
            )

            # 计算技术强度3日变化率（当日技术强度与3日前的比值）
            stock_data.loc[group.index, '技术强度3日变化率'] = (
                group['技术强度'] / group['技术强度'].shift(3).replace(0, 1)
            )

            # 计算技术强度5日变化率（当日技术强度与5日前的比值）
            stock_data.loc[group.index, '技术强度5日变化率'] = (
                group['技术强度'] / group['技术强度'].shift(5).replace(0, 1)
            )

            # 计算技术强度趋势（连续3日技术强度是否上升）
            stock_data.loc[group.index, '技术强度趋势'] = (
                (group['技术强度'] > group['技术强度'].shift(1)) &
                (group['技术强度'].shift(1) > group['技术强度'].shift(2))
            ).astype(int)

            # 计算价格趋势（连续3日价格是否上升）
            stock_data.loc[group.index, '价格趋势'] = (
                (group['当前价格'] > group['当前价格'].shift(1)) &
                (group['当前价格'].shift(1) > group['当前价格'].shift(2))
            ).astype(int)

            # 计算涨跌幅趋势（连续3日涨跌幅是否上升）
            if '涨跌幅' in group.columns:
                stock_data.loc[group.index, '涨跌幅趋势'] = (
                    (group['涨跌幅'] > group['涨跌幅'].shift(1)) &
                    (group['涨跌幅'].shift(1) > group['涨跌幅'].shift(2))
                ).astype(int)

            # 计算连续技术强度9天数（连续9天技术强度求和）
            stock_data.loc[group.index, '连续技术强度9天数'] = (
                group['技术强度'] +
                group['技术强度'].shift(1).fillna(0) +
                group['技术强度'].shift(2).fillna(0) +
                group['技术强度'].shift(3).fillna(0) +
                group['技术强度'].shift(4).fillna(0) +
                group['技术强度'].shift(5).fillna(0) +
                group['技术强度'].shift(6).fillna(0) +
                group['技术强度'].shift(7).fillna(0) +
                group['技术强度'].shift(8).fillna(0)
            )

            # 计算连续技术强度10天数（连续10天技术强度求和）
            stock_data.loc[group.index, '连续技术强度10天数'] = (
                group['技术强度'] +
                group['技术强度'].shift(1).fillna(0) +
                group['技术强度'].shift(2).fillna(0) +
                group['技术强度'].shift(3).fillna(0) +
                group['技术强度'].shift(4).fillna(0) +
                group['技术强度'].shift(5).fillna(0) +
                group['技术强度'].shift(6).fillna(0) +
                group['技术强度'].shift(7).fillna(0) +
                group['技术强度'].shift(8).fillna(0) +
                group['技术强度'].shift(9).fillna(0)
            )

            # 计算次日涨跌方向和收益率
            group['次日涨跌方向'] = (group['当前价格'].shift(-1) > group['当前价格']).astype(int)
            group['次日收益率'] = (group['当前价格'].shift(-1) / group['当前价格'] - 1) * 100

            # 计算后日涨跌方向和收益率（买入后的第二天，即卖出日）
            group['后日涨跌方向'] = (group['当前价格'].shift(-2) > group['当前价格'].shift(-1)).astype(int)
            group['后日收益率'] = (group['当前价格'].shift(-2) / group['当前价格'].shift(-1) - 1) * 100

            # 计算两日收益率（买入后持有两天的总收益）
            group['两日收益率'] = (group['当前价格'].shift(-2) / group['当前价格'] - 1) * 100

            # 计算是否盈利（两日收益率为正）
            group['是否盈利'] = (group['两日收益率'] > 0).astype(int)

            # 更新原始数据
            stock_data.loc[group.index, '次日涨跌方向'] = group['次日涨跌方向']
            stock_data.loc[group.index, '次日收益率'] = group['次日收益率']
            stock_data.loc[group.index, '后日涨跌方向'] = group['后日涨跌方向']
            stock_data.loc[group.index, '后日收益率'] = group['后日收益率']
            stock_data.loc[group.index, '两日收益率'] = group['两日收益率']
            stock_data.loc[group.index, '是否盈利'] = group['是否盈利']

        # 删除没有完整数据的记录
        stock_data = stock_data.dropna(subset=['次日涨跌方向', '次日收益率', '后日涨跌方向', '后日收益率', '两日收益率', '是否盈利'])

        print(f"处理后的数据集大小: {len(stock_data)} 条记录")

        # 特征工程
        print("\n特征工程...")

        # 检查数据集中的可用列
        print(f"数据集中的列: {stock_data.columns.tolist()}")

        # 处理技术指标特征（将文本转换为数值特征）
        if '技术指标' in stock_data.columns:
            # 提取常见的技术指标关键词
            tech_indicators = ['均线多头排列', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
                              '均线空头排列', 'MACD死叉', 'RSI超买', 'KDJ死叉', '布林带收缩']

            # 为每个技术指标创建一个新列
            for indicator in tech_indicators:
                col_name = f'技术指标_{indicator}'
                # 检查技术指标文本中是否包含该关键词
                stock_data[col_name] = stock_data['技术指标'].str.contains(indicator, na=False).astype(int)

            print(f"已将技术指标文本转换为 {len(tech_indicators)} 个二进制特征")

        # 提取特征（进一步移除冗余特征，包括当前价格）
        available_features = [
            '技术强度',
            '连续技术强度2天数', '连续技术强度3天数',
            '连续技术强度4天数', '连续技术强度5天数', '连续技术强度6天数',
            '连续技术强度7天数', '连续技术强度8天数', '连续技术强度9天数', '连续技术强度10天数',
            '技术强度趋势', '价格趋势', '涨跌幅趋势',
            '涨跌幅'  # 移除了当前价格
        ]

        # 添加技术指标二进制特征
        if '技术指标' in stock_data.columns:
            for indicator in tech_indicators:
                available_features.append(f'技术指标_{indicator}')

        # 添加其他可能存在的特征
        optional_features = ['成交量', '换手率', '市盈率', '市净率']
        for feature in optional_features:
            if feature in stock_data.columns:
                available_features.append(feature)

        features = available_features
        print(f"最终使用的特征: {features}")

        # 准备训练数据
        X = stock_data[features]
        y = stock_data['是否盈利']

        # 处理缺失值
        print("\n处理缺失值...")
        print(f"缺失值统计: \n{X.isnull().sum()}")

        # 删除包含NaN的行
        valid_indices = ~X.isnull().any(axis=1)
        X = X[valid_indices]
        y = y[valid_indices]

        print(f"处理缺失值后的数据集大小: {len(X)} 条记录")

        # 转换为numpy数组
        X = X.values
        y = y.values

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 划分训练集和测试集（不按日期划分，随机划分）
        X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)

        print(f"训练集大小: {len(X_train)} 条记录")
        print(f"测试集大小: {len(X_test)} 条记录")

        # 计算训练集中正样本（盈利）的比例
        positive_ratio = y_train.mean()
        print(f"训练集中盈利样本比例: {positive_ratio:.4f} ({positive_ratio*100:.2f}%)")

        # 模型训练
        print("\n模型训练...")

        # 随机森林模型
        print("\n训练随机森林模型...")
        rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
        rf_model.fit(X_train, y_train)

        # 梯度提升模型
        print("\n训练梯度提升模型...")
        gb_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
        gb_model.fit(X_train, y_train)

        # 模型评估
        print("\n模型评估...")

        # 随机森林模型评估
        rf_pred = rf_model.predict(X_test)
        rf_prob = rf_model.predict_proba(X_test)[:, 1]

        rf_accuracy = accuracy_score(y_test, rf_pred)
        rf_precision = precision_score(y_test, rf_pred)
        rf_recall = recall_score(y_test, rf_pred)
        rf_f1 = f1_score(y_test, rf_pred)
        rf_auc = roc_auc_score(y_test, rf_prob)

        print("\n随机森林模型评估结果:")
        print(f"准确率: {rf_accuracy:.4f}")
        print(f"精确率: {rf_precision:.4f}")
        print(f"召回率: {rf_recall:.4f}")
        print(f"F1分数: {rf_f1:.4f}")
        print(f"AUC: {rf_auc:.4f}")

        # 梯度提升模型评估
        gb_pred = gb_model.predict(X_test)
        gb_prob = gb_model.predict_proba(X_test)[:, 1]

        gb_accuracy = accuracy_score(y_test, gb_pred)
        gb_precision = precision_score(y_test, gb_pred)
        gb_recall = recall_score(y_test, gb_pred)
        gb_f1 = f1_score(y_test, gb_pred)
        gb_auc = roc_auc_score(y_test, gb_prob)

        print("\n梯度提升模型评估结果:")
        print(f"准确率: {gb_accuracy:.4f}")
        print(f"精确率: {gb_precision:.4f}")
        print(f"召回率: {gb_recall:.4f}")
        print(f"F1分数: {gb_f1:.4f}")
        print(f"AUC: {gb_auc:.4f}")

        # 特征重要性分析
        print("\n特征重要性分析...")

        # 随机森林特征重要性
        rf_importances = rf_model.feature_importances_
        rf_indices = np.argsort(rf_importances)[::-1]

        print("\n随机森林模型特征重要性:")
        for i in range(len(features)):
            print(f"{features[rf_indices[i]]}: {rf_importances[rf_indices[i]]:.4f}")

        # 绘制特征重要性图
        plt.figure(figsize=(10, 6))
        plt.title('随机森林模型特征重要性')
        plt.bar(range(len(features)), rf_importances[rf_indices])
        plt.xticks(range(len(features)), [features[i] for i in rf_indices], rotation=45)
        plt.tight_layout()
        plt.savefig('ml_model_results/rf_feature_importance.png')

        # 梯度提升特征重要性
        gb_importances = gb_model.feature_importances_
        gb_indices = np.argsort(gb_importances)[::-1]

        print("\n梯度提升模型特征重要性:")
        for i in range(len(features)):
            print(f"{features[gb_indices[i]]}: {gb_importances[gb_indices[i]]:.4f}")

        # 绘制特征重要性图
        plt.figure(figsize=(10, 6))
        plt.title('梯度提升模型特征重要性')
        plt.bar(range(len(features)), gb_importances[gb_indices])
        plt.xticks(range(len(features)), [features[i] for i in gb_indices], rotation=45)
        plt.tight_layout()
        plt.savefig('ml_model_results/gb_feature_importance.png')

        # 保存模型
        print("\n保存模型...")
        joblib.dump(rf_model, 'ml_model_results/random_forest_model.pkl')
        joblib.dump(gb_model, 'ml_model_results/gradient_boosting_model.pkl')
        joblib.dump(scaler, 'ml_model_results/scaler.pkl')

        # 保存特征列表
        with open('ml_model_results/features.txt', 'w') as f:
            f.write(','.join(features))

        # 使用模型进行预测
        print("\n使用模型进行预测...")

        # 获取最新日期
        latest_date = all_dates[-1]
        print(f"最新日期: {latest_date}")

        # 获取最新日期的数据（用于预测次日买入后一日卖出是否盈利）
        latest_data = stock_data[stock_data['日期'] == latest_date]
        print(f"最新日期的数据: {len(latest_data)} 条记录")
        print(f"预测 {latest_date} 的股票次日买入后一日卖出是否盈利...")

        # 准备预测数据
        X_pred = latest_data[features]

        # 处理预测数据中的缺失值
        print(f"预测数据缺失值统计: \n{X_pred.isnull().sum()}")

        # 删除包含NaN的行
        valid_pred_indices = ~X_pred.isnull().any(axis=1)
        X_pred = X_pred[valid_pred_indices]
        latest_data_filtered = latest_data.loc[valid_pred_indices]

        print(f"处理缺失值后的预测数据集大小: {len(X_pred)} 条记录")

        # 转换为numpy数组
        X_pred = X_pred.values
        X_pred_scaled = scaler.transform(X_pred)

        # 使用随机森林模型预测
        rf_pred_prob = rf_model.predict_proba(X_pred_scaled)[:, 1]

        # 使用梯度提升模型预测
        gb_pred_prob = gb_model.predict_proba(X_pred_scaled)[:, 1]

        # 综合两个模型的预测结果
        ensemble_pred_prob = (rf_pred_prob + gb_pred_prob) / 2

        # 创建预测结果DataFrame（移除当前价格）
        result_dict = {
            '股票代码': latest_data_filtered['股票代码'],
            '股票名称': latest_data_filtered['股票名称'],
            '涨跌幅': latest_data_filtered['涨跌幅'],
            '技术强度': latest_data_filtered['技术强度'],
            '连续技术强度天数': latest_data_filtered['连续技术强度天数'],
            '随机森林预测概率': rf_pred_prob,
            '梯度提升预测概率': gb_pred_prob,
            '综合预测概率': ensemble_pred_prob,
            '次日买入后一日卖出预测': ['盈利' if p > 0.5 else '亏损' for p in ensemble_pred_prob],
            '预测信号强度': [abs(p - 0.5) * 2 for p in ensemble_pred_prob],
            '盈利概率': ensemble_pred_prob
        }

        # 如果有技术指标列，添加到结果中
        if '技术指标' in latest_data_filtered.columns:
            result_dict['技术指标'] = latest_data_filtered['技术指标']

        predictions = pd.DataFrame(result_dict)

        # 按综合预测概率降序排序
        predictions = predictions.sort_values('综合预测概率', ascending=False)

        # 保存预测结果
        predictions.to_excel('ml_model_results/predictions.xlsx', index=False)

        print("\n预测结果:")
        print(predictions.head(20))

        # 结合情绪分析
        print("\n结合情绪分析...")

        try:
            # 初始化情绪分析器
            sentiment_analyzer = SentimentAnalyzer()

            # 获取市场情绪
            market_sentiment = sentiment_analyzer.get_market_sentiment()
            print(f"市场情绪: {market_sentiment}")

            # 获取股票情绪
            stock_codes = predictions['股票代码'].tolist()
            if len(stock_codes) > 50:
                stock_codes = stock_codes[:50]  # 只分析前50只股票
            stock_sentiment = sentiment_analyzer.get_stock_sentiment(stock_codes)

            # 合并情绪数据
            predictions_with_sentiment = pd.merge(
                predictions,
                stock_sentiment,
                on='股票代码',
                how='left'
            )

            # 计算综合得分（预测概率 * 情绪得分）
            predictions_with_sentiment['综合得分'] = predictions_with_sentiment['综合预测概率'] * predictions_with_sentiment['情绪得分']

            # 按综合得分降序排序
            predictions_with_sentiment = predictions_with_sentiment.sort_values('综合得分', ascending=False)

            # 保存带情绪分析的预测结果
            predictions_with_sentiment.to_excel('ml_model_results/predictions_with_sentiment.xlsx', index=False)

            print("\n带情绪分析的预测结果:")
            print(predictions_with_sentiment.head(20))

            # 返回最终推荐结果
            return predictions_with_sentiment

        except Exception as e:
            print(f"情绪分析失败: {e}")
            print("返回不带情绪分析的预测结果")
            return predictions

    except Exception as e:
        print(f"模型构建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    build_ml_trading_model()
