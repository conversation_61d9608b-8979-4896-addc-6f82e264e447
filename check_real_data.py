#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def check_real_data():
    """检查真实生成的数据"""
    
    print("=== 检查真实生成的数据 ===")
    
    # 检查主文件
    main_file = 'E:/机器学习/complete_excel_results/tech_strength/daily/tech_strength_2025-05-15.xlsx'
    
    if os.path.exists(main_file):
        print(f"✅ 主文件存在: {main_file}")
        
        try:
            # 读取数据，指定关键字段为字符串类型
            df = pd.read_excel(main_file, dtype={
                '股票代码': str,
                '技术指标特征': str,
                '趋势组合': str
            })
            
            print(f"数据行数: {len(df)}")
            print(f"数据列数: {len(df.columns)}")
            
            # 检查前5行真实数据
            print(f"\n=== 前5行真实数据 ===")
            for i in range(min(5, len(df))):
                row = df.iloc[i]
                
                print(f"\n行 {i+1}:")
                print(f"  股票代码: {row.get('股票代码', 'N/A')}")
                print(f"  股票名称: {row.get('股票名称', 'N/A')}")
                print(f"  技术强度: {row.get('技术强度', 'N/A')}")
                
                # 重点检查技术指标特征和趋势组合
                tech_feature = row.get('技术指标特征', 'N/A')
                trend_combo = row.get('趋势组合', 'N/A')
                
                print(f"  技术指标特征: '{tech_feature}'")
                print(f"    类型: {type(tech_feature)}")
                print(f"    长度: {len(str(tech_feature))}")
                print(f"    原始值: {repr(tech_feature)}")
                
                print(f"  趋势组合: '{trend_combo}'")
                print(f"    类型: {type(trend_combo)}")
                print(f"    长度: {len(str(trend_combo))}")
                print(f"    原始值: {repr(trend_combo)}")
                
                # 检查连续技术强度
                c3 = row.get('连续技术强度3天数', 'N/A')
                c5 = row.get('连续技术强度5天数', 'N/A')
                c10 = row.get('连续技术强度10天数', 'N/A')
                print(f"  连续技术强度: 3天={c3}, 5天={c5}, 10天={c10}")
            
            # 检查字段类型
            print(f"\n=== 字段类型检查 ===")
            print(f"技术指标特征列类型: {df['技术指标特征'].dtype}")
            print(f"趋势组合列类型: {df['趋势组合'].dtype}")
            
            # 检查唯一值
            print(f"\n=== 唯一值检查 ===")
            tech_unique = df['技术指标特征'].unique()[:10]
            trend_unique = df['趋势组合'].unique()[:10]
            
            print(f"技术指标特征前10个唯一值: {tech_unique}")
            print(f"趋势组合前10个唯一值: {trend_unique}")
            
            # 专门检查前导0
            print(f"\n=== 前导0检查 ===")
            
            tech_features_str = df['技术指标特征'].astype(str)
            trend_combos_str = df['趋势组合'].astype(str)
            
            # 统计前导0情况
            tech_with_zero = sum(1 for x in tech_features_str if x.startswith('0'))
            trend_with_zero = sum(1 for x in trend_combos_str if x.startswith('0'))
            
            print(f"技术指标特征:")
            print(f"  总数: {len(tech_features_str)}")
            print(f"  以0开头的数量: {tech_with_zero}")
            print(f"  前导0保留率: {tech_with_zero/len(tech_features_str)*100:.2f}%")
            
            print(f"趋势组合:")
            print(f"  总数: {len(trend_combos_str)}")
            print(f"  以0开头的数量: {trend_with_zero}")
            print(f"  前导0保留率: {trend_with_zero/len(trend_combos_str)*100:.2f}%")
            
            # 显示一些以0开头的例子
            tech_zero_examples = [x for x in tech_features_str if x.startswith('0')][:5]
            trend_zero_examples = [x for x in trend_combos_str if x.startswith('0')][:5]
            
            if tech_zero_examples:
                print(f"技术指标特征以0开头的例子: {tech_zero_examples}")
            else:
                print("❌ 没有找到以0开头的技术指标特征")
            
            if trend_zero_examples:
                print(f"趋势组合以0开头的例子: {trend_zero_examples}")
            else:
                print("❌ 没有找到以0开头的趋势组合")
            
            # 检查长度问题
            print(f"\n=== 长度问题检查 ===")
            
            tech_lengths = [len(str(x)) for x in tech_features_str]
            trend_lengths = [len(str(x)) for x in trend_combos_str]
            
            tech_short = sum(1 for l in tech_lengths if l < 6)
            trend_short = sum(1 for l in trend_lengths if l < 6)
            
            print(f"技术指标特征长度小于6位的数量: {tech_short}")
            print(f"趋势组合长度小于6位的数量: {trend_short}")
            
            if tech_short > 0 or trend_short > 0:
                print("❌ 发现长度不足6位的数据，前导0可能丢失")
                
                # 显示一些长度不足的例子
                tech_short_examples = [str(x) for x in tech_features_str if len(str(x)) < 6][:5]
                trend_short_examples = [str(x) for x in trend_combos_str if len(str(x)) < 6][:5]
                
                if tech_short_examples:
                    print(f"技术指标特征长度不足的例子: {tech_short_examples}")
                if trend_short_examples:
                    print(f"趋势组合长度不足的例子: {trend_short_examples}")
            else:
                print("✅ 所有数据长度都是6位")
            
            print(f"\n✅ 真实数据检查完成")
            
        except Exception as e:
            print(f"❌ 读取数据时出错: {e}")
            import traceback
            traceback.print_exc()
    else:
        print(f"❌ 主文件不存在: {main_file}")

if __name__ == "__main__":
    check_real_data()
