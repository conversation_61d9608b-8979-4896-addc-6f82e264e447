"""
股票交易策略接口模块

提供简单的接口函数，用于调用各种交易策略
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from .strategy_implementations import (
    preprocess_data, train_model, predict_stocks,
    strategy_1, strategy_A, strategy_B, strategy_C
)

def load_data(file_path='股票明细.xlsx'):
    """
    加载股票数据

    参数:
    file_path: 股票数据文件路径

    返回:
    加载的股票数据DataFrame
    """
    print(f"加载股票数据: {file_path}")
    try:
        stock_data = pd.read_excel(file_path)
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
        return stock_data
    except Exception as e:
        print(f"加载股票数据失败: {e}")
        return None

def get_strategy_recommendations(strategy_name, prediction_date_str, data_file_path='股票明细.xlsx', use_saved_model=False, model_dir='trained_models'):
    """
    获取指定策略的推荐股票

    参数:
    strategy_name: 策略名称，可选值为 'strategy_1', 'strategy_A', 'strategy_B', 'strategy_C'
    prediction_date_str: 预测日期，格式为'YYYY-MM-DD'
    data_file_path: 股票数据文件路径
    use_saved_model: 是否使用保存的模型，如果为True，则加载最新训练的模型
    model_dir: 模型保存目录

    返回:
    推荐股票DataFrame和风险说明
    """
    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return None, None

    # 预处理数据
    processed_data = preprocess_data(stock_data)

    # 转换预测日期
    prediction_date = pd.to_datetime(prediction_date_str)

    # 获取模型
    if use_saved_model:
        # 导入模型加载函数
        from .model_trainer import load_latest_model
        model, scaler, features = load_latest_model(model_dir)
        if model is None:
            print("无法加载保存的模型，将重新训练模型")
            model, scaler, features = train_model(processed_data, prediction_date)
    else:
        # 训练新模型
        model, scaler, features = train_model(processed_data, prediction_date)

    # 预测股票
    predictions = predict_stocks(processed_data, model, scaler, features, prediction_date)
    if predictions is None:
        return None, None

    # 应用策略
    if strategy_name == 'strategy_1':
        strategy_stocks = strategy_1(predictions)
        strategy_description = "策略1：100%高胜率策略"
        buy_risk = "低风险：该策略在历史测试中表现出100%的胜率，但交易机会较少。"
        sell_risk = "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。"
        expected_win_rate = "100%"
        expected_return = "约2.38%"
    elif strategy_name == 'strategy_A':
        strategy_stocks = strategy_A(predictions)
        strategy_description = "策略A：最高胜率策略"
        buy_risk = "低风险：该策略在历史测试中表现出约86.77%的胜率，交易机会适中。"
        sell_risk = "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。"
        expected_win_rate = "约86.77%"
        expected_return = "约3.42%"
    elif strategy_name == 'strategy_B':
        strategy_stocks = strategy_B(predictions)
        strategy_description = "策略B：最高收益率策略"
        buy_risk = "中低风险：该策略在历史测试中表现出约83.67%的胜率，但交易机会较少。"
        sell_risk = "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。"
        expected_win_rate = "约83.67%"
        expected_return = "约4.83%"
    elif strategy_name == 'strategy_C':
        strategy_stocks = strategy_C(predictions)
        strategy_description = "策略C：平衡策略（胜率和交易机会的平衡）"
        buy_risk = "中低风险：该策略在历史测试中表现出约84.82%的胜率，交易机会较多。"
        sell_risk = "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。"
        expected_win_rate = "约84.82%"
        expected_return = "约2.59%"
    else:
        print(f"未知策略: {strategy_name}")
        return None, None

    # 创建结果目录
    if not os.path.exists('strategy_results'):
        os.makedirs('strategy_results')

    # 保存推荐股票
    result_file = f'strategy_results/{prediction_date_str}_{strategy_name}_推荐股票.xlsx'
    strategy_stocks.to_excel(result_file, index=False)

    # 生成风险说明
    risk_description = {
        'strategy_name': strategy_name,
        'strategy_description': strategy_description,
        'prediction_date': prediction_date_str,
        'stock_count': len(strategy_stocks),
        'buy_risk': buy_risk,
        'sell_risk': sell_risk,
        'expected_win_rate': expected_win_rate,
        'expected_return': expected_return,
        'important_note': "以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！",
        'trading_strategy': "买入后在第二个交易日开盘时卖出",
        'result_file': result_file
    }

    print(f"\n{strategy_description}")
    print(f"预测日期: {prediction_date_str}")
    print(f"推荐股票数: {len(strategy_stocks)}")
    print(f"预期胜率: {expected_win_rate}")
    print(f"预期收益率: {expected_return}")
    print(f"买入风险: {buy_risk}")
    print(f"卖出风险: {sell_risk}")
    print(f"结果已保存至: {result_file}")

    if len(strategy_stocks) > 0:
        print("\n推荐买入的股票:")
        print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！")
        print("【交易策略】: 买入后在第二个交易日开盘时卖出")
        print("\n股票列表:")
        for i, row in strategy_stocks.iterrows():
            print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")

    return strategy_stocks, risk_description

def get_all_strategy_recommendations(prediction_date_str, data_file_path='股票明细.xlsx', use_saved_model=False, model_dir='trained_models'):
    """
    获取所有策略的推荐股票

    参数:
    prediction_date_str: 预测日期，格式为'YYYY-MM-DD'
    data_file_path: 股票数据文件路径
    use_saved_model: 是否使用保存的模型，如果为True，则加载最新训练的模型
    model_dir: 模型保存目录

    返回:
    包含所有策略推荐股票和风险说明的字典
    """
    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return None, None

    # 预处理数据
    processed_data = preprocess_data(stock_data)

    # 转换预测日期
    prediction_date = pd.to_datetime(prediction_date_str)

    # 获取模型
    if use_saved_model:
        # 导入模型加载函数
        from .model_trainer import load_latest_model
        model, scaler, features = load_latest_model(model_dir)
        if model is None:
            print("无法加载保存的模型，将重新训练模型")
            model, scaler, features = train_model(processed_data, prediction_date)
    else:
        # 训练新模型
        model, scaler, features = train_model(processed_data, prediction_date)

    # 预测股票
    predictions = predict_stocks(processed_data, model, scaler, features, prediction_date)
    if predictions is None:
        return None, None

    # 应用所有策略
    strategy_results = {}
    risk_descriptions = {}

    # 策略1
    strategy_1_stocks = strategy_1(predictions)
    strategy_results['strategy_1'] = strategy_1_stocks
    risk_descriptions['strategy_1'] = {
        'strategy_name': 'strategy_1',
        'strategy_description': "策略1：100%高胜率策略",
        'prediction_date': prediction_date_str,
        'stock_count': len(strategy_1_stocks),
        'buy_risk': "低风险：该策略在历史测试中表现出100%的胜率，但交易机会较少。",
        'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
        'expected_win_rate': "100%",
        'expected_return': "约2.38%",
        'important_note': "以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！",
        'trading_strategy': "买入后在第二个交易日开盘时卖出"
    }

    # 策略A
    strategy_A_stocks = strategy_A(predictions)
    strategy_results['strategy_A'] = strategy_A_stocks
    risk_descriptions['strategy_A'] = {
        'strategy_name': 'strategy_A',
        'strategy_description': "策略A：最高胜率策略",
        'prediction_date': prediction_date_str,
        'stock_count': len(strategy_A_stocks),
        'buy_risk': "低风险：该策略在历史测试中表现出约86.77%的胜率，交易机会适中。",
        'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
        'expected_win_rate': "约86.77%",
        'expected_return': "约3.42%",
        'important_note': "以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！",
        'trading_strategy': "买入后在第二个交易日开盘时卖出"
    }

    # 策略B
    strategy_B_stocks = strategy_B(predictions)
    strategy_results['strategy_B'] = strategy_B_stocks
    risk_descriptions['strategy_B'] = {
        'strategy_name': 'strategy_B',
        'strategy_description': "策略B：最高收益率策略",
        'prediction_date': prediction_date_str,
        'stock_count': len(strategy_B_stocks),
        'buy_risk': "中低风险：该策略在历史测试中表现出约83.67%的胜率，但交易机会较少。",
        'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
        'expected_win_rate': "约83.67%",
        'expected_return': "约4.83%",
        'important_note': "以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！",
        'trading_strategy': "买入后在第二个交易日开盘时卖出"
    }

    # 策略C
    strategy_C_stocks = strategy_C(predictions)
    strategy_results['strategy_C'] = strategy_C_stocks
    risk_descriptions['strategy_C'] = {
        'strategy_name': 'strategy_C',
        'strategy_description': "策略C：平衡策略（胜率和交易机会的平衡）",
        'prediction_date': prediction_date_str,
        'stock_count': len(strategy_C_stocks),
        'buy_risk': "中低风险：该策略在历史测试中表现出约84.82%的胜率，交易机会较多。",
        'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
        'expected_win_rate': "约84.82%",
        'expected_return': "约2.59%",
        'important_note': "以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！",
        'trading_strategy': "买入后在第二个交易日开盘时卖出"
    }

    # 创建结果目录
    if not os.path.exists('strategy_results'):
        os.makedirs('strategy_results')

    # 保存所有推荐股票
    for strategy_name, strategy_stocks in strategy_results.items():
        result_file = f'strategy_results/{prediction_date_str}_{strategy_name}_推荐股票.xlsx'
        strategy_stocks.to_excel(result_file, index=False)
        risk_descriptions[strategy_name]['result_file'] = result_file

    # 保存汇总结果
    summary_file = f'strategy_results/{prediction_date_str}_所有策略推荐汇总.xlsx'
    with pd.ExcelWriter(summary_file) as writer:
        for strategy_name, strategy_stocks in strategy_results.items():
            strategy_stocks.to_excel(writer, sheet_name=strategy_name, index=False)

    print(f"\n预测日期: {prediction_date_str}")
    print(f"策略1推荐股票数: {len(strategy_results['strategy_1'])}")
    print(f"策略A推荐股票数: {len(strategy_results['strategy_A'])}")
    print(f"策略B推荐股票数: {len(strategy_results['strategy_B'])}")
    print(f"策略C推荐股票数: {len(strategy_results['strategy_C'])}")
    print(f"结果已保存至: {summary_file}")

    return strategy_results, risk_descriptions

def compare_strategies(start_date_str, end_date_str, data_file_path='股票明细.xlsx'):
    """
    比较不同策略在一段时间内的表现

    参数:
    start_date_str: 开始日期，格式为'YYYY-MM-DD'
    end_date_str: 结束日期，格式为'YYYY-MM-DD'
    data_file_path: 股票数据文件路径

    返回:
    包含各策略表现的DataFrame
    """
    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return None

    # 预处理数据
    processed_data = preprocess_data(stock_data)

    # 转换日期
    start_date = pd.to_datetime(start_date_str)
    end_date = pd.to_datetime(end_date_str)

    # 获取日期范围内的所有日期
    all_dates = sorted(processed_data['日期'].unique())
    date_range = [date for date in all_dates if start_date <= date <= end_date]

    # 创建结果DataFrame
    results = pd.DataFrame(columns=[
        '日期', '策略', '推荐股票数', '买入时上涨股票数', '胜率', '平均收益率'
    ])

    # 对每个日期进行预测和评估
    for prediction_date in date_range:
        print(f"\n分析日期: {prediction_date}")

        # 训练模型
        model, scaler, features = train_model(processed_data, prediction_date)

        # 预测股票
        predictions = predict_stocks(processed_data, model, scaler, features, prediction_date)
        if predictions is None:
            continue

        # 应用所有策略
        strategy_stocks = {
            '策略1': strategy_1(predictions),
            '策略A': strategy_A(predictions),
            '策略B': strategy_B(predictions),
            '策略C': strategy_C(predictions)
        }

        # 评估每个策略
        for strategy_name, stocks in strategy_stocks.items():
            if len(stocks) == 0:
                continue

            # 获取实际结果
            actual_results = []
            for i, row in stocks.iterrows():
                code = row['股票代码']
                date = prediction_date

                # 查找该股票在预测日期后的数据
                future_data = processed_data[
                    (processed_data['股票代码'] == code) &
                    (processed_data['日期'] > date)
                ].sort_values('日期')

                if len(future_data) >= 2:
                    # 获取次日涨跌方向和两日收益率
                    next_day_up = future_data.iloc[0]['次日涨跌方向']
                    profit = future_data.iloc[0]['是否盈利']
                    return_rate = future_data.iloc[0]['两日收益率']

                    actual_results.append({
                        '股票代码': code,
                        '股票名称': row['股票名称'],
                        '次日涨跌方向': next_day_up,
                        '是否盈利': profit,
                        '两日收益率': return_rate
                    })

            if len(actual_results) == 0:
                continue

            # 转换为DataFrame
            actual_df = pd.DataFrame(actual_results)

            # 计算买入时上涨的股票
            up_stocks = actual_df[actual_df['次日涨跌方向'] == 1]

            # 计算胜率和平均收益率
            if len(up_stocks) > 0:
                win_rate = sum(up_stocks['是否盈利']) / len(up_stocks)
                avg_return = up_stocks['两日收益率'].mean()
            else:
                win_rate = np.nan
                avg_return = np.nan

            # 添加到结果
            new_row = pd.DataFrame({
                '日期': [prediction_date],
                '策略': [strategy_name],
                '推荐股票数': [len(stocks)],
                '买入时上涨股票数': [len(up_stocks)],
                '胜率': [win_rate],
                '平均收益率': [avg_return]
            })
            results = pd.concat([results, new_row], ignore_index=True)

    # 保存结果
    result_file = f'strategy_results/{start_date_str}至{end_date_str}_策略比较.xlsx'
    results.to_excel(result_file, index=False)

    # 计算每个策略的整体表现
    strategy_summary = results.groupby('策略').agg({
        '推荐股票数': 'sum',
        '买入时上涨股票数': 'sum',
        '胜率': 'mean',
        '平均收益率': 'mean'
    }).reset_index()

    # 保存策略汇总
    summary_file = f'strategy_results/{start_date_str}至{end_date_str}_策略汇总.xlsx'
    strategy_summary.to_excel(summary_file, index=False)

    print(f"\n策略比较完成，结果已保存至: {result_file}")
    print(f"策略汇总已保存至: {summary_file}")

    return results, strategy_summary
