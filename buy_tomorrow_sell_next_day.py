import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score
from sklearn.impute import SimpleImputer
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline

# 读取数据
print("读取股票数据...")
data = pd.read_excel('股票明细.xlsx')
print(f"数据形状: {data.shape}")

# 检查数据日期范围
data['日期'] = pd.to_datetime(data['日期'])
print("\n数据日期范围:")
print(f"{data['日期'].min()} 至 {data['日期'].max()}")
print("\n日期分布:")
print(data['日期'].value_counts().sort_index())

# 计算每只股票在每个日期的次日涨跌幅和后日涨跌幅
def calculate_returns(group):
    """计算每只股票的次日涨跌幅和后日涨跌幅"""
    group = group.sort_values('日期')
    
    # 计算次日涨跌幅 (T+1)
    group['次日涨跌幅'] = group['当前价格'].shift(-1) / group['当前价格'] - 1
    
    # 计算后日涨跌幅 (T+2)
    group['后日涨跌幅'] = group['当前价格'].shift(-2) / group['当前价格'].shift(-1) - 1
    
    # 计算次日买入后日卖出的收益率
    group['次日买入后日卖出收益率'] = group['后日涨跌幅']
    
    return group

# 按股票代码分组，计算涨跌幅
print("\n计算次日和后日涨跌幅...")
data_with_returns = data.groupby('股票代码', group_keys=False).apply(calculate_returns)

# 删除没有后日数据的记录
data_with_returns = data_with_returns.dropna(subset=['次日买入后日卖出收益率'])

# 分析次日买入后日卖出收益率的分布
print("\n次日买入后日卖出收益率的分布统计:")
print(data_with_returns['次日买入后日卖出收益率'].describe([0.01, 0.05, 0.1, 0.9, 0.95, 0.99]))

# 定义目标变量：次日买入后日卖出是否盈利
data_with_returns['目标_次日买入后日卖出盈利'] = (data_with_returns['次日买入后日卖出收益率'] > 0).astype(int)

# 定义目标变量：次日买入后日卖出是否显著盈利 (收益率超过90%分位数)
significant_profit_threshold = data_with_returns['次日买入后日卖出收益率'].quantile(0.9)
data_with_returns['目标_次日买入后日卖出显著盈利'] = (data_with_returns['次日买入后日卖出收益率'] >= significant_profit_threshold).astype(int)
print(f"\n次日买入后日卖出显著盈利阈值 (90%分位数): {significant_profit_threshold:.4f}")

# 显示目标变量的分布
print("\n目标变量分布:")
for col in ['目标_次日买入后日卖出盈利', '目标_次日买入后日卖出显著盈利']:
    print(f"{col}:")
    print(data_with_returns[col].value_counts())
    print(f"比例: {data_with_returns[col].mean():.4f}")
    print()

# 特征工程
print("进行特征工程...")

# 提取技术指标特征
data_with_returns['均线多头排列'] = data_with_returns['技术指标'].str.contains('均线多头排列').astype(int)
data_with_returns['成交量放大'] = data_with_returns['技术指标'].str.contains('成交量放大').astype(int)
data_with_returns['MACD金叉'] = data_with_returns['技术指标'].str.contains('MACD金叉').astype(int)
data_with_returns['RSI反弹'] = data_with_returns['技术指标'].str.contains('RSI反弹').astype(int)
data_with_returns['KDJ金叉'] = data_with_returns['技术指标'].str.contains('KDJ金叉').astype(int)
data_with_returns['布林带突破'] = data_with_returns['技术指标'].str.contains('布林带突破').astype(int)

# 趋势特征
data_with_returns['趋势_强势上涨'] = (data_with_returns['趋势'] == 'strong_up').astype(int)
data_with_returns['趋势_上涨'] = (data_with_returns['趋势'] == 'up').astype(int)
data_with_returns['趋势_盘整'] = (data_with_returns['趋势'] == 'neutral').astype(int)
data_with_returns['趋势_下跌'] = (data_with_returns['趋势'] == 'down').astype(int)
data_with_returns['趋势_强势下跌'] = (data_with_returns['趋势'] == 'strong_down').astype(int)

# 价格与目标价/止损价的关系
data_with_returns['目标价差比'] = (data_with_returns['目标价'] - data_with_returns['当前价格']) / data_with_returns['当前价格']
data_with_returns['止损价差比'] = (data_with_returns['当前价格'] - data_with_returns['止损价']) / data_with_returns['当前价格']

# 添加前一日涨跌幅特征
def add_previous_day_change(group):
    """添加前一日涨跌幅特征"""
    group = group.sort_values('日期')
    group['前一日涨跌幅'] = group['涨跌幅'].shift(1)
    return group

data_with_returns = data_with_returns.groupby('股票代码', group_keys=False).apply(add_previous_day_change)

# 添加连续上涨/下跌天数特征
def add_streak_features(group):
    """添加连续上涨/下跌天数特征"""
    group = group.sort_values('日期')
    
    # 初始化连续上涨/下跌天数
    up_streak = 0
    down_streak = 0
    
    # 计算每一天的连续上涨/下跌天数
    streaks = []
    
    for i, row in group.iterrows():
        if pd.isna(row['前一日涨跌幅']):
            # 如果没有前一日数据，重置连续天数
            up_streak = 0
            down_streak = 0
        else:
            # 更新连续上涨/下跌天数
            if row['前一日涨跌幅'] > 0:
                up_streak += 1
                down_streak = 0
            elif row['前一日涨跌幅'] < 0:
                down_streak += 1
                up_streak = 0
            else:
                # 涨跌幅为0，保持不变
                pass
        
        streaks.append((up_streak, down_streak))
    
    # 添加连续上涨/下跌天数特征
    group['连续上涨天数'] = [s[0] for s in streaks]
    group['连续下跌天数'] = [s[1] for s in streaks]
    
    return group

data_with_returns = data_with_returns.groupby('股票代码', group_keys=False).apply(add_streak_features)

# 选择特征
features = [
    '技术强度', '涨跌幅', '前一日涨跌幅', '连续上涨天数', '连续下跌天数',
    '目标价差比', '止损价差比',
    '均线多头排列', '成交量放大', 'MACD金叉', 'RSI反弹', 'KDJ金叉', '布林带突破',
    '趋势_强势上涨', '趋势_上涨', '趋势_盘整', '趋势_下跌', '趋势_强势下跌'
]

# 处理缺失值
print("\n处理缺失值...")
data_with_returns = data_with_returns.dropna(subset=features)
print(f"处理缺失值后的数据形状: {data_with_returns.shape}")

# 分析目标变量
target_variables = ['目标_次日买入后日卖出盈利', '目标_次日买入后日卖出显著盈利']

for target in target_variables:
    print(f"\n\n分析目标变量: {target}")
    
    X = data_with_returns[features]
    y = data_with_returns[target]
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)
    
    print(f"训练集大小: {X_train.shape}")
    print(f"测试集大小: {X_test.shape}")
    
    # 创建包含预处理和模型的管道
    pipeline = Pipeline([
        ('imputer', SimpleImputer(strategy='mean')),  # 使用均值填充缺失值
        ('scaler', StandardScaler()),  # 标准化特征
        ('classifier', RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced'))
    ])
    
    # 训练模型
    print(f"\n训练随机森林模型...")
    pipeline.fit(X_train, y_train)
    
    # 评估模型
    y_pred = pipeline.predict(X_test)
    y_prob = pipeline.predict_proba(X_test)[:, 1]
    
    print(f"\n随机森林模型评估:")
    print(f"准确率: {accuracy_score(y_test, y_pred):.4f}")
    print(f"AUC: {roc_auc_score(y_test, y_prob):.4f}")
    print("\n分类报告:")
    print(classification_report(y_test, y_pred))
    
    # 特征重要性
    feature_importance = pd.DataFrame({
        'feature': features,
        'importance': pipeline.named_steps['classifier'].feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("\n特征重要性:")
    print(feature_importance.head(10))
    
    # 分析正样本的特征分布
    positive_samples = data_with_returns[data_with_returns[target] == 1]
    negative_samples = data_with_returns[data_with_returns[target] == 0]
    
    print(f"\n正样本({target})的特征统计:")
    for feature in feature_importance.head(5)['feature'].tolist():
        pos_mean = positive_samples[feature].mean()
        neg_mean = negative_samples[feature].mean()
        print(f"{feature}: 正样本均值={pos_mean:.4f}, 负样本均值={neg_mean:.4f}, 差异比例={pos_mean/neg_mean if neg_mean != 0 else 'inf':.4f}")
    
    # 找出最可能导致目标结果的条件组合
    print(f"\n寻找最可能导致{target}的条件组合...")
    
    # 获取前五个最重要的特征
    top_features = feature_importance.head(5)['feature'].tolist()
    print(f"分析前五个最重要的特征: {top_features}")
    
    # 尝试不同的条件组合，找出最佳规则
    print("\n尝试不同的条件组合，找出最佳规则...")
    
    # 测试不同数量的特征组合
    best_combination = None
    best_success_rate = 0
    best_sample_size = 0
    best_improvement = 0
    best_conditions = {}
    
    for n_features in range(1, min(6, len(top_features) + 1)):
        selected_features = top_features[:n_features]
        
        # 应用条件组合筛选股票
        filtered = data_with_returns.copy()
        conditions = {}
        
        for feature in selected_features:
            # 对于二元特征，使用是否为1作为条件
            if data_with_returns[feature].nunique() <= 2:
                # 检查哪个值对应更高的目标概率
                pos_rate_when_1 = data_with_returns[data_with_returns[feature] == 1][target].mean()
                pos_rate_when_0 = data_with_returns[data_with_returns[feature] == 0][target].mean()
                
                if pos_rate_when_1 > pos_rate_when_0:
                    filtered = filtered[filtered[feature] == 1]
                    conditions[feature] = 1
                else:
                    filtered = filtered[filtered[feature] == 0]
                    conditions[feature] = 0
            else:
                # 对于连续特征，检查是大于还是小于正样本平均值更好
                threshold = positive_samples[feature].mean()
                
                # 检查大于阈值的目标概率
                greater_rate = data_with_returns[data_with_returns[feature] >= threshold][target].mean()
                less_rate = data_with_returns[data_with_returns[feature] < threshold][target].mean()
                
                if greater_rate > less_rate:
                    filtered = filtered[filtered[feature] >= threshold]
                    conditions[feature] = (threshold, ">=")
                else:
                    filtered = filtered[filtered[feature] < threshold]
                    conditions[feature] = (threshold, "<")
        
        # 计算满足条件的股票目标概率
        if len(filtered) > 100:  # 确保样本量足够
            rate = filtered[target].mean()
            improvement = rate / data_with_returns[target].mean()
            
            print(f"使用前{n_features}个特征: 样本量={len(filtered)}, 成功率={rate:.4f}, 提升={improvement:.2f}倍")
            
            if improvement > best_improvement:
                best_success_rate = rate
                best_combination = selected_features
                best_sample_size = len(filtered)
                best_improvement = improvement
                best_conditions = conditions
    
    print(f"\n最佳特征组合: {best_combination}")
    print(f"样本量: {best_sample_size}")
    print(f"成功率: {best_success_rate:.4f}")
    print(f"相比基准提升: {best_improvement:.2f}倍")
    
    # 总结发现的规则
    print("\n总结发现的最佳规则:")
    print(f"基于特征重要性和模型分析，以下是预测{target}的关键规则:")
    
    for feature, condition in best_conditions.items():
        if isinstance(condition, tuple):
            threshold, operator = condition
            print(f"- {feature} {operator} {threshold:.4f}")
        else:
            print(f"- {feature} = {condition}")
    
    # 应用最佳规则筛选股票
    best_filtered = data_with_returns.copy()
    for feature, condition in best_conditions.items():
        if isinstance(condition, tuple):
            threshold, operator = condition
            if operator == ">=":
                best_filtered = best_filtered[best_filtered[feature] >= threshold]
            else:
                best_filtered = best_filtered[best_filtered[feature] < threshold]
        else:
            best_filtered = best_filtered[best_filtered[feature] == condition]
    
    # 保存满足条件的股票列表
    if len(best_filtered) > 0:
        recent_date = data_with_returns['日期'].max()
        recent_signals = best_filtered[best_filtered['日期'] == recent_date][['股票代码', '股票名称', '当前价格', '涨跌幅', '技术强度', '技术指标']]
        
        if len(recent_signals) > 0:
            print(f"\n最近日期 {recent_date.date()} 满足条件的股票:")
            print(recent_signals.head(10))
            
            # 保存推荐股票
            filename = f"{target}_推荐股票.xlsx"
            recent_signals.to_excel(filename, index=False)
            print(f"已将推荐股票保存到 '{filename}'")
        else:
            print(f"\n最近日期 {recent_date.date()} 没有满足条件的股票")
    
    # 验证规则在历史数据上的表现
    print("\n验证规则在历史数据上的表现:")
    
    # 按日期分组，计算每天满足条件的股票在次日买入后日卖出的平均收益率
    daily_performance = []
    
    for date in sorted(data_with_returns['日期'].unique())[:-2]:  # 排除最后两天，因为没有完整的后日数据
        # 获取当天满足条件的股票
        day_data = best_filtered[best_filtered['日期'] == date]
        
        if len(day_data) > 0:
            # 计算这些股票次日买入后日卖出的平均收益率
            avg_return = day_data['次日买入后日卖出收益率'].mean()
            success_rate = day_data[target].mean()
            
            daily_performance.append({
                '日期': date,
                '满足条件的股票数': len(day_data),
                '次日买入后日卖出平均收益率': avg_return * 100,  # 转换为百分比
                f'{target}成功率': success_rate
            })
    
    if daily_performance:
        daily_df = pd.DataFrame(daily_performance)
        print(daily_df)
        
        # 计算总体表现
        print("\n总体表现:")
        print(f"平均每天满足条件的股票数: {daily_df['满足条件的股票数'].mean():.2f}")
        print(f"次日买入后日卖出平均收益率: {daily_df['次日买入后日卖出平均收益率'].mean():.2f}%")
        print(f"{target}平均成功率: {daily_df[f'{target}成功率'].mean():.4f}")
        
        # 保存每日表现数据
        daily_df.to_excel(f"{target}_每日表现.xlsx", index=False)
        print(f"已将每日表现数据保存到 '{target}_每日表现.xlsx'")
    else:
        print("没有足够的历史数据来验证规则表现")
