import os
import pandas as pd
import time
import argparse

def convert_excel_to_parquet(input_file, output_file=None):
    """将Excel文件转换为Parquet格式，以加速数据加载"""
    start_time = time.time()
    print(f"开始转换文件: {input_file}")
    
    # 如果未指定输出文件，使用相同的文件名但扩展名为.parquet
    if output_file is None:
        output_file = os.path.splitext(input_file)[0] + '.parquet'
    
    # 读取Excel文件
    try:
        df = pd.read_excel(input_file)
        print(f"成功读取Excel文件，共 {len(df)} 行，{len(df.columns)} 列")
        
        # 转换日期列
        date_columns = [col for col in df.columns if '日期' in col or 'date' in col.lower()]
        for col in date_columns:
            if col in df.columns:
                try:
                    df[col] = pd.to_datetime(df[col])
                    print(f"已将列 '{col}' 转换为日期类型")
                except Exception as e:
                    print(f"警告: 无法将列 '{col}' 转换为日期类型: {e}")
        
        # 保存为Parquet格式
        df.to_parquet(output_file, index=False)
        
        # 验证转换结果
        parquet_size = os.path.getsize(output_file)
        excel_size = os.path.getsize(input_file)
        compression_ratio = excel_size / parquet_size if parquet_size > 0 else 0
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"转换完成！")
        print(f"Excel文件大小: {excel_size / 1024 / 1024:.2f} MB")
        print(f"Parquet文件大小: {parquet_size / 1024 / 1024:.2f} MB")
        print(f"压缩比: {compression_ratio:.2f}x")
        print(f"耗时: {elapsed_time:.2f} 秒")
        
        return True
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def convert_all_excel_files(directory, pattern=None):
    """转换指定目录下的所有Excel文件"""
    print(f"开始转换目录 {directory} 下的所有Excel文件")
    
    # 查找所有Excel文件
    excel_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.xlsx') or file.endswith('.xls'):
                if pattern is None or pattern in file:
                    excel_files.append(os.path.join(root, file))
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    # 转换所有文件
    success_count = 0
    for file in excel_files:
        if convert_excel_to_parquet(file):
            success_count += 1
    
    print(f"转换完成！成功: {success_count}, 失败: {len(excel_files) - success_count}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='将Excel文件转换为Parquet格式')
    parser.add_argument('--file', type=str, help='要转换的Excel文件路径')
    parser.add_argument('--dir', type=str, help='要转换的目录路径')
    parser.add_argument('--pattern', type=str, help='文件名匹配模式')
    
    args = parser.parse_args()
    
    if args.file:
        convert_excel_to_parquet(args.file)
    elif args.dir:
        convert_all_excel_files(args.dir, args.pattern)
    else:
        print("请指定要转换的文件或目录")
