import itertools

# 优化后的特征及其值的数量
features = {
    '技术强度': 1,  # 固定值，每个技术强度一个
    '连续技术强度3天数': 3,  # 84, 156, 228
    '连续技术强度5天数': 3,   # 140, 260, 380
    '连续技术强度10天数': 3,  # 280, 520, 760
    '成交量是前一日几倍': 4,   # 1.0, 1.5, 2.0, 3.0
    '技术指标特征': 20,        # 生成的技术指标组合
    '趋势组合': 16,            # 生成的趋势组合
    '日内股票标记': 23         # 生成的日内股票标记
}

print("优化后的特征及其值的数量:")
for feature, count in features.items():
    print(f"  {feature}: {count}")

print("\n计算各种特征组合的数量:")

# 技术强度值的数量
tech_strength_count = 6  # 28, 42, 57, 71, 85, 100

# 计算不同特征数量的组合
for size in [3, 4, 5, 6, 7, 8]:
    print(f"\n{size}个特征的组合:")

    # 除了技术强度外的其他特征
    other_features = {k: v for k, v in features.items() if k != '技术强度'}
    other_feature_names = list(other_features.keys())

    # 需要从其他特征中选择 size-1 个特征（因为技术强度是必须的）
    if size - 1 > len(other_feature_names):
        print(f"  无法生成{size}个特征的组合，因为总特征数不足")
        continue

    total_combinations = 0
    feature_combinations = list(itertools.combinations(other_feature_names, size - 1))

    print(f"  特征组合数: {len(feature_combinations)}")

    # 生成所有可能的特征组合
    for i, feature_combo in enumerate(feature_combinations, 1):
        # 计算这个特征组合的值数量
        combo_count = 1
        for feature in feature_combo:
            combo_count *= other_features[feature]

        total_combinations += combo_count

        # 显示每个特征组合的详细信息
        feature_values = [f"{feature}({other_features[feature]})" for feature in feature_combo]
        print(f"    {i:2d}. 技术强度 + {' + '.join(feature_values)}: {combo_count:,}")

    print(f"  单个技术强度的总组合数: {total_combinations:,}")

    # 所有技术强度的总组合数
    total_all_tech_strength = total_combinations * tech_strength_count
    print(f"  所有技术强度的总组合数: {total_all_tech_strength:,}")

    # 检查是否超过Excel限制
    if total_combinations > 500000:
        print(f"  ⚠️  单个技术强度超过Excel限制(50万行)")
        num_files = (total_combinations + 499999) // 500000
        print(f"  每个技术强度需要拆分成 {num_files} 个文件")
        total_files = num_files * tech_strength_count
        print(f"  所有技术强度总共需要 {total_files} 个文件")
    else:
        print(f"  ✅ 单个技术强度在Excel限制范围内")
        print(f"  所有技术强度总共需要 {tech_strength_count} 个文件")

print(f"\n总结:")
print(f"技术强度值数量: {tech_strength_count}")

# 计算总的文件数量
total_files = 0
for size in [3, 4, 5, 6, 7]:
    other_features = {k: v for k, v in features.items() if k != '技术强度'}
    other_feature_names = list(other_features.keys())

    if size - 1 <= len(other_feature_names):
        total_combinations = 0
        for feature_combo in itertools.combinations(other_feature_names, size - 1):
            combo_count = 1
            for feature in feature_combo:
                combo_count *= other_features[feature]
            total_combinations += combo_count

        if total_combinations > 500000:
            num_files = (total_combinations + 499999) // 500000
            files_per_size = num_files * tech_strength_count
        else:
            files_per_size = tech_strength_count

        total_files += files_per_size
        print(f"{size}个特征: {files_per_size} 个文件")

print(f"预计总文件数: {total_files}")

# 计算优化前后的对比
print(f"\n优化效果对比:")
print(f"优化前成交量倍数: 7个值 (0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5)")
print(f"优化后成交量倍数: 5个值 (0.5, 1.0, 1.5, 2.0, 3.0)")
print(f"成交量倍数减少: {7-5} 个值")

# 计算连续技术强度的优化效果
print(f"优化前连续技术强度3天数: 4个值")
print(f"优化后连续技术强度3天数: 3个值")
print(f"连续技术强度减少: {4-3} 个值")

reduction_ratio = (7/5) * (4/3) * (4/3) * (4/3)
print(f"总体组合数量减少比例: {reduction_ratio:.2f}倍")
