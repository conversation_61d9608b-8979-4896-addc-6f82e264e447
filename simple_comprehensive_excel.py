#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版全面策略Excel报告生成器
作者: Augment AI
版本: 1.0.0

该脚本生成两类Excel文件：
1. 主Excel文件：包含所有策略的汇总和条件
2. 每个策略的详细分析Excel文件：包含单个策略的详细分析
"""

import pandas as pd
import numpy as np
import os
import time
from datetime import datetime
from tqdm import tqdm
import random

# 创建结果目录
results_dir = 'comprehensive_strategy_results'
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

# 创建策略详细分析目录
strategy_details_dir = os.path.join(results_dir, 'strategy_details')
if not os.path.exists(strategy_details_dir):
    os.makedirs(strategy_details_dir)

def load_data(data_file='股票明细.xlsx'):
    """加载数据"""
    print("正在加载数据...")
    try:
        df = pd.read_excel(data_file)
        print(f"成功加载数据，共 {len(df)} 条记录")
        
        # 确保日期列是datetime类型
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 获取所有日期
        all_dates = sorted(df['日期'].unique())
        print(f"数据日期范围: {all_dates[0].strftime('%Y-%m-%d')} 至 {all_dates[-1].strftime('%Y-%m-%d')}")
        
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def backtest_feature_combination(data, feature_combination, start_date, end_date, initial_capital=100000):
    """回测特征组合策略"""
    # 确保日期是datetime类型
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)
    
    # 筛选日期范围内的数据
    date_range_data = data[(data['日期'] >= start_date) & (data['日期'] <= end_date)]
    
    # 获取日期范围内的所有交易日
    trading_dates = sorted(date_range_data['日期'].unique())
    
    # 初始化结果
    results = {
        'feature_combination': feature_combination,
        'feature_count': len(feature_combination),
        'initial_capital': initial_capital,
        'current_capital': initial_capital,
        'total_trades': 0,
        'win_count': 0,
        'daily_returns': [],
        'daily_win_rates': [],
        'daily_trade_counts': [],
        'capital_history': [initial_capital],
        'dates': [trading_dates[0]],
        'feature_conditions': []  # 存储每个特征的具体筛选条件
    }
    
    # 记录每个特征的具体筛选条件
    for feature in feature_combination:
        if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', 
                     '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势', 
                     '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势', 
                     '连续技术强度10天数趋势', '连续技术强度3天数价格趋势', 
                     '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势', 
                     '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势', 
                     '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
            # 二元特征，使用==1条件
            results['feature_conditions'].append({
                'feature': feature,
                'condition': '== 1',
                'description': f"{feature} 为 1（是）"
            })
        elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
            # 连续值特征，使用>=中位数条件
            # 计算所有交易日的中位数
            all_medians = []
            for date in trading_dates[:-1]:  # 排除最后一个交易日
                daily_data = date_range_data[date_range_data['日期'] == date]
                median_value = np.median(daily_data[feature])
                all_medians.append(median_value)
            
            # 使用所有交易日中位数的平均值作为条件
            avg_median = np.mean(all_medians)
            
            results['feature_conditions'].append({
                'feature': feature,
                'condition': f">= {avg_median:.2f}",
                'description': f"{feature} 大于等于 {avg_median:.2f}（日均中位数）"
            })
        elif feature == '看涨技术指标数量':
            # 看涨技术指标数量，使用>=3条件
            results['feature_conditions'].append({
                'feature': feature,
                'condition': '>= 3',
                'description': f"{feature} 大于等于 3"
            })
    
    # 对每个交易日进行回测
    for i, current_date in enumerate(trading_dates):
        # 跳过最后一个交易日，因为没有后续数据来验证
        if i >= len(trading_dates) - 1:
            continue
        
        # 获取次日日期
        next_date = trading_dates[i + 1]
        
        # 获取当日数据
        daily_data = date_range_data[date_range_data['日期'] == current_date]
        
        # 应用特征组合条件
        selected = daily_data.copy()
        for feature in feature_combination:
            if feature in ['技术指标_均线多头排列', '技术指标_MACD金叉', '技术指标_RSI反弹', 
                         '技术指标_KDJ金叉', '技术指标_布林带突破', '价格趋势', '涨跌幅趋势', 
                         '技术强度趋势', '连续技术强度3天数趋势', '连续技术强度5天数趋势', 
                         '连续技术强度10天数趋势', '连续技术强度3天数价格趋势', 
                         '连续技术强度5天数价格趋势', '连续技术强度10天数价格趋势', 
                         '连续技术强度3天数涨跌幅趋势', '连续技术强度5天数涨跌幅趋势', 
                         '连续技术强度10天数涨跌幅趋势', '开盘涨跌']:
                # 二元特征，使用==1条件
                selected = selected[selected[feature] == 1]
            elif feature in ['技术强度', '连续技术强度3天数', '连续技术强度5天数', '连续技术强度10天数']:
                # 连续值特征，使用>=中位数条件
                median_value = np.median(daily_data[feature])
                selected = selected[selected[feature] >= median_value]
            elif feature == '看涨技术指标数量':
                # 看涨技术指标数量，使用>=3条件
                selected = selected[selected[feature] >= 3]
        
        # 如果有推荐的股票，模拟买入
        if len(selected) > 0:
            # 获取推荐股票的代码
            recommended_codes = selected['股票代码'].tolist()
            
            # 获取次日这些股票的数据
            next_day_data = date_range_data[
                (date_range_data['日期'] == next_date) & 
                (date_range_data['股票代码'].isin(recommended_codes))
            ]
            
            # 如果次日有这些股票的数据
            if len(next_day_data) > 0:
                # 计算平均涨跌幅
                avg_return = next_day_data['涨跌幅'].mean() / 100  # 转换为小数
                
                # 计算盈利股票数量
                win_stocks = len(next_day_data[next_day_data['涨跌幅'] > 0])
                win_rate = win_stocks / len(next_day_data) * 100
                
                # 更新资金
                results['current_capital'] *= (1 + avg_return)
                
                # 更新统计数据
                results['total_trades'] += len(next_day_data)
                results['win_count'] += win_stocks
                results['daily_returns'].append(avg_return * 100)  # 转换为百分比
                results['daily_win_rates'].append(win_rate)
                results['daily_trade_counts'].append(len(next_day_data))
                results['capital_history'].append(results['current_capital'])
                results['dates'].append(next_date)
            else:
                # 如果次日没有这些股票的数据，资金保持不变
                results['capital_history'].append(results['current_capital'])
                results['dates'].append(next_date)
                results['daily_returns'].append(0)
                results['daily_win_rates'].append(0)
                results['daily_trade_counts'].append(0)
        else:
            # 如果没有推荐股票，资金保持不变
            results['capital_history'].append(results['current_capital'])
            results['dates'].append(next_date)
            results['daily_returns'].append(0)
            results['daily_win_rates'].append(0)
            results['daily_trade_counts'].append(0)
    
    # 计算最终统计结果
    if results['total_trades'] > 0:
        results['win_rate'] = results['win_count'] / results['total_trades'] * 100
        results['total_return_pct'] = (results['current_capital'] / initial_capital - 1) * 100
        results['avg_daily_return'] = np.mean([r for r in results['daily_returns'] if r != 0])
        results['avg_daily_trades'] = np.mean([count for count in results['daily_trade_counts'] if count > 0])
        results['trading_days'] = len([count for count in results['daily_trade_counts'] if count > 0])
        results['total_days'] = len(trading_dates) - 1
        results['trading_frequency'] = results['trading_days'] / results['total_days'] * 100
    else:
        results['win_rate'] = 0
        results['total_return_pct'] = 0
        results['avg_daily_return'] = 0
        results['avg_daily_trades'] = 0
        results['trading_days'] = 0
        results['total_days'] = len(trading_dates) - 1
        results['trading_frequency'] = 0
    
    return results

def backtest_predefined_combinations(data, start_date, end_date):
    """回测预定义的特征组合"""
    print("回测预定义的特征组合...")
    
    # 定义特征组合
    predefined_combinations = [
        # 2特征组合
        ('技术强度趋势', '技术指标_KDJ金叉'),
        ('连续技术强度5天数趋势', '技术指标_KDJ金叉'),
        ('技术强度趋势', '技术指标_RSI反弹'),
        ('技术强度趋势', '连续技术强度5天数趋势'),
        ('技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '技术强度趋势'),
        ('技术强度', '技术指标_KDJ金叉'),
        ('技术强度', '技术指标_RSI反弹'),
        ('连续技术强度5天数', '技术指标_KDJ金叉'),
        ('连续技术强度5天数', '技术指标_RSI反弹'),
        
        # 3特征组合
        ('技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度趋势', '连续技术强度5天数趋势', '技术指标_KDJ金叉'),
        ('技术强度', '技术强度趋势', '技术指标_KDJ金叉'),
        ('连续技术强度5天数', '技术强度趋势', '技术指标_KDJ金叉'),
        ('技术强度', '连续技术强度5天数', '技术指标_KDJ金叉'),
        ('技术强度', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('连续技术强度5天数', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '连续技术强度5天数', '技术强度趋势'),
        ('技术强度', '连续技术强度5天数', '连续技术强度5天数趋势'),
        
        # 4特征组合
        ('技术强度趋势', '连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('连续技术强度5天数', '技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '连续技术强度5天数', '技术强度趋势', '技术指标_KDJ金叉'),
        ('技术强度', '连续技术强度5天数', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        
        # 5特征组合
        ('技术强度', '连续技术强度5天数', '技术强度趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '连续技术强度5天数', '连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉'),
        ('技术强度', '技术强度趋势', '连续技术强度5天数趋势', '技术指标_RSI反弹', '技术指标_KDJ金叉')
    ]
    
    # 回测每个组合
    results = []
    for combination in tqdm(predefined_combinations, desc="回测进度"):
        result = backtest_feature_combination(data, combination, start_date, end_date)
        
        # 只保留有交易的结果
        if result['total_trades'] > 0:
            results.append(result)
    
    # 按总收益率排序
    results.sort(key=lambda x: x['total_return_pct'], reverse=True)
    
    return results

def create_main_excel(results, output_file):
    """创建主Excel文件，包含所有策略的汇总和条件"""
    print(f"创建主Excel文件: {output_file}")
    
    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 创建策略汇总表格
        create_strategy_summary_sheet(results, writer)
        
        # 创建策略条件表格
        create_strategy_conditions_sheet(results, writer)
    
    print(f"主Excel文件已保存到: {output_file}")

def create_strategy_summary_sheet(results, writer):
    """创建策略汇总表格"""
    # 创建汇总数据
    summary_data = []
    
    for i, result in enumerate(results, 1):
        feature_str = ', '.join(result['feature_combination'])
        summary_data.append({
            '策略编号': i,
            '策略组合': feature_str,
            '特征数量': result['feature_count'],
            '总收益率(%)': result['total_return_pct'],
            '平均收益率(%)': result['avg_daily_return'],
            '平均胜率(%)': result['win_rate'],
            '平均每日交易笔数': result['avg_daily_trades'],
            '总交易笔数': result['total_trades'],
            '交易天数': result['trading_days'],
            '总天数': result['total_days'],
            '交易频率(%)': result['trading_frequency'],
            '详细分析文件': f"strategy_{i}.xlsx"
        })
    
    # 转换为DataFrame
    summary_df = pd.DataFrame(summary_data)
    
    # 写入Excel
    summary_df.to_excel(writer, sheet_name='策略汇总', index=False)

def create_strategy_conditions_sheet(results, writer):
    """创建策略条件表格"""
    # 创建策略条件数据
    conditions_data = []
    
    for i, result in enumerate(results, 1):
        feature_str = ', '.join(result['feature_combination'])
        
        # 生成策略条件描述
        conditions_str = ' AND '.join([cond['description'] for cond in result['feature_conditions']])
        
        # 生成策略代码
        code_parts = []
        for cond in result['feature_conditions']:
            code_parts.append(f"df['{cond['feature']}'] {cond['condition']}")
        code_str = 'df[' + ' & '.join(code_parts) + ']'
        
        conditions_data.append({
            '策略编号': i,
            '策略组合': feature_str,
            '特征数量': result['feature_count'],
            '总收益率(%)': result['total_return_pct'],
            '平均胜率(%)': result['win_rate'],
            '策略条件描述': conditions_str,
            '策略代码': code_str
        })
    
    # 转换为DataFrame
    conditions_df = pd.DataFrame(conditions_data)
    
    # 写入Excel
    conditions_df.to_excel(writer, sheet_name='策略条件', index=False)

def create_strategy_detail_excel(result, index, output_file):
    """创建策略详细分析Excel文件"""
    print(f"创建策略详细分析Excel文件: {output_file}")
    
    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 创建策略统计信息表格
        create_strategy_stats_sheet(result, index, writer)
        
        # 创建策略条件详情表格
        create_strategy_conditions_detail_sheet(result, writer)
        
        # 创建每日表现数据表格
        create_daily_performance_sheet(result, writer)
    
    print(f"策略详细分析Excel文件已保存到: {output_file}")

def create_strategy_stats_sheet(result, index, writer):
    """创建策略统计信息表格"""
    # 创建策略统计信息
    stats_data = {
        '统计项': [
            '策略编号',
            '特征组合',
            '特征数量',
            '总收益率(%)',
            '平均收益率(%)',
            '平均胜率(%)',
            '平均每日交易笔数',
            '总交易笔数',
            '交易天数',
            '总天数',
            '交易频率(%)'
        ],
        '数值': [
            index,
            ', '.join(result['feature_combination']),
            result['feature_count'],
            result['total_return_pct'],
            result['avg_daily_return'],
            result['win_rate'],
            result['avg_daily_trades'],
            result['total_trades'],
            result['trading_days'],
            result['total_days'],
            result['trading_frequency']
        ]
    }
    
    # 转换为DataFrame
    stats_df = pd.DataFrame(stats_data)
    
    # 写入Excel
    stats_df.to_excel(writer, sheet_name='策略统计', index=False)

def create_strategy_conditions_detail_sheet(result, writer):
    """创建策略条件详情表格"""
    # 创建策略条件数据
    conditions_data = {
        '特征': [cond['feature'] for cond in result['feature_conditions']],
        '条件': [cond['condition'] for cond in result['feature_conditions']],
        '描述': [cond['description'] for cond in result['feature_conditions']]
    }
    
    # 转换为DataFrame
    conditions_df = pd.DataFrame(conditions_data)
    
    # 写入Excel
    conditions_df.to_excel(writer, sheet_name='策略条件详情', index=False)

def create_daily_performance_sheet(result, writer):
    """创建每日表现数据表格"""
    # 创建每日表现数据
    daily_data = {
        '日期': result['dates'],
        '资金': result['capital_history'],
        '收益率(%)': result['daily_returns'] + [0] * (len(result['dates']) - len(result['daily_returns'])),
        '胜率(%)': result['daily_win_rates'] + [0] * (len(result['dates']) - len(result['daily_win_rates'])),
        '交易笔数': result['daily_trade_counts'] + [0] * (len(result['dates']) - len(result['daily_trade_counts']))
    }
    
    # 转换为DataFrame
    daily_df = pd.DataFrame(daily_data)
    
    # 写入Excel
    daily_df.to_excel(writer, sheet_name='每日表现', index=False)

if __name__ == "__main__":
    # 加载数据
    df = load_data()
    if df is None:
        exit(1)
    
    # 设置回测参数
    start_date = '2025-03-01'  # 使用更长的历史数据
    end_date = '2025-05-15'
    
    print(f"回测周期: {start_date} 至 {end_date}")
    
    # 回测预定义的特征组合
    results = backtest_predefined_combinations(df, start_date, end_date)
    
    # 创建主Excel文件
    main_excel_file = os.path.join(results_dir, f"所有策略汇总_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
    create_main_excel(results, main_excel_file)
    
    # 为每个策略创建详细分析Excel文件
    for i, result in enumerate(results, 1):
        strategy_excel_file = os.path.join(strategy_details_dir, f"strategy_{i}.xlsx")
        create_strategy_detail_excel(result, i, strategy_excel_file)
