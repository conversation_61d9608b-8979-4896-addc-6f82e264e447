import pandas as pd
import numpy as np
import requests
import json
from datetime import datetime, timedelta
import os
import time
import random
from bs4 import BeautifulSoup
import re

class EnhancedDataSources:
    """
    增强数据源模块，用于获取宏观经济数据、行业基本面数据等
    """
    
    def __init__(self):
        """初始化数据源模块"""
        # 创建数据目录
        if not os.path.exists('enhanced_data'):
            os.makedirs('enhanced_data')
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    def get_macro_economic_data(self):
        """
        获取宏观经济数据
        返回宏观经济数据DataFrame
        """
        print("获取宏观经济数据...")
        
        # 宏观经济数据文件路径
        macro_file = 'enhanced_data/macro_economic_data.xlsx'
        
        # 如果今天已经获取过数据，直接返回
        if os.path.exists(macro_file) and self._is_file_updated_today(macro_file):
            print(f"今日已获取宏观经济数据，直接读取 {macro_file}")
            return pd.read_excel(macro_file)
        
        try:
            # 获取GDP增长率
            gdp_growth = self._get_gdp_growth()
            
            # 获取CPI数据
            cpi = self._get_cpi_data()
            
            # 获取PMI数据
            pmi = self._get_pmi_data()
            
            # 获取利率数据
            interest_rate = self._get_interest_rate()
            
            # 获取汇率数据
            exchange_rate = self._get_exchange_rate()
            
            # 获取大宗商品价格
            commodity_prices = self._get_commodity_prices()
            
            # 合并所有宏观经济数据
            macro_data = pd.DataFrame({
                '日期': [datetime.now().strftime('%Y-%m-%d')],
                'GDP增长率': [gdp_growth],
                'CPI同比': [cpi],
                'PMI': [pmi],
                '贷款基准利率': [interest_rate],
                '美元兑人民币汇率': [exchange_rate],
                '原油价格': [commodity_prices.get('原油', 0)],
                '黄金价格': [commodity_prices.get('黄金', 0)],
                '铜价格': [commodity_prices.get('铜', 0)]
            })
            
            # 保存宏观经济数据
            macro_data.to_excel(macro_file, index=False)
            print(f"已将宏观经济数据保存至 {macro_file}")
            
            return macro_data
        except Exception as e:
            print(f"获取宏观经济数据失败: {e}")
            
            # 如果获取失败但文件存在，返回最近的数据
            if os.path.exists(macro_file):
                print(f"返回最近的宏观经济数据")
                return pd.read_excel(macro_file)
            
            # 如果文件不存在，返回空DataFrame
            return pd.DataFrame()
    
    def get_industry_fundamentals(self):
        """
        获取行业基本面数据
        返回行业基本面数据DataFrame
        """
        print("获取行业基本面数据...")
        
        # 行业基本面数据文件路径
        industry_file = 'enhanced_data/industry_fundamentals.xlsx'
        
        # 如果今天已经获取过数据，直接返回
        if os.path.exists(industry_file) and self._is_file_updated_today(industry_file):
            print(f"今日已获取行业基本面数据，直接读取 {industry_file}")
            return pd.read_excel(industry_file)
        
        try:
            # 获取行业市盈率
            industry_pe = self._get_industry_pe()
            
            # 获取行业市净率
            industry_pb = self._get_industry_pb()
            
            # 获取行业营收增长率
            industry_revenue_growth = self._get_industry_revenue_growth()
            
            # 获取行业净利润增长率
            industry_profit_growth = self._get_industry_profit_growth()
            
            # 获取行业资金流向
            industry_fund_flow = self._get_industry_fund_flow()
            
            # 合并所有行业基本面数据
            industry_data = pd.DataFrame()
            
            for industry in industry_pe.keys():
                industry_data = pd.concat([industry_data, pd.DataFrame({
                    '日期': [datetime.now().strftime('%Y-%m-%d')],
                    '行业': [industry],
                    '市盈率': [industry_pe.get(industry, 0)],
                    '市净率': [industry_pb.get(industry, 0)],
                    '营收增长率': [industry_revenue_growth.get(industry, 0)],
                    '净利润增长率': [industry_profit_growth.get(industry, 0)],
                    '资金流向': [industry_fund_flow.get(industry, 0)]
                })], ignore_index=True)
            
            # 保存行业基本面数据
            industry_data.to_excel(industry_file, index=False)
            print(f"已将行业基本面数据保存至 {industry_file}")
            
            return industry_data
        except Exception as e:
            print(f"获取行业基本面数据失败: {e}")
            
            # 如果获取失败但文件存在，返回最近的数据
            if os.path.exists(industry_file):
                print(f"返回最近的行业基本面数据")
                return pd.read_excel(industry_file)
            
            # 如果文件不存在，返回空DataFrame
            return pd.DataFrame()
    
    def get_stock_fundamentals(self, stock_codes):
        """
        获取股票基本面数据
        参数:
            stock_codes: 股票代码列表
        返回股票基本面数据DataFrame
        """
        print("获取股票基本面数据...")
        
        # 股票基本面数据文件路径
        stock_file = 'enhanced_data/stock_fundamentals.xlsx'
        
        # 如果今天已经获取过数据，直接返回
        if os.path.exists(stock_file) and self._is_file_updated_today(stock_file):
            print(f"今日已获取股票基本面数据，直接读取 {stock_file}")
            stock_data = pd.read_excel(stock_file)
            
            # 检查是否包含所有需要的股票
            existing_codes = set(stock_data['股票代码'].unique())
            missing_codes = [code for code in stock_codes if code not in existing_codes]
            
            if not missing_codes:
                return stock_data
            
            print(f"需要获取 {len(missing_codes)} 只新股票的基本面数据")
            stock_codes = missing_codes
        
        try:
            stock_data = pd.DataFrame()
            
            for code in stock_codes:
                # 获取股票基本信息
                stock_info = self._get_stock_info(code)
                
                # 获取股票财务指标
                financial_indicators = self._get_financial_indicators(code)
                
                # 获取股票估值指标
                valuation_indicators = self._get_valuation_indicators(code)
                
                # 获取股票成长指标
                growth_indicators = self._get_growth_indicators(code)
                
                # 获取股票质量指标
                quality_indicators = self._get_quality_indicators(code)
                
                # 合并股票基本面数据
                stock_data = pd.concat([stock_data, pd.DataFrame({
                    '日期': [datetime.now().strftime('%Y-%m-%d')],
                    '股票代码': [code],
                    '股票名称': [stock_info.get('name', '')],
                    '所属行业': [stock_info.get('industry', '')],
                    '市值(亿元)': [stock_info.get('market_cap', 0)],
                    '市盈率': [valuation_indicators.get('pe', 0)],
                    '市净率': [valuation_indicators.get('pb', 0)],
                    '市销率': [valuation_indicators.get('ps', 0)],
                    '股息率': [valuation_indicators.get('dividend_yield', 0)],
                    '营收增长率': [growth_indicators.get('revenue_growth', 0)],
                    '净利润增长率': [growth_indicators.get('profit_growth', 0)],
                    'ROE': [quality_indicators.get('roe', 0)],
                    '毛利率': [quality_indicators.get('gross_margin', 0)],
                    '净利率': [quality_indicators.get('net_margin', 0)],
                    '资产负债率': [financial_indicators.get('debt_ratio', 0)],
                    '流动比率': [financial_indicators.get('current_ratio', 0)],
                    '速动比率': [financial_indicators.get('quick_ratio', 0)]
                })], ignore_index=True)
                
                # 随机暂停1-3秒，避免请求过于频繁
                time.sleep(random.uniform(1, 3))
            
            # 如果文件存在，合并新数据和旧数据
            if os.path.exists(stock_file):
                old_data = pd.read_excel(stock_file)
                
                # 删除旧数据中与新数据重复的记录
                old_data = old_data[~old_data['股票代码'].isin(stock_data['股票代码'])]
                
                # 合并新旧数据
                stock_data = pd.concat([old_data, stock_data], ignore_index=True)
            
            # 保存股票基本面数据
            stock_data.to_excel(stock_file, index=False)
            print(f"已将股票基本面数据保存至 {stock_file}")
            
            return stock_data
        except Exception as e:
            print(f"获取股票基本面数据失败: {e}")
            
            # 如果获取失败但文件存在，返回最近的数据
            if os.path.exists(stock_file):
                print(f"返回最近的股票基本面数据")
                return pd.read_excel(stock_file)
            
            # 如果文件不存在，返回空DataFrame
            return pd.DataFrame()
    
    def _is_file_updated_today(self, file_path):
        """
        检查文件是否在今天更新过
        参数:
            file_path: 文件路径
        返回布尔值
        """
        if not os.path.exists(file_path):
            return False
        
        file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
        today = datetime.now().date()
        
        return file_time.date() == today
    
    def _get_gdp_growth(self):
        """
        获取GDP增长率
        返回最新的GDP同比增长率
        """
        # 模拟获取GDP增长率
        # 实际应用中，可以从国家统计局网站或金融数据API获取
        return 5.2  # 假设当前GDP增长率为5.2%
    
    def _get_cpi_data(self):
        """
        获取CPI数据
        返回最新的CPI同比增长率
        """
        # 模拟获取CPI数据
        return 2.1  # 假设当前CPI同比增长率为2.1%
    
    def _get_pmi_data(self):
        """
        获取PMI数据
        返回最新的PMI指数
        """
        # 模拟获取PMI数据
        return 50.8  # 假设当前PMI指数为50.8
    
    def _get_interest_rate(self):
        """
        获取利率数据
        返回最新的贷款基准利率
        """
        # 模拟获取利率数据
        return 3.85  # 假设当前贷款基准利率为3.85%
    
    def _get_exchange_rate(self):
        """
        获取汇率数据
        返回最新的美元兑人民币汇率
        """
        # 模拟获取汇率数据
        return 6.45  # 假设当前美元兑人民币汇率为6.45
    
    def _get_commodity_prices(self):
        """
        获取大宗商品价格
        返回最新的大宗商品价格字典
        """
        # 模拟获取大宗商品价格
        return {
            '原油': 75.2,  # 美元/桶
            '黄金': 1850.5,  # 美元/盎司
            '铜': 9500.3  # 美元/吨
        }
    
    def _get_industry_pe(self):
        """
        获取行业市盈率
        返回行业市盈率字典
        """
        # 模拟获取行业市盈率
        return {
            '银行': 6.5,
            '券商': 15.8,
            '保险': 9.2,
            '房地产': 8.7,
            '汽车': 18.5,
            '医药': 35.2,
            '电子': 42.1,
            '能源': 12.3,
            '消费': 28.6,
            '科技': 45.3,
            '制造': 22.1
        }
    
    def _get_industry_pb(self):
        """
        获取行业市净率
        返回行业市净率字典
        """
        # 模拟获取行业市净率
        return {
            '银行': 0.8,
            '券商': 1.5,
            '保险': 1.2,
            '房地产': 1.3,
            '汽车': 2.1,
            '医药': 4.5,
            '电子': 3.8,
            '能源': 1.5,
            '消费': 4.2,
            '科技': 5.1,
            '制造': 2.5
        }
    
    def _get_industry_revenue_growth(self):
        """
        获取行业营收增长率
        返回行业营收增长率字典
        """
        # 模拟获取行业营收增长率
        return {
            '银行': 8.5,
            '券商': 15.2,
            '保险': 10.5,
            '房地产': 5.8,
            '汽车': 12.3,
            '医药': 18.5,
            '电子': 22.1,
            '能源': 7.5,
            '消费': 15.8,
            '科技': 25.3,
            '制造': 11.2
        }
    
    def _get_industry_profit_growth(self):
        """
        获取行业净利润增长率
        返回行业净利润增长率字典
        """
        # 模拟获取行业净利润增长率
        return {
            '银行': 7.2,
            '券商': 18.5,
            '保险': 9.8,
            '房地产': 4.5,
            '汽车': 10.2,
            '医药': 20.5,
            '电子': 25.3,
            '能源': 6.8,
            '消费': 16.2,
            '科技': 28.5,
            '制造': 9.8
        }
    
    def _get_industry_fund_flow(self):
        """
        获取行业资金流向
        返回行业资金流向字典（正值表示流入，负值表示流出）
        """
        # 模拟获取行业资金流向
        return {
            '银行': 15.2,
            '券商': 25.8,
            '保险': 8.5,
            '房地产': -5.2,
            '汽车': 12.5,
            '医药': 28.5,
            '电子': 35.2,
            '能源': -8.5,
            '消费': 18.5,
            '科技': 42.3,
            '制造': 5.8
        }
    
    def _get_stock_info(self, stock_code):
        """
        获取股票基本信息
        参数:
            stock_code: 股票代码
        返回股票基本信息字典
        """
        # 模拟获取股票基本信息
        # 实际应用中，可以从股票行情API获取
        return {
            'name': f'股票{stock_code[-4:]}',
            'industry': random.choice(['银行', '券商', '保险', '房地产', '汽车', '医药', '电子', '能源', '消费', '科技', '制造']),
            'market_cap': round(random.uniform(50, 5000), 2)
        }
    
    def _get_financial_indicators(self, stock_code):
        """
        获取股票财务指标
        参数:
            stock_code: 股票代码
        返回股票财务指标字典
        """
        # 模拟获取股票财务指标
        return {
            'debt_ratio': round(random.uniform(0.3, 0.7), 2),
            'current_ratio': round(random.uniform(1.0, 3.0), 2),
            'quick_ratio': round(random.uniform(0.8, 2.5), 2)
        }
    
    def _get_valuation_indicators(self, stock_code):
        """
        获取股票估值指标
        参数:
            stock_code: 股票代码
        返回股票估值指标字典
        """
        # 模拟获取股票估值指标
        return {
            'pe': round(random.uniform(10, 50), 2),
            'pb': round(random.uniform(1, 5), 2),
            'ps': round(random.uniform(2, 10), 2),
            'dividend_yield': round(random.uniform(0, 3), 2)
        }
    
    def _get_growth_indicators(self, stock_code):
        """
        获取股票成长指标
        参数:
            stock_code: 股票代码
        返回股票成长指标字典
        """
        # 模拟获取股票成长指标
        return {
            'revenue_growth': round(random.uniform(5, 30), 2),
            'profit_growth': round(random.uniform(0, 35), 2)
        }
    
    def _get_quality_indicators(self, stock_code):
        """
        获取股票质量指标
        参数:
            stock_code: 股票代码
        返回股票质量指标字典
        """
        # 模拟获取股票质量指标
        return {
            'roe': round(random.uniform(5, 25), 2),
            'gross_margin': round(random.uniform(20, 60), 2),
            'net_margin': round(random.uniform(5, 30), 2)
        }

if __name__ == "__main__":
    # 测试数据源模块
    data_sources = EnhancedDataSources()
    
    # 获取宏观经济数据
    macro_data = data_sources.get_macro_economic_data()
    print("\n宏观经济数据:")
    print(macro_data)
    
    # 获取行业基本面数据
    industry_data = data_sources.get_industry_fundamentals()
    print("\n行业基本面数据:")
    print(industry_data.head())
    
    # 获取股票基本面数据
    stock_codes = ['sh.600000', 'sh.600036', 'sz.000001']
    stock_data = data_sources.get_stock_fundamentals(stock_codes)
    print("\n股票基本面数据:")
    print(stock_data)
