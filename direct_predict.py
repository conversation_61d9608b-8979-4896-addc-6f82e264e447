"""
直接预测股票走势

使用训练好的模型直接预测股票走势，不依赖于预测日期的数据
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler

# 添加当前目录到系统路径，以便导入trading_strategies包
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from trading_strategies.model_trainer import load_latest_model
    from trading_strategies.strategy_implementations import (
        strategy_1, strategy_A, strategy_B, strategy_C
    )
except ImportError:
    print("无法导入trading_strategies包，请确保该包已正确安装")
    sys.exit(1)

def clear_screen():
    """清除屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """打印界面标题"""
    print("=" * 80)
    print("                        直接预测股票走势")
    print("=" * 80)
    print()

def load_data(file_path='股票明细.xlsx'):
    """加载股票数据"""
    print(f"加载股票数据: {file_path}")
    try:
        stock_data = pd.read_excel(file_path)
        print(f"成功加载股票数据，共 {len(stock_data)} 条记录")
        return stock_data
    except Exception as e:
        print(f"加载股票数据失败: {e}")
        return None

def get_latest_data(stock_data):
    """获取最新日期的数据"""
    # 确保日期格式正确
    if isinstance(stock_data['日期'].iloc[0], str):
        stock_data['日期'] = pd.to_datetime(stock_data['日期'])
    
    # 获取最新日期
    latest_date = stock_data['日期'].max()
    print(f"数据集中最新的交易日期: {latest_date}")
    
    # 获取最新日期的数据
    latest_data = stock_data[stock_data['日期'] == latest_date]
    print(f"最新日期的数据记录数: {len(latest_data)}")
    
    return latest_data, latest_date

def direct_predict(strategy_name, data_file_path='股票明细.xlsx', model_dir='trained_models'):
    """
    直接预测股票走势
    
    参数:
    strategy_name: 策略名称，可选值为 'strategy_1', 'strategy_A', 'strategy_B', 'strategy_C'
    data_file_path: 股票数据文件路径
    model_dir: 模型保存目录
    
    返回:
    推荐股票DataFrame和风险说明
    """
    # 加载模型
    model, scaler, features = load_latest_model(model_dir)
    if model is None:
        print("无法加载模型，请先训练模型")
        return None, None
    
    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return None, None
    
    # 获取最新数据
    latest_data, latest_date = get_latest_data(stock_data)
    
    # 计算下一个交易日（简单地加一天，实际应考虑周末和节假日）
    next_date = latest_date + timedelta(days=1)
    next_date_str = next_date.strftime('%Y-%m-%d')
    print(f"预测下一个交易日: {next_date_str}")
    
    # 准备预测数据
    try:
        # 检查特征是否存在
        missing_features = [f for f in features if f not in latest_data.columns]
        if missing_features:
            print(f"缺少以下特征: {missing_features}")
            print("尝试计算缺失的特征...")
            
            # 计算连续技术强度天数
            if '连续技术强度天数' in missing_features:
                # 按股票代码分组处理
                for code, group in stock_data.groupby('股票代码'):
                    # 确保数据按日期排序
                    group = group.sort_values('日期')
                    
                    # 计算连续技术强度天数（连续多少天为100）
                    consecutive_days = []
                    current_count = 0
                    
                    for strength in group['技术强度'].values:
                        if strength == 100:
                            current_count += 1
                        else:
                            current_count = 0
                        consecutive_days.append(current_count)
                    
                    # 更新原始数据
                    stock_data.loc[group.index, '连续技术强度天数'] = consecutive_days
                
                # 更新最新数据
                latest_data, latest_date = get_latest_data(stock_data)
            
            # 计算连续技术强度5天数
            if '连续技术强度5天数' in missing_features:
                # 按股票代码分组处理
                for code, group in stock_data.groupby('股票代码'):
                    # 确保数据按日期排序
                    group = group.sort_values('日期')
                    
                    # 计算技术强度累积值（5天）
                    cumulative_strength = group['技术强度'].copy()
                    for i in range(1, 5):
                        cumulative_strength += group['技术强度'].shift(i).fillna(0)
                    
                    # 更新原始数据
                    stock_data.loc[group.index, '连续技术强度5天数'] = cumulative_strength
                
                # 更新最新数据
                latest_data, latest_date = get_latest_data(stock_data)
            
            # 计算趋势特征
            if '技术强度趋势' in missing_features or '价格趋势' in missing_features or '涨跌幅趋势' in missing_features:
                # 按股票代码分组处理
                for code, group in stock_data.groupby('股票代码'):
                    # 确保数据按日期排序
                    group = group.sort_values('日期')
                    
                    if '技术强度趋势' in missing_features:
                        stock_data.loc[group.index, '技术强度趋势'] = (
                            (group['技术强度'] > group['技术强度'].shift(1)) & 
                            (group['技术强度'].shift(1) > group['技术强度'].shift(2))
                        ).astype(int)
                    
                    if '价格趋势' in missing_features:
                        stock_data.loc[group.index, '价格趋势'] = (
                            (group['当前价格'] > group['当前价格'].shift(1)) & 
                            (group['当前价格'].shift(1) > group['当前价格'].shift(2))
                        ).astype(int)
                    
                    if '涨跌幅趋势' in missing_features and '涨跌幅' in group.columns:
                        stock_data.loc[group.index, '涨跌幅趋势'] = (
                            (group['涨跌幅'] > group['涨跌幅'].shift(1)) & 
                            (group['涨跌幅'].shift(1) > group['涨跌幅'].shift(2))
                        ).astype(int)
                
                # 更新最新数据
                latest_data, latest_date = get_latest_data(stock_data)
        
        # 再次检查特征是否存在
        missing_features = [f for f in features if f not in latest_data.columns]
        if missing_features:
            print(f"仍然缺少以下特征: {missing_features}")
            print("为缺失特征填充0值...")
            
            for feature in missing_features:
                latest_data[feature] = 0
        
        # 提取特征
        X_pred = latest_data[features]
        
        # 处理预测数据中的缺失值
        valid_pred_indices = ~X_pred.isnull().any(axis=1)
        X_pred = X_pred[valid_pred_indices]
        latest_data_filtered = latest_data.loc[valid_pred_indices.index[valid_pred_indices]]
        
        if len(X_pred) == 0:
            print(f"预测数据不足，无法进行预测")
            return None, None
        
        # 标准化预测数据
        X_pred_scaled = scaler.transform(X_pred)
        
        # 预测盈利概率
        pred_proba = model.predict_proba(X_pred_scaled)[:, 1]
        
        # 创建预测结果DataFrame
        predictions = pd.DataFrame({
            '股票代码': latest_data_filtered['股票代码'],
            '股票名称': latest_data_filtered['股票名称'],
            '涨跌幅': latest_data_filtered['涨跌幅'] if '涨跌幅' in latest_data_filtered.columns else 0,
            '技术强度': latest_data_filtered['技术强度'],
            '连续技术强度天数': latest_data_filtered['连续技术强度天数'] if '连续技术强度天数' in latest_data_filtered.columns else 0,
            '连续技术强度5天数': latest_data_filtered['连续技术强度5天数'] if '连续技术强度5天数' in latest_data_filtered.columns else 0,
            '看涨技术指标数量': latest_data_filtered['看涨技术指标数量'] if '看涨技术指标数量' in latest_data_filtered.columns else 0,
            '预测盈利概率': pred_proba
        })
        
        # 按预测盈利概率降序排序
        predictions = predictions.sort_values('预测盈利概率', ascending=False)
        
        print(f"预测完成，共 {len(predictions)} 只股票")
        
        # 应用策略
        if strategy_name == 'strategy_1':
            strategy_stocks = strategy_1(predictions)
            strategy_description = "策略1：100%高胜率策略"
            buy_risk = "低风险：该策略在历史测试中表现出100%的胜率，但交易机会较少。"
            sell_risk = "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。"
            expected_win_rate = "100%"
            expected_return = "约2.38%"
        elif strategy_name == 'strategy_A':
            strategy_stocks = strategy_A(predictions)
            strategy_description = "策略A：最高胜率策略"
            buy_risk = "低风险：该策略在历史测试中表现出约86.77%的胜率，交易机会适中。"
            sell_risk = "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。"
            expected_win_rate = "约86.77%"
            expected_return = "约3.42%"
        elif strategy_name == 'strategy_B':
            strategy_stocks = strategy_B(predictions)
            strategy_description = "策略B：最高收益率策略"
            buy_risk = "中低风险：该策略在历史测试中表现出约83.67%的胜率，但交易机会较少。"
            sell_risk = "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。"
            expected_win_rate = "约83.67%"
            expected_return = "约4.83%"
        elif strategy_name == 'strategy_C':
            strategy_stocks = strategy_C(predictions)
            strategy_description = "策略C：平衡策略（胜率和交易机会的平衡）"
            buy_risk = "中低风险：该策略在历史测试中表现出约84.82%的胜率，交易机会较多。"
            sell_risk = "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。"
            expected_win_rate = "约84.82%"
            expected_return = "约2.59%"
        else:
            print(f"未知策略: {strategy_name}")
            return None, None
        
        # 创建结果目录
        if not os.path.exists('strategy_results'):
            os.makedirs('strategy_results')
        
        # 保存推荐股票
        result_file = f'strategy_results/{next_date_str}_{strategy_name}_推荐股票.xlsx'
        strategy_stocks.to_excel(result_file, index=False)
        
        # 生成风险说明
        risk_description = {
            'strategy_name': strategy_name,
            'strategy_description': strategy_description,
            'prediction_date': next_date_str,
            'stock_count': len(strategy_stocks),
            'buy_risk': buy_risk,
            'sell_risk': sell_risk,
            'expected_win_rate': expected_win_rate,
            'expected_return': expected_return,
            'important_note': "以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！",
            'trading_strategy': "买入后在第二个交易日开盘时卖出",
            'result_file': result_file
        }
        
        print(f"\n{strategy_description}")
        print(f"预测日期: {next_date_str}")
        print(f"推荐股票数: {len(strategy_stocks)}")
        print(f"预期胜率: {expected_win_rate}")
        print(f"预期收益率: {expected_return}")
        print(f"买入风险: {buy_risk}")
        print(f"卖出风险: {sell_risk}")
        print(f"结果已保存至: {result_file}")
        
        if len(strategy_stocks) > 0:
            print("\n推荐买入的股票:")
            print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！")
            print("【交易策略】: 买入后在第二个交易日开盘时卖出")
            print("\n股票列表:")
            for i, row in strategy_stocks.iterrows():
                print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
        
        return strategy_stocks, risk_description
    
    except Exception as e:
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def display_recommendations(recommended_stocks, risk_description):
    """显示推荐股票和风险说明"""
    if recommended_stocks is None:
        print("\n生成推荐失败！")
        return
    
    if len(recommended_stocks) == 0:
        print("\n没有找到符合条件的股票")
        return
    
    print("\n风险说明:")
    print(f"策略: {risk_description['strategy_description']}")
    print(f"预期胜率: {risk_description['expected_win_rate']}")
    print(f"预期收益率: {risk_description['expected_return']}")
    print(f"买入风险: {risk_description['buy_risk']}")
    print(f"卖出风险: {risk_description['sell_risk']}")
    print(f"重要提示: {risk_description['important_note']}")
    print(f"交易策略: {risk_description['trading_strategy']}")
    
    print("\n推荐股票:")
    print("-" * 80)
    print(f"{'股票代码':<10} {'股票名称':<15} {'技术强度':<8} {'连续技术强度5天数':<15} {'预测盈利概率':<12}")
    print("-" * 80)
    
    for i, row in recommended_stocks.iterrows():
        print(f"{row['股票代码']:<10} {row['股票名称']:<15} {row['技术强度']:<8} {row['连续技术强度5天数']:<15} {row['预测盈利概率']*100:.2f}%")
    
    print("-" * 80)
    print(f"共 {len(recommended_stocks)} 只推荐股票")
    print(f"结果已保存至: {risk_description['result_file']}")

def main():
    """主函数"""
    clear_screen()
    print_header()
    
    print("请选择要使用的策略:")
    print("1. 策略1：100%高胜率策略")
    print("2. 策略A：最高胜率策略")
    print("3. 策略B：最高收益率策略")
    print("4. 策略C：平衡策略")
    
    choice = input("\n请输入选项 (1-4): ")
    
    strategy_map = {
        '1': 'strategy_1',
        '2': 'strategy_A',
        '3': 'strategy_B',
        '4': 'strategy_C'
    }
    
    if choice in strategy_map:
        strategy_name = strategy_map[choice]
        recommended_stocks, risk_description = direct_predict(strategy_name)
        
        if recommended_stocks is not None and len(recommended_stocks) > 0:
            display_recommendations(recommended_stocks, risk_description)
        else:
            print("\n没有找到符合条件的股票")
    else:
        print("无效的选项")
    
    input("\n按Enter键退出...")

if __name__ == "__main__":
    main()
