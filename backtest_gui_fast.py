"""
策略回测程序 v19-fast 版本 - 针对打包环境优化
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import subprocess
import time
import os
import sys
import pandas as pd

class BacktestGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("策略回测收益程序 v19-fast")
        self.root.geometry("800x600")
        
        # 初始化变量
        self.running = False
        self.start_time = None
        self.completed_strategies = 0
        self.total_strategies = 0
        
        # 获取程序目录
        if getattr(sys, 'frozen', False):
            self.script_dir = os.path.dirname(sys.executable)
            self.working_dir = sys._MEIPASS
        else:
            self.script_dir = os.path.dirname(os.path.abspath(__file__))
            self.working_dir = self.script_dir
        
        self.base_dir = "E:/机器学习/complete_excel_results"
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建GUI组件"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="策略回测收益程序 v19-fast", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 输入框架
        input_frame = ttk.LabelFrame(main_frame, text="回测参数", padding="10")
        input_frame.pack(fill=tk.X, pady=10)
        
        # 策略范围
        ttk.Label(input_frame, text="起始策略ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.start_id = tk.StringVar(value="1")
        ttk.Entry(input_frame, textvariable=self.start_id, width=15).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(input_frame, text="结束策略ID:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.end_id = tk.StringVar(value="10")
        ttk.Entry(input_frame, textvariable=self.end_id, width=15).grid(row=0, column=3, padx=5, pady=5)
        
        # 快速模式选项
        ttk.Label(input_frame, text="执行模式:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.fast_mode = tk.BooleanVar(value=True)
        ttk.Checkbutton(input_frame, text="快速模式（跳过数据预加载，适合测试）", 
                       variable=self.fast_mode).grid(row=1, column=1, columnspan=3, sticky=tk.W, padx=5, pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="开始回测", command=self.start_backtest)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="停止回测", command=self.stop_backtest, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # 进度框架
        progress_frame = ttk.LabelFrame(main_frame, text="执行进度", padding="10")
        progress_frame.pack(fill=tk.X, pady=10)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        self.status_label = ttk.Label(progress_frame, text="就绪")
        self.status_label.pack(anchor=tk.W, pady=5)
        
        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="执行日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.log_text = tk.Text(log_frame, height=15, font=("Courier", 9))
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
    def log(self, message, level="info"):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update()
        
    def start_backtest(self):
        """开始回测"""
        try:
            start_id = int(self.start_id.get())
            end_id = int(self.end_id.get())
            
            if start_id <= 0 or end_id <= 0:
                messagebox.showerror("输入错误", "策略编号必须为正整数")
                return
                
            if start_id > end_id:
                messagebox.showerror("输入错误", "起始策略编号必须小于或等于结束策略编号")
                return
            
            self.total_strategies = end_id - start_id + 1
            self.completed_strategies = 0
            self.progress_var.set(0)
            self.log_text.delete(1.0, tk.END)
            
            self.running = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.start_time = time.time()
            
            # 在新线程中执行
            thread = threading.Thread(target=self.run_backtest, args=(start_id, end_id))
            thread.daemon = True
            thread.start()
            
        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的整数")
    
    def run_backtest(self, start_id, end_id):
        """执行回测"""
        self.log(f"开始回测策略 {start_id} 到 {end_id}")
        
        if self.fast_mode.get():
            self.log("✅ 使用快速模式（跳过数据预加载）")
            self.run_fast_mode(start_id, end_id)
        else:
            self.log("使用标准模式（包含数据预加载）")
            self.run_standard_mode(start_id, end_id)
    
    def run_fast_mode(self, start_id, end_id):
        """快速模式执行"""
        try:
            import backtest_local
            self.log("✅ 成功导入回测模块")
            
            for strategy_id in range(start_id, end_id + 1):
                if not self.running:
                    break
                    
                self.status_label.config(text=f"执行策略 {strategy_id}")
                self.log(f"🎯 执行策略 {strategy_id}（快速模式）")
                
                start_time = time.time()
                
                try:
                    # 直接执行策略，不预加载数据
                    result = backtest_local.process_strategy(strategy_id)
                    
                    elapsed = time.time() - start_time
                    if result:
                        self.log(f"✅ 策略 {strategy_id} 完成，耗时: {elapsed:.1f}秒")
                    else:
                        self.log(f"⚠️  策略 {strategy_id} 无结果，耗时: {elapsed:.1f}秒")
                        
                except Exception as e:
                    elapsed = time.time() - start_time
                    self.log(f"❌ 策略 {strategy_id} 失败: {e}，耗时: {elapsed:.1f}秒")
                
                # 更新进度
                self.completed_strategies += 1
                progress = (self.completed_strategies / self.total_strategies) * 100
                self.progress_var.set(progress)
                
                self.root.update()
                
        except Exception as e:
            self.log(f"❌ 快速模式执行失败: {e}")
        
        self.finish_backtest()
    
    def run_standard_mode(self, start_id, end_id):
        """标准模式执行"""
        try:
            import backtest_local
            self.log("✅ 成功导入回测模块")
            self.log("开始数据预加载...")
            
            # 预加载数据
            backtest_local.preload_data()
            self.log("✅ 数据预加载完成")
            
            for strategy_id in range(start_id, end_id + 1):
                if not self.running:
                    break
                    
                self.status_label.config(text=f"执行策略 {strategy_id}")
                self.log(f"🎯 执行策略 {strategy_id}（标准模式）")
                
                start_time = time.time()
                
                try:
                    result = backtest_local.process_strategy(
                        strategy_id,
                        backtest_local.next_trading_day_map,
                        backtest_local.date_to_data
                    )
                    
                    elapsed = time.time() - start_time
                    if result:
                        self.log(f"✅ 策略 {strategy_id} 完成，耗时: {elapsed:.1f}秒")
                    else:
                        self.log(f"⚠️  策略 {strategy_id} 无结果，耗时: {elapsed:.1f}秒")
                        
                except Exception as e:
                    elapsed = time.time() - start_time
                    self.log(f"❌ 策略 {strategy_id} 失败: {e}，耗时: {elapsed:.1f}秒")
                
                # 更新进度
                self.completed_strategies += 1
                progress = (self.completed_strategies / self.total_strategies) * 100
                self.progress_var.set(progress)
                
                self.root.update()
                
        except Exception as e:
            self.log(f"❌ 标准模式执行失败: {e}")
        
        self.finish_backtest()
    
    def stop_backtest(self):
        """停止回测"""
        self.running = False
        self.log("⏹️ 用户停止回测")
        
    def finish_backtest(self):
        """完成回测"""
        total_time = time.time() - self.start_time if self.start_time else 0
        self.log(f"🏁 回测完成，总耗时: {total_time:.1f}秒")
        
        self.running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="完成")

def main():
    root = tk.Tk()
    app = BacktestGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
