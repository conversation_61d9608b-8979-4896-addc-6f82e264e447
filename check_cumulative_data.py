"""
检查累积涨幅数据
"""
import pandas as pd
import os

def check_cumulative_data():
    # 检查累积涨幅的实际数值
    tech_dir = r'E:\机器学习\complete_excel_results\tech_strength\daily'
    file_path = os.path.join(tech_dir, 'tech_strength_strong_2025-03-07_smart.xlsx')

    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return

    print(f"读取文件: {file_path}")
    df = pd.read_excel(file_path, engine='openpyxl')
    print('📊 累积涨幅数据样本:')
    print('=' * 50)

    # 显示前5行的累积涨幅数据
    cumulative_cols = [
        '股票代码', '股票名称',
        '买入后连续2个交易日累计涨幅',
        '买入后连续3个交易日累计涨幅', 
        '买入日起2日累计涨幅(含买入日)',
        '买入日起3日累计涨幅(含买入日)',
        '累积涨幅已生成'
    ]

    sample_data = df[cumulative_cols].head()
    for idx, row in sample_data.iterrows():
        print(f'股票: {row["股票代码"]} {row["股票名称"]}')
        print(f'  连续2天: {row["买入后连续2个交易日累计涨幅"]}%')
        print(f'  连续3天: {row["买入后连续3个交易日累计涨幅"]}%')
        print(f'  含买入2天: {row["买入日起2日累计涨幅(含买入日)"]}%')
        print(f'  含买入3天: {row["买入日起3日累计涨幅(含买入日)"]}%')
        print(f'  标记: {row["累积涨幅已生成"]}')
        print('-' * 30)
        
    # 统计非零数据
    non_zero_2days = (df['买入后连续2个交易日累计涨幅'] != 0).sum()
    non_zero_3days = (df['买入后连续3个交易日累计涨幅'] != 0).sum()
    total_rows = len(df)
    
    print(f'\n📈 数据统计:')
    print(f'总行数: {total_rows}')
    print(f'连续2天非零数据: {non_zero_2days} ({non_zero_2days/total_rows*100:.1f}%)')
    print(f'连续3天非零数据: {non_zero_3days} ({non_zero_3days/total_rows*100:.1f}%)')

if __name__ == "__main__":
    check_cumulative_data()
    input("\n按回车键退出...")
