"""
股票数据管理模块
用于管理按日期存储的股票数据
"""

import os
import pandas as pd
import datetime
import glob
from tqdm import tqdm

# 默认文件路径
_base_dir = r'E:\机器学习\complete_excel_results'
_stock_data_dir = os.path.join(_base_dir, 'stock_data')
_daily_data_dir = os.path.join(_stock_data_dir, 'daily')
_stock_details_file = os.path.join(_base_dir, '股票明细_完整.xlsx')
_history_data_file = os.path.join(_stock_data_dir, 'stock_history_data.xlsx')

# 当前使用的文件路径
base_dir = _base_dir
stock_data_dir = _stock_data_dir
daily_data_dir = _daily_data_dir
stock_details_file = _stock_details_file
history_data_file = _history_data_file

def set_base_dir(new_base_dir):
    """
    设置基础目录，并更新所有相关路径

    参数:
        new_base_dir: 新的基础目录路径

    返回:
        dict: 包含所有更新后路径的字典
    """
    global base_dir, stock_data_dir, daily_data_dir, stock_details_file, history_data_file

    # 更新基础目录
    base_dir = new_base_dir

    # 更新其他路径
    stock_data_dir = os.path.join(base_dir, 'stock_data')
    daily_data_dir = os.path.join(stock_data_dir, 'daily')
    stock_details_file = os.path.join(base_dir, '股票明细_完整.xlsx')
    history_data_file = os.path.join(stock_data_dir, 'stock_history_data.xlsx')

    # 确保目录存在
    if not os.path.exists(stock_data_dir):
        os.makedirs(stock_data_dir)
    if not os.path.exists(daily_data_dir):
        os.makedirs(daily_data_dir)

    # 返回更新后的路径
    return {
        'base_dir': base_dir,
        'stock_data_dir': stock_data_dir,
        'daily_data_dir': daily_data_dir,
        'stock_details_file': stock_details_file,
        'history_data_file': history_data_file
    }

# 确保默认目录存在
if not os.path.exists(stock_data_dir):
    os.makedirs(stock_data_dir)
if not os.path.exists(daily_data_dir):
    os.makedirs(daily_data_dir)

def get_daily_data_path(date):
    """
    获取指定日期的股票数据文件路径

    参数:
        date: 日期，可以是字符串(YYYY-MM-DD)、datetime对象或pandas Timestamp

    返回:
        str: 文件路径
    """
    if isinstance(date, str):
        date_str = date.replace('-', '')[:8]  # 提取YYYYMMDD格式
    elif isinstance(date, (datetime.datetime, pd.Timestamp)):
        date_str = date.strftime('%Y%m%d')
    else:
        raise ValueError(f"不支持的日期格式: {type(date)}")

    return os.path.join(daily_data_dir, f"stock_data_{date_str}.xlsx")

def get_daily_parquet_path(date):
    """
    获取指定日期的股票数据Parquet文件路径

    参数:
        date: 日期，可以是字符串(YYYY-MM-DD)、datetime对象或pandas Timestamp

    返回:
        str: 文件路径
    """
    if isinstance(date, str):
        date_str = date.replace('-', '')[:8]  # 提取YYYYMMDD格式
    elif isinstance(date, (datetime.datetime, pd.Timestamp)):
        date_str = date.strftime('%Y%m%d')
    else:
        raise ValueError(f"不支持的日期格式: {type(date)}")

    return os.path.join(daily_data_dir, f"stock_data_{date_str}.parquet")

def save_daily_data(df, date):
    """
    保存指定日期的股票数据

    参数:
        df: 包含股票数据的DataFrame
        date: 日期，可以是字符串(YYYY-MM-DD)、datetime对象或pandas Timestamp

    返回:
        bool: 是否成功保存
    """
    try:
        # 获取文件路径
        excel_path = get_daily_data_path(date)

        # 保存Excel格式
        df.to_excel(excel_path, index=False)

        # 不保存Parquet格式
        # parquet_path = get_daily_parquet_path(date)
        # try:
        #     df.to_parquet(parquet_path, index=False)
        # except Exception as e:
        #     print(f"保存Parquet格式失败: {e}")

        return True
    except Exception as e:
        print(f"保存日期 {date} 的数据失败: {e}")
        return False

def load_daily_data(date):
    """
    加载指定日期的股票数据

    参数:
        date: 日期，可以是字符串(YYYY-MM-DD)、datetime对象或pandas Timestamp

    返回:
        DataFrame: 股票数据，如果文件不存在则返回空DataFrame
    """
    try:
        # 获取文件路径
        excel_path = get_daily_data_path(date)

        # 只使用Excel格式
        if os.path.exists(excel_path):
            df = pd.read_excel(excel_path)
            return df
        else:
            return pd.DataFrame()
    except Exception as e:
        print(f"加载日期 {date} 的数据失败: {e}")
        return pd.DataFrame()

def load_date_range_data(start_date, end_date):
    """
    加载指定日期范围内的所有股票数据

    参数:
        start_date: 开始日期，可以是字符串(YYYY-MM-DD)、datetime对象或pandas Timestamp
        end_date: 结束日期，可以是字符串(YYYY-MM-DD)、datetime对象或pandas Timestamp

    返回:
        DataFrame: 合并后的股票数据
    """
    # 转换日期格式
    if isinstance(start_date, str):
        start_date = pd.to_datetime(start_date)
    if isinstance(end_date, str):
        end_date = pd.to_datetime(end_date)

    # 生成日期范围
    date_range = pd.date_range(start=start_date, end=end_date)

    # 加载每个日期的数据
    all_data = []
    for date in tqdm(date_range, desc="加载日期范围数据"):
        df = load_daily_data(date)
        if not df.empty:
            all_data.append(df)

    # 合并所有数据
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f"成功加载日期范围 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的数据，共 {len(combined_df)} 条记录")
        return combined_df
    else:
        print(f"日期范围 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 内没有可用数据")
        return pd.DataFrame()

def get_available_dates():
    """
    获取所有可用的日期

    返回:
        list: 可用日期列表，按时间排序
    """
    # 查找所有日期文件
    file_pattern = os.path.join(daily_data_dir, "stock_data_*.xlsx")
    files = glob.glob(file_pattern)

    # 提取日期
    dates = []
    for file in files:
        filename = os.path.basename(file)
        date_str = filename.replace("stock_data_", "").replace(".xlsx", "")
        try:
            date = pd.to_datetime(date_str, format="%Y%m%d")
            dates.append(date)
        except:
            pass

    # 排序并返回
    return sorted(dates)

def split_combined_data_by_date(combined_file=history_data_file):
    """
    将合并的数据文件按日期拆分为单独的文件

    参数:
        combined_file: 合并数据文件的路径

    返回:
        bool: 是否成功拆分
    """
    try:
        # 读取合并数据
        print(f"读取合并数据文件: {combined_file}")
        if combined_file.endswith('.parquet'):
            df = pd.read_parquet(combined_file)
        else:
            df = pd.read_excel(combined_file)

        print(f"成功读取合并数据，共 {len(df)} 条记录")

        # 确保日期列是日期类型
        date_column = 'date' if 'date' in df.columns else '日期'
        if date_column in df.columns:
            df[date_column] = pd.to_datetime(df[date_column])
        else:
            print(f"错误: 数据中没有日期列")
            return False

        # 按日期分组并保存
        for date, group in tqdm(df.groupby(date_column), desc="按日期拆分数据"):
            save_daily_data(group, date)

        print(f"成功将合并数据拆分为 {df[date_column].nunique()} 个日期文件")
        return True
    except Exception as e:
        print(f"拆分合并数据失败: {e}")
        return False

def merge_daily_data_to_combined(output_file=history_data_file):
    """
    将所有日期文件合并为一个文件

    参数:
        output_file: 输出文件路径

    返回:
        bool: 是否成功合并
    """
    try:
        # 获取所有可用日期
        dates = get_available_dates()

        if not dates:
            print("没有找到可用的日期文件")
            return False

        # 加载所有日期的数据
        all_data = []
        for date in tqdm(dates, desc="合并日期数据"):
            df = load_daily_data(date)
            if not df.empty:
                all_data.append(df)

        # 合并所有数据
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)

            # 保存合并数据 - 只使用Excel格式
            combined_df.to_excel(output_file, index=False)

            print(f"成功合并 {len(dates)} 个日期的数据，共 {len(combined_df)} 条记录，已保存到 {output_file}")
            return True
        else:
            print("没有可用数据可合并")
            return False
    except Exception as e:
        print(f"合并日期数据失败: {e}")
        return False

if __name__ == "__main__":
    # 测试代码
    print("股票数据管理模块")

    # 检查是否有合并数据文件，如果有则拆分
    if os.path.exists(history_data_file):
        print("发现合并数据文件，开始拆分...")
        split_combined_data_by_date(history_data_file)

    # 获取可用日期
    dates = get_available_dates()
    print(f"可用日期: {len(dates)} 个")
    if dates:
        print(f"日期范围: {dates[0].strftime('%Y-%m-%d')} 到 {dates[-1].strftime('%Y-%m-%d')}")
