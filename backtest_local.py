import pandas as pd
import os
import datetime
import warnings
import argparse
import time
import glob
import sys
warnings.filterwarnings('ignore')

# 添加当前目录到Python路径，确保能够导入stock_data_manager模块和config模块
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)
try:
    import stock_data_manager as sdm
except ImportError:
    print("警告: 无法导入stock_data_manager模块，将使用传统方式加载数据")
    sdm = None

# 导入配置模块
try:
    import config
    print("成功导入配置模块")
except ImportError:
    print("警告: 无法导入config模块，将使用默认配置")
    config = None

# 设置文件路径
# 获取当前脚本的目录
script_dir = os.path.dirname(os.path.abspath(__file__))

# 解析命令行参数，支持指定数据目录
parser = argparse.ArgumentParser(description='股票回测程序')
parser.add_argument('--data_dir', type=str, help='数据目录路径，默认为自动检测')
parser.add_argument('--id', type=int, help='要回测的策略ID')
parser.add_argument('--start', type=int, help='起始策略ID')
parser.add_argument('--end', type=int, help='结束策略ID')
parser.add_argument('--batch', type=int, default=1, help='批处理大小')
parser.add_argument('--preload', action='store_true', help='是否预加载数据')

# 解析命令行参数
args, unknown = parser.parse_known_args()

# 设置数据目录
if args.data_dir and os.path.exists(args.data_dir):
    # 使用命令行参数指定的数据目录
    base_dir = args.data_dir
    print(f"使用命令行参数指定的数据目录: {base_dir}")
    # 如果存在配置模块，更新配置
    if config:
        config.set_data_dir(base_dir)
        print(f"已更新配置模块中的数据目录: {config.DATA_DIR}")
elif 'STOCK_DATA_BASE_DIR' in os.environ:
    # 使用环境变量指定的数据目录
    base_dir = os.environ['STOCK_DATA_BASE_DIR']
    print(f"使用环境变量指定的数据目录: {base_dir}")
    # 如果存在配置模块，更新配置
    if config:
        config.set_data_dir(base_dir)
        print(f"已更新配置模块中的数据目录: {config.DATA_DIR}")
elif config:
    # 使用配置模块中的数据目录
    base_dir = config.DATA_DIR
    print(f"使用配置模块中的数据目录: {base_dir}")
else:
    # 自动检测数据目录
    # 首先尝试当前脚本目录下的complete_excel_results文件夹
    base_dir = os.path.join(script_dir, 'complete_excel_results')

    if not os.path.exists(base_dir):
        # 尝试在上一级目录查找
        base_dir = os.path.join(os.path.dirname(script_dir), 'complete_excel_results')

        if not os.path.exists(base_dir):
            # 尝试在常见位置查找
            common_locations = [
                r'E:\机器学习\complete_excel_results',
                r'D:\机器学习\complete_excel_results',
                r'C:\机器学习\complete_excel_results'
            ]
            for location in common_locations:
                if os.path.exists(location):
                    base_dir = location
                    break

    print(f"自动检测到数据目录: {base_dir}")
    # 如果存在配置模块，更新配置
    if config:
        config.set_data_dir(base_dir)
        print(f"已更新配置模块中的数据目录: {config.DATA_DIR}")

print(f"使用数据目录: {base_dir}")

# 设置文件路径
if config:
    # 使用配置模块中的路径
    paths = config.get_data_paths()
    # 优先使用已回测的汇总表，如果不存在则使用原始汇总表
    summary_file_backtest = os.path.join(paths['data_dir'], '所有策略汇总_已回测.xlsx')
    summary_file_original = os.path.join(paths['data_dir'], '所有策略汇总.xlsx')
    summary_file = summary_file_backtest if os.path.exists(summary_file_backtest) else summary_file_original
    details_file = os.path.join(paths['data_dir'], '股票明细_完整.xlsx')  # 使用更完整的数据集
    output_dir = os.path.join(paths['data_dir'], 'new_strategy_details')
    stock_data_dir = paths['stock_data_dir']
    history_data_file = paths['history_data_file']
    calendar_file = os.path.join(stock_data_dir, 'trading_calendar.xlsx')
else:
    # 使用传统方式设置路径
    # 优先使用已回测的汇总表，如果不存在则使用原始汇总表
    summary_file_backtest = os.path.join(base_dir, '所有策略汇总_已回测.xlsx')
    summary_file_original = os.path.join(base_dir, '所有策略汇总.xlsx')
    summary_file = summary_file_backtest if os.path.exists(summary_file_backtest) else summary_file_original
    details_file = os.path.join(base_dir, '股票明细_完整.xlsx')  # 使用更完整的数据集
    output_dir = os.path.join(base_dir, 'new_strategy_details')
    stock_data_dir = os.path.join(base_dir, 'stock_data')
    history_data_file = os.path.join(stock_data_dir, 'stock_history_data.xlsx')
    calendar_file = os.path.join(stock_data_dir, 'trading_calendar.xlsx')

print(f"使用汇总表文件: {summary_file}")

# 确保输出目录存在
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 读取数据
print("读取策略汇总文件...")
summary_parquet_file = os.path.splitext(summary_file)[0] + '.parquet'
if os.path.exists(summary_parquet_file):
    start_time = time.time()
    summary_df = pd.read_parquet(summary_parquet_file)
    load_time = time.time() - start_time
    print(f"成功读取策略汇总文件(Parquet格式)，共 {len(summary_df)} 条记录，耗时: {load_time:.2f}秒")
else:
    start_time = time.time()
    summary_df = pd.read_excel(summary_file)
    load_time = time.time() - start_time
    print(f"成功读取策略汇总文件(Excel格式)，共 {len(summary_df)} 条记录，耗时: {load_time:.2f}秒")

    # 自动转换为Parquet格式以加速后续加载
    try:
        summary_df.to_parquet(summary_parquet_file, index=False)
        print(f"已将策略汇总文件转换为Parquet格式并保存到 {summary_parquet_file}")
    except Exception as e:
        print(f"转换策略汇总文件为Parquet格式时出错: {e}")

print("读取股票明细文件...")

# 新的数据加载逻辑：创建按日期读取数据的索引，不合并所有数据
def check_and_generate_cumulative_columns(df, date_obj):
    """检查并生成累积涨幅列"""
    required_columns = [
        '买入后连续2个交易日累计涨幅',
        '买入后连续3个交易日累计涨幅',
        '买入日起2日累计涨幅(含买入日)',
        '买入日起3日累计涨幅(含买入日)'
    ]

    # 检查是否需要生成累积涨幅列
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        print(f"  检测到缺失累积涨幅列，开始生成: {date_obj.strftime('%Y-%m-%d')}")

        # 为缺失的列添加默认值
        for col in missing_columns:
            df[col] = 0.0

        # 如果有历史数据，尝试计算真实的累积涨幅
        if not history_df.empty:
            try:
                # 获取全局交易日历
                global_trading_days = sorted(pd.to_datetime(history_df['date']).unique())

                # 找到当前日期在交易日历中的位置
                current_date_index = None
                for i, trading_day in enumerate(global_trading_days):
                    if trading_day.date() == date_obj.date():
                        current_date_index = i
                        break

                if current_date_index is not None and current_date_index < len(global_trading_days) - 3:
                    # 获取后续交易日
                    next_1_date = global_trading_days[current_date_index + 1]
                    next_2_date = global_trading_days[current_date_index + 2] if current_date_index + 2 < len(global_trading_days) else None
                    next_3_date = global_trading_days[current_date_index + 3] if current_date_index + 3 < len(global_trading_days) else None

                    # 创建历史数据映射
                    history_key_to_pctChg = {}
                    for _, row in history_df.iterrows():
                        key = f"{row['code']}_{row['date'].strftime('%Y-%m-%d')}"
                        history_key_to_pctChg[key] = row.get('pctChg', 0)

                    # 为每只股票计算累积涨幅
                    for idx, stock_row in df.iterrows():
                        stock_code = stock_row['股票代码']

                        # 获取买入日涨跌幅（当前日期）
                        buy_key = f"{stock_code}_{date_obj.strftime('%Y-%m-%d')}"
                        buy_pct = history_key_to_pctChg.get(buy_key, 0)

                        # 获取后续交易日涨跌幅
                        next_1_pct = 0
                        next_2_pct = 0
                        next_3_pct = 0

                        if next_1_date:
                            next_1_key = f"{stock_code}_{next_1_date.strftime('%Y-%m-%d')}"
                            next_1_pct = history_key_to_pctChg.get(next_1_key, 0)

                        if next_2_date:
                            next_2_key = f"{stock_code}_{next_2_date.strftime('%Y-%m-%d')}"
                            next_2_pct = history_key_to_pctChg.get(next_2_key, 0)

                        if next_3_date:
                            next_3_key = f"{stock_code}_{next_3_date.strftime('%Y-%m-%d')}"
                            next_3_pct = history_key_to_pctChg.get(next_3_key, 0)

                        # 计算累积涨幅
                        df.at[idx, '买入后连续2个交易日累计涨幅'] = round(next_1_pct + next_2_pct, 2)
                        df.at[idx, '买入后连续3个交易日累计涨幅'] = round(next_1_pct + next_2_pct + next_3_pct, 2)

                        if buy_pct != 0:  # 避免除零错误
                            cumulative_2days_with = (1 + buy_pct/100) * (1 + next_1_pct/100) - 1
                            cumulative_3days_with = (1 + buy_pct/100) * (1 + next_1_pct/100) * (1 + next_2_pct/100) - 1
                            df.at[idx, '买入日起2日累计涨幅(含买入日)'] = round(cumulative_2days_with * 100, 2)
                            df.at[idx, '买入日起3日累计涨幅(含买入日)'] = round(cumulative_3days_with * 100, 2)

                    print(f"    ✅ 已生成 {date_obj.strftime('%Y-%m-%d')} 的累积涨幅数据")
                else:
                    print(f"    ⚠️ 无法找到 {date_obj.strftime('%Y-%m-%d')} 的后续交易日，使用默认值0")
            except Exception as e:
                print(f"    ❌ 生成累积涨幅时出错: {e}")

    return df

def load_all_daily_data():
    """一次性加载所有按日期存储的技术强度数据到内存中，并检查/生成累积涨幅列"""
    tech_strength_daily_dir = os.path.join(base_dir, 'tech_strength', 'daily')

    if not os.path.exists(tech_strength_daily_dir):
        print(f"警告: 技术强度日期文件夹不存在: {tech_strength_daily_dir}")
        return None, None, None

    print(f"开始加载技术强度日期文件夹中的所有数据: {tech_strength_daily_dir}")

    # 查找所有技术强度文件
    pattern = os.path.join(tech_strength_daily_dir, "tech_strength_strong_*_smart.xlsx")
    tech_files = glob.glob(pattern)

    if not tech_files:
        print(f"警告: 在 {tech_strength_daily_dir} 中没有找到技术强度文件")
        return None, None, None

    print(f"找到 {len(tech_files)} 个技术强度文件，开始逐个加载...")

    # 存储所有数据
    all_daily_data = {}  # 日期 -> DataFrame 的映射
    available_dates = []
    total_records = 0
    files_updated = 0

    for i, file_path in enumerate(tech_files, 1):
        try:
            # 从文件名中提取日期
            filename = os.path.basename(file_path)
            # 文件名格式: tech_strength_strong_2025-05-15_smart.xlsx
            date_part = filename.replace('tech_strength_strong_', '').replace('_smart.xlsx', '')
            date_obj = pd.to_datetime(date_part)

            # 只在开始、中间和结束时打印进度
            if i == 1 or i == len(tech_files) // 2 or i == len(tech_files):
                print(f"  [{i}/{len(tech_files)}] 加载技术强度数据...")

            # 读取文件
            df = pd.read_excel(file_path)
            df['日期'] = date_obj

            # 检查并生成累积涨幅列
            original_columns = set(df.columns)
            df = check_and_generate_cumulative_columns(df, date_obj)
            new_columns = set(df.columns)

            # 如果添加了新列，保存回文件
            if new_columns != original_columns:
                try:
                    df.to_excel(file_path, index=False)
                    files_updated += 1
                    print(f"    💾 已更新文件: {filename}")
                except Exception as e:
                    print(f"    ❌ 保存文件失败: {e}")

            # 存储到字典中
            all_daily_data[date_obj] = df
            available_dates.append(date_obj)
            total_records += len(df)

        except Exception as e:
            print(f"    读取文件 {file_path} 时出错: {e}")
            continue

    if not all_daily_data:
        print("没有成功加载任何技术强度文件")
        return None, None, None

    available_dates.sort()
    print(f"数据加载完成！")
    print(f"  总文件数: {len(all_daily_data)}")
    print(f"  总记录数: {total_records:,}")
    print(f"  更新文件数: {files_updated}")
    print(f"  日期范围: {available_dates[0].strftime('%Y-%m-%d')} 到 {available_dates[-1].strftime('%Y-%m-%d')}")

    return all_daily_data, available_dates, total_records

def get_daily_stock_data(date, all_daily_data):
    """从预加载的数据中获取指定日期的股票数据"""
    if isinstance(date, str):
        date = pd.to_datetime(date)

    if date not in all_daily_data:
        print(f"警告: 没有找到日期 {date.strftime('%Y-%m-%d')} 的数据")
        return pd.DataFrame()

    return all_daily_data[date].copy()  # 返回副本，避免修改原始数据

def load_stock_details_from_single_file():
    """从单个股票明细_完整.xlsx文件加载数据（备用方案）"""
    details_parquet_file = os.path.splitext(details_file)[0] + '.parquet'
    if os.path.exists(details_parquet_file):
        start_time = time.time()
        stock_df = pd.read_parquet(details_parquet_file)
        load_time = time.time() - start_time
        print(f"成功读取股票明细文件(Parquet格式)，共 {len(stock_df)} 条记录，耗时: {load_time:.2f}秒")
        return stock_df
    elif os.path.exists(details_file):
        start_time = time.time()
        stock_df = pd.read_excel(details_file)
        load_time = time.time() - start_time
        print(f"成功读取股票明细文件(Excel格式)，共 {len(stock_df)} 条记录，耗时: {load_time:.2f}秒")

        # 自动转换为Parquet格式以加速后续加载
        try:
            stock_df.to_parquet(details_parquet_file, index=False)
            print(f"已将股票明细文件转换为Parquet格式并保存到 {details_parquet_file}")
        except Exception as e:
            print(f"转换股票明细文件为Parquet格式时出错: {e}")

        return stock_df
    else:
        print(f"错误: 找不到股票明细文件: {details_file}")
        return pd.DataFrame()

# 全局变量，用于存储预加载的数据
all_daily_data = None
available_dates = None
total_records = 0
stock_df = pd.DataFrame()
use_daily_files = False

# 全局变量，用于存储历史数据和交易日历
history_df = pd.DataFrame()
calendar_df = pd.DataFrame()
next_trading_day_map = {}
date_to_data = {}
all_trading_dates = []

# 读取历史数据和交易日历（只在程序启动时读取一次）
def load_historical_data():
    """加载历史数据和交易日历，只使用按日期存储的数据"""
    global history_df, calendar_df, all_trading_dates

    print("读取历史数据和交易日历...")
    try:
        # 定义文件路径
        calendar_excel_file = calendar_file
        calendar_parquet_file = os.path.splitext(calendar_file)[0] + '.parquet'
        daily_data_dir = os.path.join(stock_data_dir, 'daily')

        # 检查是否存在按日期存储的数据
        has_daily_data = False
        if sdm is not None and os.path.exists(daily_data_dir):
            daily_files = glob.glob(os.path.join(daily_data_dir, "stock_data_*.xlsx")) + glob.glob(os.path.join(daily_data_dir, "stock_data_*.parquet"))
            has_daily_data = len(daily_files) > 0

        if has_daily_data:
            print(f"发现按日期存储的数据文件，共 {len(daily_files)} 个文件")

            # 使用stock_data_manager加载按日期存储的数据
            try:
                # 获取所有可用日期
                available_dates = sdm.get_available_dates()
                if available_dates:
                    print(f"可用日期范围: {available_dates[0].strftime('%Y-%m-%d')} 到 {available_dates[-1].strftime('%Y-%m-%d')}")

                    # 加载所有日期的数据
                    start_time = time.time()
                    all_data = []
                    for i, date in enumerate(available_dates, 1):
                        # 只在开始、中间和结束时打印进度
                        if i == 1 or i == len(available_dates) // 2 or i == len(available_dates):
                            print(f"  [{i}/{len(available_dates)}] 加载历史数据...")
                        df = sdm.load_daily_data(date)
                        if not df.empty:
                            all_data.append(df)

                    if all_data:
                        history_df = pd.concat(all_data, ignore_index=True)
                        load_time = time.time() - start_time
                        print(f"成功加载按日期存储的数据，共 {len(history_df)} 条记录，耗时: {load_time:.2f}秒")
                    else:
                        print("没有找到可用的按日期存储的数据")
                        history_df = pd.DataFrame()
                else:
                    print("没有找到可用的日期")
                    history_df = pd.DataFrame()
            except Exception as e:
                print(f"加载按日期存储的数据时出错: {e}")
                import traceback
                traceback.print_exc()
                history_df = pd.DataFrame()
        else:
            print("没有找到按日期存储的数据，无法进行回测")
            history_df = pd.DataFrame()

        # 加载交易日历，优先使用Parquet格式
        if os.path.exists(calendar_parquet_file):
            start_time = time.time()
            calendar_df = pd.read_parquet(calendar_parquet_file)
            load_time = time.time() - start_time
            print(f"成功读取交易日历(Parquet格式)，共 {len(calendar_df)} 天，耗时: {load_time:.2f}秒")
        elif os.path.exists(calendar_excel_file):
            start_time = time.time()
            calendar_df = pd.read_excel(calendar_excel_file)
            load_time = time.time() - start_time
            print(f"成功读取交易日历(Excel格式)，共 {len(calendar_df)} 天，耗时: {load_time:.2f}秒")

            # 自动转换为Parquet格式以加速后续加载
            try:
                if not os.path.exists(os.path.dirname(calendar_parquet_file)):
                    os.makedirs(os.path.dirname(calendar_parquet_file))
                calendar_df.to_parquet(calendar_parquet_file, index=False)
                print(f"已将交易日历转换为Parquet格式并保存到 {calendar_parquet_file}")
            except Exception as e:
                print(f"转换交易日历为Parquet格式时出错: {e}")
        else:
            print(f"警告: 交易日历文件 {calendar_excel_file} 不存在")
            calendar_df = pd.DataFrame()

        # 将日期列转换为日期类型
        if not history_df.empty:
            # 检查历史数据结构（简化日志）
            print(f"历史数据列名: {history_df.columns.tolist()}")

            # 查找日期列（可能的列名：date, 日期, Date等）
            date_column = None
            possible_date_columns = ['date', '日期', 'Date', 'DATE', 'trading_date', '交易日期']

            for col in possible_date_columns:
                if col in history_df.columns:
                    date_column = col
                    print(f"找到日期列: {date_column}")
                    break

            if date_column:
                if not pd.api.types.is_datetime64_any_dtype(history_df[date_column]):
                    history_df[date_column] = pd.to_datetime(history_df[date_column])
                # 提取所有交易日期并排序
                all_trading_dates = sorted(history_df[date_column].unique())
                print(f"历史数据中包含 {len(all_trading_dates)} 个交易日")

                # 如果日期列不是'date'，重命名为'date'以保持兼容性
                if date_column != 'date':
                    history_df['date'] = history_df[date_column]
                    print(f"已将日期列 '{date_column}' 重命名为 'date'")
            else:
                print("警告: 在历史数据中没有找到日期列")
                all_trading_dates = []
        if not calendar_df.empty:
            if not pd.api.types.is_datetime64_any_dtype(calendar_df['calendar_date']):
                calendar_df['calendar_date'] = pd.to_datetime(calendar_df['calendar_date'])
    except Exception as e:
        print(f"读取历史数据时出错: {e}")
        import traceback
        traceback.print_exc()
        history_df = pd.DataFrame()
        calendar_df = pd.DataFrame()
        all_trading_dates = []

# 历史数据将在preload_data()函数中加载

def get_next_trading_day(date_str):
    """获取下一个交易日"""
    # 将日期转换为日期类型
    if isinstance(date_str, str):
        try:
            date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            print(f"警告: 日期格式错误 '{date_str}'，使用当前日期")
            date_obj = datetime.datetime.now()
    else:
        date_obj = date_str

    # 如果有历史数据，优先使用历史数据中的交易日
    if not history_df.empty:
        # 获取历史数据中的所有交易日
        all_trading_days = history_df['date'].drop_duplicates().sort_values().tolist()

        # 查找下一个交易日
        for day in all_trading_days:
            if day > date_obj:
                return day.strftime('%Y-%m-%d')

        # 如果找不到下一个交易日（可能是最后一个交易日），使用最后一个交易日+1天
        if all_trading_days:
            print(f"警告: 日期 {date_str} 可能是历史数据中的最后一个日期，使用最后一个交易日+1天")
            last_day = all_trading_days[-1]
            next_day = last_day + datetime.timedelta(days=1)

            # 跳过周末
            while next_day.weekday() >= 5:
                next_day += datetime.timedelta(days=1)

            return next_day.strftime('%Y-%m-%d')

    # 如果没有历史数据或找不到下一个交易日，尝试使用交易日历
    if not calendar_df.empty:
        # 查找下一个交易日
        next_trading_days = calendar_df[
            (calendar_df['calendar_date'] > date_obj) &
            (calendar_df['is_trading_day'] == 1)
        ].sort_values('calendar_date')

        if not next_trading_days.empty:
            return next_trading_days.iloc[0]['calendar_date'].strftime('%Y-%m-%d')

    # 如果没有本地数据或找不到下一个交易日，使用启发式方法
    # 从原日期开始，逐天检查是否是交易日（排除周末）
    next_day = date_obj + datetime.timedelta(days=1)

    # 检查是否是周末（5=周六，6=周日）
    while next_day.weekday() >= 5:
        next_day += datetime.timedelta(days=1)

    # 检查是否是已知的节假日（如果有节假日数据）
    if not calendar_df.empty:
        holiday_dates = calendar_df[calendar_df['is_trading_day'] == 0]['calendar_date'].tolist()
        while next_day in holiday_dates:
            next_day += datetime.timedelta(days=1)
            # 再次检查是否是周末
            while next_day.weekday() >= 5:
                next_day += datetime.timedelta(days=1)

    print(f"使用启发式方法计算日期 {date_str} 的下一个交易日: {next_day.strftime('%Y-%m-%d')}")
    return next_day.strftime('%Y-%m-%d')

# 🚀 预计算缓存系统函数
def get_cumulative_cache_path():
    """获取累积涨幅缓存文件路径"""
    cache_dir = os.path.join(base_dir, 'cache')
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
    return os.path.join(cache_dir, 'cumulative_returns_cache.parquet')

def build_cumulative_cache():
    """构建累积涨幅缓存"""
    print("🔥 开始构建累积涨幅缓存...")
    cache_path = get_cumulative_cache_path()

    if not history_df.empty:
        print("使用历史数据构建缓存...")

        # 获取全局交易日历
        global_trading_days = sorted(pd.to_datetime(history_df['date']).unique())
        print(f"全局交易日历包含 {len(global_trading_days)} 个交易日")

        # 获取所有股票代码
        all_stocks = history_df['code'].unique()
        print(f"需要计算 {len(all_stocks)} 只股票的累积涨幅")

        # 创建关联键到涨跌幅的映射
        history_key_to_pctChg = {}
        for _, row in history_df.iterrows():
            key = f"{row['code']}_{row['date'].strftime('%Y-%m-%d')}"
            history_key_to_pctChg[key] = row.get('pctChg', 0)

        print(f"创建了 {len(history_key_to_pctChg)} 个历史数据映射")

        # 构建缓存数据
        cache_data = []

        for i, buy_date in enumerate(global_trading_days[:-3], 1):  # 排除最后3天，确保有足够的后续交易日
            if i % 10 == 0:
                print(f"处理进度: {i}/{len(global_trading_days)-3}")

            # 找到后续交易日
            future_days = [d for d in global_trading_days if d > buy_date]
            if len(future_days) >= 3:
                next_1_date = future_days[0].strftime('%Y-%m-%d')
                next_2_date = future_days[1].strftime('%Y-%m-%d')
                next_3_date = future_days[2].strftime('%Y-%m-%d')

                # 为每只股票计算累积涨幅
                for stock_code in all_stocks:
                    # 获取买入日涨跌幅
                    buy_key = f"{stock_code}_{buy_date.strftime('%Y-%m-%d')}"
                    buy_pct = history_key_to_pctChg.get(buy_key, 0)

                    # 获取后续交易日涨跌幅
                    next_1_key = f"{stock_code}_{next_1_date}"
                    next_2_key = f"{stock_code}_{next_2_date}"
                    next_3_key = f"{stock_code}_{next_3_date}"

                    next_1_pct = history_key_to_pctChg.get(next_1_key, 0)
                    next_2_pct = history_key_to_pctChg.get(next_2_key, 0)
                    next_3_pct = history_key_to_pctChg.get(next_3_key, 0)

                    # 计算累积涨幅
                    cumulative_2days = next_1_pct + next_2_pct
                    cumulative_3days = next_1_pct + next_2_pct + next_3_pct
                    cumulative_2days_with = (1 + buy_pct/100) * (1 + next_1_pct/100) - 1
                    cumulative_3days_with = (1 + buy_pct/100) * (1 + next_1_pct/100) * (1 + next_2_pct/100) - 1

                    cache_data.append({
                        'stock_code': stock_code,
                        'buy_date': buy_date.strftime('%Y-%m-%d'),
                        'cumulative_2days': round(cumulative_2days, 2),
                        'cumulative_3days': round(cumulative_3days, 2),
                        'cumulative_2days_with': round(cumulative_2days_with * 100, 2),
                        'cumulative_3days_with': round(cumulative_3days_with * 100, 2)
                    })

        # 保存缓存
        if cache_data:
            cache_df = pd.DataFrame(cache_data)
            cache_df.to_parquet(cache_path, index=False)
            print(f"✅ 累积涨幅缓存构建完成，保存到: {cache_path}")
            print(f"缓存包含 {len(cache_df)} 条记录")
            return True
        else:
            print("❌ 没有生成缓存数据")
            return False
    else:
        print("❌ 历史数据为空，无法构建缓存")
        return False

def load_cumulative_cache_for_stocks(selected_stocks):
    """为选中的股票加载累积涨幅缓存"""
    cache_path = get_cumulative_cache_path()

    if not os.path.exists(cache_path):
        print("缓存文件不存在，开始构建缓存...")
        if not build_cumulative_cache():
            return False

    try:
        # 加载缓存
        cache_df = pd.read_parquet(cache_path)
        print(f"加载缓存文件，包含 {len(cache_df)} 条记录")

        # 创建查找键
        selected_stocks['cache_key'] = selected_stocks['历史股票代码'] + '_' + selected_stocks['买入日期对象'].dt.strftime('%Y-%m-%d')
        cache_df['cache_key'] = cache_df['stock_code'] + '_' + cache_df['buy_date']

        # 合并数据
        merged = selected_stocks.merge(cache_df[['cache_key', 'cumulative_2days', 'cumulative_3days', 'cumulative_2days_with', 'cumulative_3days_with']],
                                     on='cache_key', how='left')

        # 更新累积涨幅列
        selected_stocks['买入后连续2个交易日累计涨幅'] = merged['cumulative_2days'].fillna(0)
        selected_stocks['买入后连续3个交易日累计涨幅'] = merged['cumulative_3days'].fillna(0)
        selected_stocks['买入日起2日累计涨幅(含买入日)'] = merged['cumulative_2days_with'].fillna(0)
        selected_stocks['买入日起3日累计涨幅(含买入日)'] = merged['cumulative_3days_with'].fillna(0)

        # 清理临时列
        selected_stocks.drop(['cache_key'], axis=1, inplace=True)

        # 统计命中率
        hit_count = merged['cumulative_2days'].notna().sum()
        hit_rate = hit_count / len(selected_stocks) * 100
        print(f"缓存命中率: {hit_rate:.1f}% ({hit_count}/{len(selected_stocks)})")

        return hit_rate > 50  # 命中率超过50%认为成功

    except Exception as e:
        print(f"加载缓存时出错: {e}")
        return False

# 不再需要get_stock_data函数，因为我们使用批量关联操作来获取涨跌幅

def process_strategy(strategy_id, next_trading_day_map=None, date_to_data=None):
    """处理单个策略，更新汇总表并生成详细分析文件"""
    # 记录开始时间
    start_time = time.time()

    # 如果没有提供next_trading_day_map和date_to_data，则使用全局变量
    if next_trading_day_map is None and 'next_trading_day_map' in globals() and globals()['next_trading_day_map']:
        next_trading_day_map = globals()['next_trading_day_map']
        print(f"使用全局交易日历映射，包含 {len(next_trading_day_map)} 个日期")

    if date_to_data is None and 'date_to_data' in globals() and globals()['date_to_data']:
        date_to_data = globals()['date_to_data']
        print(f"使用全局日期数据映射，包含 {len(date_to_data)} 个日期")

    # 回测策略
    result = backtest_strategy_manually(strategy_id, next_trading_day_map, date_to_data)

    # 计算耗时
    end_time = time.time()
    elapsed_time = end_time - start_time
    minutes, seconds = divmod(elapsed_time, 60)
    print(f"策略 {strategy_id} 回测完成！耗时: {int(minutes)}分钟 {int(seconds)}秒")

    if result:
        # 更新汇总表中的统计数据
        # 查找策略编号对应的行
        strategy_rows = summary_df[summary_df['策略编号'] == strategy_id]
        if len(strategy_rows) == 0:
            print(f"警告：在汇总表中找不到策略 {strategy_id}，但仍将保存实际筛选结果")

            # 保存实际筛选结果，即使策略不在汇总表中
            detail_file = os.path.join(output_dir, f"strategy_{strategy_id}.xlsx")

            # 检查是否有选出的股票
            if '选出的股票' in result:
                stock_count = len(result['选出的股票'])
                print(f"策略 {strategy_id} 实际选出了 {stock_count} 只股票")

                try:
                    with pd.ExcelWriter(detail_file, engine='openpyxl') as writer:
                        # 1. 策略汇总参数 - 使用实际结果
                        strategy_summary = pd.DataFrame({
                            '策略编号': [strategy_id],
                            '策略组合': [f'未知策略{strategy_id}'],
                            '特征数量': [0],
                            '平均收益率(%)': [result['平均收益率(%)']],
                            '平均胜率(%)': [result['平均胜率(%)']],
                            '平均每日交易笔数': [result['平均每日交易笔数']],
                            '总交易笔数': [result['总交易笔数']],
                            '交易天数': [result['交易天数']],
                            '总天数': [result['总天数']],
                            '交易频率(%)': [result['交易频率(%)']],
                            '初始资金(元)': [result['初始资金']],
                            '最终资金(元)': [result['最终资金']],
                            '盈利(元)': [result['盈利']],
                            '累计收益率(%)': [result['累计收益率(%)']],
                            '年化收益率(%)': [result['年化收益率(%)']],
                        })
                        strategy_summary.to_excel(writer, sheet_name='策略汇总参数', index=False)

                        # 2. 策略参数
                        strategy_params = pd.DataFrame({
                            '策略编号': [strategy_id],
                            '策略条件描述': [f'未知策略{strategy_id}'],
                        })
                        strategy_params.to_excel(writer, sheet_name='策略参数', index=False)

                        # 3. 实际选股结果
                        if stock_count > 0:
                            selected_stocks = result['选出的股票'].copy()
                            selected_stocks['策略编号'] = strategy_id
                            selected_stocks.to_excel(writer, sheet_name='选股明细', index=False)
                        else:
                            # 如果没有选出股票，创建空的DataFrame
                            empty_df = pd.DataFrame({
                                '股票代码': ['000000'],
                                '股票名称': ['空数据'],
                                '策略编号': [strategy_id]
                            })
                            empty_df.to_excel(writer, sheet_name='选股明细', index=False)

                        # 4. 每日收益明细
                        if '每日收益' in result:
                            daily_profit_df = result['每日收益'].copy()
                            daily_profit_df['策略编号'] = strategy_id
                            daily_profit_df.to_excel(writer, sheet_name='每日收益明细', index=False)

                        # 5. 说明
                        info_df = pd.DataFrame({
                            '说明': [
                                f'策略编号: {strategy_id}',
                                f'创建时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                                '该策略不在汇总表中，但包含实际筛选结果',
                                f'实际选出股票数量: {stock_count}'
                            ]
                        })
                        info_df.to_excel(writer, sheet_name='说明', index=False)

                    print(f"已保存策略 {strategy_id} 的实际筛选结果到 {detail_file}")
                    return True

                except Exception as e:
                    print(f"保存实际筛选结果时出错: {e}")
                    return False
            else:
                print(f"策略 {strategy_id} 的结果中没有选股数据")
                return False

        idx = strategy_rows.index[0]
        print(f"找到策略 {strategy_id} 在汇总表中的索引: {idx}")

        # 添加新的财务指标列（如果不存在）
        for col in ['初始资金', '最终资金', '盈利', '累计收益率(%)', '年化收益率(%)']:
            if col not in summary_df.columns:
                summary_df[col] = None

        # 更新所有指标
        update_columns = {
            '平均收益率(%)': result['平均收益率(%)'],
            '平均胜率(%)': result['平均胜率(%)'],
            '平均每日交易笔数': result['平均每日交易笔数'],
            '总交易笔数': result['总交易笔数'],
            '交易天数': result['交易天数'],
            '总天数': result['总天数'],
            '交易频率(%)': result['交易频率(%)'],
            '初始资金': result['初始资金'],
            '最终资金': result['最终资金'],
            '盈利': result['盈利'],
            '累计收益率(%)': result['累计收益率(%)'],
            '年化收益率(%)': result['年化收益率(%)']
        }

        # 批量更新列
        for col, value in update_columns.items():
            try:
                summary_df.loc[idx, col] = value
            except Exception as e:
                print(f"更新列 '{col}' 时出错: {e}")

        # 保存更新后的汇总表
        try:
            output_path = os.path.join(base_dir, '所有策略汇总_已回测.xlsx')

            # 检查是否已经存在汇总表文件
            output_parquet_path = os.path.splitext(output_path)[0] + '.parquet'

            if os.path.exists(output_parquet_path):
                # 优先读取Parquet格式的汇总表
                try:
                    start_time = time.time()
                    existing_summary_df = pd.read_parquet(output_parquet_path)
                    load_time = time.time() - start_time
                    print(f"读取现有汇总表(Parquet格式)，共 {len(existing_summary_df)} 条记录，耗时: {load_time:.2f}秒")

                    # 只更新当前策略的数据，保留其他策略的数据
                    strategy_idx = existing_summary_df[existing_summary_df['策略编号'] == strategy_id].index
                    if len(strategy_idx) > 0:
                        # 更新现有记录
                        for col in update_columns.keys():
                            if col in existing_summary_df.columns:
                                existing_summary_df.loc[strategy_idx[0], col] = update_columns[col]

                        # 保存更新后的汇总表（同时保存Excel和Parquet格式）
                        existing_summary_df.to_parquet(output_parquet_path, index=False)
                        existing_summary_df.to_excel(output_path, index=False)
                        print(f"已更新现有汇总表中策略 {strategy_id} 的数据（Excel和Parquet格式）")
                    else:
                        # 如果现有汇总表中没有当前策略，则将当前策略添加到汇总表中
                        print(f"现有汇总表中没有策略 {strategy_id}，将添加新记录")
                        summary_df.to_parquet(output_parquet_path, index=False)
                        summary_df.to_excel(output_path, index=False)
                except Exception as read_error:
                    print(f"读取Parquet格式汇总表时出错: {read_error}，尝试读取Excel格式")
                    try:
                        if os.path.exists(output_path):
                            start_time = time.time()
                            existing_summary_df = pd.read_excel(output_path)
                            load_time = time.time() - start_time
                            print(f"读取现有汇总表(Excel格式)，共 {len(existing_summary_df)} 条记录，耗时: {load_time:.2f}秒")

                            # 只更新当前策略的数据，保留其他策略的数据
                            strategy_idx = existing_summary_df[existing_summary_df['策略编号'] == strategy_id].index
                            if len(strategy_idx) > 0:
                                # 更新现有记录
                                for col in update_columns.keys():
                                    if col in existing_summary_df.columns:
                                        existing_summary_df.loc[strategy_idx[0], col] = update_columns[col]

                                # 保存更新后的汇总表（同时保存Excel和Parquet格式）
                                existing_summary_df.to_excel(output_path, index=False)
                                try:
                                    existing_summary_df.to_parquet(output_parquet_path, index=False)
                                    print(f"已将汇总表转换为Parquet格式并保存")
                                except Exception as e:
                                    print(f"保存Parquet格式汇总表时出错: {e}")
                                print(f"已更新现有汇总表中策略 {strategy_id} 的数据")
                            else:
                                # 如果现有汇总表中没有当前策略，则将当前策略添加到汇总表中
                                print(f"现有汇总表中没有策略 {strategy_id}，将添加新记录")
                                summary_df.to_excel(output_path, index=False)
                                try:
                                    summary_df.to_parquet(output_parquet_path, index=False)
                                    print(f"已将汇总表转换为Parquet格式并保存")
                                except Exception as e:
                                    print(f"保存Parquet格式汇总表时出错: {e}")
                        else:
                            # 如果不存在Excel格式汇总表文件，则直接保存
                            summary_df.to_excel(output_path, index=False)
                            try:
                                summary_df.to_parquet(output_parquet_path, index=False)
                                print(f"已将汇总表转换为Parquet格式并保存")
                            except Exception as e:
                                print(f"保存Parquet格式汇总表时出错: {e}")
                    except Exception as excel_error:
                        print(f"读取Excel格式汇总表时也出错: {excel_error}")
                        print("将覆盖现有汇总表")
                        summary_df.to_excel(output_path, index=False)
                        try:
                            summary_df.to_parquet(output_parquet_path, index=False)
                            print(f"已将汇总表转换为Parquet格式并保存")
                        except Exception as e:
                            print(f"保存Parquet格式汇总表时出错: {e}")
            elif os.path.exists(output_path):
                # 读取Excel格式的汇总表
                try:
                    start_time = time.time()
                    existing_summary_df = pd.read_excel(output_path)
                    load_time = time.time() - start_time
                    print(f"读取现有汇总表(Excel格式)，共 {len(existing_summary_df)} 条记录，耗时: {load_time:.2f}秒")

                    # 只更新当前策略的数据，保留其他策略的数据
                    strategy_idx = existing_summary_df[existing_summary_df['策略编号'] == strategy_id].index
                    if len(strategy_idx) > 0:
                        # 更新现有记录
                        for col in update_columns.keys():
                            if col in existing_summary_df.columns:
                                existing_summary_df.loc[strategy_idx[0], col] = update_columns[col]

                        # 保存更新后的汇总表（同时保存Excel和Parquet格式）
                        existing_summary_df.to_excel(output_path, index=False)
                        try:
                            existing_summary_df.to_parquet(output_parquet_path, index=False)
                            print(f"已将汇总表转换为Parquet格式并保存")
                        except Exception as e:
                            print(f"保存Parquet格式汇总表时出错: {e}")
                        print(f"已更新现有汇总表中策略 {strategy_id} 的数据")
                    else:
                        # 如果现有汇总表中没有当前策略，则将当前策略添加到汇总表中
                        print(f"现有汇总表中没有策略 {strategy_id}，将添加新记录")
                        summary_df.to_excel(output_path, index=False)
                        try:
                            summary_df.to_parquet(output_parquet_path, index=False)
                            print(f"已将汇总表转换为Parquet格式并保存")
                        except Exception as e:
                            print(f"保存Parquet格式汇总表时出错: {e}")
                except Exception as read_error:
                    print(f"读取现有汇总表时出错: {read_error}")
                    print("将覆盖现有汇总表")
                    summary_df.to_excel(output_path, index=False)
                    try:
                        summary_df.to_parquet(output_parquet_path, index=False)
                        print(f"已将汇总表转换为Parquet格式并保存")
                    except Exception as e:
                        print(f"保存Parquet格式汇总表时出错: {e}")
            else:
                # 如果不存在汇总表文件，则直接保存
                summary_df.to_excel(output_path, index=False)
                try:
                    summary_df.to_parquet(output_parquet_path, index=False)
                    print(f"已将汇总表转换为Parquet格式并保存")
                except Exception as e:
                    print(f"保存Parquet格式汇总表时出错: {e}")

            print(f"已保存更新后的汇总表到 {output_path}")

            # 验证保存是否成功
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"汇总表文件大小: {file_size} 字节")
                if file_size > 0:
                    print(f"已成功更新策略 {strategy_id} 的汇总数据")
                else:
                    print(f"警告: 汇总表文件大小为0")
            else:
                print(f"错误: 汇总表文件不存在")
        except Exception as e:
            print(f"保存汇总表时出错: {e}")
            # 尝试使用不同的文件名
            try:
                backup_path = os.path.join(base_dir, f'所有策略汇总_已回测_{pd.Timestamp.now().strftime("%Y%m%d%H%M%S")}.xlsx')
                summary_df.to_excel(backup_path, index=False)
                print(f"已保存备份汇总表到 {backup_path}")
            except Exception as backup_error:
                print(f"保存备份汇总表时出错: {backup_error}")

        # 保存详细分析文件
        # 检查汇总表中是否有指定的文件名
        if '详细分析文件' in summary_df.columns and not pd.isna(summary_df.loc[idx, '详细分析文件']):
            # 使用汇总表中指定的文件名
            file_name = summary_df.loc[idx, '详细分析文件']
            print(f"使用汇总表中指定的文件名: {file_name}")
        else:
            # 使用默认的文件名格式
            file_name = f"strategy_{strategy_id}.xlsx"
            print(f"汇总表中没有指定文件名，使用默认文件名: {file_name}")

        detail_file = os.path.join(output_dir, file_name)

        # 如果文件已存在，尝试删除
        if os.path.exists(detail_file):
            try:
                os.remove(detail_file)
                print(f"已删除现有文件: {detail_file}")
            except Exception as e:
                print(f"无法删除文件 {detail_file}: {e}")
                print("尝试使用不同的文件名...")
                detail_file = os.path.join(output_dir, f"strategy_{strategy_id}_{pd.Timestamp.now().strftime('%Y%m%d%H%M%S')}.xlsx")

        # 如果有选出的股票或者返回了空的DataFrame，保存详细分析
        if '选出的股票' in result:
            stock_count = len(result['选出的股票'])
            if stock_count > 0:
                print(f"策略 {strategy_id} 选出了 {stock_count} 只股票，准备生成详细分析文件")
            else:
                print(f"策略 {strategy_id} 没有选出任何股票，但仍将创建Excel文件")

            try:
                # 准备数据
                print("准备策略汇总参数数据...")
                strategy_summary = pd.DataFrame({
                    '策略编号': [strategy_id],
                    '策略组合': [summary_df.loc[idx, '策略组合']],
                    '特征数量': [summary_df.loc[idx, '特征数量']],
                    '平均收益率(%)': [result['平均收益率(%)']],
                    '平均胜率(%)': [result['平均胜率(%)']],
                    '平均每日交易笔数': [result['平均每日交易笔数']],
                    '总交易笔数': [result['总交易笔数']],
                    '交易天数': [result['交易天数']],
                    '总天数': [result['总天数']],
                    '交易频率(%)': [result['交易频率(%)']],
                    '初始资金(元)': [result['初始资金']],
                    '最终资金(元)': [result['最终资金']],
                    '盈利(元)': [result['盈利']],
                    '累计收益率(%)': [result['累计收益率(%)']],
                    '年化收益率(%)': [result['年化收益率(%)']],
                })

                print("准备策略参数数据...")
                strategy_params = pd.DataFrame({
                    '策略编号': [strategy_id],
                    '策略条件描述': [summary_df.loc[idx, '策略条件描述']],
                })

                print("准备选股明细数据...")
                selected_stocks = result['选出的股票'].copy()

                # 不再计算每日平均收益率
                print("准备选股明细数据...")

                # 重新组织列顺序，添加买入日开盘涨跌幅、卖出日开盘涨跌幅、买入后连续2天累计涨幅和买入后连续3天累计涨幅
                columns_to_include = [
                    '股票代码', '股票名称', '选股日期', '技术强度', '涨跌幅',
                    '买入日期', '买入日涨跌幅', '卖出日期', '卖出日股票涨跌幅',
                    '是否盈利'
                ]

                # 检查是否存在买入日开盘涨跌幅和卖出日开盘涨跌幅列
                if '买入日开盘涨跌幅' in selected_stocks.columns:
                    columns_to_include.append('买入日开盘涨跌幅')
                    print("添加买入日开盘涨跌幅列到选股明细")

                if '卖出日开盘涨跌幅' in selected_stocks.columns:
                    columns_to_include.append('卖出日开盘涨跌幅')
                    print("添加卖出日开盘涨跌幅列到选股明细")

                # 检查是否存在买入后连续2个交易日累计涨幅和买入后连续3个交易日累计涨幅列
                if '买入后连续2个交易日累计涨幅' in selected_stocks.columns:
                    columns_to_include.append('买入后连续2个交易日累计涨幅')
                    print("添加买入后连续2个交易日累计涨幅列到选股明细")

                if '买入后连续3个交易日累计涨幅' in selected_stocks.columns:
                    columns_to_include.append('买入后连续3个交易日累计涨幅')
                    print("添加买入后连续3个交易日累计涨幅列到选股明细")

                stock_details = selected_stocks[columns_to_include].copy()

                # 添加策略信息
                stock_details['策略编号'] = strategy_id

                # 准备每日收益明细数据
                if '每日收益' in result:
                    print("准备每日收益明细数据...")
                    daily_profit_df = result['每日收益']
                    # 格式化日期列
                    if isinstance(daily_profit_df['日期'].iloc[0], pd.Timestamp):
                        daily_profit_df['日期'] = daily_profit_df['日期'].dt.strftime('%Y-%m-%d')

                    # 添加策略信息
                    daily_profit_df['策略编号'] = strategy_id

                    # 格式化数值列
                    for col in ['日平均涨幅(%)', '日收益率', '日收益金额', '累计资金', '交易股票数量']:
                        if col in daily_profit_df.columns:
                            daily_profit_df[col] = daily_profit_df[col].round(2)

                # 创建Excel文件
                print(f"创建Excel文件: {detail_file}")
                try:
                    # 使用to_excel直接保存各个DataFrame到不同的sheet
                    with pd.ExcelWriter(detail_file, engine='openpyxl') as writer:
                        print("保存策略汇总参数...")
                        strategy_summary.to_excel(writer, sheet_name='策略汇总参数', index=False)

                        print("保存策略参数...")
                        strategy_params.to_excel(writer, sheet_name='策略参数', index=False)

                        print("保存选股明细...")
                        stock_details.to_excel(writer, sheet_name='选股明细', index=False)

                        if '每日收益' in result:
                            print("保存每日收益明细...")
                            daily_profit_df.to_excel(writer, sheet_name='每日收益明细', index=False)

                            # 设置当日胜率列的格式为百分比格式
                            try:
                                # 获取工作表
                                worksheet = writer.sheets['每日收益明细']

                                # 找到当日胜率列的索引
                                col_idx = daily_profit_df.columns.get_loc('当日胜率(%)') + 1  # Excel列从1开始

                                # 设置列格式为百分比，保留2位小数
                                for row in range(2, len(daily_profit_df) + 2):  # Excel行从1开始，第1行是标题
                                    cell = worksheet.cell(row=row, column=col_idx)
                                    cell.number_format = '0.00%'

                                print("成功设置当日胜率列的格式为百分比格式")
                            except Exception as e:
                                print(f"设置Excel格式时出错: {e}")

                    # 验证文件是否成功保存
                    if os.path.exists(detail_file):
                        file_size = os.path.getsize(detail_file)
                        print(f"详细分析文件大小: {file_size} 字节")
                        if file_size > 0:
                            print(f"已成功保存策略 {strategy_id} 的详细分析到 {detail_file}")
                            return True
                        else:
                            print(f"警告: 详细分析文件大小为0")
                    else:
                        print(f"错误: 详细分析文件不存在")
                except Exception as excel_error:
                    print(f"保存Excel文件时出错: {excel_error}")

                    # 尝试使用不同的方法保存
                    try:
                        print("尝试使用替代方法保存Excel文件...")
                        backup_file = os.path.join(output_dir, f"strategy_{strategy_id}_backup.xlsx")

                        # 分别保存各个DataFrame
                        strategy_summary.to_excel(backup_file, sheet_name='策略汇总参数', index=False)
                        print(f"已保存策略汇总参数到 {backup_file}")

                        return True
                    except Exception as backup_error:
                        print(f"备份方法也失败: {backup_error}")
            except Exception as e:
                print(f"准备详细分析数据时出错: {e}")

            return False
        else:
            print(f"策略 {strategy_id} 没有选出任何股票，创建空的Excel文件")

            # 创建空的Excel文件
            detail_file = os.path.join(output_dir, f"strategy_{strategy_id}.xlsx")

            # 创建一个Excel写入器
            with pd.ExcelWriter(detail_file, engine='openpyxl') as writer:
                # 1. 策略汇总参数
                strategy_summary = pd.DataFrame({
                    '策略编号': [strategy_id],
                    '策略组合': [summary_df.loc[idx, '策略组合']],
                    '特征数量': [summary_df.loc[idx, '特征数量']],
                    '平均收益率(%)': [0],
                    '平均胜率(%)': [0],
                    '平均每日交易笔数': [0],
                    '总交易笔数': [0],
                    '交易天数': [0],
                    '总天数': [len(stock_df['日期'].unique())],
                    '交易频率(%)': [0],
                    '初始资金(元)': [10000],
                    '最终资金(元)': [10000],
                    '盈利(元)': [0],
                    '累计收益率(%)': [0],
                    '年化收益率(%)': [0],
                })
                strategy_summary.to_excel(writer, sheet_name='策略汇总参数', index=False)

                # 2. 策略参数
                strategy_params = pd.DataFrame({
                    '策略编号': [strategy_id],
                    '策略条件描述': [summary_df.loc[idx, '策略条件描述']],
                })
                strategy_params.to_excel(writer, sheet_name='策略参数', index=False)

                # 3. 空的股票选择结果 - 添加一行假数据，确保文件能被正确读取
                empty_df = pd.DataFrame({
                    '股票代码': ['000000'],
                    '股票名称': ['空数据'],
                    '选股日期': [pd.Timestamp('2025-01-01')],
                    '技术强度': [0],
                    '涨跌幅': [0],
                    '买入日期': [pd.Timestamp('2025-01-02')],
                    '买入日涨跌幅': [0.00],  # 使用0.00表示百分比
                    '卖出日期': [pd.Timestamp('2025-01-03')],
                    '卖出日股票涨跌幅': [0.00],  # 使用0.00表示百分比
                    '买入日开盘涨跌幅': [0.00],  # 添加买入日开盘涨跌幅，使用0.00表示百分比
                    '卖出日开盘涨跌幅': [0.00],  # 添加卖出日开盘涨跌幅，使用0.00表示百分比
                    '买入后连续2个交易日累计涨幅': [0.00],  # 添加买入后连续2个交易日累计涨幅，使用0.00表示百分比
                    '买入后连续3个交易日累计涨幅': [0.00],  # 添加买入后连续3个交易日累计涨幅，使用0.00表示百分比
                    '是否盈利': [False],
                    '策略编号': [strategy_id]
                })
                empty_df.to_excel(writer, sheet_name='选股明细', index=False)

                # 4. 空的每日收益明细 - 添加一行假数据，确保文件能被正确读取
                empty_daily_df = pd.DataFrame({
                    '日期': [pd.Timestamp('2025-01-01')],
                    '日平均涨幅(%)': [0],
                    '当日胜率(%)': [0],
                    '日收益率': [0],
                    '日收益金额': [0],
                    '累计资金': [10000],
                    '交易股票数量': [0],
                    '策略编号': [strategy_id]
                })
                empty_daily_df.to_excel(writer, sheet_name='每日收益明细', index=False)

                # 设置当日胜率列的格式为百分比格式
                try:
                    # 获取工作表
                    worksheet = writer.sheets['每日收益明细']

                    # 找到当日胜率列的索引
                    col_idx = empty_daily_df.columns.get_loc('当日胜率(%)') + 1  # Excel列从1开始

                    # 设置列格式为百分比，保留2位小数
                    for row in range(2, len(empty_daily_df) + 2):  # Excel行从1开始，第1行是标题
                        cell = worksheet.cell(row=row, column=col_idx)
                        cell.number_format = '0.00%'

                    print("成功设置空的每日收益明细中当日胜率列的格式为百分比格式")
                except Exception as e:
                    print(f"设置Excel格式时出错: {e}")

                # 5. 说明
                info_df = pd.DataFrame({
                    '说明': [
                        f'策略编号: {strategy_id}',
                        f'创建时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                        '该策略没有筛选出符合条件的股票',
                        '这是一个空的Excel文件，用于确保程序能够继续执行下一个组合'
                    ]
                })
                info_df.to_excel(writer, sheet_name='说明', index=False)

            print(f"已创建空的Excel文件: {detail_file}")

            # 验证文件是否成功保存
            if os.path.exists(detail_file):
                file_size = os.path.getsize(detail_file)
                print(f"详细分析文件大小: {file_size} 字节")
                if file_size > 0:
                    print(f"已成功保存策略 {strategy_id} 的详细分析到 {detail_file}")
                    return True
                else:
                    print(f"警告: 详细分析文件大小为0")
            else:
                print(f"错误: 详细分析文件不存在")

            return True

    return False

def backtest_strategy_manually(strategy_id, next_trading_day_map=None, date_to_data=None):
    """
    手动回测特定策略

    参数:
    strategy_id: 策略ID
    next_trading_day_map: 日期到下一个交易日的映射，如果提供，将优先使用
    date_to_data: 日期到股票数据的映射，如果提供，将优先使用

    返回:
    dict: 包含策略回测结果的字典
    """
    try:
        # 获取策略信息
        strategy_rows = summary_df[summary_df['策略编号'] == strategy_id]
        if len(strategy_rows) == 0:
            print(f"错误: 在汇总表中找不到策略 {strategy_id}，立即创建空文件并返回")

            # 创建策略结果目录（如果不存在）
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                print(f"创建目录: {output_dir}")

            # 创建空的Excel文件
            detail_file = os.path.join(output_dir, f"strategy_{strategy_id}.xlsx")

            # 如果文件已存在，尝试删除
            if os.path.exists(detail_file):
                try:
                    os.remove(detail_file)
                    print(f"已删除现有文件: {detail_file}")
                except Exception as e:
                    print(f"无法删除文件 {detail_file}: {e}")
                    detail_file = os.path.join(output_dir, f"strategy_{strategy_id}_{pd.Timestamp.now().strftime('%Y%m%d%H%M%S')}.xlsx")

            # 创建一个Excel写入器，确保至少有一个可见工作表
            try:
                # 先创建一个简单的DataFrame确保有内容
                empty_summary = pd.DataFrame({
                    '策略编号': [strategy_id],
                    '说明': ['策略不在汇总表中']
                })

                with pd.ExcelWriter(detail_file, engine='openpyxl') as writer:
                    # 先写入一个基本工作表，确保文件有内容
                    empty_summary.to_excel(writer, sheet_name='说明', index=False)
                    # 1. 策略汇总参数
                    strategy_summary = pd.DataFrame({
                        '策略编号': [strategy_id],
                        '策略组合': [f'未知策略{strategy_id}'],
                        '特征数量': [0],
                        '平均收益率(%)': [0],
                        '平均胜率(%)': [0],
                        '平均每日交易笔数': [0],
                        '总交易笔数': [0],
                        '交易天数': [0],
                        '总天数': [len(all_daily_data) if use_daily_files and all_daily_data else (len(stock_df['日期'].unique()) if not stock_df.empty else 0)],
                        '交易频率(%)': [0],
                        '初始资金(元)': [10000],
                        '最终资金(元)': [10000],
                        '盈利(元)': [0],
                        '累计收益率(%)': [0],
                        '年化收益率(%)': [0],
                    })
                    strategy_summary.to_excel(writer, sheet_name='策略汇总参数', index=False)

                    # 2. 策略参数
                    strategy_params = pd.DataFrame({
                        '策略编号': [strategy_id],
                        '策略条件描述': [f'未知策略{strategy_id}'],
                    })
                    strategy_params.to_excel(writer, sheet_name='策略参数', index=False)

                    # 3. 空的股票选择结果 - 添加一行假数据，确保文件能被正确读取
                    empty_stock_df = pd.DataFrame({
                        '股票代码': ['000000'],
                        '股票名称': ['空数据'],
                        '选股日期': [pd.Timestamp('2025-01-01')],
                        '技术强度': [0],
                        '涨跌幅': [0],
                        '买入日期': [pd.Timestamp('2025-01-02')],
                        '买入日涨跌幅': [0.00],  # 使用0.00表示百分比
                        '卖出日期': [pd.Timestamp('2025-01-03')],
                        '卖出日股票涨跌幅': [0.00],  # 使用0.00表示百分比
                        '买入日开盘涨跌幅': [0.00],  # 添加买入日开盘涨跌幅，使用0.00表示百分比
                        '卖出日开盘涨跌幅': [0.00],  # 添加卖出日开盘涨跌幅，使用0.00表示百分比
                        '买入后连续2个交易日累计涨幅': [0.00],  # 添加买入后连续2个交易日累计涨幅，使用0.00表示百分比
                        '买入后连续3个交易日累计涨幅': [0.00],  # 添加买入后连续3个交易日累计涨幅，使用0.00表示百分比
                        '是否盈利': [False],
                        '策略编号': [strategy_id]
                    })
                    empty_stock_df.to_excel(writer, sheet_name='选股明细', index=False)

                    # 4. 空的每日收益明细 - 添加一行假数据，确保文件能被正确读取
                    empty_daily_df = pd.DataFrame({
                        '日期': [pd.Timestamp('2025-01-01')],
                        '日平均涨幅(%)': [0],
                        '当日胜率(%)': [0],
                        '日收益率': [0],
                        '日收益金额': [0],
                        '累计资金': [10000],
                        '交易股票数量': [0],
                        '策略编号': [strategy_id]
                    })
                    empty_daily_df.to_excel(writer, sheet_name='每日收益明细', index=False)

                    # 5. 说明
                    info_df = pd.DataFrame({
                        '说明': [
                            f'策略编号: {strategy_id}',
                            f'创建时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                            '在汇总表中找不到该策略',
                            '这是一个空的Excel文件，用于确保程序能够继续执行下一个组合'
                        ]
                    })
                    info_df.to_excel(writer, sheet_name='说明', index=False)
            except Exception as e:
                print(f"创建Excel文件时出错: {e}")
                # 创建一个简单的CSV文件作为备用
                backup_file = detail_file.replace('.xlsx', '_backup.csv')
                pd.DataFrame({'策略编号': [strategy_id], '说明': ['Excel创建失败']}).to_csv(backup_file, index=False)
                detail_file = backup_file

            print(f"已创建空的Excel文件: {detail_file}")

            # 验证文件是否成功保存
            if os.path.exists(detail_file):
                file_size = os.path.getsize(detail_file)
                print(f"详细分析文件大小: {file_size} 字节")
                if file_size > 0:
                    print(f"已成功保存策略 {strategy_id} 的详细分析到 {detail_file}")
                else:
                    print(f"警告: 详细分析文件大小为0")

            # 创建空的每日收益DataFrame和选股DataFrame
            empty_daily_df = pd.DataFrame({
                '日期': [pd.Timestamp('2025-01-01')],
                '日平均涨幅(%)': [0],
                '当日胜率(%)': [0],
                '日收益率': [0],
                '日收益金额': [0],
                '累计资金': [10000],
                '交易股票数量': [0],
                '策略编号': [strategy_id]
            })

            empty_stock_df = pd.DataFrame({
                '股票代码': ['000000'],
                '股票名称': ['空数据'],
                '选股日期': [pd.Timestamp('2025-01-01')],
                '技术强度': [0],
                '涨跌幅': [0],
                '买入日期': [pd.Timestamp('2025-01-02')],
                '买入日涨跌幅': [0.00],
                '卖出日期': [pd.Timestamp('2025-01-03')],
                '卖出日股票涨跌幅': [0.00],
                '买入日开盘涨跌幅': [0.00],
                '卖出日开盘涨跌幅': [0.00],
                '买入后连续2个交易日累计涨幅': [0.00],
                '买入后连续3个交易日累计涨幅': [0.00],
                '是否盈利': [False],
                '策略编号': [strategy_id]
            })

            # 创建空的每日收益DataFrame
            empty_daily_df = pd.DataFrame({
                '日期': [pd.Timestamp('2025-01-01')],
                '日平均涨幅(%)': [0],
                '当日胜率(%)': [0],
                '日收益率': [0],
                '日收益金额': [0],
                '累计资金': [10000],
                '交易股票数量': [0],
                '策略编号': [strategy_id]
            })

            # 创建空的选股DataFrame
            empty_stock_df = pd.DataFrame({
                '股票代码': ['000000'],
                '股票名称': ['空数据'],
                '选股日期': [pd.Timestamp('2025-01-01')],
                '技术强度': [0],
                '涨跌幅': [0],
                '买入日期': [pd.Timestamp('2025-01-02')],
                '买入日涨跌幅': [0.00],
                '卖出日期': [pd.Timestamp('2025-01-03')],
                '卖出日股票涨跌幅': [0.00],
                '买入日开盘涨跌幅': [0.00],
                '卖出日开盘涨跌幅': [0.00],
                '买入后连续2个交易日累计涨幅': [0.00],
                '买入后连续3个交易日累计涨幅': [0.00],
                '是否盈利': [False],
                '策略编号': [strategy_id]
            })

            return {
                '平均收益率(%)': 0,
                '平均胜率(%)': 0,
                '平均每日交易笔数': 0,
                '总交易笔数': 0,
                '交易天数': 0,
                '总天数': len(all_daily_data) if use_daily_files and all_daily_data else (len(stock_df['日期'].unique()) if not stock_df.empty and '日期' in stock_df.columns else 0),
                '交易频率(%)': 0,
                '初始资金': 10000,
                '最终资金': 10000,
                '盈利': 0,
                '累计收益率(%)': 0,
                '年化收益率(%)': 0,
                '每日收益': empty_daily_df,
                '选出的股票': empty_stock_df
            }

        strategy_row = strategy_rows.iloc[0]
        strategy_desc = strategy_row['策略条件描述']

        print(f"回测策略 {strategy_id}: {strategy_desc}")

        # 解析策略条件
        conditions = []
        if "AND" in strategy_desc:
            condition_parts = strategy_desc.split("AND")
            for part in condition_parts:
                part = part.strip()
                conditions.append(part)
        else:
            conditions.append(strategy_desc.strip())

        # 记录原始条件，用于后续验证
        original_conditions = []

        # 解析所有条件
        parsed_conditions = []
        for condition in conditions:
            condition = condition.strip()

            # 解析条件
            if "大于等于" in condition:
                parts = condition.split("大于等于")
                col_name = parts[0].strip()
                value_part = parts[1].strip()

                # 提取数值
                if "（" in value_part:
                    value = float(value_part.split("（")[0].strip())
                else:
                    value = float(value_part)

                parsed_conditions.append((col_name, ">=", value))
                original_conditions.append((col_name, ">=", value))

            elif "小于等于" in condition:
                parts = condition.split("小于等于")
                col_name = parts[0].strip()
                value_part = parts[1].strip()

                # 提取数值
                if "（" in value_part:
                    value = float(value_part.split("（")[0].strip())
                else:
                    value = float(value_part)

                parsed_conditions.append((col_name, "<=", value))
                original_conditions.append((col_name, "<=", value))

            elif "为" in condition:
                parts = condition.split("为")
                col_name = parts[0].strip()
                value_part = parts[1].strip()

                # 提取数值
                if "（是）" in value_part:
                    value = 1
                elif "（否）" in value_part:
                    value = 0
                else:
                    value = value_part

                parsed_conditions.append((col_name, "==", value))
                original_conditions.append((col_name, "==", value))

            elif "等于" in condition or " = " in condition:
                # 处理"等于"或"="条件
                if "等于" in condition:
                    parts = condition.split("等于")
                else:
                    parts = condition.split(" = ")

                col_name = parts[0].strip()
                value_part = parts[1].strip()

                # 提取数值
                try:
                    if "（" in value_part:
                        value = float(value_part.split("（")[0].strip())
                    else:
                        value = float(value_part)
                except ValueError:
                    # 如果无法转换为浮点数，保留原始值
                    value = value_part

                parsed_conditions.append((col_name, "==", value))
                original_conditions.append((col_name, "==", value))

        # 根据是否使用按日期存储的文件来选择不同的筛选策略
        if use_daily_files and all_daily_data is not None:
            # 按日期逐个加载和筛选数据
            all_selected_stocks = []

            # 获取可用日期列表
            dates_to_process = list(all_daily_data.keys()) if all_daily_data else []

            for date in dates_to_process:
                # 从预加载的数据中获取当天的数据
                daily_df = get_daily_stock_data(date, all_daily_data)

                if daily_df.empty:
                    continue

                # 应用筛选条件
                filtered_daily_df = daily_df.copy()

                for col_name, operator, value in parsed_conditions:
                    if col_name not in filtered_daily_df.columns:
                        print(f"警告: 列 '{col_name}' 在日期 {date.strftime('%Y-%m-%d')} 的数据中不存在")
                        continue

                    if operator == ">=":
                        filtered_daily_df = filtered_daily_df[filtered_daily_df[col_name] >= value]
                    elif operator == "<=":
                        filtered_daily_df = filtered_daily_df[filtered_daily_df[col_name] <= value]
                    elif operator == "==":
                        # 特殊处理技术指标特征和趋势组合等二进制编码字段
                        if col_name in ['技术指标特征', '趋势组合', '日内股票标记'] and isinstance(value, str):
                            filtered_daily_df[col_name] = filtered_daily_df[col_name].astype(str)
                            filtered_daily_df = filtered_daily_df[filtered_daily_df[col_name] == value]
                        else:
                            # 确保数据类型匹配
                            if isinstance(value, (int, float)):
                                try:
                                    filtered_daily_df[col_name] = pd.to_numeric(filtered_daily_df[col_name], errors='coerce')
                                except:
                                    pass
                            filtered_daily_df = filtered_daily_df[filtered_daily_df[col_name] == value]

                if not filtered_daily_df.empty:
                    all_selected_stocks.append(filtered_daily_df)

            # 合并所有筛选结果
            if all_selected_stocks:
                selected_stocks = pd.concat(all_selected_stocks, ignore_index=True)
                print(f"选出 {len(selected_stocks)} 条记录")
            else:
                selected_stocks = pd.DataFrame()
                print("没有选出任何股票")
        else:
            # 使用传统的单文件筛选方式
            filtered_df = stock_df.copy()

            for col_name, operator, value in parsed_conditions:
                if col_name not in filtered_df.columns:
                    print(f"警告: 列 '{col_name}' 不存在")
                    continue

                if operator == ">=":
                    filtered_df = filtered_df[filtered_df[col_name] >= value]
                elif operator == "<=":
                    filtered_df = filtered_df[filtered_df[col_name] <= value]
                elif operator == "==":
                    # 特殊处理技术指标特征和趋势组合等二进制编码字段
                    if col_name in ['技术指标特征', '趋势组合', '日内股票标记'] and isinstance(value, str):
                        filtered_df[col_name] = filtered_df[col_name].astype(str)
                        filtered_df = filtered_df[filtered_df[col_name] == value]
                    else:
                        # 确保数据类型匹配
                        if isinstance(value, (int, float)):
                            try:
                                filtered_df[col_name] = pd.to_numeric(filtered_df[col_name], errors='coerce')
                            except:
                                pass
                        filtered_df = filtered_df[filtered_df[col_name] == value]

            selected_stocks = filtered_df

        # 保存原始筛选条件，用于后续验证
        selected_stocks.attrs['original_conditions'] = original_conditions

        if len(selected_stocks) == 0:
            print(f"策略 {strategy_id} 没有选出股票")

            # 立即返回空结果，跳过所有后续计算
            return {
                '平均收益率(%)': 0,
                '平均胜率(%)': 0,
                '平均每日交易笔数': 0,
                '总交易笔数': 0,
                '交易天数': 0,
                '总天数': len(all_daily_data) if use_daily_files and all_daily_data else (len(stock_df['日期'].unique()) if not stock_df.empty and '日期' in stock_df.columns else 0),
                '交易频率(%)': 0,
                '初始资金': 10000,
                '最终资金': 10000,
                '盈利': 0,
                '累计收益率(%)': 0,
                '年化收益率(%)': 0,
                '每日收益': pd.DataFrame({
                    '日期': [pd.Timestamp('2025-01-01')],
                    '日平均涨幅(%)': [0],
                    '当日胜率(%)': [0],
                    '日收益率': [0],
                    '日收益金额': [0],
                    '累计资金': [10000],
                    '交易股票数量': [0],
                    '策略编号': [strategy_id]
                }),
                '选出的股票': pd.DataFrame({
                    '股票代码': ['000000'],
                    '股票名称': ['空数据'],
                    '选股日期': [pd.Timestamp('2025-01-01')],
                    '技术强度': [0],
                    '涨跌幅': [0],
                    '买入日期': [pd.Timestamp('2025-01-02')],
                    '买入日涨跌幅': [0.00],
                    '卖出日期': [pd.Timestamp('2025-01-03')],
                    '卖出日股票涨跌幅': [0.00],
                    '买入日开盘涨跌幅': [0.00],
                    '卖出日开盘涨跌幅': [0.00],
                    '买入后连续2个交易日累计涨幅': [0.00],
                    '买入后连续3个交易日累计涨幅': [0.00],
                    '是否盈利': [False],
                    '策略编号': [strategy_id]
                })
            }



        # 获取买入日和卖出日涨跌幅，以及买入日开盘涨跌幅和卖出日开盘涨跌幅
        # 添加选股日期
        selected_stocks['选股日期'] = selected_stocks['日期']

        # 如果提供了日期到下一个交易日的映射，优先使用
        if next_trading_day_map is not None:
            # 使用映射快速获取买入日期
            selected_stocks['买入日期'] = selected_stocks['选股日期'].map(next_trading_day_map)

            # 检查是否有买入日期为空的情况
            missing_buy_dates = selected_stocks[pd.isna(selected_stocks['买入日期'])]
            if not missing_buy_dates.empty:
                for idx in missing_buy_dates.index:
                    selection_date = selected_stocks.loc[idx, '选股日期']
                    if isinstance(selection_date, str):
                        selection_date = datetime.datetime.strptime(selection_date, '%Y-%m-%d')

                    # 计算下一个工作日（跳过周末）
                    next_day = selection_date + datetime.timedelta(days=1)
                    while next_day.weekday() >= 5:  # 5是周六，6是周日
                        next_day += datetime.timedelta(days=1)

                    selected_stocks.loc[idx, '买入日期'] = next_day.strftime('%Y-%m-%d')

            # 使用相同的映射获取卖出日期（买入日期的下一个交易日）
            selected_stocks['卖出日期'] = selected_stocks['买入日期'].map(next_trading_day_map)

            # 检查是否有卖出日期为空的情况
            missing_sell_dates = selected_stocks[pd.isna(selected_stocks['卖出日期'])]
            if not missing_sell_dates.empty:
                print(f"警告: 有 {len(missing_sell_dates)} 条记录的卖出日期为空，使用交易日历查找下一个交易日")
                for idx in missing_sell_dates.index:
                    buy_date = selected_stocks.loc[idx, '买入日期']
                    if isinstance(buy_date, str):
                        # 使用交易日历查找下一个交易日
                        next_trading_day = get_next_trading_day(buy_date)
                        selected_stocks.loc[idx, '卖出日期'] = next_trading_day
                    else:
                        print(f"警告: 买入日期格式错误: {buy_date}")
                        # 如果买入日期格式错误，跳过这条记录
                        continue
        else:
            # 如果有历史数据，使用历史数据中的交易日
            if not history_df.empty:
                print("使用历史数据中的交易日...")
                # 获取历史数据中的所有交易日
                trading_days = history_df['date'].drop_duplicates().sort_values().tolist()

                # 创建一个函数，用于查找下一个交易日
                def find_next_trading_day(date):
                    if isinstance(date, str):
                        date_obj = datetime.datetime.strptime(date, '%Y-%m-%d')
                    else:
                        date_obj = date

                    # 查找大于当前日期的最近交易日
                    for trading_day in trading_days:
                        if trading_day > date_obj:
                            return trading_day.strftime('%Y-%m-%d')

                    # 如果找不到（可能是最后一个交易日），使用启发式方法
                    next_day = get_next_trading_day(date)

                    # 如果启发式方法也返回空值，使用日期+1天（跳过周末）
                    if not next_day or pd.isna(next_day):
                        next_day_obj = date_obj + datetime.timedelta(days=1)
                        # 跳过周末
                        while next_day_obj.weekday() >= 5:  # 5是周六，6是周日
                            next_day_obj += datetime.timedelta(days=1)
                        return next_day_obj.strftime('%Y-%m-%d')

                    return next_day

                print("计算选股日期的下一个交易日（买入日期）...")
                # 获取所有唯一的选股日期
                unique_selection_dates = selected_stocks['选股日期'].unique()

                # 创建选股日期到买入日期的映射
                selection_to_buy_map = {}
                for selection_date in unique_selection_dates:
                    selection_to_buy_map[selection_date] = find_next_trading_day(selection_date)

                # 批量更新买入日期
                selected_stocks['买入日期'] = selected_stocks['选股日期'].map(selection_to_buy_map)

                print("计算买入日期的下一个交易日（卖出日期）...")
                # 获取所有唯一的买入日期
                unique_buy_dates = selected_stocks['买入日期'].unique()

                # 创建买入日期到卖出日期的映射
                buy_to_sell_map = {}
                for buy_date in unique_buy_dates:
                    buy_to_sell_map[buy_date] = find_next_trading_day(buy_date)

                # 批量更新卖出日期
                selected_stocks['卖出日期'] = selected_stocks['买入日期'].map(buy_to_sell_map)

                print(f"已计算 {len(selected_stocks)} 条记录的买入日期和卖出日期")
            else:
                # 创建日期到下一个交易日的映射，避免重复计算
                unique_dates = pd.Series(list(selected_stocks['选股日期'].unique()) +
                                        list(selected_stocks['买入日期'].unique() if '买入日期' in selected_stocks.columns else []))
                unique_dates = unique_dates.drop_duplicates()

                print(f"计算 {len(unique_dates)} 个唯一日期的下一个交易日...")
                next_trading_day_map = {}
                for date in unique_dates:
                    next_trading_day_map[date] = get_next_trading_day(date)

                # 使用映射快速获取买入日期
                selected_stocks['买入日期'] = selected_stocks['选股日期'].map(next_trading_day_map)

                # 检查是否有买入日期为空的情况
                missing_buy_dates = selected_stocks[pd.isna(selected_stocks['买入日期'])]
                if not missing_buy_dates.empty:
                    print(f"警告: 有 {len(missing_buy_dates)} 条记录的买入日期为空，使用选股日期+1天作为买入日期")
                    for idx in missing_buy_dates.index:
                        selection_date = selected_stocks.loc[idx, '选股日期']
                        if isinstance(selection_date, str):
                            selection_date = datetime.datetime.strptime(selection_date, '%Y-%m-%d')

                        # 计算下一个工作日（跳过周末）
                        next_day = selection_date + datetime.timedelta(days=1)
                        while next_day.weekday() >= 5:  # 5是周六，6是周日
                            next_day += datetime.timedelta(days=1)

                        selected_stocks.loc[idx, '买入日期'] = next_day.strftime('%Y-%m-%d')

                # 使用相同的映射获取卖出日期（买入日期的下一个交易日）
                selected_stocks['卖出日期'] = selected_stocks['买入日期'].map(next_trading_day_map)

                # 检查是否有卖出日期为空的情况
                missing_sell_dates = selected_stocks[pd.isna(selected_stocks['卖出日期'])]
                if not missing_sell_dates.empty:
                    print(f"警告: 有 {len(missing_sell_dates)} 条记录的卖出日期为空，使用交易日历查找下一个交易日")
                    for idx in missing_sell_dates.index:
                        buy_date = selected_stocks.loc[idx, '买入日期']
                        if isinstance(buy_date, str):
                            # 使用交易日历查找下一个交易日
                            next_trading_day = get_next_trading_day(buy_date)
                            selected_stocks.loc[idx, '卖出日期'] = next_trading_day
                        else:
                            print(f"警告: 买入日期格式错误: {buy_date}")
                            # 如果买入日期格式错误，跳过这条记录
                            continue

        # 获取唯一的买入和卖出日期
        all_buy_dates = selected_stocks['买入日期'].unique()
        all_sell_dates = selected_stocks['卖出日期'].unique()

        # 如果提供了日期到股票数据的映射，优先使用
        if date_to_data is not None:
            # 检查是否包含所有需要的日期
            missing_buy_dates = [date for date in all_buy_dates if date not in date_to_data]
            missing_sell_dates = [date for date in all_sell_dates if date not in date_to_data]

            if missing_buy_dates or missing_sell_dates:
                # 对于缺失的日期，使用空的DataFrame，避免重复加载
                for date in missing_buy_dates:
                    date_to_data[date] = pd.DataFrame()

                for date in missing_sell_dates:
                    if date not in date_to_data:
                        date_to_data[date] = pd.DataFrame()
        else:
            # 如果没有预加载数据，使用空的映射，避免重复加载
            date_to_data = {}
            for date in set(list(all_buy_dates) + list(all_sell_dates)):
                date_to_data[date] = pd.DataFrame()

        # 创建历史数据格式的股票代码和关联键
        selected_stocks['历史股票代码'] = selected_stocks['股票代码']
        selected_stocks['买入键'] = selected_stocks['历史股票代码'] + '_' + selected_stocks['买入日期'].astype(str)
        selected_stocks['卖出键'] = selected_stocks['历史股票代码'] + '_' + selected_stocks['卖出日期'].astype(str)

        # 为每个日期的数据创建查找键
        for date, date_data in date_to_data.items():
            if not date_data.empty:
                date_data['查找键'] = date_data['股票代码'] + '_' + date_data['日期'].astype(str)

        # 将日期转换为日期类型
        if not history_df.empty:
            # 创建买入日期和卖出日期的副本，确保它们是日期类型
            # 首先创建空列，确保列存在
            selected_stocks['买入日期对象'] = pd.Series(dtype='datetime64[ns]')
            selected_stocks['卖出日期对象'] = pd.Series(dtype='datetime64[ns]')

            if not selected_stocks.empty:
                try:
                    # 检查买入日期列是否存在
                    if '买入日期' in selected_stocks.columns:
                        # 安全地转换买入日期
                        try:
                            selected_stocks['买入日期对象'] = pd.to_datetime(selected_stocks['买入日期'], errors='coerce')
                            # 填充可能的NaT值
                            selected_stocks['买入日期对象'] = selected_stocks['买入日期对象'].fillna(pd.Timestamp('2025-01-01'))
                            # 成功转换买入日期列
                        except Exception as e:
                            print(f"转换买入日期时出错: {e}")
                            # 使用默认日期
                            selected_stocks['买入日期对象'] = pd.Timestamp('2025-01-01')
                    else:
                        print(f"警告: 买入日期列不存在，使用默认日期")
                        selected_stocks['买入日期对象'] = pd.Timestamp('2025-01-01')

                    # 检查卖出日期列是否存在
                    if '卖出日期' in selected_stocks.columns:
                        # 安全地转换卖出日期
                        try:
                            selected_stocks['卖出日期对象'] = pd.to_datetime(selected_stocks['卖出日期'], errors='coerce')
                            # 填充可能的NaT值
                            selected_stocks['卖出日期对象'] = selected_stocks['卖出日期对象'].fillna(pd.Timestamp('2025-01-02'))
                            print(f"成功转换卖出日期列为日期时间类型")
                        except Exception as e:
                            print(f"转换卖出日期时出错: {e}")
                            # 使用默认日期
                            selected_stocks['卖出日期对象'] = pd.Timestamp('2025-01-02')
                    else:
                        print(f"警告: 卖出日期列不存在，使用默认日期")
                        selected_stocks['卖出日期对象'] = pd.Timestamp('2025-01-02')
                except Exception as e:
                    print(f"处理日期时出错: {e}")
                    # 使用默认日期
                    selected_stocks['买入日期对象'] = pd.Timestamp('2025-01-01')
                    selected_stocks['卖出日期对象'] = pd.Timestamp('2025-01-02')

            # 创建买入日和卖出日的关联键
            if not selected_stocks.empty:
                try:
                    # 创建买入关联键
                    if pd.api.types.is_datetime64_any_dtype(selected_stocks['买入日期对象']):
                        selected_stocks['买入关联键'] = selected_stocks['历史股票代码'] + '_' + selected_stocks['买入日期对象'].dt.strftime('%Y-%m-%d')
                        print("成功使用日期时间对象创建买入关联键")
                    else:
                        # 使用替代方法
                        date_strings = []
                        for date_obj in selected_stocks['买入日期对象']:
                            try:
                                if isinstance(date_obj, pd.Timestamp):
                                    date_strings.append(date_obj.strftime('%Y-%m-%d'))
                                elif isinstance(date_obj, str):
                                    date_strings.append(date_obj)
                                else:
                                    date_strings.append('2025-01-01')
                            except:
                                date_strings.append('2025-01-01')
                        selected_stocks['买入关联键'] = selected_stocks['历史股票代码'] + '_' + pd.Series(date_strings)

                    # 创建卖出关联键
                    if pd.api.types.is_datetime64_any_dtype(selected_stocks['卖出日期对象']):
                        selected_stocks['卖出关联键'] = selected_stocks['历史股票代码'] + '_' + selected_stocks['卖出日期对象'].dt.strftime('%Y-%m-%d')
                        print("成功使用日期时间对象创建卖出关联键")
                    else:
                        # 使用替代方法
                        date_strings = []
                        for date_obj in selected_stocks['卖出日期对象']:
                            try:
                                if isinstance(date_obj, pd.Timestamp):
                                    date_strings.append(date_obj.strftime('%Y-%m-%d'))
                                elif isinstance(date_obj, str):
                                    date_strings.append(date_obj)
                                else:
                                    date_strings.append('2025-01-02')
                            except:
                                date_strings.append('2025-01-02')
                        selected_stocks['卖出关联键'] = selected_stocks['历史股票代码'] + '_' + pd.Series(date_strings)
                except Exception as e:
                    print(f"创建关联键时出错: {e}")
                    selected_stocks['买入关联键'] = selected_stocks['历史股票代码'] + '_2025-01-01'
                    selected_stocks['卖出关联键'] = selected_stocks['历史股票代码'] + '_2025-01-02'
            else:
                selected_stocks['买入关联键'] = pd.Series(dtype='object')
                selected_stocks['卖出关联键'] = pd.Series(dtype='object')

            print("创建历史数据的关联键...")
            # 创建历史数据的关联键
            try:
                # 历史股票明细表的列名是固定的：证券代码、日期、涨跌幅
                if '证券代码' not in history_df.columns:
                    print(f"错误: 历史数据中缺少'证券代码'列")
                    print(f"实际列名: {history_df.columns.tolist()}")
                    raise ValueError("历史数据格式不正确，缺少'证券代码'列")

                if '日期' not in history_df.columns:
                    print(f"错误: 历史数据中缺少'日期'列")
                    print(f"实际列名: {history_df.columns.tolist()}")
                    raise ValueError("历史数据格式不正确，缺少'日期'列")

                if '涨跌幅' not in history_df.columns:
                    print(f"错误: 历史数据中缺少'涨跌幅'列")
                    print(f"实际列名: {history_df.columns.tolist()}")
                    raise ValueError("历史数据格式不正确，缺少'涨跌幅'列")

                # 使用正确的列名
                code_column = '证券代码'
                date_column = '日期'
                pctchg_column = '涨跌幅'

                print(f"使用历史股票明细表的标准列名:")
                print(f"  股票代码列: {code_column}")
                print(f"  日期列: {date_column}")
                print(f"  涨跌幅列: {pctchg_column}")

                # 重命名列以保持兼容性
                history_df['code'] = history_df[code_column]
                history_df['date'] = pd.to_datetime(history_df[date_column])
                history_df['pctChg'] = history_df[pctchg_column]

                # 映射开盘价和收盘价列名
                if '开盘价' in history_df.columns:
                    history_df['open'] = history_df['开盘价']

                if '收盘价' in history_df.columns:
                    history_df['close'] = history_df['收盘价']

                if '前收盘价' in history_df.columns:
                    history_df['prev_close_original'] = history_df['前收盘价']

                print("✅ 历史数据列名映射完成")

                # 确保code列是字符串类型
                history_df['code'] = history_df['code'].astype(str)

                # 检查历史数据的日期列是否为日期时间类型
                if pd.api.types.is_datetime64_any_dtype(history_df['date']):
                    history_df['关联键'] = history_df['code'] + '_' + history_df['date'].dt.strftime('%Y-%m-%d')
                    print("成功使用日期时间对象创建历史数据关联键")
                else:
                    # 如果不是日期时间类型，尝试转换
                    print("历史数据日期列不是日期时间类型，尝试转换...")
                    try:
                        # 尝试转换为日期时间类型
                        history_df['date'] = pd.to_datetime(history_df['date'], errors='coerce')
                        history_df['关联键'] = history_df['code'] + '_' + history_df['date'].dt.strftime('%Y-%m-%d')
                        print("成功转换历史数据日期列并创建关联键")
                    except Exception as e:
                        print(f"转换历史数据日期列时出错: {e}")
                        # 使用字符串方法创建关联键
                        history_df['关联键'] = history_df['code'] + '_' + history_df['date'].astype(str)
                        print("使用字符串方法创建历史数据关联键")
            except Exception as e:
                print(f"创建历史数据关联键时出错: {e}")
                # 使用备用方法
                try:
                    # 查找股票代码列
                    code_column = None
                    possible_code_columns = ['证券代码', 'code', '股票代码', 'Code', 'CODE', 'symbol', 'Symbol', 'ts_code', 'stock_code', 'stockcode', 'StockCode', 'STOCK_CODE']

                    for col in possible_code_columns:
                        if col in history_df.columns:
                            code_column = col
                            break

                    if code_column:
                        # 确保code列是字符串类型
                        history_df['code'] = history_df[code_column].astype(str)
                        history_df['date_str'] = history_df['date'].astype(str)
                        history_df['关联键'] = history_df['code'] + '_' + history_df['date_str']
                        print("使用备用方法创建历史数据关联键")
                    else:
                        # 创建一个空的关联键列
                        history_df['code'] = ''  # 创建空的code列
                        history_df['关联键'] = ''
                        print("创建空的历史数据关联键列")
                except Exception as e2:
                    print(f"使用备用方法创建历史数据关联键时也出错: {e2}")
                    # 创建一个空的关联键列
                    history_df['code'] = ''  # 创建空的code列
                    history_df['关联键'] = ''
                    print("创建空的历史数据关联键列")

            print("创建关联键到涨跌幅和开盘价涨跌幅的映射...")

            # 检查历史数据中是否包含open列（开盘价）
            if 'open' in history_df.columns and 'close' in history_df.columns:
                print("📊 计算开盘涨跌幅...")

                # 创建一个临时DataFrame，按股票代码和日期排序（不使用copy以提高性能）
                temp_df = history_df.sort_values(['code', 'date'])

                # 优先使用原始数据中的前收盘价，如果没有则使用shift方法
                if 'prev_close_original' in temp_df.columns:
                    temp_df['prev_close'] = temp_df['prev_close_original']
                else:
                    temp_df['prev_close'] = temp_df.groupby('code')['close'].shift(1)

                # 计算开盘涨跌幅 = (今日开盘价 - 前收盘价) / 前收盘价 * 100%
                temp_df['open_pct_change'] = (temp_df['open'] - temp_df['prev_close']) / temp_df['prev_close'] * 100

                # 过滤掉无效的计算结果
                temp_df['open_pct_change'] = temp_df['open_pct_change'].replace([float('inf'), -float('inf')], 0)
                temp_df['open_pct_change'] = temp_df['open_pct_change'].fillna(0)

                # 查找涨跌幅列（可能的列名：pctChg, 涨跌幅, pct_change等）
                pctchg_column = None
                possible_pctchg_columns = ['pctChg', '涨跌幅', 'pct_change', 'change_pct', 'return', '收益率']

                for col in possible_pctchg_columns:
                    if col in history_df.columns:
                        pctchg_column = col
                        print(f"找到涨跌幅列: {pctchg_column}")
                        break

                if pctchg_column is None:
                    print("警告: 在历史数据中没有找到涨跌幅列，将使用0作为默认值")
                    history_df['pctChg'] = 0
                else:
                    # 如果涨跌幅列不是'pctChg'，重命名为'pctChg'以保持兼容性
                    if pctchg_column != 'pctChg':
                        history_df['pctChg'] = history_df[pctchg_column]
                        print(f"已将涨跌幅列 '{pctchg_column}' 重命名为 'pctChg'")

                # 创建关联键到涨跌幅的映射
                history_key_to_pctChg = dict(zip(history_df['关联键'], history_df['pctChg']))

                # 创建关联键到开盘涨跌幅的映射
                history_key_to_open_pctChg = dict(zip(temp_df['关联键'], temp_df['open_pct_change']))

                # 计算买入后连续2天和3天的累计涨幅
                print("📈 计算连续累计涨幅...")

                # 创建股票代码和买入日期的组合键，用于快速查找
                buy_date_keys = {}
                for idx, row in selected_stocks.iterrows():
                    code = row['历史股票代码']
                    if '买入日期对象' in row and pd.notna(row['买入日期对象']):
                        buy_date = row['买入日期对象']
                        # 直接存储代码和日期，不需要创建额外的key
                        buy_date_keys[idx] = (code, buy_date)

                # 按股票代码分组，提高处理效率
                stocks_by_code = {}
                for idx, (code, buy_date) in buy_date_keys.items():
                    if code not in stocks_by_code:
                        stocks_by_code[code] = []
                    stocks_by_code[code].append((idx, buy_date))

                # 初始化结果字典
                cumulative_return_2days = {}  # 买入后连续2天累计涨幅（不含买入日）
                cumulative_return_3days = {}  # 买入后连续3天累计涨幅（不含买入日）
                cumulative_return_2days_with = {}  # 买入日起2日累计涨幅（含买入日）
                cumulative_return_3days_with = {}  # 买入日起3日累计涨幅（含买入日）

                # 批量处理每个股票的数据
                for code, idx_dates in stocks_by_code.items():
                    try:
                        # 获取该股票的所有数据
                        stock_data = history_df[history_df['code'] == code].copy()

                        if stock_data.empty:
                            # 如果找不到历史数据，直接设置为0并继续
                            for idx, _ in idx_dates:
                                cumulative_return_2days[idx] = 0
                                cumulative_return_3days[idx] = 0
                            continue

                        # 确保日期列是日期类型
                        if not pd.api.types.is_datetime64_any_dtype(stock_data['date']):
                            stock_data['date'] = pd.to_datetime(stock_data['date'])

                        # 按日期排序
                        stock_data = stock_data.sort_values('date')

                        # 创建日期到行的映射，加速查找
                        date_to_row = {row['date']: row for _, row in stock_data.iterrows()}

                        # 获取所有交易日并排序
                        all_trading_dates = sorted(stock_data['date'].unique())
                        date_to_idx = {date: i for i, date in enumerate(all_trading_dates)}

                        # 处理每个买入日期
                        for idx, buy_date in idx_dates:
                            # 将买入日期转换为日期对象
                            if isinstance(buy_date, str):
                                buy_date = datetime.datetime.strptime(buy_date, '%Y-%m-%d')

                            # 找到买入日期在交易日列表中的位置
                            if buy_date not in date_to_idx:
                                # 如果找不到交易记录，直接设置为0并继续
                                cumulative_return_2days[idx] = 0
                                cumulative_return_3days[idx] = 0
                                continue

                            # 获取买入日期及其后的所有可用交易日
                            buy_date_pctChg = date_to_row[buy_date]['pctChg'] if buy_date in date_to_row else 0
                            available_dates = all_trading_dates[date_to_idx[buy_date]+1:]

                            # 计算买入后连续2天的累计涨幅（不包含买入日）
                            next_2_dates = available_dates[:2] if len(available_dates) > 0 else []
                            ret_2days_after = sum(date_to_row[date]['pctChg'] for date in next_2_dates if date in date_to_row)
                            cumulative_return_2days[idx] = ret_2days_after

                            # 计算买入后连续3天的累计涨幅（不包含买入日）
                            next_3_dates = available_dates[:3] if len(available_dates) > 0 else []
                            ret_3days_after = sum(date_to_row[date]['pctChg'] for date in next_3_dates if date in date_to_row)
                            cumulative_return_3days[idx] = ret_3days_after

                            # 计算买入日起2日累计涨幅（包含买入日）- 使用复利计算方法
                            next_1_dates = available_dates[:1] if len(available_dates) > 0 else []
                            # 计算买入后1天的涨跌幅
                            next_1_day_pctChg = sum(date_to_row[date]['pctChg'] for date in next_1_dates if date in date_to_row)
                            # 使用复利计算买入日起2日累计涨幅
                            ret_2days_with = (1 + buy_date_pctChg/100) * (1 + next_1_day_pctChg/100) - 1
                            ret_2days_with = ret_2days_with * 100  # 转换为百分比
                            cumulative_return_2days_with[idx] = ret_2days_with

                            # 计算买入日起3日累计涨幅（包含买入日）- 使用复利计算方法
                            next_2_dates = available_dates[:2] if len(available_dates) > 0 else []
                            # 计算买入后2天的涨跌幅
                            next_2_days_pctChg = sum(date_to_row[date]['pctChg'] for date in next_2_dates if date in date_to_row)
                            # 使用复利计算买入日起3日累计涨幅
                            ret_3days_with = (1 + buy_date_pctChg/100) * (1 + next_2_days_pctChg/100) - 1
                            ret_3days_with = ret_3days_with * 100  # 转换为百分比
                            cumulative_return_3days_with[idx] = ret_3days_with

                    except Exception as e:
                        print(f"计算股票 {code} 的累计涨幅时出错: {e}")
                        import traceback
                        traceback.print_exc()
                        for idx, _ in idx_dates:
                            cumulative_return_2days[idx] = 0
                            cumulative_return_3days[idx] = 0

                # 将计算结果添加到selected_stocks中
                selected_stocks['买入后连续2个交易日累计涨幅'] = pd.Series(cumulative_return_2days)
                selected_stocks['买入后连续3个交易日累计涨幅'] = pd.Series(cumulative_return_3days)
                selected_stocks['买入日起2日累计涨幅(含买入日)'] = pd.Series(cumulative_return_2days_with)
                selected_stocks['买入日起3日累计涨幅(含买入日)'] = pd.Series(cumulative_return_3days_with)

                # 填充缺失值
                selected_stocks['买入后连续2个交易日累计涨幅'] = selected_stocks['买入后连续2个交易日累计涨幅'].fillna(0)
                selected_stocks['买入后连续3个交易日累计涨幅'] = selected_stocks['买入后连续3个交易日累计涨幅'].fillna(0)
                selected_stocks['买入日起2日累计涨幅(含买入日)'] = selected_stocks['买入日起2日累计涨幅(含买入日)'].fillna(0)
                selected_stocks['买入日起3日累计涨幅(含买入日)'] = selected_stocks['买入日起3日累计涨幅(含买入日)'].fillna(0)

                # 四舍五入到两位小数
                selected_stocks['买入后连续2个交易日累计涨幅'] = selected_stocks['买入后连续2个交易日累计涨幅'].round(2)
                selected_stocks['买入后连续3个交易日累计涨幅'] = selected_stocks['买入后连续3个交易日累计涨幅'].round(2)
                selected_stocks['买入日起2日累计涨幅(含买入日)'] = selected_stocks['买入日起2日累计涨幅(含买入日)'].round(2)
                selected_stocks['买入日起3日累计涨幅(含买入日)'] = selected_stocks['买入日起3日累计涨幅(含买入日)'].round(2)

                print(f"成功计算买入后连续2天和3天的累计涨幅")

                print("💰 获取交易日涨跌幅数据...")
                # 批量获取买入日涨跌幅
                selected_stocks['买入日涨跌幅'] = selected_stocks['买入关联键'].map(history_key_to_pctChg)
                # 批量获取卖出日涨跌幅
                selected_stocks['卖出日股票涨跌幅'] = selected_stocks['卖出关联键'].map(history_key_to_pctChg)
                # 批量获取买入日开盘涨跌幅
                selected_stocks['买入日开盘涨跌幅'] = selected_stocks['买入关联键'].map(history_key_to_open_pctChg)
                # 批量获取卖出日开盘涨跌幅
                selected_stocks['卖出日开盘涨跌幅'] = selected_stocks['卖出关联键'].map(history_key_to_open_pctChg)

                # 填充缺失值
                selected_stocks['买入日开盘涨跌幅'] = selected_stocks['买入日开盘涨跌幅'].fillna(0)
                selected_stocks['卖出日开盘涨跌幅'] = selected_stocks['卖出日开盘涨跌幅'].fillna(0)

                # 四舍五入到两位小数
                selected_stocks['买入日开盘涨跌幅'] = selected_stocks['买入日开盘涨跌幅'].round(2)
                selected_stocks['卖出日开盘涨跌幅'] = selected_stocks['卖出日开盘涨跌幅'].round(2)

                print("✅ 开盘涨跌幅计算完成")
            else:
                print("警告: 历史数据中不包含开盘价信息，将使用替代方法计算")

                # 查找涨跌幅列（可能的列名：pctChg, 涨跌幅, pct_change等）
                pctchg_column = None
                possible_pctchg_columns = ['pctChg', '涨跌幅', 'pct_change', 'change_pct', 'return', '收益率']

                for col in possible_pctchg_columns:
                    if col in history_df.columns:
                        pctchg_column = col
                        print(f"找到涨跌幅列: {pctchg_column}")
                        break

                if pctchg_column is None:
                    print("警告: 在历史数据中没有找到涨跌幅列，将使用0作为默认值")
                    history_df['pctChg'] = 0
                else:
                    # 如果涨跌幅列不是'pctChg'，重命名为'pctChg'以保持兼容性
                    if pctchg_column != 'pctChg':
                        history_df['pctChg'] = history_df[pctchg_column]
                        print(f"已将涨跌幅列 '{pctchg_column}' 重命名为 'pctChg'")

                # 创建关联键到涨跌幅的映射
                history_key_to_pctChg = dict(zip(history_df['关联键'], history_df['pctChg']))

                # 批量获取买入日和卖出日涨跌幅
                selected_stocks['买入日涨跌幅'] = selected_stocks['买入关联键'].map(history_key_to_pctChg)
                selected_stocks['卖出日股票涨跌幅'] = selected_stocks['卖出关联键'].map(history_key_to_pctChg)

                # 计算买入后连续涨幅

                # 初始化结果列
                selected_stocks['买入后连续2个交易日累计涨幅'] = 0.0
                selected_stocks['买入后连续3个交易日累计涨幅'] = 0.0
                selected_stocks['买入日起2日累计涨幅(含买入日)'] = 0.0
                selected_stocks['买入日起3日累计涨幅(含买入日)'] = 0.0

                # 🚀 颠覆性优化：预计算缓存系统
                USE_CUMULATIVE_CACHE = True  # 使用预计算缓存系统
                ENABLE_CUMULATIVE_CALCULATION = True  # 启用累积涨幅计算

                if USE_CUMULATIVE_CACHE:
                    print("🚀 使用预计算缓存系统获取累积涨幅...")

                    # 尝试从缓存加载累积涨幅数据
                    cache_success = load_cumulative_cache_for_stocks(selected_stocks)

                    if cache_success:
                        print("✅ 成功从缓存加载累积涨幅数据")
                    else:
                        print("⚠️ 缓存未命中，使用传统计算方法")
                        # 如果缓存失败，回退到传统计算
                        USE_CUMULATIVE_CACHE = False

                if not USE_CUMULATIVE_CACHE:
                    if not ENABLE_CUMULATIVE_CALCULATION:
                        print("⚡ 快速模式：跳过累积涨幅计算以获得极致性能")
                        # 设置默认值
                        selected_stocks['买入后连续2个交易日累计涨幅'] = 0
                        selected_stocks['买入后连续3个交易日累计涨幅'] = 0
                        selected_stocks['买入日起2日累计涨幅(含买入日)'] = 0
                        selected_stocks['买入日起3日累计涨幅(含买入日)'] = 0
                    elif len(selected_stocks) > 50000:  # 提高阈值，允许更多记录进行计算
                        print(f"记录数量过大({len(selected_stocks)}条)，跳过买入后连续涨幅计算以提升性能")
                        # 设置默认值
                        selected_stocks['买入后连续2个交易日累计涨幅'] = 0
                        selected_stocks['买入后连续3个交易日累计涨幅'] = 0
                        selected_stocks['买入日起2日累计涨幅(含买入日)'] = 0
                        selected_stocks['买入日起3日累计涨幅(含买入日)'] = 0
                    else:
                        print(f"批量计算买入后连续涨幅（{len(selected_stocks)}条记录）...")

                        # 🚀 终极优化：使用全局交易日历，避免按股票筛选历史数据
                        print("使用全局交易日历进行超高速计算...")

                    # 直接使用全局交易日历（所有股票的交易日都是相同的）
                    if 'all_trading_dates' in globals() and all_trading_dates:
                        # 使用预加载的全局交易日历
                        global_trading_days = sorted(all_trading_dates)
                        print(f"使用预加载的全局交易日历，共 {len(global_trading_days)} 个交易日")
                    else:
                        # 如果没有全局交易日历，从历史数据中快速提取
                        print("从历史数据中提取全局交易日历...")
                        global_trading_days = sorted(pd.to_datetime(history_df['date']).unique())
                        print(f"提取到 {len(global_trading_days)} 个交易日")

                    # 🚀 真正的向量化优化：完全避免逐行遍历
                    print("使用完全向量化方法计算连续累计涨幅...")

                    # 预计算：为每个买入日期计算后续交易日（只计算一次）
                    unique_buy_dates = selected_stocks['买入日期对象'].unique()

                    # 使用全局交易日历进行计算
                    if global_trading_days:
                        # 创建买入日期到后续交易日的映射DataFrame
                        date_mapping_data = []
                        for buy_date in unique_buy_dates:
                            future_days = [d for d in global_trading_days if d > buy_date]
                            date_mapping_data.append({
                                'buy_date': buy_date,
                                'next_1': future_days[0].strftime('%Y-%m-%d') if len(future_days) >= 1 else None,
                                'next_2': future_days[1].strftime('%Y-%m-%d') if len(future_days) >= 2 else None,
                                'next_3': future_days[2].strftime('%Y-%m-%d') if len(future_days) >= 3 else None
                            })

                        date_mapping_df = pd.DataFrame(date_mapping_data)

                        # 向量化合并：将后续交易日信息合并到主DataFrame
                        selected_stocks = selected_stocks.merge(
                            date_mapping_df,
                            left_on='买入日期对象',
                            right_on='buy_date',
                            how='left'
                        )

                        # 向量化构建关联键：使用字符串拼接而不是apply
                        selected_stocks['next_1_key'] = selected_stocks['历史股票代码'] + '_' + selected_stocks['next_1'].fillna('')
                        selected_stocks['next_2_key'] = selected_stocks['历史股票代码'] + '_' + selected_stocks['next_2'].fillna('')
                        selected_stocks['next_3_key'] = selected_stocks['历史股票代码'] + '_' + selected_stocks['next_3'].fillna('')

                        # 清理空的关联键
                        selected_stocks.loc[selected_stocks['next_1'].isna(), 'next_1_key'] = None
                        selected_stocks.loc[selected_stocks['next_2'].isna(), 'next_2_key'] = None
                        selected_stocks.loc[selected_stocks['next_3'].isna(), 'next_3_key'] = None

                    # 批量获取涨跌幅数据
                    selected_stocks['next_1_pct'] = selected_stocks['next_1_key'].map(history_key_to_pctChg).fillna(0)
                    selected_stocks['next_2_pct'] = selected_stocks['next_2_key'].map(history_key_to_pctChg).fillna(0)
                    selected_stocks['next_3_pct'] = selected_stocks['next_3_key'].map(history_key_to_pctChg).fillna(0)

                    # 向量化计算累积涨幅
                    # 买入后连续2个交易日累计涨幅（不含买入日）
                    selected_stocks['买入后连续2个交易日累计涨幅'] = (selected_stocks['next_1_pct'] + selected_stocks['next_2_pct']).round(2)

                    # 买入后连续3个交易日累计涨幅（不含买入日）
                    selected_stocks['买入后连续3个交易日累计涨幅'] = (selected_stocks['next_1_pct'] + selected_stocks['next_2_pct'] + selected_stocks['next_3_pct']).round(2)

                    # 买入日起2日累计涨幅（含买入日）- 使用复利计算
                    selected_stocks['买入日起2日累计涨幅(含买入日)'] = (
                        (1 + selected_stocks['买入日涨跌幅']/100) * (1 + selected_stocks['next_1_pct']/100) - 1
                    ).fillna(0) * 100
                    selected_stocks['买入日起2日累计涨幅(含买入日)'] = selected_stocks['买入日起2日累计涨幅(含买入日)'].round(2)

                    # 买入日起3日累计涨幅（含买入日）- 使用复利计算
                    selected_stocks['买入日起3日累计涨幅(含买入日)'] = (
                        (1 + selected_stocks['买入日涨跌幅']/100) *
                        (1 + selected_stocks['next_1_pct']/100) *
                        (1 + selected_stocks['next_2_pct']/100) - 1
                    ).fillna(0) * 100
                    selected_stocks['买入日起3日累计涨幅(含买入日)'] = selected_stocks['买入日起3日累计涨幅(含买入日)'].round(2)

                    # 清理临时列
                    selected_stocks.drop(['buy_date', 'next_1', 'next_2', 'next_3', 'next_1_key', 'next_2_key', 'next_3_key', 'next_1_pct', 'next_2_pct', 'next_3_pct'], axis=1, inplace=True)

                    # 填充缺失值
                    selected_stocks['买入后连续2个交易日累计涨幅'] = selected_stocks['买入后连续2个交易日累计涨幅'].fillna(0)
                    selected_stocks['买入后连续3个交易日累计涨幅'] = selected_stocks['买入后连续3个交易日累计涨幅'].fillna(0)
                    selected_stocks['买入日起2日累计涨幅(含买入日)'] = selected_stocks['买入日起2日累计涨幅(含买入日)'].fillna(0)
                    selected_stocks['买入日起3日累计涨幅(含买入日)'] = selected_stocks['买入日起3日累计涨幅(含买入日)'].fillna(0)

                    print(f"批量计算完成：买入后连续2天和3天的累计涨幅")

                # 买入后连续涨幅计算完成

                # 使用买入日涨跌幅和卖出日涨跌幅作为开盘涨跌幅的估计值
                selected_stocks['买入日开盘涨跌幅'] = selected_stocks['买入日涨跌幅'] * 0.5  # 假设开盘涨跌幅约为全天涨跌幅的一半
                selected_stocks['卖出日开盘涨跌幅'] = selected_stocks['卖出日股票涨跌幅'] * 0.5

                # 四舍五入到两位小数
                selected_stocks['买入日开盘涨跌幅'] = selected_stocks['买入日开盘涨跌幅'].round(2)
                selected_stocks['卖出日开盘涨跌幅'] = selected_stocks['卖出日开盘涨跌幅'].round(2)

                # 使用全天涨跌幅的一半作为开盘涨跌幅的估计值

            # 统计缺失数据
            missing_buy_data_count = selected_stocks['买入日涨跌幅'].isna().sum()
            missing_sell_data_count = selected_stocks['卖出日股票涨跌幅'].isna().sum()

            # 填充缺失值为0
            selected_stocks['买入日涨跌幅'] = selected_stocks['买入日涨跌幅'].fillna(0)
            selected_stocks['卖出日股票涨跌幅'] = selected_stocks['卖出日股票涨跌幅'].fillna(0)

            # 删除临时列
            selected_stocks.drop(['历史股票代码', '买入日期对象', '卖出日期对象', '买入关联键', '卖出关联键'], axis=1, inplace=True)

            # 打印缺失数据统计
            if missing_buy_data_count > 0 or missing_sell_data_count > 0:
                total_records = len(selected_stocks)
                print(f"警告: 共有 {missing_buy_data_count} 条买入日数据和 {missing_sell_data_count} 条卖出日数据缺失")
                missing_rate_buy = missing_buy_data_count / total_records * 100
                missing_rate_sell = missing_sell_data_count / total_records * 100
                print(f"买入日数据缺失率: {missing_rate_buy:.2f}%, 卖出日数据缺失率: {missing_rate_sell:.2f}%")
        else:
            print("警告: 历史数据为空，无法获取买入日和卖出日涨跌幅")
            # 如果历史数据为空，设置涨跌幅为0
            selected_stocks['买入日涨跌幅'] = 0
            selected_stocks['卖出日股票涨跌幅'] = 0

            # 同时设置开盘涨跌幅为0
            selected_stocks['买入日开盘涨跌幅'] = 0
            selected_stocks['卖出日开盘涨跌幅'] = 0

            # 设置买入后连续2天和3天累计涨幅为0
            selected_stocks['买入后连续2个交易日累计涨幅'] = 0
            selected_stocks['买入后连续3个交易日累计涨幅'] = 0
            selected_stocks['买入日起2日累计涨幅(含买入日)'] = 0
            selected_stocks['买入日起3日累计涨幅(含买入日)'] = 0

            print("警告: 历史数据为空，买入日和卖出日的开盘涨跌幅以及买入后连续2天和3天累计涨幅也设置为0")

        # 不再需要删除临时列，因为我们没有创建这些列

        # 计算策略统计数据（基于买入日涨跌幅）
        # 使用买入日涨跌幅作为交易收益率
        selected_stocks['买入日收益率'] = selected_stocks['买入日涨跌幅'] / 100  # 转换为小数形式

        # 计算平均收益率
        avg_return = selected_stocks['买入日收益率'].mean()

        # 计算胜率（仅基于买入当日涨跌幅是否大于0）
        selected_stocks['是否盈利'] = selected_stocks['买入日涨跌幅'] > 0
        win_rate = selected_stocks['是否盈利'].mean() * 100

        # 计算交易统计
        trade_days = len(selected_stocks['日期'].unique())
        # 根据是否使用按日期文件来获取总天数
        if use_daily_files and all_daily_data is not None:
            total_days = len(all_daily_data)
        elif not stock_df.empty:
            total_days = len(stock_df['日期'].unique())
        else:
            total_days = 0
        trade_frequency = (trade_days / total_days) * 100 if total_days > 0 else 0
        total_trades = len(selected_stocks)
        avg_daily_trades = total_trades / trade_days if trade_days > 0 else 0

        # 计算基于初始资金的收益
        initial_capital = 10000  # 初始资金10000元

        # 使用买入日涨跌幅作为交易收益率
        selected_stocks['买入日收益率'] = selected_stocks['买入日涨跌幅'] / 100  # 转换为小数形式

        # 检查是否有买入日期
        if '买入日期' not in selected_stocks.columns or selected_stocks.empty:
            print("警告: 没有买入日期数据，无法计算收益")
            # 创建空的每日收益DataFrame
            daily_profit_df = pd.DataFrame(columns=['日期', '日平均涨幅(%)', '当日胜率(%)', '日收益率', '日收益金额', '累计资金', '交易股票数量'])

            # 设置默认值
            avg_return = 0
            win_rate = 0
            trade_days = 0
            total_trades = 0
            trade_frequency = 0
            cumulative_return = 0
            final_capital = initial_capital
            profit = 0
            annualized_return = 0

            # 返回结果
            return {
                '平均收益率(%)': 0,
                '平均胜率(%)': 0,
                '平均每日交易笔数': 0,
                '总交易笔数': 0,
                '交易天数': 0,
                '总天数': len(all_daily_data) if use_daily_files and all_daily_data else (len(stock_df['日期'].unique()) if not stock_df.empty else 0),
                '交易频率(%)': 0,
                '初始资金': initial_capital,
                '最终资金': initial_capital,
                '盈利': 0,
                '累计收益率(%)': 0,
                '年化收益率(%)': 0,
                '每日收益': daily_profit_df,
                '选出的股票': selected_stocks
            }

        # 按买入日期分组计算每日平均收益率
        buy_day_returns = selected_stocks.groupby('买入日期')['买入日收益率'].mean()

        # 计算每日收益金额
        daily_profit = {}
        running_capital = initial_capital
        daily_capital = {}
        daily_stock_count = {}     # 每日交易股票数量
        daily_win_rate = {}        # 每日胜率

        # 按买入日期分组统计
        buy_date_groups = selected_stocks.groupby('买入日期')

        # 计算每个买入日期的平均涨跌幅
        buy_date_avg_pct_change = selected_stocks.groupby('买入日期')['买入日涨跌幅'].mean()

        # 按买入日期排序
        sorted_buy_dates = sorted(buy_day_returns.index)

        for date in sorted_buy_dates:
            # 每日平均涨幅直接使用买入日涨跌幅的平均值
            daily_return = buy_day_returns[date]

            # 每日交易股票数量
            if date in buy_date_groups.groups:
                date_group = buy_date_groups.get_group(date)
                daily_stock_count[date] = len(date_group)

                # 计算当日胜率：当日上涨股票/当日的推荐股票（不乘以100，让Excel格式处理）
                daily_win_rate[date] = (date_group['买入日涨跌幅'] > 0).mean()
            else:
                daily_stock_count[date] = 0
                daily_win_rate[date] = 0

            # 每日收益金额和累计资金
            daily_profit_amount = running_capital * daily_return
            daily_profit[date] = daily_profit_amount
            running_capital += daily_profit_amount
            daily_capital[date] = running_capital

        # 创建每日收益DataFrame
        daily_profit_df = pd.DataFrame({
            '日期': daily_profit.keys(),
            '日平均涨幅(%)': [buy_date_avg_pct_change[date] for date in daily_profit.keys()],
            '当日胜率(%)': [daily_win_rate[date] for date in daily_profit.keys()],
            '日收益率': [buy_day_returns[date] for date in daily_profit.keys()],
            '日收益金额': daily_profit.values(),
            '累计资金': daily_capital.values(),
            '交易股票数量': [daily_stock_count[date] for date in daily_profit.keys()]
        })

        # 计算累计收益
        cumulative_return = (1 + buy_day_returns).prod() - 1
        final_capital = initial_capital * (1 + cumulative_return)
        profit = final_capital - initial_capital

        # 计算年化收益率（假设一年有252个交易日）
        if trade_days > 0:
            annualized_return = (1 + cumulative_return) ** (252 / trade_days) - 1
        else:
            annualized_return = 0

        print(f"🎯 策略 {strategy_id} 回测完成:")
        print(f"  📊 收益率: {round(avg_return * 100, 2)}% | 胜率: {round(win_rate, 2)}%")
        print(f"  📈 交易: {total_trades}笔/{trade_days}天 | 累计收益: {round(cumulative_return * 100, 2)}%")

        # 验证所有股票是否满足原始条件
        if 'original_conditions' in selected_stocks.attrs:
            original_conditions = selected_stocks.attrs['original_conditions']
            print("验证所有股票是否满足原始条件...")

            # 创建一个副本，避免修改原始数据
            verified_stocks = selected_stocks.copy()

            # 应用每个原始条件进行验证
            for col_name, operator, value in original_conditions:
                if operator == ">=":
                    mask = verified_stocks[col_name] >= value
                elif operator == "<=":
                    mask = verified_stocks[col_name] <= value
                elif operator == "==":
                    mask = verified_stocks[col_name] == value

                # 检查是否有不满足条件的记录
                invalid_count = (~mask).sum()
                if invalid_count > 0:
                    print(f"警告: 发现 {invalid_count} 条记录不满足条件 {col_name} {operator} {value}")
                    # 移除不满足条件的记录
                    verified_stocks = verified_stocks[mask]
                    print(f"已移除不满足条件的记录，剩余 {len(verified_stocks)} 条记录")

            # 如果有记录被移除，使用验证后的数据
            if len(verified_stocks) < len(selected_stocks):
                print(f"警告: 总共移除了 {len(selected_stocks) - len(verified_stocks)} 条不满足原始条件的记录")
                selected_stocks = verified_stocks

        # 返回结果
        return {
            '平均收益率(%)': round(avg_return * 100, 2),
            '平均胜率(%)': round(win_rate, 2),
            '平均每日交易笔数': round(avg_daily_trades, 2),
            '总交易笔数': total_trades,
            '交易天数': trade_days,
            '总天数': total_days,
            '交易频率(%)': round(trade_frequency, 2),
            '初始资金': initial_capital,
            '最终资金': round(final_capital, 2),
            '盈利': round(profit, 2),
            '累计收益率(%)': round(cumulative_return * 100, 2),
            '年化收益率(%)': round(annualized_return * 100, 2),
            '每日收益': daily_profit_df,  # 添加每日收益DataFrame
            '选出的股票': selected_stocks
        }
    except Exception as e:
        print(f"策略 {strategy_id} 回测过程中发生错误: {e}")
        import traceback
        print("详细错误信息:")
        print(traceback.format_exc())
        return None

# 主函数
# 全局变量，用于存储预加载的数据
_preloaded_data = {
    'next_trading_day_map': None,
    'date_to_data': None,
    'is_preloaded': False
}

def preload_data():
    """预加载数据，只在程序启动时执行一次"""
    global next_trading_day_map, date_to_data, _preloaded_data
    global all_daily_data, available_dates, total_records, stock_df, use_daily_files
    global history_df, calendar_df, all_trading_dates

    if _preloaded_data['is_preloaded']:
        print("数据已预加载，直接使用缓存数据")
        return

    # 检查是否有其他进程已经预加载了数据（通过标记文件）
    import tempfile

    preload_flag_file = os.path.join(tempfile.gettempdir(), 'backtest_preloaded.flag')
    preload_lock_file = os.path.join(tempfile.gettempdir(), 'backtest_preloading.lock')

    # 检查是否已经预加载完成
    if os.path.exists(preload_flag_file):
        print("检测到预加载完成标记，但仍需要在当前进程中加载数据")
        print("因为进程间无法共享内存数据，所以需要重新加载")
        # 不能直接return，需要继续执行数据加载
        # 但可以跳过一些初始化步骤

    # 检查是否有其他进程正在预加载（只有在没有完成标记时才检查）
    if not os.path.exists(preload_flag_file) and os.path.exists(preload_lock_file):
        print("其他进程正在预加载数据，等待完成...")
        max_wait_time = 300  # 最多等待5分钟
        wait_time = 0
        while wait_time < max_wait_time and os.path.exists(preload_lock_file):
            time.sleep(1)
            wait_time += 1

        # 再次检查是否完成
        if os.path.exists(preload_flag_file):
            print("其他进程预加载完成，继续加载数据到当前进程")
        else:
            print("等待超时或其他进程失败，开始自己的预加载")

    # 如果没有完成标记，创建预加载锁文件
    if not os.path.exists(preload_flag_file):
        try:
            with open(preload_lock_file, 'w') as f:
                f.write(f"预加载开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("创建预加载锁文件，开始预加载数据...")
        except Exception as e:
            print(f"创建锁文件失败: {e}，继续预加载")

    print("开始一次性预加载所有数据...")
    start_time = time.time()

    # 1. 加载技术强度数据
    print("1/3 加载技术强度数据...")
    all_daily_data, available_dates, total_records = load_all_daily_data()

    if all_daily_data is not None:
        print("使用按日期存储的技术强度数据文件")
        stock_df = pd.DataFrame()  # 空的DataFrame，实际数据从all_daily_data获取
        use_daily_files = True
    else:
        print("使用原始的股票明细_完整.xlsx文件作为备用方案")
        stock_df = load_stock_details_from_single_file()
        use_daily_files = False
        all_daily_data = None

        # 确保日期列是日期类型，并按股票代码和日期排序
        if not stock_df.empty:
            stock_df['日期'] = pd.to_datetime(stock_df['日期'])
            stock_df = stock_df.sort_values(['股票代码', '日期'])

    # 2. 加载历史数据
    print("2/3 加载历史数据...")
    load_historical_data()

    # 3. 创建数据映射
    print("3/3 创建数据映射...")

    # 创建日期到下一个交易日的映射
    # 根据是否使用按日期文件来获取所有日期
    if use_daily_files and available_dates is not None:
        all_dates = available_dates
        print(f"使用按日期文件模式，共 {len(all_dates)} 个日期")
    elif not stock_df.empty:
        all_dates = stock_df['日期'].unique()
        print(f"使用传统模式，共 {len(all_dates)} 个日期")
    else:
        print("警告: 没有可用的日期数据")
        all_dates = []

    if not next_trading_day_map:
        print("创建日期到下一个交易日的映射...")

        # 如果有历史数据，优先使用历史数据中的交易日
        if not history_df.empty:
            print("使用历史数据创建交易日映射...")

            # 创建一个函数，用于查找下一个交易日
            def find_next_trading_day(date):
                if isinstance(date, str):
                    date_obj = datetime.datetime.strptime(date, '%Y-%m-%d')
                else:
                    date_obj = date

                # 查找大于当前日期的最近交易日
                for trading_day in all_trading_dates:
                    if trading_day > date_obj:
                        return trading_day.strftime('%Y-%m-%d')

                # 如果找不到，使用启发式方法
                next_day = date_obj + datetime.timedelta(days=1)
                while next_day.weekday() >= 5:  # 跳过周末
                    next_day += datetime.timedelta(days=1)
                return next_day.strftime('%Y-%m-%d')

            # 使用函数为每个日期找到下一个交易日
            for date in all_dates:
                next_trading_day_map[date] = find_next_trading_day(date)

            print(f"使用历史数据创建了 {len(next_trading_day_map)} 个日期到下一个交易日的映射")

        # 如果没有历史数据但有交易日历数据，使用交易日历
        elif not calendar_df.empty:
            print("使用交易日历数据创建交易日映射...")
            # 获取所有交易日
            trading_days = calendar_df[calendar_df['is_trading_day'] == 1]['calendar_date'].sort_values().tolist()

            # 创建交易日到下一个交易日的映射
            trading_day_to_next = {}
            for i in range(len(trading_days) - 1):
                trading_day_to_next[trading_days[i]] = trading_days[i + 1].strftime('%Y-%m-%d')

            # 对于最后一个交易日，使用启发式方法
            if trading_days:
                last_day = trading_days[-1]
                next_day = last_day + datetime.timedelta(days=1)
                while next_day.weekday() >= 5:  # 跳过周末
                    next_day += datetime.timedelta(days=1)
                trading_day_to_next[last_day] = next_day.strftime('%Y-%m-%d')

            # 使用交易日映射快速获取下一个交易日
            for date in all_dates:
                if date in trading_day_to_next:
                    next_trading_day_map[date] = trading_day_to_next[date]
                else:
                    next_trading_day_map[date] = get_next_trading_day(date)
        else:
            # 如果没有交易日历数据，使用启发式方法
            print("没有交易日历数据，使用启发式方法计算下一个交易日...")
            for date in all_dates:
                next_trading_day_map[date] = get_next_trading_day(date)

    # 创建日期到股票数据的映射
    if not date_to_data:
        print("创建日期到股票数据的映射...")
        if use_daily_files and all_daily_data is not None:
            # 使用按日期文件模式
            for date in all_dates:
                if date in all_daily_data:
                    date_data = all_daily_data[date].copy()
                    # 创建查找键
                    date_data['查找键'] = date_data['股票代码'] + '_' + date_data['日期'].astype(str)
                    date_to_data[date] = date_data
        else:
            # 使用传统模式
            for date in all_dates:
                date_data = stock_df[stock_df['日期'] == date]
                if not date_data.empty:
                    # 创建查找键
                    date_data['查找键'] = date_data['股票代码'] + '_' + date_data['日期'].astype(str)
                    date_to_data[date] = date_data

    # 保存预加载的数据
    _preloaded_data['next_trading_day_map'] = next_trading_day_map
    _preloaded_data['date_to_data'] = date_to_data
    _preloaded_data['is_preloaded'] = True

    end_time = time.time()
    elapsed_time = end_time - start_time
    minutes, seconds = divmod(elapsed_time, 60)
    print(f"数据预加载完成，耗时: {int(minutes)}分钟 {int(seconds)}秒")
    print(f"预加载完成，共 {len(all_dates)} 个交易日")

    # 只有在第一次预加载时才创建完成标记文件
    if not os.path.exists(preload_flag_file):
        try:
            with open(preload_flag_file, 'w') as f:
                f.write(f"预加载完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"耗时: {int(minutes)}分钟 {int(seconds)}秒\n")
                f.write(f"交易日数量: {len(all_dates)}\n")
            print("创建预加载完成标记文件")
        except Exception as e:
            print(f"创建完成标记文件失败: {e}")

        # 清理锁文件
        try:
            if os.path.exists(preload_lock_file):
                os.remove(preload_lock_file)
            print("清理预加载锁文件")
        except Exception as e:
            print(f"清理锁文件失败: {e}")
    else:
        print("预加载完成标记文件已存在，跳过创建")

def main():
    # 记录总开始时间
    total_start_time = time.time()

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='回测股票策略')
    parser.add_argument('--start', type=int, help='起始策略ID')
    parser.add_argument('--end', type=int, help='结束策略ID')
    parser.add_argument('--id', type=int, help='单个策略ID')
    parser.add_argument('--batch', type=int, default=1, help='批处理大小，同时处理多少个策略')
    parser.add_argument('--preload', action='store_true', help='是否预加载数据')
    parser.add_argument('--data_dir', type=str, help='数据目录路径，默认为自动检测')
    args = parser.parse_args()

    # 预加载所有数据
    preload_data()

    try:
        # 获取所有策略ID
        all_strategy_ids = summary_df['策略编号'].tolist()
        print(f"总共有 {len(all_strategy_ids)} 个策略")

        # 确定要处理的策略ID
        if args.id is not None:
            # 处理单个策略
            strategy_ids = [args.id]
            print(f"将处理单个策略 (ID: {args.id})")
        elif args.start is not None and args.end is not None:
            # 处理指定范围的策略（不限制必须在汇总表中）
            strategy_ids = list(range(args.start, args.end + 1))
            existing_strategies = [sid for sid in strategy_ids if sid in all_strategy_ids]
            print(f"将处理 {len(strategy_ids)} 个策略 (ID范围: {args.start}-{args.end})")
            print(f"其中 {len(existing_strategies)} 个策略在汇总表中，{len(strategy_ids) - len(existing_strategies)} 个策略不在汇总表中")
        else:
            # 如果没有提供命令行参数，则提示用户输入
            try:
                start_id = int(input("请输入起始策略ID (例如: 1): "))
                end_id = int(input("请输入结束策略ID (例如: 100): "))
                strategy_ids = [sid for sid in all_strategy_ids if start_id <= sid <= end_id]
                print(f"将处理 {len(strategy_ids)} 个策略 (ID范围: {start_id}-{end_id})")
            except ValueError:
                print("输入无效，请输入有效的数字")
                return
            except KeyboardInterrupt:
                print("\n操作已取消")
                return

        # 检查策略ID列表是否为空
        if not strategy_ids:
            print("没有找到符合条件的策略ID")
            return

        # 统计成功和失败的策略
        success_count = 0
        fail_count = 0

        # 批处理大小
        batch_size = args.batch
        if batch_size < 1:
            batch_size = 1

        # 使用全局变量，避免重复加载数据
        global next_trading_day_map, date_to_data, _preloaded_data

        # 使用预加载的数据
        print("使用已预加载的数据")
        next_trading_day_map = _preloaded_data['next_trading_day_map']
        date_to_data = _preloaded_data['date_to_data']

        # 根据是否使用按日期文件来获取所有日期
        if use_daily_files and available_dates is not None:
            all_dates = available_dates
        elif not stock_df.empty:
            all_dates = stock_df['日期'].unique()
        else:
            all_dates = []

        print(f"使用已加载的交易日映射，共 {len(next_trading_day_map) if next_trading_day_map else 0} 个映射")
        print(f"使用已加载的股票数据映射，共 {len(date_to_data) if date_to_data else 0} 个日期的数据")
        print(f"预加载完成，共 {len(all_dates)} 个交易日")

        # 逐个处理策略
        for i, strategy_id in enumerate(strategy_ids):
            print(f"\n{'='*50}")
            print(f"开始回测策略 {strategy_id}... ({i+1}/{len(strategy_ids)})")

            # 处理策略
            success = process_strategy(strategy_id, next_trading_day_map, date_to_data)

            if success:
                print(f"策略 {strategy_id} 回测完成并已更新汇总表")
                success_count += 1
            else:
                print(f"策略 {strategy_id} 回测失败或未选出股票")
                fail_count += 1

            print(f"{'='*50}\n")

            # 每处理batch_size个策略，显示进度
            if (i + 1) % batch_size == 0 or i == len(strategy_ids) - 1:
                elapsed_time = time.time() - total_start_time
                minutes, seconds = divmod(elapsed_time, 60)
                hours, minutes = divmod(minutes, 60)

                # 估计剩余时间
                if i > 0:
                    avg_time_per_strategy = elapsed_time / (i + 1)
                    remaining_strategies = len(strategy_ids) - (i + 1)
                    estimated_remaining_time = avg_time_per_strategy * remaining_strategies

                    r_hours, r_remainder = divmod(estimated_remaining_time, 3600)
                    r_minutes, r_seconds = divmod(r_remainder, 60)

                    print(f"进度: {i+1}/{len(strategy_ids)} ({(i+1)/len(strategy_ids)*100:.2f}%)")
                    print(f"已用时间: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
                    print(f"预计剩余时间: {int(r_hours)}小时 {int(r_minutes)}分钟 {int(r_seconds)}秒")
                    print(f"预计完成时间: {time.strftime('%H:%M:%S', time.localtime(time.time() + estimated_remaining_time))}")

        # 计算总耗时
        total_elapsed_time = time.time() - total_start_time
        hours, remainder = divmod(total_elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)

        # 打印总结
        print(f"\n回测完成！成功: {success_count}, 失败: {fail_count}, 总计: {len(strategy_ids)}")
        print(f"总耗时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
        print(f"平均每个策略耗时: {total_elapsed_time/len(strategy_ids):.2f}秒")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        print(traceback.format_exc())
    finally:
        print("回测完成")

if __name__ == "__main__":
    main()
