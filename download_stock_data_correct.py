"""
使用Baostock API下载股票数据
按照官方文档的正确用法实现
"""

import pandas as pd
import baostock as bs
import os
import datetime
import time
import threading
import queue
import stock_data_manager as sdm

# 设置文件路径
base_dir = r'E:\机器学习\complete_excel_results'
stock_data_dir = os.path.join(base_dir, 'stock_data')
daily_data_dir = os.path.join(stock_data_dir, 'daily')
stock_details_file = os.path.join(base_dir, '股票明细_完整.xlsx')
history_data_file = os.path.join(stock_data_dir, 'stock_history_data.xlsx')

# 确保目录存在
if not os.path.exists(stock_data_dir):
    os.makedirs(stock_data_dir)
if not os.path.exists(daily_data_dir):
    os.makedirs(daily_data_dir)

def get_all_stock_codes():
    """获取所有股票代码"""
    try:
        # 尝试从股票明细文件中读取股票代码
        stock_df = pd.read_excel(stock_details_file)
        if '股票代码' in stock_df.columns:
            stock_codes = stock_df['股票代码'].tolist()
            return stock_codes
    except Exception as e:
        print(f"读取股票明细文件时出错: {e}")
    
    # 如果无法从文件中读取，则获取上证和深证所有股票列表
    rs = bs.query_all_stock(day=datetime.datetime.now().strftime('%Y-%m-%d'))
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    stock_list_df = pd.DataFrame(data_list, columns=rs.fields)
    stock_codes = stock_list_df['code'].tolist()
    print(f"使用默认股票列表，共 {len(stock_codes)} 只股票")
    
    return stock_codes

def download_trading_calendar(start_date, end_date):
    """下载交易日历"""
    rs = bs.query_trade_dates(start_date=start_date, end_date=end_date)
    data_list = []
    while (rs.error_code == '0') & rs.next():
        data_list.append(rs.get_row_data())
    calendar_df = pd.DataFrame(data_list, columns=rs.fields)
    
    # 转换日期列为日期类型
    calendar_df['calendar_date'] = pd.to_datetime(calendar_df['calendar_date'])
    
    # 转换交易日标志为整数
    calendar_df['is_trading_day'] = calendar_df['is_trading_day'].apply(lambda x: 1 if x == '1' else 0)
    
    return calendar_df

def download_stock_k_data(stock_code, start_date, end_date):
    """
    下载单只股票在指定日期范围内的日K数据
    
    参数:
        stock_code: 股票代码
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD
        
    返回:
        DataFrame: 包含该股票在日期范围内K线数据的DataFrame
    """
    # 使用query_history_k_data_plus接口获取日K数据
    rs = bs.query_history_k_data_plus(
        stock_code,
        "date,code,open,high,low,close,volume,amount,pctChg,isST",
        start_date=start_date,
        end_date=end_date,
        frequency="d",
        adjustflag="3"  # 3表示前复权
    )
    
    # 检查API调用是否成功
    if rs.error_code != '0':
        print(f"获取股票 {stock_code} 数据失败: {rs.error_code}, {rs.error_msg}")
        return None
    
    # 处理数据
    data_list = []
    while rs.next():
        data_list.append(rs.get_row_data())
    
    # 如果没有数据，返回None
    if not data_list:
        return None
    
    # 创建DataFrame
    df = pd.DataFrame(data_list, columns=rs.fields)
    
    # 转换数据类型
    for field in ['open', 'high', 'low', 'close', 'volume', 'amount', 'pctChg']:
        if field in df.columns:
            df[field] = pd.to_numeric(df[field], errors='coerce')
    
    return df

def download_all_stocks_data(start_date, end_date, max_stocks=None):
    """
    下载所有股票在指定日期范围内的日K数据
    
    参数:
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD
        max_stocks: 最大下载股票数量，用于测试
        
    返回:
        DataFrame: 包含所有股票在日期范围内K线数据的DataFrame
    """
    print(f"开始下载从 {start_date} 到 {end_date} 的所有股票日K数据")
    start_time = time.time()
    
    # 获取所有股票代码
    stock_codes = get_all_stock_codes()
    print(f"共找到 {len(stock_codes)} 只股票")
    
    # 限制股票数量（用于测试）
    if max_stocks is not None and max_stocks > 0 and max_stocks < len(stock_codes):
        stock_codes = stock_codes[:max_stocks]
        print(f"限制下载股票数量为 {max_stocks} 只")
    
    # 创建一个空的DataFrame来存储所有数据
    all_data = []
    success_count = 0
    error_count = 0
    
    # 使用多线程下载数据
    num_threads = 10
    result_queue = queue.Queue()
    
    def download_batch(batch_codes, thread_id):
        batch_data = []
        batch_success = 0
        batch_error = 0
        
        for code in batch_codes:
            try:
                # 下载单只股票在日期范围内的数据
                stock_data = download_stock_k_data(code, start_date, end_date)
                if stock_data is not None and not stock_data.empty:
                    batch_data.append(stock_data)
                    batch_success += 1
                else:
                    batch_error += 1
            except Exception as e:
                print(f"下载股票 {code} 数据时出错: {e}")
                batch_error += 1
        
        # 将结果放入队列
        result_queue.put((batch_data, batch_success, batch_error))
        print(f"线程{thread_id}: 完成 {len(batch_codes)} 只股票的下载，成功: {batch_success}，失败: {batch_error}")
    
    # 创建并启动线程
    threads = []
    stocks_per_thread = len(stock_codes) // num_threads
    
    for i in range(num_threads):
        start_idx = i * stocks_per_thread
        end_idx = len(stock_codes) if i == num_threads - 1 else (i + 1) * stocks_per_thread
        batch_codes = stock_codes[start_idx:end_idx]
        
        thread = threading.Thread(target=download_batch, args=(batch_codes, i+1))
        thread.daemon = True
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 收集所有线程的结果
    while not result_queue.empty():
        batch_data, batch_success, batch_error = result_queue.get()
        all_data.extend(batch_data)
        success_count += batch_success
        error_count += batch_error
    
    # 合并所有数据
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"日期范围 {start_date} 到 {end_date} 的日K数据下载完成，共 {len(combined_df)} 条记录，成功: {success_count}，失败: {error_count}，耗时: {elapsed_time:.2f}秒")
        
        return combined_df
    else:
        print(f"日期范围 {start_date} 到 {end_date} 没有下载到任何数据")
        return pd.DataFrame()

def download_date_range(start_date, end_date, max_stocks=None):
    """
    下载指定日期范围内的所有股票日K数据
    
    参数:
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD
        max_stocks: 最大下载股票数量，用于测试
    """
    print(f"开始下载从 {start_date} 到 {end_date} 的日K数据")
    
    # 登录baostock
    print("登录baostock...")
    lg = bs.login()
    print(f"登录状态: {lg.error_code}, {lg.error_msg}")
    
    try:
        # 下载交易日历
        calendar_df = download_trading_calendar(start_date, end_date)
        print(f"交易日历下载完成，共 {len(calendar_df)} 天")
        
        # 保存交易日历
        calendar_file = os.path.join(stock_data_dir, f'trading_calendar_{start_date.replace("-", "")}_{end_date.replace("-", "")}.xlsx')
        calendar_df.to_excel(calendar_file, index=False)
        print(f"交易日历已保存到 {calendar_file}")
        
        # 提取交易日列表
        trading_days = calendar_df[calendar_df['is_trading_day'] == 1]['calendar_date'].tolist()
        print(f"交易日历中包含 {len(trading_days)} 个交易日")
        
        # 一次性下载所有股票在日期范围内的数据
        combined_df = download_all_stocks_data(start_date, end_date, max_stocks)
        
        if not combined_df.empty:
            # 按日期拆分并保存
            combined_df['date'] = pd.to_datetime(combined_df['date'])
            dates = combined_df['date'].unique()
            
            print(f"开始按日期拆分数据，共 {len(dates)} 个日期")
            for date in dates:
                date_str = date.strftime('%Y-%m-%d')
                day_df = combined_df[combined_df['date'] == date]
                
                # 保存该日期的数据
                sdm.save_daily_data(day_df, date_str)
                print(f"日期 {date_str} 的数据已保存，共 {len(day_df)} 条记录")
            
            # 保存合并文件
            history_data_file = os.path.join(stock_data_dir, f'stock_history_data_{start_date.replace("-", "")}_{end_date.replace("-", "")}.xlsx')
            combined_df.to_excel(history_data_file, index=False)
            print(f"合并数据已保存到 {history_data_file}，共 {len(combined_df)} 条记录")
            
            print(f"下载完成，共处理 {len(dates)} 个交易日，总记录数: {len(combined_df)}")
        else:
            print("没有下载到任何数据")
    
    finally:
        # 登出baostock
        bs.logout()
        print("已登出baostock")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='下载股票日K数据')
    parser.add_argument('--start_date', type=str, default='2025-02-05', help='开始日期，格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, default='2025-02-05', help='结束日期，格式为YYYY-MM-DD')
    parser.add_argument('--max_stocks', type=int, default=None, help='最大下载股票数量，用于测试')
    
    args = parser.parse_args()
    
    # 执行下载
    download_date_range(args.start_date, args.end_date, args.max_stocks)
