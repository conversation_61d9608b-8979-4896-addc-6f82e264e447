import pandas as pd
import numpy as np

# 读取数据
print("读取股票数据...")
data = pd.read_excel('股票明细.xlsx')
print(f"数据形状: {data.shape}")

# 检查数据日期范围
data['日期'] = pd.to_datetime(data['日期'])
print("\n数据日期范围:")
print(f"{data['日期'].min()} 至 {data['日期'].max()}")

# 计算每只股票在每个日期的次日涨跌幅
def calculate_next_day_return(group):
    """计算每只股票的次日涨跌幅"""
    group = group.sort_values('日期')
    group['次日涨跌幅'] = group['当前价格'].shift(-1) / group['当前价格'] - 1
    return group

# 按股票代码分组，计算次日涨跌幅
print("\n计算次日涨跌幅...")
data_with_returns = data.groupby('股票代码', group_keys=False).apply(calculate_next_day_return)

# 删除没有次日数据的记录
data_with_returns = data_with_returns.dropna(subset=['次日涨跌幅'])

# 分析技术强度=100的股票在次日的表现
print("\n分析技术强度=100的股票在次日的表现...")

# 筛选技术强度=100的股票
strength_100_stocks = data_with_returns[data_with_returns['技术强度'] == 100]

# 按日期分组，计算每天技术强度=100的股票在次日的平均涨跌幅
daily_performance = []

for date in sorted(data_with_returns['日期'].unique())[:-1]:  # 排除最后一天，因为没有次日数据
    # 获取当天技术强度=100的股票
    day_data = strength_100_stocks[strength_100_stocks['日期'] == date]
    
    if len(day_data) > 0:
        # 计算这些股票次日的平均涨跌幅
        avg_return = day_data['次日涨跌幅'].mean() * 100
        positive_rate = (day_data['次日涨跌幅'] > 0).mean() * 100
        
        # 计算收益分布
        returns_distribution = {
            '下跌超过3%': (day_data['次日涨跌幅'] < -0.03).sum() / len(day_data) * 100,
            '下跌1%-3%': ((day_data['次日涨跌幅'] >= -0.03) & (day_data['次日涨跌幅'] < -0.01)).sum() / len(day_data) * 100,
            '小幅波动±1%': ((day_data['次日涨跌幅'] >= -0.01) & (day_data['次日涨跌幅'] <= 0.01)).sum() / len(day_data) * 100,
            '上涨1%-3%': ((day_data['次日涨跌幅'] > 0.01) & (day_data['次日涨跌幅'] <= 0.03)).sum() / len(day_data) * 100,
            '上涨超过3%': (day_data['次日涨跌幅'] > 0.03).sum() / len(day_data) * 100
        }
        
        daily_performance.append({
            '日期': date,
            '股票数量': len(day_data),
            '次日平均涨跌幅(%)': avg_return,
            '次日上涨比例(%)': positive_rate,
            '下跌超过3%(%)': returns_distribution['下跌超过3%'],
            '下跌1%-3%(%)': returns_distribution['下跌1%-3%'],
            '小幅波动±1%(%)': returns_distribution['小幅波动±1%'],
            '上涨1%-3%(%)': returns_distribution['上涨1%-3%'],
            '上涨超过3%(%)': returns_distribution['上涨超过3%']
        })

# 转换为DataFrame并显示结果
if daily_performance:
    daily_df = pd.DataFrame(daily_performance)
    print("\n每日技术强度=100的股票次日表现:")
    print(daily_df)
    
    # 计算总体表现
    print("\n总体表现:")
    print(f"平均每天技术强度=100的股票数量: {daily_df['股票数量'].mean():.2f}")
    print(f"次日平均涨跌幅: {daily_df['次日平均涨跌幅(%)'].mean():.2f}%")
    print(f"次日平均上涨比例: {daily_df['次日上涨比例(%)'].mean():.2f}%")
    
    # 计算收益分布
    print("\n次日收益分布:")
    print(f"下跌超过3%的比例: {daily_df['下跌超过3%(%)'].mean():.2f}%")
    print(f"下跌1%-3%的比例: {daily_df['下跌1%-3%(%)'].mean():.2f}%")
    print(f"小幅波动±1%的比例: {daily_df['小幅波动±1%(%)'].mean():.2f}%")
    print(f"上涨1%-3%的比例: {daily_df['上涨1%-3%(%)'].mean():.2f}%")
    print(f"上涨超过3%的比例: {daily_df['上涨超过3%(%)'].mean():.2f}%")
    
    # 保存每日表现数据
    daily_df.to_excel("技术强度100_每日收益率.xlsx", index=False)
    print("\n已将每日表现数据保存到 '技术强度100_每日收益率.xlsx'")
else:
    print("没有足够的数据来分析每日表现")

# 分析技术强度从100变为非100的情况
print("\n分析技术强度从100变为非100的情况...")

# 按股票代码分组，找出技术强度从100变为非100的情况
def find_strength_changes(group):
    """找出技术强度从100变为非100的情况"""
    group = group.sort_values('日期')
    
    # 初始化结果列表
    changes = []
    
    # 遍历每一行（除了最后一行）
    for i in range(len(group) - 1):
        current_row = group.iloc[i]
        next_row = group.iloc[i + 1]
        
        # 检查技术强度是否从100变为非100
        if current_row['技术强度'] == 100 and next_row['技术强度'] != 100:
            changes.append({
                '股票代码': current_row['股票代码'],
                '股票名称': current_row['股票名称'],
                '变化日期': next_row['日期'],
                '变化前技术强度': current_row['技术强度'],
                '变化后技术强度': next_row['技术强度'],
                '变化前价格': current_row['当前价格'],
                '变化后价格': next_row['当前价格'],
                '价格变化率': next_row['当前价格'] / current_row['当前价格'] - 1
            })
    
    return changes

# 应用到每个股票组
all_changes = []
for code, group in data.groupby('股票代码'):
    changes = find_strength_changes(group)
    all_changes.extend(changes)

# 转换为DataFrame
if all_changes:
    changes_df = pd.DataFrame(all_changes)
    
    # 计算平均持有天数（这里无法直接计算，因为我们只知道变化的日期，不知道初始日期）
    
    # 计算平均价格变化率
    avg_price_change = changes_df['价格变化率'].mean() * 100
    
    print(f"\n找到 {len(changes_df)} 次技术强度从100变为非100的情况")
    print(f"平均价格变化率: {avg_price_change:.2f}%")
    
    # 按变化后技术强度分组
    strength_groups = changes_df.groupby('变化后技术强度')
    
    print("\n按变化后技术强度分组的价格变化率:")
    for strength, group in strength_groups:
        avg_change = group['价格变化率'].mean() * 100
        print(f"技术强度变为 {strength}: {len(group)} 次, 平均价格变化率 {avg_change:.2f}%")
    
    # 保存结果
    changes_df.to_excel("技术强度100变化分析.xlsx", index=False)
    print("\n已将技术强度变化分析保存到 '技术强度100变化分析.xlsx'")
else:
    print("没有找到技术强度从100变为非100的情况")

# 分析技术强度=100持续多天的情况
print("\n分析技术强度=100持续多天的情况...")

def find_continuous_strength_100(group):
    """找出技术强度=100持续多天的情况"""
    group = group.sort_values('日期')
    
    # 初始化结果
    continuous_periods = []
    current_period = None
    
    # 遍历每一行
    for i, row in group.iterrows():
        if row['技术强度'] == 100:
            # 如果当前没有进行中的周期，开始一个新周期
            if current_period is None:
                current_period = {
                    '股票代码': row['股票代码'],
                    '股票名称': row['股票名称'],
                    '开始日期': row['日期'],
                    '开始价格': row['当前价格'],
                    '日期列表': [row['日期']]
                }
            else:
                # 将当前日期添加到日期列表
                current_period['日期列表'].append(row['日期'])
        else:
            # 如果技术强度不是100，且有进行中的周期，结束当前周期
            if current_period is not None:
                current_period['结束日期'] = group[group['日期'] < row['日期']].iloc[-1]['日期']
                current_period['结束价格'] = group[group['日期'] < row['日期']].iloc[-1]['当前价格']
                current_period['持续天数'] = len(current_period['日期列表'])
                current_period['价格变化率'] = current_period['结束价格'] / current_period['开始价格'] - 1
                
                # 删除日期列表（不需要保存在结果中）
                del current_period['日期列表']
                
                continuous_periods.append(current_period)
                current_period = None
    
    # 处理最后一个周期（如果存在且未结束）
    if current_period is not None:
        current_period['结束日期'] = current_period['日期列表'][-1]
        current_period['结束价格'] = group[group['日期'] == current_period['结束日期']]['当前价格'].values[0]
        current_period['持续天数'] = len(current_period['日期列表'])
        current_period['价格变化率'] = current_period['结束价格'] / current_period['开始价格'] - 1
        
        # 删除日期列表
        del current_period['日期列表']
        
        continuous_periods.append(current_period)
    
    return continuous_periods

# 应用到每个股票组
all_periods = []
for code, group in data.groupby('股票代码'):
    periods = find_continuous_strength_100(group)
    all_periods.extend(periods)

# 转换为DataFrame
if all_periods:
    periods_df = pd.DataFrame(all_periods)
    
    # 计算平均持续天数和价格变化率
    avg_duration = periods_df['持续天数'].mean()
    avg_price_change = periods_df['价格变化率'].mean() * 100
    
    print(f"\n找到 {len(periods_df)} 个技术强度=100持续多天的周期")
    print(f"平均持续天数: {avg_duration:.2f}")
    print(f"平均价格变化率: {avg_price_change:.2f}%")
    
    # 按持续天数分组
    duration_groups = periods_df.groupby('持续天数')
    
    print("\n按持续天数分组的价格变化率:")
    for duration, group in duration_groups:
        avg_change = group['价格变化率'].mean() * 100
        print(f"持续 {duration} 天: {len(group)} 次, 平均价格变化率 {avg_change:.2f}%")
    
    # 保存结果
    periods_df.to_excel("技术强度100持续分析.xlsx", index=False)
    print("\n已将技术强度持续分析保存到 '技术强度100持续分析.xlsx'")
else:
    print("没有找到技术强度=100持续多天的情况")
