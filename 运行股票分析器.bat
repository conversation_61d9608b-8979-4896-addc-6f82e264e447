@echo off
chcp 65001 > nul
echo ===================================================
echo        股票高胜率策略分析工具
echo ===================================================
echo.
echo 1. 图形界面模式
echo 2. 命令行模式
echo.
set /p choice=请选择运行模式 (1/2): 

if "%choice%"=="1" (
    echo.
    echo 正在启动图形界面模式...
    start dist\stock_analyzer.exe --gui
) else if "%choice%"=="2" (
    echo.
    echo 正在启动命令行模式...
    echo.
    set /p start_date=请输入回测开始日期 (格式: YYYY-MM-DD): 
    set /p end_date=请输入回测结束日期 (格式: YYYY-MM-DD): 
    set /p output_file=请输入输出文件名 (默认: 股票高胜率策略分析结果.txt): 
    
    if "%output_file%"=="" set output_file=股票高胜率策略分析结果.txt
    
    dist\stock_analyzer.exe --data stock_data.xlsx --start_date %start_date% --end_date %end_date% --output %output_file%
    
    echo.
    echo 分析完成，结果已保存到 %output_file%
) else (
    echo.
    echo 无效的选择，请重新运行脚本并选择1或2
)

echo.
pause
