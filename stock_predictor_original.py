"""
股票预测工具

使用机器学习模型预测股票涨跌，并根据不同策略推荐股票
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib
import argparse

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header(title):
    """打印标题"""
    print("\n" + " " * 24 + title)
    print("=" * 80 + "\n")

def load_model(model_dir='models'):
    """加载模型"""
    print("加载模型...")
    try:
        # 获取最新的模型文件
        model_files = [f for f in os.listdir(model_dir) if f.endswith('.joblib')]
        if not model_files:
            print("没有找到模型文件")
            return None, None, None

        latest_model_file = max(model_files)
        model_path = os.path.join(model_dir, latest_model_file)

        # 加载模型
        model_data = joblib.load(model_path)
        model = model_data['model']
        scaler = model_data['scaler']
        features = model_data['features']

        # 提取训练时间
        training_time = latest_model_file.split('.')[0]

        print(f"成功加载模型 (训练时间: {training_time})")
        print(f"模型使用的特征: {features}")

        return model, scaler, features
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None, None, None

def load_data(file_path):
    """加载股票数据"""
    print(f"加载股票数据: {file_path}")
    try:
        df = pd.read_excel(file_path)
        print(f"成功加载股票数据，共 {len(df)} 条记录")
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def preprocess_data(df):
    """预处理数据"""
    print("预处理数据...")

    # 确保日期列是datetime类型
    if '日期' in df.columns:
        df['日期'] = pd.to_datetime(df['日期'])

    # 生成目标变量：如果涨跌幅>0，则为1，否则为0
    if '涨跌幅' in df.columns and '是否盈利' not in df.columns:
        df['是否盈利'] = (df['涨跌幅'] > 0).astype(int)

    print("预处理完成")
    return df

def get_latest_data(df):
    """获取最新日期的数据"""
    latest_date = df['日期'].max()
    latest_data = df[df['日期'] == latest_date]
    return latest_data, latest_date

def train_model(data_file_path='股票明细.xlsx'):
    """训练模型"""
    print_header("训练股票预测模型")

    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return False

    # 预处理数据
    processed_data = preprocess_data(stock_data)

    # 准备训练数据
    print("准备训练数据...")

    # 提取特征和目标变量
    # 只使用数值特征
    features = []
    for col in processed_data.columns:
        if col not in ['日期', '股票代码', '股票名称', '是否盈利']:
            # 检查列是否为数值类型
            if pd.api.types.is_numeric_dtype(processed_data[col]):
                features.append(col)

    print(f"使用的特征: {features}")

    # 检查特征是否存在
    missing_features = [f for f in features if f not in processed_data.columns]
    if missing_features:
        print(f"数据中缺少以下特征: {missing_features}")
        return False

    X = processed_data[features]
    y = processed_data['是否盈利'] if '是否盈利' in processed_data.columns else None

    if y is None:
        print("数据中缺少目标变量 '是否盈利'")
        return False

    # 处理缺失值
    valid_indices = ~X.isnull().any(axis=1) & ~y.isnull()
    X = X[valid_indices]
    y = y[valid_indices]

    if len(X) == 0:
        print("处理缺失值后没有有效数据")
        return False

    print(f"训练数据准备完成，共 {len(X)} 条记录")

    # 标准化特征
    from sklearn.preprocessing import StandardScaler
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # 训练模型
    print("训练模型...")
    from sklearn.ensemble import RandomForestClassifier
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_scaled, y)

    # 评估模型
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    y_pred = model.predict(X_scaled)
    accuracy = accuracy_score(y, y_pred)
    precision = precision_score(y, y_pred)
    recall = recall_score(y, y_pred)
    f1 = f1_score(y, y_pred)

    print(f"模型训练完成")
    print(f"准确率: {accuracy:.4f}")
    print(f"精确率: {precision:.4f}")
    print(f"召回率: {recall:.4f}")
    print(f"F1分数: {f1:.4f}")

    # 保存模型
    print("保存模型...")
    model_dir = 'models'
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    # 使用当前时间作为文件名
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = os.path.join(model_dir, f"{current_time}.joblib")

    # 保存模型、缩放器和特征列表
    model_data = {
        'model': model,
        'scaler': scaler,
        'features': features
    }
    joblib.dump(model_data, model_path)

    print(f"模型已保存至: {model_path}")
    return True

def apply_strategy_1(predictions):
    """
    策略1：100%高胜率策略

    条件：
    1. 预测盈利概率>75%
    2. 技术强度≥70且≤85
    3. 连续技术强度5天数≥400

    注意：只买入开盘时上涨的股票！这是保持100%胜率的关键条件！
    """
    # 筛选出满足条件的股票
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] >= 70) & (predictions['技术强度'] <= 85) &  # 条件2: 技术强度≥70且≤85
        (predictions['连续技术强度5天数'] >= 400)  # 条件3: 5天累积值≥400
    ]

    # 按预测盈利概率降序排序
    strategy_stocks = strategy_stocks.sort_values('预测盈利概率', ascending=False)

    return strategy_stocks

def apply_strategy_A(predictions):
    """
    策略A：最高胜率策略

    条件：
    1. 技术强度=21-30
    2. 预测盈利概率>75%
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] >= 21) &  # 条件2: 技术强度≥21
        (predictions['技术强度'] <= 30)  # 条件2: 技术强度≤30
    ]

    return strategy_stocks

def apply_strategy_B(predictions):
    """
    策略B：最高收益率策略

    条件：
    1. 技术强度=100
    2. 预测盈利概率>75%
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] == 100)  # 条件2: 技术强度=100
    ]

    return strategy_stocks

def apply_strategy_B2(predictions):
    """
    策略B2：高收益率策略（稍微放宽条件）

    条件：
    1. 预测盈利概率>70%
    2. 技术强度>=90
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.70) &  # 条件1: 预测盈利概率>70%
        (predictions['技术强度'] >= 90)  # 条件2: 技术强度>=90
    ]

    return strategy_stocks

def apply_strategy_C(predictions):
    """
    策略C：平衡策略（胜率和交易机会的平衡）

    条件：
    1. 技术强度=71-80
    2. 预测盈利概率>75%
    """
    strategy_stocks = predictions[
        (predictions['预测盈利概率'] > 0.75) &  # 条件1: 预测盈利概率>75%
        (predictions['技术强度'] >= 71) &  # 条件2: 技术强度≥71
        (predictions['技术强度'] <= 80)  # 条件2: 技术强度≤80
    ]

    return strategy_stocks

def predict_with_strategy(strategy_name, strategy_func, data_file_path='股票明细.xlsx', prediction_date=None):
    """使用指定策略预测股票，可以指定预测日期"""
    strategy_descriptions = {
        'strategy_1': "策略1：100%高胜率策略",
        'strategy_A': "策略A：最高胜率策略",
        'strategy_B': "策略B：最高收益率策略",
        'strategy_C': "策略C：平衡策略（胜率和交易机会的平衡）",
        'strategy_B2': "策略B2：高收益率策略（放宽条件版）"
    }

    risk_descriptions = {
        'strategy_1': {
            'buy_risk': "低风险：该策略在历史测试中表现出100%的胜率，但交易机会较少。",
            'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
            'expected_win_rate': "100%",
            'expected_return': "约2.38%"
        },
        'strategy_A': {
            'buy_risk': "低风险：该策略在历史测试中表现出约86.77%的胜率，交易机会适中。",
            'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
            'expected_win_rate': "约86.77%",
            'expected_return': "约3.42%"
        },
        'strategy_B': {
            'buy_risk': "中低风险：该策略在历史测试中表现出约83.67%的胜率，但交易机会较少。",
            'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
            'expected_win_rate': "约83.67%",
            'expected_return': "约4.83%"
        },
        'strategy_C': {
            'buy_risk': "中低风险：该策略在历史测试中表现出约84.82%的胜率，交易机会较多。",
            'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
            'expected_win_rate': "约84.82%",
            'expected_return': "约2.59%"
        },
        'strategy_B2': {
            'buy_risk': "中风险：该策略是策略B的放宽条件版本，预期胜率约80%，交易机会较策略B更多。",
            'sell_risk': "低风险：该策略要求在买入后的第二个交易日开盘时卖出，持有时间短，市场波动风险较小。",
            'expected_win_rate': "约80%",
            'expected_return': "约4.0%"
        }
    }

    print_header(strategy_descriptions[strategy_name])

    # 加载模型
    model, scaler, features = load_model()
    if model is None:
        print("无法加载模型，请先训练模型")
        return

    # 加载数据
    stock_data = load_data(data_file_path)
    if stock_data is None:
        return

    # 预处理数据
    processed_data = preprocess_data(stock_data)

    # 如果指定了预测日期，使用指定日期
    if prediction_date:
        # 将字符串转换为datetime对象
        target_date = datetime.strptime(prediction_date, '%Y-%m-%d')
        # 找到小于等于目标日期的最近日期
        all_dates = sorted(processed_data['日期'].unique())
        earlier_dates = [d for d in all_dates if d <= target_date]

        if earlier_dates:
            latest_date = max(earlier_dates)
            latest_data = processed_data[processed_data['日期'] == latest_date]
            print(f"使用 {latest_date.strftime('%Y-%m-%d')} 的数据进行预测")
        else:
            # 如果没有小于等于目标日期的日期，使用最早的日期
            latest_date = min(all_dates)
            latest_data = processed_data[processed_data['日期'] == latest_date]
            print(f"警告: {prediction_date} 之前没有数据，使用最早的日期 {latest_date.strftime('%Y-%m-%d')} 的数据进行预测")

        # 使用指定的预测日期
        next_date_str = prediction_date
        next_date = target_date
    else:
        # 获取最新数据
        latest_data, latest_date = get_latest_data(processed_data)

        # 计算下一个交易日（简单地加一天，实际应考虑周末和节假日）
        next_date = latest_date + timedelta(days=1)
        next_date_str = next_date.strftime('%Y-%m-%d')

    print(f"预测日期: {next_date_str}")

    # 准备预测数据
    try:
        # 提取特征
        X_pred = latest_data[features]

        # 处理预测数据中的缺失值
        valid_indices = ~X_pred.isnull().any(axis=1)
        X_pred = X_pred[valid_indices]
        latest_data_filtered = latest_data.loc[valid_indices.index[valid_indices]]

        if len(X_pred) == 0:
            print("预测数据不足，无法进行预测")
            return

        # 标准化特征
        X_pred_scaled = scaler.transform(X_pred)

        # 预测盈利概率
        pred_proba = model.predict_proba(X_pred_scaled)[:, 1]

        # 创建预测结果DataFrame
        predictions = pd.DataFrame({
            '股票代码': latest_data_filtered['股票代码'],
            '股票名称': latest_data_filtered['股票名称'],
            '涨跌幅': latest_data_filtered['涨跌幅'] if '涨跌幅' in latest_data_filtered.columns else 0,
            '技术强度': latest_data_filtered['技术强度'],
            '连续技术强度天数': 100,  # 默认值
            '连续技术强度5天数': 400,  # 默认值
            '预测盈利概率': pred_proba
        })

        # 按预测盈利概率降序排序
        predictions = predictions.sort_values('预测盈利概率', ascending=False)

        print(f"预测完成，共 {len(predictions)} 只股票")

        # 创建结果目录
        if not os.path.exists('strategy_results'):
            os.makedirs('strategy_results')

        # 应用策略
        strategy_stocks = strategy_func(predictions)

        # 保存结果
        result_file = f'strategy_results/{next_date_str}_{strategy_name}.xlsx'
        strategy_stocks.to_excel(result_file, index=False)

        print(f"\n预测结果已保存至: {result_file}")
        print(f"{strategy_descriptions[strategy_name]}推荐股票数: {len(strategy_stocks)}")
        print(f"预期胜率: {risk_descriptions[strategy_name]['expected_win_rate']}")
        print(f"预期收益率: {risk_descriptions[strategy_name]['expected_return']}")
        print(f"买入风险: {risk_descriptions[strategy_name]['buy_risk']}")
        print(f"卖出风险: {risk_descriptions[strategy_name]['sell_risk']}")

        # 显示推荐股票
        if len(strategy_stocks) > 0:
            print(f"\n{strategy_descriptions[strategy_name]}推荐买入的股票:")
            print("【重要提示】: 以下股票必须在开盘时上涨才买入，否则不买入！这是保持高胜率的关键条件！")
            print("【交易策略】: 买入后在第二个交易日开盘时卖出")
            print("\n股票列表:")
            for i, row in strategy_stocks.iterrows():
                print(f"{row['股票代码']} {row['股票名称']}: 技术强度={row['技术强度']}, 连续技术强度5天数={row['连续技术强度5天数']}, 预测盈利概率={row['预测盈利概率']*100:.2f}%")
        else:
            print(f"\n{strategy_descriptions[strategy_name]}没有推荐的股票")

        return strategy_stocks

    except Exception as e:
        print(f"预测失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票预测工具')
    parser.add_argument('option', type=int, choices=[1, 2, 3, 4, 5, 6],
                        help='选择操作: 1=训练模型, 2=策略1, 3=策略A, 4=策略B, 5=策略C, 6=策略B2')
    parser.add_argument('--data', type=str, default='股票明细.xlsx',
                        help='数据文件路径 (默认: 股票明细.xlsx)')
    parser.add_argument('--date', type=str, default=None,
                        help='预测日期 (格式: YYYY-MM-DD)')

    args = parser.parse_args()

    clear_screen()

    try:
        if args.option == 1:
            result = train_model(args.data)
            if result:
                print("模型训练成功完成")
            else:
                print("模型训练失败")
        elif args.option == 2:
            # 策略1
            predict_with_strategy('strategy_1', apply_strategy_1, args.data, args.date)
        elif args.option == 3:
            # 策略A
            predict_with_strategy('strategy_A', apply_strategy_A, args.data, args.date)
        elif args.option == 4:
            # 策略B
            predict_with_strategy('strategy_B', apply_strategy_B, args.data, args.date)
        elif args.option == 5:
            # 策略C
            predict_with_strategy('strategy_C', apply_strategy_C, args.data, args.date)
        elif args.option == 6:
            # 策略B2
            predict_with_strategy('strategy_B2', apply_strategy_B2, args.data, args.date)
    except Exception as e:
        print(f"执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
