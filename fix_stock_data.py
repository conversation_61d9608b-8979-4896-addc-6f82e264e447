#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复股票数据Excel文件
作者: Augment AI
版本: 1.0.0
"""

import os
import pandas as pd
import numpy as np
import glob
import re
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def extract_date_from_folder(folder_name):
    """从文件夹名称中提取日期"""
    match = re.search(r'选股结果_(\d{4}-\d{2}-\d{2})', folder_name)
    if match:
        return match.group(1)
    return None

def extract_technical_indicators(indicator_text):
    """从技术指标文本中提取各个指标"""
    indicators = {
        '均线多头排列': 0,
        'MACD金叉': 0,
        'RSI反弹': 0,
        'KDJ金叉': 0,
        '布林带突破': 0
    }
    
    if pd.isna(indicator_text):
        return indicators
    
    # 标准化文本
    text = indicator_text.replace(' ', '')
    
    # 检查各个指标
    if '均线多头排列' in text:
        indicators['均线多头排列'] = 1
    if 'MACD金叉' in text:
        indicators['MACD金叉'] = 1
    if 'RSI反弹' in text:
        indicators['RSI反弹'] = 1
    if 'KDJ金叉' in text:
        indicators['KDJ金叉'] = 1
    if '布林带突破' in text:
        indicators['布林带突破'] = 1
    
    return indicators

def count_bullish_indicators(indicators):
    """计算看涨技术指标的数量"""
    return sum(indicators.values())

def process_excel_file(file_path, date):
    """处理单个Excel文件"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 添加日期列
        df['日期'] = pd.to_datetime(date)
        
        # 处理技术指标
        technical_indicators = df['技术指标'].apply(extract_technical_indicators)
        
        # 添加各个技术指标列
        df['技术指标_均线多头排列'] = technical_indicators.apply(lambda x: x['均线多头排列'])
        df['技术指标_MACD金叉'] = technical_indicators.apply(lambda x: x['MACD金叉'])
        df['技术指标_RSI反弹'] = technical_indicators.apply(lambda x: x['RSI反弹'])
        df['技术指标_KDJ金叉'] = technical_indicators.apply(lambda x: x['KDJ金叉'])
        df['技术指标_布林带突破'] = technical_indicators.apply(lambda x: x['布林带突破'])
        
        # 计算看涨技术指标数量
        df['看涨技术指标数量'] = technical_indicators.apply(count_bullish_indicators)
        
        # 添加其他必要的列
        if '连续技术强度5天数' not in df.columns:
            # 根据技术强度估算连续技术强度5天数
            df['连续技术强度5天数'] = df['技术强度'] * 5
        
        if '价格趋势' not in df.columns:
            # 根据涨跌幅估算价格趋势
            df['价格趋势'] = np.where(df['涨跌幅'] > 0, 1, 0)
        
        if '涨跌幅趋势' not in df.columns:
            # 根据涨跌幅估算涨跌幅趋势
            df['涨跌幅趋势'] = np.where(df['涨跌幅'] > 0, 1, 0)
        
        return df
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return None

def process_all_data(data_dir, output_file='股票明细.xlsx'):
    """处理所有数据并合并"""
    # 获取所有选股结果文件夹
    folders = [f for f in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, f)) and f.startswith('选股结果_')]
    
    # 按日期排序文件夹
    folders.sort()
    
    all_data = []
    
    for folder in folders:
        date = extract_date_from_folder(folder)
        if date:
            # 查找Excel文件
            excel_files = glob.glob(os.path.join(data_dir, folder, '*.xlsx'))
            if excel_files:
                print(f"处理日期 {date} 的数据...")
                df = process_excel_file(excel_files[0], date)
                if df is not None:
                    all_data.append(df)
    
    if not all_data:
        print("没有找到有效的数据")
        return False
    
    # 合并所有数据
    merged_data = pd.concat(all_data, ignore_index=True)
    
    # 计算额外的特征
    print("计算额外的特征...")
    
    # 按股票代码和日期排序
    merged_data = merged_data.sort_values(['股票代码', '日期'])
    
    # 计算技术强度趋势
    merged_data['技术强度趋势'] = merged_data.groupby('股票代码')['技术强度'].diff().fillna(0)
    merged_data['技术强度趋势'] = np.where(merged_data['技术强度趋势'] > 0, 1, 0)
    
    # 计算连续技术强度3天数
    merged_data['连续技术强度3天数'] = merged_data.groupby('股票代码')['技术强度'].rolling(window=3, min_periods=1).sum().reset_index(level=0, drop=True)
    
    # 计算连续技术强度10天数
    merged_data['连续技术强度10天数'] = merged_data.groupby('股票代码')['技术强度'].rolling(window=10, min_periods=1).sum().reset_index(level=0, drop=True)
    
    # 计算各种趋势
    for period in [3, 5, 10]:
        col_name = f'连续技术强度{period}天数'
        if col_name in merged_data.columns:
            # 计算趋势
            trend_col = f'{col_name}趋势'
            merged_data[trend_col] = merged_data.groupby('股票代码')[col_name].diff().fillna(0)
            merged_data[trend_col] = np.where(merged_data[trend_col] > 0, 1, 0)
            
            # 计算价格趋势
            price_trend_col = f'{col_name}价格趋势'
            merged_data[price_trend_col] = merged_data['价格趋势']
            
            # 计算涨跌幅趋势
            change_trend_col = f'{col_name}涨跌幅趋势'
            merged_data[change_trend_col] = merged_data['涨跌幅趋势']
    
    # 添加是否盈利列（根据次日涨跌幅）
    merged_data['是否盈利'] = merged_data.groupby('股票代码')['涨跌幅'].shift(-1).fillna(0)
    merged_data['是否盈利'] = np.where(merged_data['是否盈利'] > 0, 1, 0)
    
    # 添加开盘涨跌列（假设为1，表示开盘上涨）
    merged_data['开盘涨跌'] = 1
    
    # 保存合并后的数据
    merged_data.to_excel(output_file, index=False)
    print(f"数据处理完成，共 {len(merged_data)} 条记录，已保存到 {output_file}")
    
    return True

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='修复股票数据Excel文件')
    parser.add_argument('--data_dir', type=str, default='E:/桌面/AI+BI/A股强势股选股系统_v1.0.0/A股强势股选股系统_v1.0.0', help='数据目录路径')
    parser.add_argument('--output', type=str, default='股票明细.xlsx', help='输出文件路径')
    
    args = parser.parse_args()
    
    process_all_data(args.data_dir, args.output)
