"""
数据获取模块

用于获取股票数据并保存到股票明细表中
"""

import os
import time
import datetime
import pandas as pd
import numpy as np
import requests
import json
import tushare as ts
from tqdm import tqdm

# 设置Tushare API Token（用户需要替换为自己的token）
TUSHARE_TOKEN = "your_tushare_token"

def setup_tushare():
    """设置Tushare API"""
    try:
        ts.set_token(TUSHARE_TOKEN)
        pro = ts.pro_api()
        return pro
    except Exception as e:
        print(f"设置Tushare API失败: {e}")
        return None

def get_stock_list(pro):
    """获取股票列表"""
    try:
        # 获取股票列表
        stock_data = pro.stock_basic(exchange='', list_status='L', 
                                     fields='ts_code,symbol,name,area,industry,list_date')
        return stock_data
    except Exception as e:
        print(f"获取股票列表失败: {e}")
        return None

def get_trade_dates(pro, start_date, end_date):
    """获取交易日历"""
    try:
        # 获取交易日历
        trade_cal = pro.trade_cal(exchange='', start_date=start_date, end_date=end_date)
        trade_dates = trade_cal[trade_cal['is_open'] == 1]['cal_date'].tolist()
        return trade_dates
    except Exception as e:
        print(f"获取交易日历失败: {e}")
        return None

def calculate_technical_indicators(df):
    """计算技术指标"""
    # 确保数据按日期排序
    df = df.sort_values('trade_date')
    
    # 计算移动平均线
    df['MA5'] = df['close'].rolling(window=5).mean()
    df['MA10'] = df['close'].rolling(window=10).mean()
    df['MA20'] = df['close'].rolling(window=20).mean()
    df['MA60'] = df['close'].rolling(window=60).mean()
    
    # 计算MACD
    df['EMA12'] = df['close'].ewm(span=12, adjust=False).mean()
    df['EMA26'] = df['close'].ewm(span=26, adjust=False).mean()
    df['DIF'] = df['EMA12'] - df['EMA26']
    df['DEA'] = df['DIF'].ewm(span=9, adjust=False).mean()
    df['MACD'] = 2 * (df['DIF'] - df['DEA'])
    
    # 计算RSI
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    avg_gain = gain.rolling(window=14).mean()
    avg_loss = loss.rolling(window=14).mean()
    rs = avg_gain / avg_loss
    df['RSI'] = 100 - (100 / (1 + rs))
    
    # 计算KDJ
    low_9 = df['low'].rolling(window=9).min()
    high_9 = df['high'].rolling(window=9).max()
    df['RSV'] = (df['close'] - low_9) / (high_9 - low_9) * 100
    df['K'] = df['RSV'].ewm(com=2).mean()
    df['D'] = df['K'].ewm(com=2).mean()
    df['J'] = 3 * df['K'] - 2 * df['D']
    
    # 计算布林带
    df['BOLL_MID'] = df['close'].rolling(window=20).mean()
    df['BOLL_STD'] = df['close'].rolling(window=20).std()
    df['BOLL_UP'] = df['BOLL_MID'] + 2 * df['BOLL_STD']
    df['BOLL_DOWN'] = df['BOLL_MID'] - 2 * df['BOLL_STD']
    
    return df

def calculate_technical_strength(df):
    """计算技术强度"""
    # 初始化技术指标列
    df['技术指标_均线多头排列'] = 0
    df['技术指标_MACD金叉'] = 0
    df['技术指标_RSI反弹'] = 0
    df['技术指标_KDJ金叉'] = 0
    df['技术指标_布林带突破'] = 0
    
    # 计算均线多头排列
    df.loc[(df['MA5'] > df['MA10']) & (df['MA10'] > df['MA20']) & (df['MA20'] > df['MA60']), '技术指标_均线多头排列'] = 1
    
    # 计算MACD金叉
    df.loc[(df['DIF'] > df['DEA']) & (df['DIF'].shift(1) <= df['DEA'].shift(1)), '技术指标_MACD金叉'] = 1
    
    # 计算RSI反弹
    df.loc[(df['RSI'] > 50) & (df['RSI'].shift(1) <= 50), '技术指标_RSI反弹'] = 1
    
    # 计算KDJ金叉
    df.loc[(df['K'] > df['D']) & (df['K'].shift(1) <= df['D'].shift(1)), '技术指标_KDJ金叉'] = 1
    
    # 计算布林带突破
    df.loc[df['close'] > df['BOLL_UP'], '技术指标_布林带突破'] = 1
    
    # 计算看涨技术指标数量
    df['看涨技术指标数量'] = df['技术指标_均线多头排列'] + df['技术指标_MACD金叉'] + df['技术指标_RSI反弹'] + df['技术指标_KDJ金叉'] + df['技术指标_布林带突破']
    
    # 计算技术强度
    df['技术强度'] = 0
    df.loc[df['看涨技术指标数量'] >= 4, '技术强度'] = 85
    df.loc[df['看涨技术指标数量'] == 5, '技术强度'] = 100
    
    return df

def calculate_continuous_strength(df, days=5):
    """计算连续技术强度"""
    # 初始化连续技术强度列
    col_name = f'连续技术强度{days}天数'
    df[col_name] = 0
    
    # 按股票代码分组
    grouped = df.groupby('ts_code')
    
    # 计算连续技术强度
    for name, group in grouped:
        # 确保数据按日期排序
        group = group.sort_values('trade_date')
        
        # 计算连续技术强度
        strength_sum = 0
        for i in range(len(group)):
            if i < days:
                # 不足days天的情况
                strength_sum += group.iloc[i]['技术强度']
            else:
                # days天及以上的情况
                strength_sum = strength_sum - group.iloc[i-days]['技术强度'] + group.iloc[i]['技术强度']
            
            # 更新连续技术强度
            df.loc[group.index[i], col_name] = strength_sum
    
    return df

def calculate_trends(df):
    """计算趋势"""
    # 初始化趋势列
    df['价格趋势'] = 0
    df['涨跌幅趋势'] = 0
    
    # 按股票代码分组
    grouped = df.groupby('ts_code')
    
    # 计算趋势
    for name, group in grouped:
        # 确保数据按日期排序
        group = group.sort_values('trade_date')
        
        # 计算价格趋势
        for i in range(1, len(group)):
            if group.iloc[i]['close'] > group.iloc[i-1]['close']:
                df.loc[group.index[i], '价格趋势'] = 1
        
        # 计算涨跌幅趋势
        for i in range(1, len(group)):
            if group.iloc[i]['pct_chg'] > group.iloc[i-1]['pct_chg']:
                df.loc[group.index[i], '涨跌幅趋势'] = 1
    
    return df

def get_stock_data(pro, ts_code, start_date, end_date):
    """获取单只股票的历史数据"""
    try:
        # 获取日线数据
        df = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
        
        # 获取基本信息
        stock_info = pro.stock_basic(ts_code=ts_code, fields='name,industry')
        
        if df.empty or stock_info.empty:
            return None
        
        # 添加股票名称和行业
        df['股票名称'] = stock_info['name'].values[0]
        df['行业'] = stock_info['industry'].values[0]
        
        # 计算技术指标
        df = calculate_technical_indicators(df)
        
        # 计算技术强度
        df = calculate_technical_strength(df)
        
        # 计算连续技术强度
        df = calculate_continuous_strength(df, days=5)
        
        # 计算趋势
        df = calculate_trends(df)
        
        # 添加开盘涨跌列
        df['开盘涨跌'] = 0
        df.loc[df['open'] > df['close'].shift(1), '开盘涨跌'] = 1
        
        # 添加次日买后日卖收益率列
        df['次日买后日卖收益率'] = df['close'].shift(-2) / df['open'].shift(-1) - 1
        
        # 添加是否盈利列
        df['是否盈利'] = 0
        df.loc[df['次日买后日卖收益率'] > 0, '是否盈利'] = 1
        
        # 重命名列
        df = df.rename(columns={
            'ts_code': '股票代码',
            'trade_date': '日期',
            'open': '开盘价',
            'high': '最高价',
            'low': '最低价',
            'close': '当前价格',
            'pre_close': '前收盘价',
            'change': '涨跌额',
            'pct_chg': '涨跌幅',
            'vol': '成交量',
            'amount': '成交额'
        })
        
        return df
    except Exception as e:
        print(f"获取股票 {ts_code} 的历史数据失败: {e}")
        return None

def fetch_all_stock_data(start_date, end_date, max_stocks=None, output_file='股票明细.xlsx'):
    """获取所有股票的历史数据并保存到Excel文件"""
    print(f"开始获取从 {start_date} 到 {end_date} 的股票数据")
    
    # 设置Tushare API
    pro = setup_tushare()
    if pro is None:
        return False
    
    # 获取股票列表
    stock_list = get_stock_list(pro)
    if stock_list is None:
        return False
    
    # 限制股票数量（用于测试）
    if max_stocks is not None:
        stock_list = stock_list.head(max_stocks)
    
    # 获取所有股票的历史数据
    all_data = []
    for i, row in tqdm(stock_list.iterrows(), total=len(stock_list), desc="获取股票数据"):
        ts_code = row['ts_code']
        stock_data = get_stock_data(pro, ts_code, start_date, end_date)
        if stock_data is not None:
            all_data.append(stock_data)
        
        # 避免频繁请求导致API限制
        time.sleep(0.5)
    
    # 合并所有数据
    if all_data:
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 保存到Excel文件
        combined_data.to_excel(output_file, index=False)
        print(f"股票数据已保存到 {output_file}，共 {len(combined_data)} 条记录")
        return True
    else:
        print("没有获取到任何股票数据")
        return False

def main():
    """主函数"""
    # 设置日期范围（默认为最近一个月）
    end_date = datetime.datetime.now().strftime('%Y%m%d')
    start_date = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime('%Y%m%d')
    
    # 获取命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='获取股票数据')
    parser.add_argument('--start_date', type=str, default=start_date, help='开始日期 (YYYYMMDD)')
    parser.add_argument('--end_date', type=str, default=end_date, help='结束日期 (YYYYMMDD)')
    parser.add_argument('--max_stocks', type=int, default=None, help='最大股票数量')
    parser.add_argument('--output', type=str, default='股票明细.xlsx', help='输出文件路径')
    args = parser.parse_args()
    
    # 获取股票数据
    fetch_all_stock_data(args.start_date, args.end_date, args.max_stocks, args.output)

if __name__ == "__main__":
    main()
