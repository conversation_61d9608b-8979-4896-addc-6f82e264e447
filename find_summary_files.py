import os
import pandas as pd

def find_files(directory, pattern):
    """查找指定目录下包含特定模式的所有文件"""
    found_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if pattern.lower() in file.lower():
                found_files.append(os.path.join(root, file))
    return found_files

def check_excel_columns(file_path):
    """检查Excel文件的列名"""
    try:
        df = pd.read_excel(file_path)
        print(f"文件: {file_path}")
        print(f"列名: {df.columns.tolist()}")
        print(f"行数: {len(df)}")
        print("前5行数据:")
        print(df.head())
        print("-" * 80)
        return True
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return False

# 查找所有包含"summary"的Excel文件
base_dir = "E:/机器学习"
summary_files = find_files(base_dir, "summary")
print(f"找到 {len(summary_files)} 个包含'summary'的文件:")
for file in summary_files:
    if file.endswith('.xlsx') or file.endswith('.xls'):
        print(f"- {file}")
        check_excel_columns(file)

# 查找所有包含"策略"和"汇总"的Excel文件
strategy_summary_files = find_files(base_dir, "策略") + find_files(base_dir, "汇总")
print(f"\n找到 {len(strategy_summary_files)} 个包含'策略'或'汇总'的文件:")
for file in strategy_summary_files:
    if file.endswith('.xlsx') or file.endswith('.xls'):
        print(f"- {file}")
        check_excel_columns(file)
