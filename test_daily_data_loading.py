#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试从按日期存储的技术强度文件中加载数据的功能
"""

import pandas as pd
import os
import glob
import time
import sys

# 添加当前目录到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

def test_daily_data_loading():
    """测试从按日期存储的文件中加载数据"""
    print("=== 测试从按日期存储的技术强度文件中加载数据 ===")
    
    # 设置基础目录
    base_dir = r'E:\机器学习\complete_excel_results'
    tech_strength_daily_dir = os.path.join(base_dir, 'tech_strength', 'daily')
    
    print(f"技术强度日期文件夹: {tech_strength_daily_dir}")
    
    # 检查目录是否存在
    if not os.path.exists(tech_strength_daily_dir):
        print(f"错误: 技术强度日期文件夹不存在: {tech_strength_daily_dir}")
        return False
    
    # 查找所有技术强度文件
    pattern = os.path.join(tech_strength_daily_dir, "tech_strength_strong_*_smart.xlsx")
    tech_files = glob.glob(pattern)
    
    if not tech_files:
        print(f"错误: 在 {tech_strength_daily_dir} 中没有找到技术强度文件")
        return False
    
    print(f"找到 {len(tech_files)} 个技术强度文件")
    
    # 显示前几个文件
    print("前5个文件:")
    for i, file_path in enumerate(tech_files[:5]):
        print(f"  {i+1}. {os.path.basename(file_path)}")
    
    # 测试读取几个文件
    print("\n=== 测试读取文件 ===")
    all_data = []
    test_files = tech_files[:3]  # 只测试前3个文件
    
    for file_path in test_files:
        try:
            # 从文件名中提取日期
            filename = os.path.basename(file_path)
            # 文件名格式: tech_strength_strong_2025-05-15_smart.xlsx
            date_part = filename.replace('tech_strength_strong_', '').replace('_smart.xlsx', '')
            
            print(f"读取文件: {filename}")
            print(f"提取的日期: {date_part}")
            
            # 读取文件
            start_time = time.time()
            df = pd.read_excel(file_path)
            load_time = time.time() - start_time
            
            print(f"  文件大小: {len(df)} 行")
            print(f"  加载耗时: {load_time:.2f}秒")
            print(f"  列名: {list(df.columns)}")
            
            # 添加日期列
            df['日期'] = pd.to_datetime(date_part)
            
            all_data.append(df)
            print(f"  成功读取 {date_part} 的数据")
            
            # 显示前几行数据
            if len(df) > 0:
                print(f"  前3行数据:")
                print(df[['股票代码', '股票名称', '技术强度', '日期']].head(3).to_string(index=False))
            
            print()
            
        except Exception as e:
            print(f"  读取文件 {file_path} 时出错: {e}")
            continue
    
    if not all_data:
        print("错误: 没有成功读取任何技术强度文件")
        return False
    
    # 合并所有数据
    print("=== 合并数据 ===")
    start_time = time.time()
    combined_df = pd.concat(all_data, ignore_index=True)
    merge_time = time.time() - start_time
    
    print(f"成功合并技术强度数据，共 {len(combined_df)} 条记录")
    print(f"合并耗时: {merge_time:.2f}秒")
    
    # 检查数据质量
    print("\n=== 数据质量检查 ===")
    print(f"总记录数: {len(combined_df)}")
    print(f"唯一股票数: {combined_df['股票代码'].nunique()}")
    print(f"唯一日期数: {combined_df['日期'].nunique()}")
    print(f"日期范围: {combined_df['日期'].min()} 到 {combined_df['日期'].max()}")
    
    # 检查必要的列是否存在
    required_columns = ['股票代码', '股票名称', '技术强度', '日期']
    missing_columns = [col for col in required_columns if col not in combined_df.columns]
    
    if missing_columns:
        print(f"警告: 缺少必要的列: {missing_columns}")
    else:
        print("✓ 所有必要的列都存在")
    
    # 检查数据类型
    print(f"\n数据类型:")
    for col in required_columns:
        if col in combined_df.columns:
            print(f"  {col}: {combined_df[col].dtype}")
    
    # 显示一些统计信息
    if '技术强度' in combined_df.columns:
        print(f"\n技术强度统计:")
        print(f"  最小值: {combined_df['技术强度'].min()}")
        print(f"  最大值: {combined_df['技术强度'].max()}")
        print(f"  平均值: {combined_df['技术强度'].mean():.2f}")
        print(f"  技术强度>0的记录数: {len(combined_df[combined_df['技术强度'] > 0])}")
    
    print("\n=== 测试完成 ===")
    return True

def compare_with_original_file():
    """与原始的股票明细_完整.xlsx文件进行比较"""
    print("\n=== 与原始文件比较 ===")
    
    base_dir = r'E:\机器学习\complete_excel_results'
    original_file = os.path.join(base_dir, '股票明细_完整.xlsx')
    
    if not os.path.exists(original_file):
        print(f"原始文件不存在: {original_file}")
        return
    
    try:
        print("读取原始股票明细_完整.xlsx文件...")
        start_time = time.time()
        original_df = pd.read_excel(original_file)
        load_time = time.time() - start_time
        
        print(f"原始文件记录数: {len(original_df)}")
        print(f"原始文件加载耗时: {load_time:.2f}秒")
        
        if '日期' in original_df.columns:
            original_df['日期'] = pd.to_datetime(original_df['日期'])
            print(f"原始文件日期范围: {original_df['日期'].min()} 到 {original_df['日期'].max()}")
            print(f"原始文件唯一日期数: {original_df['日期'].nunique()}")
        
        print("原始文件列名:")
        print(list(original_df.columns))
        
    except Exception as e:
        print(f"读取原始文件时出错: {e}")

if __name__ == "__main__":
    # 测试新的数据加载功能
    success = test_daily_data_loading()
    
    if success:
        # 与原始文件比较
        compare_with_original_file()
    
    print("\n测试结束")
